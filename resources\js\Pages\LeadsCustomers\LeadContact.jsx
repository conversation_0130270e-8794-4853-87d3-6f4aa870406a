import {
    Button,
    <PERSON>ltip,
    Table,
    Label,
    TextInput,
    Select,
    Tabs,
    Badge,
    Datepicker,
    ToggleSwitch,
    Textarea,
    FileInput,
    Modal,
    ButtonGroup,
    Checkbox,
    Dropdown,
    Avatar,
} from "flowbite-react";
import { HiDownload } from 'react-icons/hi'
import { useState, useEffect } from "react";
import $ from "jquery";
import {  BiCommentAdd } from "react-icons/bi";
import {  MdOutlinePermContactCalendar, MdOutlineCall, MdOutlineTextsms, MdCallSplit, MdOutlinePlagiarism, MdOutlineEdit, MdOutlinePhoneMissed, MdOutlineMarkEmailRead, MdPendingActions, MdCallReceived, MdAdd, MdOutlineSmsFailed, MdBlock, MdOutlineArrowDropDown, MdOutlineHandyman, MdOutlineFingerprint } from "react-icons/md";
import { RiCheckDoubleFill, RiMailOpenLine } from "react-icons/ri";
import { button_custom, input_custom, tabBar_underline, table_custom, textarea_custom, toggle_custom } from "../Helpers/DesignHelper";
import { FaBold, FaItalic, FaRegFaceSmileBeam, FaRegNoteSticky, FaStrikethrough, FaWhatsapp } from "react-icons/fa6";
import { PiCaretUpDownBold, PiPhoneList } from "react-icons/pi";
import { AiOutlineMergeCells } from "react-icons/ai";
import { IoMdAdd, IoMdAlarm, IoMdImage } from "react-icons/io";
import { CgAttachment } from "react-icons/cg";
import EmojiPicker from "emoji-picker-react";
import LeadSplit from "./LeadSplit";
import { Link } from "@inertiajs/react";
import { FiPhoneCall } from "react-icons/fi";
import { LuCornerUpLeft, LuPhoneForwarded, LuPhoneOutgoing } from "react-icons/lu";
import { HiOutlineMailOpen } from "react-icons/hi";
import { VscSend } from "react-icons/vsc";
import { TiStarOutline } from "react-icons/ti";

export default function LeadContact() {
    const [openEmoji, setOpenEmoji] = useState(false);
    const [openMissedPunchModal, setMissedPunchOpenModal] = useState(false);
    const [switch2, setSwitch2] = useState(false);

    const textinput_custom = {
        colors: {
            gray: "border-gray-300 bg-gray-50 text-gray-900 focus:border-blue-500 focus:ring-blue-500 dark:border-gray-600 dark:bg-gray-700 dark:text-white dark:placeholder-gray-400 dark:focus:border-blue-500 dark:focus:ring-blue-500",
        },
    };
    const custom_datepicker = {
        root: {
            base: "relative",
        },
        field: {
            base: "relative w-full",
            icon: {
                base: "pointer-events-none absolute inset-y-0 end-3.5 flex items-center pl-3",
                svg: "h-5 w-5 text-slate-400 dark:text-gray-400",
            },
            rightIcon: {
                base: "pointer-events-none absolute border-s inset-y-0 right-0 flex items-center ps-2 pr-3  text-slate-300",
                svg: "h-5 w-5 text-slate-400 dark:text-gray-400 ",
            },
        },
        input: {
            base: "block w-full border disabled:cursor-not-allowed disabled:opacity-50",
            sizes: {
                sm: "p-2 sm:text-xs",
                md: "p-2.5 text-sm",
                lg: "p-4 sm:text-base",
            },
            gray: "border-blue-300 bg-blue-50 text-blue-900 placeholder-blue-700 focus:border-blue-500 focus:ring-blue-500 dark:border-blue-400 dark:bg-blue-100 dark:focus:border-blue-500 dark:focus:ring-blue-500",
        },

        popup: {
            root: {
                base: "absolute bottom-20 z-50 block pt-2",
                inline: "relative top-0 z-auto",
                inner: "inline-block rounded-lg bg-white p-4 shadow-lg dark:bg-gray-700",
            },
            header: {
                base: "",
                title: "px-2 py-3 text-center font-semibold text-gray-900 dark:text-white",
                selectors: {
                    base: "mb-2 flex justify-between",
                    button: {
                        base: "rounded-lg bg-white px-5 py-2.5 text-sm font-semibold text-gray-900 hover:bg-gray-100 focus:outline-none focus:ring-2 focus:ring-gray-200 dark:bg-gray-700 dark:text-white dark:hover:bg-gray-600",
                        prev: "",
                        next: "",
                        view: "",
                    },
                },
            },
            view: {
                base: "p-1",
            },
            footer: {
                base: "mt-2 flex space-x-2",
                button: {
                    base: "w-full rounded-lg px-5 py-2 text-center text-sm font-medium focus:ring-4 focus:ring-blue-300",
                    today: "bg-blue-700 text-white hover:bg-blue-800 dark:bg-blue-600 dark:hover:bg-blue-700",
                    clear: "border border-gray-300 bg-white text-gray-900 hover:bg-gray-100 dark:border-gray-600 dark:bg-gray-700 dark:text-white dark:hover:bg-gray-600",
                },
            },
        },
        views: {
            days: {
                header: {
                    base: "mb-1 grid grid-cols-7",
                    title: "h-6 text-center text-sm font-medium leading-6 text-gray-500 dark:text-gray-400",
                },
                items: {
                    base: "grid w-64 grid-cols-7",
                    item: {
                        base: "block flex-1 cursor-pointer rounded-lg border-0 text-center text-sm font-semibold leading-9 text-gray-900 hover:bg-gray-100 dark:text-white dark:hover:bg-gray-600",
                        selected: "bg-blue-700 text-white hover:bg-blue-600",
                        disabled: "text-gray-500",
                    },
                },
            },
            months: {
                items: {
                    base: "grid w-64 grid-cols-4",
                    item: {
                        base: "block flex-1 cursor-pointer rounded-lg border-0 text-center text-sm font-semibold leading-9 text-gray-900 hover:bg-gray-100 dark:text-white dark:hover:bg-gray-600",
                        selected: "bg-cyan-700 text-white hover:bg-cyan-600",
                        disabled: "text-gray-500",
                    },
                },
            },
            years: {
                items: {
                    base: "grid w-64 grid-cols-4",
                    item: {
                        base: "block flex-1 cursor-pointer rounded-lg border-0 text-center text-sm font-semibold leading-9 text-gray-900 hover:bg-gray-100 dark:text-white dark:hover:bg-gray-600",
                        selected: "bg-cyan-700 text-white hover:bg-cyan-600",
                        disabled: "text-gray-500",
                    },
                },
            },
            decades: {
                items: {
                    base: "grid w-64 grid-cols-4",
                    item: {
                        base: "block flex-1 cursor-pointer rounded-lg border-0 text-center text-sm font-semibold leading-9 text-gray-900 hover:bg-gray-100 dark:text-white dark:hover:bg-gray-600",
                        selected: "bg-cyan-700 text-white hover:bg-cyan-600",
                        disabled: "text-gray-500",
                    },
                },
            },
        },
    };

    const [isCheckAll, setIsCheckAll] = useState(false);

    useEffect(() => {
        if (isCheckAll) {
            $(".rowCheckBox").prop("checked", true);
        } else {
            $(".rowCheckBox").prop("checked", false);
        }
    }, [isCheckAll]);

    return (
        <>
            <div className="m-2">
                <Tabs
                    theme={tabBar_underline}
                    aria-label="Tabs with underline"
                    variant="underline"
                >
                    {/* Contacts */}
                    <Tabs.Item
                        className=""
                        active
                        title={
                            <div className="flex items-center gap-2">
                                Contacts
                                <sup>
                                    <Badge
                                        className="rounded-full bg-fuchsia-600 text-white"
                                        size="xs"
                                    >
                                        05
                                    </Badge>
                                </sup>
                            </div>
                        }
                        icon={MdOutlinePermContactCalendar}
                    >
                        <div className=" flex flex-col justify-between">
                            <div className="bg-white h-[60vh] rounded-lg border">
                                {/* documents table */}
                                <div className="overflow-x-auto">
                                    <Table hoverable theme={table_custom}>
                                        <Table.Head className=" bg-slate-100">
                                            <Table.HeadCell>
                                                <div className="flex items-center justify-between gap-2">
                                                    <h3>Contacts</h3>
                                                    <PiCaretUpDownBold />
                                                </div>
                                            </Table.HeadCell>

                                            <Table.HeadCell>
                                                <h3>Number/ Email</h3>
                                            </Table.HeadCell>

                                            <Table.HeadCell>
                                                <h3>DND</h3>
                                            </Table.HeadCell>

                                            <Table.HeadCell>
                                            </Table.HeadCell>

                                            <Table.HeadCell>
                                                <h3>Split Request</h3>
                                            </Table.HeadCell>
                                        </Table.Head>

                                        <Table.Body className="divide-y">
                                            <Table.Row className="bg-white dark:border-gray-700 dark:bg-gray-800">
                                                <Table.Cell className="text-blue-500">
                                                    1/07/2024
                                                </Table.Cell>

                                                <Table.Cell className="font-medium text-gray-900 whitespace-nowrap dark:text-white">
                                                    <div className="flex flex-wrap items-center gap-2 text-nowrap w-fit">
                                                        <img
                                                            src="https://img.freepik.com/premium-photo/profile-icon-white-background_941097-160810.jpg?w=50"
                                                            alt=""
                                                            className="rounded-full"
                                                        />
                                                        <div>
                                                            <div className="text-blue-500 text-sm text-nowrap">
                                                                Rohan Preet
                                                                (2568)
                                                            </div>
                                                            <div className="text-xs text-nowrap">
                                                                Sales Executive
                                                            </div>
                                                        </div>
                                                    </div>
                                                </Table.Cell>
                                                <Table.Cell>
                                                    <ToggleSwitch
                                                        theme={toggle_custom}
                                                        color="blue"
                                                        sizing="xs"
                                                        checked={switch2}
                                                        onChange={setSwitch2}
                                                    />
                                                </Table.Cell>
                                                <Table.Cell>
                                                    <div className="flex gap-2 justify-between">
                                                        <div className="flex gap-2">
                                                            <Tooltip
                                                                content="Reject"
                                                                className="bg-slate-700 p-1 px-2"
                                                            >
                                                                <Button
                                                                    theme={
                                                                        button_custom
                                                                    }
                                                                    color="blue"
                                                                    size="xxs"
                                                                >
                                                                    {" "}
                                                                    <MdOutlineCall className="text-lg" />
                                                                </Button>
                                                            </Tooltip>

                                                            <Tooltip
                                                                content="Approve"
                                                                className="bg-slate-700 p-1 px-2"
                                                            >
                                                                <Button
                                                                    theme={
                                                                        button_custom
                                                                    }
                                                                    size="xxs"
                                                                    color="green"
                                                                >
                                                                    {" "}
                                                                    <FaWhatsapp className="text-lg" />
                                                                </Button>
                                                            </Tooltip>
                                                            <Tooltip
                                                                content="Approve"
                                                                className="bg-slate-700 p-1 px-2"
                                                            >
                                                                <Button
                                                                    theme={
                                                                        button_custom
                                                                    }
                                                                    size="xxs"
                                                                    color="warning"
                                                                >
                                                                    {" "}
                                                                    <MdOutlineTextsms className="text-lg" />
                                                                </Button>
                                                            </Tooltip>
                                                        </div>
                                                    </div>
                                                </Table.Cell>
                                                <Table.Cell className="text-nowrap">
                                                    <div className="flex items-center justify-between gap-4">
                                                        <Tooltip
                                                            content="Lead Split"
                                                            className="bg-slate-700 p-1 px-2"
                                                        >
                                                            <Button
                                                                theme={
                                                                    button_custom
                                                                }
                                                                color="Fuchsia_custom"
                                                                size="xs"
                                                                onClick={() =>
                                                                    setMissedPunchOpenModal(true)
                                                                }
                                                            >
                                                                <div className="flex items-center gap-1">
                                                                    <MdCallSplit />
                                                                    <span className="text-sm">Lead Split</span>
                                                                </div>
                                                            </Button>
                                                        </Tooltip>
                                                        <div className="flex gap-2 justify-between">
                                                            <div className="flex gap-2">
                                                                <Tooltip
                                                                    content="Reject"
                                                                    className="bg-slate-700 p-1 px-2"
                                                                >
                                                                    <Button
                                                                        theme={
                                                                            button_custom
                                                                        }
                                                                        color="orange"
                                                                        size="xs"
                                                                    >
                                                                        <div className="flex items-center gap-1">
                                                                            <MdOutlinePlagiarism />
                                                                            <span className="text-sm">Details</span>
                                                                        </div>
                                                                    </Button>
                                                                </Tooltip>

                                                                <Tooltip
                                                                    content="Approve"
                                                                    className="bg-slate-700 p-1 px-2"
                                                                >
                                                                    <Button
                                                                        theme={
                                                                            button_custom
                                                                        }
                                                                        color="green"
                                                                        size="xs"
                                                                    >
                                                                        <div className="flex items-center gap-1">
                                                                            <MdOutlineEdit />
                                                                            <span className="text-sm">Edit</span>
                                                                        </div>
                                                                    </Button>
                                                                </Tooltip>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </Table.Cell>
                                            </Table.Row>
                                        </Table.Body>
                                    </Table>
                                </div>
                            </div>

                            <div className="float-end border bottom-0 p-3 w-full bg-white rounded-b-lg">
                                <div className="flex flex-wrap justify-between lg:gap-0 md:gap-0 gap-2">
                                    <div className="flex gap-3 items-center">
                                        <div className="text-gray-400 text-sm ">
                                            Showing 1 to 25 of 62 entries
                                        </div>
                                        <div>
                                            <select
                                                id="countries"
                                                className=" bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded focus:ring-blue-500 focus:border-blue-500 block p-1 ps-2 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-blue-500 dark:focus:border-blue-500"
                                            >
                                                <option
                                                    value={10}
                                                    defaultValue={10}
                                                >
                                                    10
                                                </option>
                                                <option value={15}>15</option>
                                                <option value={20}>20</option>
                                                <option value={25}>25</option>
                                                <option value={30}>30</option>
                                            </select>
                                        </div>
                                    </div>
                                    <div className="flex">
                                        <Button
                                            size="xs"
                                            color="gray"
                                            className="border-e-0 text-gray-400 px-2 rounded text-xs "
                                        >
                                            Previous
                                        </Button>
                                        <Button
                                            size="xs"
                                            color="blue"
                                            className="border text-white border-blue-600 bg-blue-600 px-2.5 rounded-none text-sm "
                                        >
                                            1
                                        </Button>
                                        <Button
                                            size="xs"
                                            color="gray"
                                            className="border text-blue-600 px-2.5 rounded-none text-xs "
                                        >
                                            2
                                        </Button>
                                        <Button
                                            size="xs"
                                            color="gray"
                                            className="border-s-0 text-blue-600 px-2 rounded text-xs "
                                        >
                                            Next
                                        </Button>
                                    </div>
                                </div>
                            </div>

                            <Tabs
                                className="mt-1"
                                theme={tabBar_underline}
                                aria-label="Tabs with underline"
                                variant="underline"
                            >
                                <Tabs.Item
                                    className=""
                                    active
                                    title={
                                        <div className="flex items-center gap-2">
                                            Add Contact
                                            <sup>
                                                <Badge
                                                    className="rounded-full bg-fuchsia-600 text-white"
                                                    size="xs"
                                                >
                                                    05
                                                </Badge>
                                            </sup>
                                        </div>
                                    }
                                    icon={BiCommentAdd}
                                >
                                    <div className="">
                                        <div className="bg-white pt-2 px-4">
                                            <div className="">
                                                <form className="flex gap-7">
                                                    <div className="w-full">
                                                        <div className="mb-2 block">
                                                            <Label
                                                                htmlFor="PrincipalPaid"
                                                                value="Name"
                                                            />
                                                        </div>
                                                        <TextInput
                                                            theme={input_custom}
                                                            color="gray"
                                                            id="PrincipalPaid"
                                                            type="text"
                                                            // placeholder="0"
                                                            required
                                                        />
                                                    </div>
                                                    <div className="w-full">
                                                        <div className="mb-2 block">
                                                            <Label
                                                                htmlFor="PrincipalPaid"
                                                                value="Designation"
                                                            />
                                                        </div>
                                                        <TextInput
                                                            theme={input_custom}
                                                            color="gray"
                                                            id="PrincipalPaid"
                                                            type="text"
                                                            // placeholder="0"
                                                            required
                                                        />
                                                    </div>
                                                </form>
                                                <form className="flex gap-2 mt-2 items-center">
                                                    <div className="w-full">
                                                        <div className="mb-2 block">
                                                            <Label
                                                                htmlFor="PrincipalPaid"
                                                                value="Mobile Number"
                                                            />
                                                        </div>
                                                        <TextInput
                                                            theme={input_custom}
                                                            color="gray"
                                                            id="PrincipalPaid"
                                                            type="text"
                                                            // placeholder="0"
                                                            required
                                                        />
                                                    </div>
                                                    <span className="mt-7">OR</span>
                                                    <div className="w-full">
                                                        <div className="mb-2 block">
                                                            <Label
                                                                htmlFor="PrincipalPaid"
                                                                value="Email Id"
                                                            />
                                                        </div>
                                                        <TextInput
                                                            theme={input_custom}
                                                            color="gray"
                                                            id="PrincipalPaid"
                                                            type="text"
                                                            // placeholder="0"
                                                            required
                                                        />
                                                    </div>
                                                </form>
                                                <div className="flex justify-end gap-5 py-3">
                                                    <Button
                                                        theme={button_custom}
                                                        color="blue"
                                                        className="rounded"
                                                        size="xs"
                                                    >
                                                        <div className="flex items-center gap-0.5">
                                                            <IoMdAdd className="text-xl" />
                                                            <span className="text-sm">
                                                                Add Contact
                                                            </span>
                                                        </div>
                                                    </Button>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </Tabs.Item>
                                <Tabs.Item
                                    className=""
                                    active
                                    title={
                                        <div className="flex items-center gap-2">
                                            Merge Lead
                                            <sup>
                                                <Badge
                                                    className="rounded-full bg-fuchsia-600 text-white"
                                                    size="xs">
                                                    05
                                                </Badge>
                                            </sup>
                                        </div>
                                    }
                                    icon={AiOutlineMergeCells}
                                >
                                    <div className="">
                                        <div >
                                            <Label htmlFor="">Reason</Label>
                                            <select
                                                id="countries"
                                                className="mt-1 w-full bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block p-2 ps-2 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-blue-500 dark:focus:border-blue-500"
                                            >
                                                <option
                                                    value={10}
                                                    defaultValue={10}
                                                >
                                                    10
                                                </option>
                                                <option value={15}>15</option>
                                                <option value={20}>20</option>
                                                <option value={25}>25</option>
                                                <option value={30}>30</option>
                                            </select>
                                        </div>
                                        <div className="">
                                            <div className="mt-2">
                                                {openEmoji && (
                                                    <div
                                                        className="absolute z-50 "
                                                        style={{
                                                            top: "25%",
                                                            bottom: "25%",
                                                        }}
                                                    >
                                                        <EmojiPicker
                                                            open={openEmoji}
                                                            lazyLoadEmojis={true}
                                                        />
                                                    </div>
                                                )}
                                                <Label htmlFor="">Reason</Label>

                                                <div className="mt-1 p-2 border border-gray-300 rounded-md bg-gray-50">
                                                    <Textarea
                                                        theme={textarea_custom}
                                                        name="message"
                                                        placeholder="Type Message here..."
                                                        rows={4}
                                                        id="myTextarea"
                                                        className="border-0 focus:ring-white"
                                                    />

                                                    <div className="flex flex-col flex-wrap justify-between gap-1 flex-center lg:flex-row">
                                                        <div className="flex flex-wrap items-center gap-1 space-x-1">
                                                            <div className="">
                                                                <Button
                                                                    className="px-0"
                                                                    size="xs"
                                                                    color="gray"
                                                                >
                                                                    <FaRegFaceSmileBeam />
                                                                </Button>
                                                            </div>
                                                            <div className="">
                                                                <Button
                                                                    className="px-0"
                                                                    size="xs"
                                                                    color="gray"
                                                                    id="boldButton"
                                                                >
                                                                    <FaBold />
                                                                </Button>
                                                            </div>
                                                            <div className="">
                                                                <Button
                                                                    className="px-0"
                                                                    size="xs"
                                                                    color="gray"
                                                                    id="italicButton"
                                                                >
                                                                    <FaItalic />
                                                                </Button>
                                                            </div>
                                                            <div className="">
                                                                <Button
                                                                    className="px-0"
                                                                    size="xs"
                                                                    color="gray"
                                                                    id="strike"
                                                                >
                                                                    <FaStrikethrough />
                                                                </Button>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                            <div className="flex justify-end gap-5 py-3">
                                                <Button
                                                    theme={button_custom}
                                                    color="blue"
                                                    className="rounded"
                                                    size="xs"
                                                >
                                                    <div className="flex items-center gap-0.5">
                                                        <AiOutlineMergeCells className="text-xl" />
                                                        <span className="text-sm">
                                                            Merge Lead
                                                        </span>
                                                    </div>
                                                </Button>
                                            </div>
                                        </div>
                                    </div>
                                </Tabs.Item>
                            </Tabs>
                        </div>
                    </Tabs.Item>
                    {/* Call */}
                    <Tabs.Item
                        title={
                            <div className="flex items-center gap-2">
                                Call
                                <sup>
                                    <Badge
                                        className="rounded-full bg-sky-600 text-white"
                                        size="xs"
                                    >
                                        05
                                    </Badge>
                                </sup>
                            </div>
                        }
                        icon={MdOutlineCall}
                    >
                        <div className=" flex flex-col justify-between">
                            <div className="flex items-center bg-white rounded-md w-full p-2 px-3 mb-1.5">
                                <ButtonGroup className="h-fit">
                                    <Button
                                        className=""
                                        size="xs"
                                        color="gray"
                                        theme={button_custom}
                                    // onClick={deleteRecords}
                                    >
                                        <div className="flex items-center gap-1">
                                            <PiPhoneList className="text-slate-500" />
                                            <span className="text-xs">All</span>
                                        </div>
                                    </Button>
                                    <Button
                                        className=""
                                        size="xs"
                                        color="gray"
                                        theme={button_custom}
                                    // onClick={deleteRecords}
                                    >
                                        <div className="flex items-center gap-1">
                                            <MdOutlinePhoneMissed className="text-red-600 text-sm ms-1" />
                                            <span className="text-xs">Missed</span>
                                        </div>
                                    </Button>
                                    <Button
                                        className=""
                                        size="xs"
                                        color="gray"
                                        theme={button_custom}
                                    // onClick={deleteRecords}
                                    >
                                        <div className="flex items-center gap-1">
                                            <FiPhoneCall className="text-green-500 text-sm ms-1" />
                                            <span className="text-xs">All</span>
                                        </div>
                                    </Button>
                                    <Button
                                        className=""
                                        size="xs"
                                        color="gray"
                                        theme={button_custom}
                                    // onClick={deleteRecords}
                                    >
                                        <div className="flex items-center gap-1">
                                            <LuPhoneOutgoing className="text-blue-500 text-sm ms-1" />
                                            <span className="text-xs">Outgoing</span>
                                        </div>
                                    </Button>
                                    <Button
                                        className=""
                                        size="xs"
                                        color="gray"
                                        theme={button_custom}
                                    // onClick={deleteRecords}
                                    >
                                        <div className="flex items-center gap-1">
                                            <IoMdAlarm className="text-cyan-600 text-sm ms-1" />
                                            <span className="text-xs">Duration</span>
                                        </div>
                                    </Button>
                                </ButtonGroup>
                            </div>
                            <div className="bg-white h-[60vh] rounded-lg border">

                                {/* documents table */}
                                <div className="overflow-x-auto">
                                    <Table hoverable theme={table_custom}>
                                        <Table.Head className="bg-slate-100">
                                            <Table.HeadCell>
                                            </Table.HeadCell>
                                            <Table.HeadCell>
                                                <Link>
                                                    <div className="flex items-center justify-between gap-2">
                                                        <h3>Lead Name</h3>
                                                        <PiCaretUpDownBold
                                                        />
                                                    </div>
                                                </Link>
                                            </Table.HeadCell>
                                            <Table.HeadCell>
                                                <Link>
                                                    <div className="flex items-center justify-between gap-2">
                                                        <h3>Date & Time</h3>
                                                        <PiCaretUpDownBold
                                                        />
                                                    </div>
                                                </Link>
                                            </Table.HeadCell>

                                            <Table.HeadCell>
                                                <Link>
                                                    <div className="flex items-center justify-between gap-2">
                                                        <h3>Duration</h3>
                                                    </div>
                                                </Link>
                                            </Table.HeadCell>
                                            <Table.HeadCell>
                                                Agent
                                            </Table.HeadCell>
                                            <Table.HeadCell>
                                            </Table.HeadCell>
                                        </Table.Head>
                                        <Table.Body className="divide-y">
                                            <Table.Row
                                                className="items-center bg-white dark:border-gray-700 dark:bg-gray-800"
                                            // key={"campaign" + k}
                                            >
                                                <Table.Cell>
                                                    <MdOutlinePhoneMissed className='text-red-600' />
                                                </Table.Cell>
                                                <Table.Cell className="text-blue-600 ">{

                                                    "Prachi < +91 8562014780> [5620]"
                                                }
                                                </Table.Cell>
                                                <Table.Cell className="text-blue-600 ">
                                                    <div className="flex flex-col">
                                                        <span className="font-medium">
                                                            11/1/2024
                                                        </span>
                                                        <span className="text-xs text-slate-500">
                                                            5:21
                                                        </span>
                                                    </div>
                                                </Table.Cell>

                                                <Table.Cell
                                                    // onClick={() => {
                                                    //     setIsOpenDetail(true);
                                                    //     setCampaignID({
                                                    //         id: "73",
                                                    //         name: "Dana Gates",
                                                    //         status: "Terminated",
                                                    //     });
                                                    // }}
                                                    className="cursor-pointer "
                                                >
                                                    Dana Gates
                                                    {/* {campaign.name} */}
                                                </Table.Cell>
                                                <Table.Cell>
                                                    <img
                                                        className="rounded-full h-8 w-8"
                                                        src="https://img.freepik.com/free-vector/businessman-character-avatar-isolated_24877-60111.jpg?t=st=1720614745~exp=1720618345~hmac=38ff343a9443982af738325ad4d54d1d39ba540be4c28013b03cf063347d255e&w=200"
                                                        alt=""
                                                    />
                                                </Table.Cell>
                                                <Table.Cell>
                                                    <div className="flex items-center justify-between gap-2">
                                                        <audio className="h-12" autoPlay="autoPlay" controls="controls" id="player">
                                                            <source src="http://www.issilissinew.com/zindorg/1/mp3/2014/Mukunda/128/02%20-%20Daredumdadum%20%5bwww.AtoZmp3.in%5d.mp3.ogg" />
                                                            <source src="http://www.issilissinew.com/zindorg/1/mp3/2014/Mukunda/128/02%20-%20Daredumdadum%20%5bwww.AtoZmp3.in%5d.mp3" />
                                                            <p> Your browser doesn't support the audio tag </p>
                                                        </audio>
                                                        <Tooltip
                                                            content="Call"
                                                        >
                                                            <Button
                                                                theme={
                                                                    button_custom
                                                                }
                                                                size="xxs"
                                                                color="Fuchsia_custom"
                                                            >
                                                                <div className="flex items-center gap-1">
                                                                    <MdOutlineCall className="text-lg" />
                                                                    Call
                                                                </div>
                                                            </Button>
                                                        </Tooltip>
                                                    </div>
                                                    {/* <div>
                                                    <button onclick="document.get('player').play()">Play</button>
                                                      <button onclick="document.get('player').pause()">Pause</button>
                                                       <button onclick="document.getElementById('player').volume+ = 0.5">Vol+ </button>
                                                    <button onclick="document.get('player').volume- = 0.5">Vol- </button>
                                                 </div>   */}
                                                </Table.Cell>
                                            </Table.Row>
                                            <Table.Row
                                                className="items-center bg-white dark:border-gray-700 dark:bg-gray-800"
                                            // key={"campaign" + k}
                                            >
                                                <Table.Cell>
                                                    <FiPhoneCall className='text-green-600' />
                                                </Table.Cell>
                                                <Table.Cell className="text-blue-600 ">{
                                                    "Prachi < +91 8562014780> [5620]"
                                                }
                                                </Table.Cell>
                                                <Table.Cell className="text-blue-600 ">
                                                    <div className="flex flex-col">
                                                        <span className="font-medium">
                                                            11/1/2024
                                                        </span>
                                                        <span className="text-xs text-slate-500">
                                                            5:21
                                                        </span>
                                                        {/* {convertDateFormat(
                                                    campaign.startTime
                                                )} */}
                                                    </div>
                                                </Table.Cell>

                                                <Table.Cell
                                                    // onClick={() => {
                                                    //     setIsOpenDetail(true);
                                                    //     setCampaignID({
                                                    //         id: "73",
                                                    //         name: "Dana Gates",
                                                    //         status: "Terminated",
                                                    //     });
                                                    // }}
                                                    className="cursor-pointer "
                                                >
                                                    Dana Gates
                                                    {/* {campaign.name} */}
                                                </Table.Cell>
                                                <Table.Cell>
                                                    <img
                                                        className="rounded-full h-8 w-8"
                                                        src="https://img.freepik.com/free-vector/businessman-character-avatar-isolated_24877-60111.jpg?t=st=1720614745~exp=1720618345~hmac=38ff343a9443982af738325ad4d54d1d39ba540be4c28013b03cf063347d255e&w=200"
                                                        alt=""
                                                    />
                                                </Table.Cell>
                                                <Table.Cell>
                                                    <div className="flex items-center justify-between gap-2">
                                                        <audio className="h-12" autoplay="autoplay" controls="controls" id="player">
                                                            <source src="http://www.issilissinew.com/zindorg/1/mp3/2014/Mukunda/128/02%20-%20Daredumdadum%20%5bwww.AtoZmp3.in%5d.mp3.ogg" />
                                                            <source src="http://www.issilissinew.com/zindorg/1/mp3/2014/Mukunda/128/02%20-%20Daredumdadum%20%5bwww.AtoZmp3.in%5d.mp3" />
                                                            <p> Your browser doesn't support the audio tag </p>
                                                        </audio>
                                                        <Tooltip
                                                            content="Call"
                                                        >
                                                            <Button
                                                                theme={
                                                                    button_custom
                                                                }
                                                                size="xxs"
                                                                color="Fuchsia_custom"
                                                            >
                                                                <div className="flex items-center gap-1">
                                                                    <MdOutlineCall className="text-lg" />
                                                                    Call
                                                                </div>
                                                            </Button>
                                                        </Tooltip>
                                                    </div>
                                                    {/* <div>
                                                    <button onclick="document.get('player').play()">Play</button>
                                                      <button onclick="document.get('player').pause()">Pause</button>
                                                       <button onclick="document.getElementById('player').volume+ = 0.5">Vol+ </button>
                                                    <button onclick="document.get('player').volume- = 0.5">Vol- </button>
                                                 </div>   */}
                                                </Table.Cell>
                                            </Table.Row>
                                            <Table.Row
                                                className="items-center bg-white dark:border-gray-700 dark:bg-gray-800"
                                            // key={"campaign" + k}
                                            >
                                                <Table.Cell>
                                                    <LuPhoneForwarded className='text-blue-600' />
                                                </Table.Cell>
                                                <Table.Cell className="text-blue-600 ">{
                                                    "Prachi < +91 8562014780> [5620]"
                                                }
                                                </Table.Cell>
                                                <Table.Cell className="text-blue-600 ">
                                                    <div className="flex flex-col">
                                                        <span className="font-medium">
                                                            11/1/2024
                                                        </span>
                                                        <span className="text-xs text-slate-500">
                                                            5:21
                                                        </span>
                                                        {/* {convertDateFormat(
                                                    campaign.startTime
                                                )} */}
                                                    </div>
                                                </Table.Cell>

                                                <Table.Cell
                                                    // onClick={() => {
                                                    //     setIsOpenDetail(true);
                                                    //     setCampaignID({
                                                    //         id: "73",
                                                    //         name: "Dana Gates",
                                                    //         status: "Terminated",
                                                    //     });
                                                    // }}
                                                    className="cursor-pointer "
                                                >
                                                    Dana Gates
                                                    {/* {campaign.name} */}
                                                </Table.Cell>
                                                <Table.Cell>
                                                    <img
                                                        className="rounded-full h-8 w-8"
                                                        src="https://img.freepik.com/free-vector/businessman-character-avatar-isolated_24877-60111.jpg?t=st=1720614745~exp=1720618345~hmac=38ff343a9443982af738325ad4d54d1d39ba540be4c28013b03cf063347d255e&w=200"
                                                        alt=""
                                                    />
                                                </Table.Cell>
                                                <Table.Cell>
                                                    <div className="flex items-center justify-between gap-2">
                                                        <audio className="h-12" autoplay="autoplay" controls="controls" id="player">
                                                            <source src="http://www.issilissinew.com/zindorg/1/mp3/2014/Mukunda/128/02%20-%20Daredumdadum%20%5bwww.AtoZmp3.in%5d.mp3.ogg" />
                                                            <source src="http://www.issilissinew.com/zindorg/1/mp3/2014/Mukunda/128/02%20-%20Daredumdadum%20%5bwww.AtoZmp3.in%5d.mp3" />
                                                            <p> Your browser doesn't support the audio tag </p>
                                                        </audio>
                                                        <Tooltip
                                                            content="Call"
                                                        >
                                                            <Button
                                                                theme={
                                                                    button_custom
                                                                }
                                                                size="xxs"
                                                                color="Fuchsia_custom"
                                                            >
                                                                <div className="flex items-center gap-1">
                                                                    <MdOutlineCall className="text-lg" />
                                                                    Call
                                                                </div>
                                                            </Button>
                                                        </Tooltip>
                                                    </div>
                                                    {/* <div>
                                                    <button onclick="document.get('player').play()">Play</button>
                                                      <button onclick="document.get('player').pause()">Pause</button>
                                                       <button onclick="document.getElementById('player').volume+ = 0.5">Vol+ </button>
                                                    <button onclick="document.get('player').volume- = 0.5">Vol- </button>
                                                 </div>   */}
                                                </Table.Cell>
                                            </Table.Row>
                                        </Table.Body>
                                    </Table>
                                </div>
                            </div>

                            <div className="float-end border bottom-0 p-3 w-full bg-white ">
                                <div className="flex flex-wrap justify-between lg:gap-0 md:gap-0 gap-2">
                                    <div className="flex gap-3 items-center">
                                        <div className="text-gray-400 text-sm ">
                                            Showing 1 to 25 of 62 entries
                                        </div>
                                        <div>
                                            <select
                                                id="countries"
                                                className="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded focus:ring-blue-500 focus:border-blue-500 block p-1 ps-2 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-blue-500 dark:focus:border-blue-500"
                                            >
                                                <option
                                                    value={10}
                                                    defaultValue={10}
                                                >
                                                    10
                                                </option>
                                                <option value={15}>15</option>
                                                <option value={20}>20</option>
                                                <option value={25}>25</option>
                                                <option value={30}>30</option>
                                            </select>
                                        </div>
                                    </div>
                                    <div className="flex">
                                        <Button
                                            size="xs"
                                            color="gray"
                                            className="border-e-0 text-gray-400 px-2 rounded text-xs "
                                        >
                                            Previous
                                        </Button>
                                        <Button
                                            size="xs"
                                            color="blue"
                                            className="border text-white border-blue-600 bg-blue-600 px-2.5 rounded-none text-sm "
                                        >
                                            1
                                        </Button>
                                        <Button
                                            size="xs"
                                            color="gray"
                                            className="border text-blue-600 px-2.5 rounded-none text-xs "
                                        >
                                            2
                                        </Button>
                                        <Button
                                            size="xs"
                                            color="gray"
                                            className="border-s-0 text-blue-600 px-2 rounded text-xs "
                                        >
                                            Next
                                        </Button>
                                    </div>
                                </div>
                            </div>

                            <Tabs
                                className="mt-1"
                                theme={tabBar_underline}
                                aria-label="Tabs with underline"
                                variant="underline"
                            >
                                <Tabs.Item
                                    className=""
                                    active
                                    title={
                                        <div className="flex items-center gap-2">
                                            Call
                                            <sup>
                                                <Badge
                                                    className="rounded-full bg-fuchsia-600 text-white"
                                                    size="xs"
                                                >
                                                    05
                                                </Badge>
                                            </sup>
                                        </div>
                                    }
                                    icon={MdOutlineCall}
                                >
                                    <div className="bg-white p-2 rounded-md ">
                                        <div >
                                            <select
                                                id="countries"
                                                className="mt-1 w-full bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block p-2 ps-2 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-blue-500 dark:focus:border-blue-500"
                                            >
                                                <option
                                                    value={10}
                                                    defaultValue={10}
                                                >
                                                    Select Number
                                                </option>
                                                <option value={15}>15</option>
                                                <option value={20}>20</option>
                                                <option value={25}>25</option>
                                                <option value={30}>30</option>
                                            </select>
                                        </div>
                                        <div className="mt-2 flex flex-wrap gap-3">
                                            <div className="flex items-center gap-2 border border-gray p-1 w-fit rounded">
                                                <div className="flex items-center justify-center rounded-full size-10 bg-green-500">
                                                    <MdOutlineCall className="text-2xl text-white" />
                                                </div>
                                                <div className="flex flex-col text-sm">
                                                    <span>
                                                        Mohit Mathur (8563)
                                                    </span>
                                                    <span>
                                                        +91 8964236589
                                                    </span>
                                                </div>
                                            </div>
                                            <div className="flex items-center gap-2 border border-gray p-1 w-fit rounded">
                                                <div className="flex items-center justify-center rounded-full size-10 bg-green-500">
                                                    <MdOutlineCall className="text-2xl text-white" />
                                                </div>
                                                <div className="flex flex-col text-sm">
                                                    <span>
                                                        Mohit Mathur (8563)
                                                    </span>
                                                    <span>
                                                        +91 8964236589
                                                    </span>
                                                </div>
                                            </div>
                                            <div className="flex items-center gap-2 border border-gray p-1 w-fit rounded">
                                                <div className="flex items-center justify-center rounded-full size-10 bg-green-500">
                                                    <MdOutlineCall className="text-2xl text-white" />
                                                </div>
                                                <div className="flex flex-col text-sm">
                                                    <span>
                                                        Mohit Mathur (8563)
                                                    </span>
                                                    <span>
                                                        +91 8964236589
                                                    </span>
                                                </div>
                                            </div>
                                            <div className="flex items-center gap-2 border border-gray p-1 w-fit rounded">
                                                <div className="flex items-center justify-center rounded-full size-10 bg-green-500">
                                                    <MdOutlineCall className="text-2xl text-white" />
                                                </div>
                                                <div className="flex flex-col text-sm">
                                                    <span>
                                                        Mohit Mathur (8563)
                                                    </span>
                                                    <span>
                                                        +91 8964236589
                                                    </span>
                                                </div>
                                            </div>
                                            <div className="flex items-center gap-2 border border-gray p-1 w-fit rounded">
                                                <div className="flex items-center justify-center rounded-full size-10 bg-green-500">
                                                    <MdOutlineCall className="text-2xl text-white" />
                                                </div>
                                                <div className="flex flex-col text-sm">
                                                    <span>
                                                        Mohit Mathur (8563)
                                                    </span>
                                                    <span>
                                                        +91 8964236589
                                                    </span>
                                                </div>
                                            </div>
                                            <div className="flex items-center gap-2 border border-gray p-1 w-fit rounded">
                                                <div className="flex items-center justify-center rounded-full size-10 bg-green-500">
                                                    <MdOutlineCall className="text-2xl text-white" />
                                                </div>
                                                <div className="flex flex-col text-sm">
                                                    <span>
                                                        Mohit Mathur (8563)
                                                    </span>
                                                    <span>
                                                        +91 8964236589
                                                    </span>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </Tabs.Item>
                                <Tabs.Item
                                    className=""
                                    active
                                    title={
                                        <div className="flex items-center gap-2">
                                            WhatsApp
                                            <sup>
                                                <Badge
                                                    className="rounded-full bg-fuchsia-600 text-white"
                                                    size="xs"
                                                >
                                                    05
                                                </Badge>
                                            </sup>
                                        </div>
                                    }
                                    icon={FaWhatsapp}
                                >
                                    <div className="">
                                        <div >
                                            <Label htmlFor="">Reason</Label>
                                            <select
                                                id="countries"
                                                className="mt-1 w-full bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block p-2 ps-2 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-blue-500 dark:focus:border-blue-500"
                                            >
                                                <option
                                                    value={10}
                                                    defaultValue={10}
                                                >
                                                    10
                                                </option>
                                                <option value={15}>15</option>
                                                <option value={20}>20</option>
                                                <option value={25}>25</option>
                                                <option value={30}>30</option>
                                            </select>
                                        </div>
                                        <div className="">
                                            <div className="mt-2">
                                                {openEmoji && (
                                                    <div
                                                        className="absolute z-50 "
                                                        style={{
                                                            top: "25%",
                                                            bottom: "25%",
                                                        }}
                                                    >
                                                        <EmojiPicker
                                                            open={openEmoji}
                                                            lazyLoadEmojis={true}
                                                        />
                                                    </div>
                                                )}
                                                <Label htmlFor="">Reason</Label>

                                                <div className="mt-1 p-2 border border-gray-300 rounded-md bg-gray-50">
                                                    <Textarea
                                                        theme={textarea_custom}
                                                        name="message"
                                                        placeholder="Type Message here..."
                                                        rows={4}
                                                        id="myTextarea"
                                                        className="border-0 focus:ring-white"
                                                    />

                                                    <div className="flex flex-col flex-wrap justify-between gap-1 flex-center lg:flex-row">
                                                        <div className="flex flex-wrap items-center gap-1 space-x-1">
                                                            <div className="">
                                                                <Button
                                                                    className="px-0"
                                                                    size="xs"
                                                                    color="gray"
                                                                >
                                                                    <FaRegFaceSmileBeam />
                                                                </Button>
                                                            </div>
                                                            <div className="">
                                                                <Button
                                                                    className="px-0"
                                                                    size="xs"
                                                                    color="gray"
                                                                    id="boldButton"
                                                                >
                                                                    <FaBold />
                                                                </Button>
                                                            </div>
                                                            <div className="">
                                                                <Button
                                                                    className="px-0"
                                                                    size="xs"
                                                                    color="gray"
                                                                    id="italicButton"
                                                                >
                                                                    <FaItalic />
                                                                </Button>
                                                            </div>
                                                            <div className="">
                                                                <Button
                                                                    className="px-0"
                                                                    size="xs"
                                                                    color="gray"
                                                                    id="strike"
                                                                >
                                                                    <FaStrikethrough />
                                                                </Button>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                            <div className="flex justify-end gap-5 py-3">
                                                <Button
                                                    theme={button_custom}
                                                    color="blue"
                                                    className="rounded"
                                                    size="xs"
                                                >
                                                    <div className="flex items-center gap-0.5">
                                                        <AiOutlineMergeCells className="text-xl" />
                                                        <span className="text-sm">
                                                            Merge Lead
                                                        </span>
                                                    </div>
                                                </Button>
                                            </div>
                                        </div>
                                    </div>
                                </Tabs.Item>
                                <Tabs.Item
                                    className=""
                                    active
                                    title={
                                        <div className="flex items-center gap-2">
                                            SMS
                                            <sup>
                                                <Badge
                                                    className="rounded-full bg-fuchsia-600 text-white"
                                                    size="xs"
                                                >
                                                    05
                                                </Badge>
                                            </sup>
                                        </div>
                                    }
                                    icon={MdOutlineTextsms}
                                >
                                    <div className="">
                                        <div >
                                            <Label htmlFor="">Reason</Label>
                                            <select
                                                id="countries"
                                                className="mt-1 w-full bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block p-2 ps-2 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-blue-500 dark:focus:border-blue-500"
                                            >
                                                <option
                                                    value={10}
                                                    defaultValue={10}
                                                >
                                                    10
                                                </option>
                                                <option value={15}>15</option>
                                                <option value={20}>20</option>
                                                <option value={25}>25</option>
                                                <option value={30}>30</option>
                                            </select>
                                        </div>
                                        <div className="">
                                            <div className="mt-2">
                                                {openEmoji && (
                                                    <div
                                                        className="absolute z-50 "
                                                        style={{
                                                            top: "25%",
                                                            bottom: "25%",
                                                        }}
                                                    >
                                                        <EmojiPicker
                                                            open={openEmoji}
                                                            lazyLoadEmojis={true}
                                                        />
                                                    </div>
                                                )}
                                                <Label htmlFor="">Reason</Label>

                                                <div className="mt-1 p-2 border border-gray-300 rounded-md bg-gray-50">
                                                    <Textarea
                                                        theme={textarea_custom}
                                                        name="message"
                                                        placeholder="Type Message here..."
                                                        rows={4}
                                                        id="myTextarea"
                                                        className="border-0 focus:ring-white"
                                                    />

                                                    <div className="flex flex-col flex-wrap justify-between gap-1 flex-center lg:flex-row">
                                                        <div className="flex flex-wrap items-center gap-1 space-x-1">
                                                            <div className="">
                                                                <Button
                                                                    className="px-0"
                                                                    size="xs"
                                                                    color="gray"
                                                                >
                                                                    <FaRegFaceSmileBeam />
                                                                </Button>
                                                            </div>
                                                            <div className="">
                                                                <Button
                                                                    className="px-0"
                                                                    size="xs"
                                                                    color="gray"
                                                                    id="boldButton"
                                                                >
                                                                    <FaBold />
                                                                </Button>
                                                            </div>
                                                            <div className="">
                                                                <Button
                                                                    className="px-0"
                                                                    size="xs"
                                                                    color="gray"
                                                                    id="italicButton"
                                                                >
                                                                    <FaItalic />
                                                                </Button>
                                                            </div>
                                                            <div className="">
                                                                <Button
                                                                    className="px-0"
                                                                    size="xs"
                                                                    color="gray"
                                                                    id="strike"
                                                                >
                                                                    <FaStrikethrough />
                                                                </Button>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                            <div className="flex justify-end gap-5 py-3">
                                                <Button
                                                    theme={button_custom}
                                                    color="blue"
                                                    className="rounded"
                                                    size="xs"
                                                >
                                                    <div className="flex items-center gap-0.5">
                                                        <AiOutlineMergeCells className="text-xl" />
                                                        <span className="text-sm">
                                                            Merge Lead
                                                        </span>
                                                    </div>
                                                </Button>
                                            </div>
                                        </div>
                                    </div>
                                </Tabs.Item>
                                <Tabs.Item
                                    className=""
                                    active
                                    title={
                                        <div className="flex items-center gap-2">
                                            Email
                                            <sup>
                                                <Badge
                                                    className="rounded-full bg-fuchsia-600 text-white"
                                                    size="xs"
                                                >
                                                    05
                                                </Badge>
                                            </sup>
                                        </div>
                                    }
                                    icon={HiOutlineMailOpen}
                                >
                                    <div className="">
                                        <div >
                                            <Label htmlFor="">Reason</Label>
                                            <select
                                                id="countries"
                                                className="mt-1 w-full bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block p-2 ps-2 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-blue-500 dark:focus:border-blue-500"
                                            >
                                                <option
                                                    value={10}
                                                    defaultValue={10}
                                                >
                                                    10
                                                </option>
                                                <option value={15}>15</option>
                                                <option value={20}>20</option>
                                                <option value={25}>25</option>
                                                <option value={30}>30</option>
                                            </select>
                                        </div>
                                        <div className="">
                                            <div className="mt-2">
                                                {openEmoji && (
                                                    <div
                                                        className="absolute z-50 "
                                                        style={{
                                                            top: "25%",
                                                            bottom: "25%",
                                                        }}
                                                    >
                                                        <EmojiPicker
                                                            open={openEmoji}
                                                            lazyLoadEmojis={true}
                                                        />
                                                    </div>
                                                )}
                                                <Label htmlFor="">Reason</Label>

                                                <div className="mt-1 p-2 border border-gray-300 rounded-md bg-gray-50">
                                                    <Textarea
                                                        theme={textarea_custom}
                                                        name="message"
                                                        placeholder="Type Message here..."
                                                        rows={4}
                                                        id="myTextarea"
                                                        className="border-0 focus:ring-white"
                                                    />

                                                    <div className="flex flex-col flex-wrap justify-between gap-1 flex-center lg:flex-row">
                                                        <div className="flex flex-wrap items-center gap-1 space-x-1">
                                                            <div className="">
                                                                <Button
                                                                    className="px-0"
                                                                    size="xs"
                                                                    color="gray"
                                                                >
                                                                    <FaRegFaceSmileBeam />
                                                                </Button>
                                                            </div>
                                                            <div className="">
                                                                <Button
                                                                    className="px-0"
                                                                    size="xs"
                                                                    color="gray"
                                                                    id="boldButton"
                                                                >
                                                                    <FaBold />
                                                                </Button>
                                                            </div>
                                                            <div className="">
                                                                <Button
                                                                    className="px-0"
                                                                    size="xs"
                                                                    color="gray"
                                                                    id="italicButton"
                                                                >
                                                                    <FaItalic />
                                                                </Button>
                                                            </div>
                                                            <div className="">
                                                                <Button
                                                                    className="px-0"
                                                                    size="xs"
                                                                    color="gray"
                                                                    id="strike"
                                                                >
                                                                    <FaStrikethrough />
                                                                </Button>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                            <div className="flex justify-end gap-5 py-3">
                                                <Button
                                                    theme={button_custom}
                                                    color="blue"
                                                    className="rounded"
                                                    size="xs"
                                                >
                                                    <div className="flex items-center gap-0.5">
                                                        <AiOutlineMergeCells className="text-xl" />
                                                        <span className="text-sm">
                                                            Merge Lead
                                                        </span>
                                                    </div>
                                                </Button>
                                            </div>
                                        </div>
                                    </div>
                                </Tabs.Item>
                            </Tabs>
                        </div>
                    </Tabs.Item>
                    {/* WhatsApp */}
                    <Tabs.Item
                        className=""
                        active
                        title={
                            <div className="flex items-center gap-2">
                                WhatsApp
                                <sup>
                                    <Badge
                                        className="rounded-full bg-green-600 text-white"
                                        size="xs"
                                    >
                                        05
                                    </Badge>
                                </sup>
                            </div>
                        }
                        icon={FaWhatsapp}>
                        <div>
                            <div className="flex items-center bg-white rounded-md w-full p-2 px-3 mb-1.5">
                                <ButtonGroup className="h-fit">
                                    <Button
                                        className=""
                                        size="xs"
                                        color="gray"
                                        theme={button_custom}
                                    // onClick={deleteRecords}
                                    >
                                        <div className="flex items-center gap-1">
                                            <MdOutlineMarkEmailRead className="text-slate-500" />
                                            <span className="text-xs">Sent</span>
                                        </div>
                                    </Button>
                                    <Button
                                        className=""
                                        size="xs"
                                        color="gray"
                                        theme={button_custom}
                                    // onClick={deleteRecords}
                                    >
                                        <div className="flex items-center gap-1 ms-1">
                                            <MdCallReceived className="text-slate-500" />
                                            <span className="text-xs">Received</span>
                                        </div>
                                    </Button>
                                    <Button
                                        className=""
                                        size="xs"
                                        color="gray"
                                        theme={button_custom}
                                    // onClick={deleteRecords}
                                    >
                                        <div className="flex items-center gap-1 ms-1">
                                            <MdPendingActions className="text-slate-500" />
                                            <span className="text-xs">Pending</span>
                                        </div>
                                    </Button>
                                </ButtonGroup>
                            </div>

                            {/* chat screen */}
                            <div>
                                <div className="bg-[url('/assets/img/bgwtsp.png')]">
                                    <div
                                        className="p-4 space-y-4 overflow-auto scroll-smooth"
                                        id="chatsBox"
                                        // ref={divRef}
                                        style={{ height: "55vh" }}
                                    >
                                        {/* Date Label */}
                                        <div className="flex flex-col items-center">

                                            <Button
                                                size="xs"
                                                className={``}
                                            // onClick={loadMore}
                                            >
                                                Load More
                                            </Button>
                                        </div>

                                        <div>
                                            <div
                                                className="flex flex-col gap-2"
                                            >
                                                <div className="text-sm text-center text-gray-700 ">
                                                    <span className="px-2 py-1 rounded-md w-fit bg-sky-200">
                                                        Today
                                                    </span>
                                                </div>
                                                {/* Receiver's Message */}
                                                <div className="flex justify-start">
                                                    <div className="flex items-start">
                                                        <div
                                                            className=" bg-white p-2 rounded-lg shadow-md"
                                                        >
                                                            <span className="text-sm text-blue-600">{"Sohan Sharma <+91 8560123450> [5630]"}</span>
                                                            <p className="text-gray-800">
                                                                It is a long established fact that a reader will be
                                                                looking at its layout.
                                                            </p>
                                                            <span className="mt-1 text-xs text-gray-500">
                                                                <div className="flex items-center justify-end gap-1">
                                                                    <div className="flex items-center justify-end gap-2" >
                                                                        <div className="text-gray-600">
                                                                            <span className="leading-2 mt-2">
                                                                                09:45
                                                                            </span>
                                                                        </div>
                                                                    </div>
                                                                </div>
                                                            </span>
                                                        </div>
                                                    </div>
                                                </div>

                                                {/* Sender's Message */}
                                                <div className="flex justify-end">
                                                    <div className="flex items-end">
                                                        <div
                                                            className=" bg-[#DCF8C6] p-2 rounded-lg shadow-md"
                                                        >
                                                            <span className="text-sm text-blue-600">{"Sohan Sharma <+91 8560123450> [5630]"}</span>
                                                            <p className="text-gray-800">
                                                                It is a long established fact that a reader will be
                                                                looking at its layout.
                                                            </p>
                                                            <span className="mt-1 text-xs text-gray-500">
                                                                <div className="flex items-center justify-end gap-1">
                                                                    <div className="flex items-center justify-end gap-2" >
                                                                        <div className="text-gray-600">
                                                                            <span className="leading-2 mt-2">
                                                                                12/2/2024
                                                                            </span>
                                                                        </div>
                                                                    </div>
                                                                </div>
                                                            </span>
                                                        </div>
                                                    </div>
                                                </div>

                                            </div>
                                        </div>

                                    </div>
                                </div>
                                <div className="bg-[#F0EEED] p-2">
                                    {/*-------- Message -----------*/}
                                    <div className="">
                                        <div className="">
                                            {openEmoji && (
                                                <div
                                                    className="absolute z-50 "
                                                    style={{
                                                        top: "25%",
                                                        bottom: "25%",
                                                    }}
                                                >
                                                    <EmojiPicker
                                                        open={openEmoji}
                                                        lazyLoadEmojis={true}
                                                    />
                                                </div>
                                            )}

                                            <div className="p-2 border border-gray-300 rounded-md bg-gray-50">
                                                <div className="grid grid-cols-3 md:grid-cols-5 lg:grid-cols-5 xl:grid-cols-7">
                                                    <div className="flex justify-center"></div>
                                                </div>
                                                <Textarea
                                                    theme={textarea_custom}
                                                    name="message"
                                                    placeholder="Type Message here..."
                                                    rows={4}
                                                    id="myTextarea"
                                                    className="border-0 focus:ring-white"
                                                />

                                                <div className="flex flex-col flex-wrap justify-between gap-1 flex-center lg:flex-row">
                                                    <div className="flex flex-wrap items-center gap-1 space-x-1">
                                                        <div className="">
                                                            <label htmlFor="message-file-input">
                                                                <Button
                                                                    className="px-0"
                                                                    size="xs"
                                                                    color="gray"
                                                                    theme={
                                                                        button_custom
                                                                    }
                                                                >
                                                                    <div className="flex items-center gap-1">
                                                                        <CgAttachment className="text-slate-400 " />
                                                                        <span className="text-sm">
                                                                            Add File
                                                                        </span>
                                                                    </div>
                                                                </Button>
                                                            </label>
                                                            <FileInput
                                                                id="message-file-input"
                                                                className="hidden"
                                                            />
                                                        </div>
                                                        <div className="">
                                                            <Button
                                                                theme={
                                                                    button_custom
                                                                }
                                                                className="px-0"
                                                                size="xxs"
                                                                color="transparentForTab"
                                                            // onClick={() =>
                                                            //     setOpenEmoji(
                                                            //         !openEmoji
                                                            //     )
                                                            // }
                                                            >
                                                                <FaRegFaceSmileBeam className="text-slate-500" />
                                                            </Button>
                                                        </div>
                                                        <div className="">
                                                            <Button
                                                                theme={
                                                                    button_custom
                                                                }
                                                                className="px-0"
                                                                size="xxs"
                                                                color="transparentForTab"
                                                                id="boldButton"
                                                            // onClick={() =>
                                                            //     textFormat(
                                                            //         "*"
                                                            //     )
                                                            // }
                                                            >
                                                                <FaBold className="text-slate-400" />
                                                            </Button>
                                                        </div>
                                                        <div className="">
                                                            <Button
                                                                theme={
                                                                    button_custom
                                                                }
                                                                className="px-0"
                                                                size="xxs"
                                                                color="transparentForTab"
                                                                id="italicButton"
                                                            // onClick={() =>
                                                            //     textFormat(
                                                            //         "_"
                                                            //     )
                                                            // }
                                                            >
                                                                <FaItalic className="text-slate-400" />
                                                            </Button>
                                                        </div>
                                                        <div className="">
                                                            <Button
                                                                theme={
                                                                    button_custom
                                                                }
                                                                className="px-0"
                                                                size="xxs"
                                                                color="transparentForTab"
                                                                id="strike"
                                                            // onClick={() =>
                                                            //     textFormat(
                                                            //         "~"
                                                            //     )
                                                            // }
                                                            >
                                                                <FaStrikethrough className="text-slate-400" />
                                                            </Button>
                                                        </div>
                                                        <div className="">
                                                            <Button
                                                                className="px-0"
                                                                size="xs"
                                                                color="gray"
                                                                theme={
                                                                    button_custom
                                                                }
                                                            >
                                                                <div className="flex items-center gap-1">
                                                                    <FaRegNoteSticky className="text-slate-400" />
                                                                    <span className="text-sm">
                                                                        Select Template
                                                                    </span>
                                                                </div>
                                                            </Button>
                                                        </div>
                                                    </div>

                                                    <div className="flex flex-wrap gap-2">
                                                        <div className="">
                                                            <select
                                                                className="p-1 px-3 text-sm border-gray-300 rounded-md hover:bg-gray-50"
                                                                name="cars"
                                                                onChange={(e) => setData('gateway', e.target.value)}
                                                            >
                                                                <option value="">Gateway <span className="text-red-600">*</span></option>
                                                                <option>gateway1</option>
                                                            </select>
                                                        </div>
                                                        <div className="flex items-center gap-2">
                                                            <Checkbox
                                                                color="blue"
                                                                id="accept"
                                                            // defaultChecked={isScheduled}
                                                            // onChange={() => {
                                                            //     setIsScheduled(!isScheduled);
                                                            //     setData(
                                                            //         "isScheduled",
                                                            //         !isScheduled
                                                            //     );
                                                            // }}
                                                            />
                                                            <Label htmlFor="accept" className="flex">
                                                                Schedule&nbsp;
                                                            </Label>
                                                        </div>
                                                        <div className="">
                                                            <div className="">
                                                                <TextInput
                                                                    sizing={"xs"}
                                                                    theme={input_custom}
                                                                    id="startTime"
                                                                    name="startTime"
                                                                    type="datetime-local"
                                                                    defaultValue={new Date()
                                                                        .toLocaleString("sv-SE")
                                                                        .slice(0, 16)}
                                                                    required
                                                                    color="gray"
                                                                    className="w-full"
                                                                // onChange={(e) =>
                                                                //     setData(
                                                                //         "ScheduleTime",
                                                                //         e.target.value
                                                                //     )
                                                                // }
                                                                // helperText={
                                                                //     errors.startTime
                                                                //         ? errors.startTime
                                                                //         : null
                                                                // }
                                                                />
                                                            </div>
                                                        </div>


                                                        <Button
                                                            type="submit"
                                                            size="xs"
                                                            color="blue"
                                                            id="var1"
                                                        // onClick={sendMessage}
                                                        // disabled={data.gateway == null || data.gateway == ''}
                                                        >
                                                            <div className="flex items-center gap-1 text-sm">
                                                                <VscSend className="text-lg" />
                                                                Send Now
                                                            </div>
                                                        </Button>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <span className="text-sm text-red-600 ">
                                        Maximum upload file size: 512 MB | Selecting a gateway is mandatory.
                                    </span>
                                </div>

                            </div>
                        </div>
                    </Tabs.Item>
                    {/* SMS */}
                    <Tabs.Item
                        className=""
                        active
                        title={
                            <div className="flex items-center gap-2">
                                SMS
                                <sup>
                                    <Badge
                                        className="rounded-full bg-yellow-300 text-black"
                                        size="xs"
                                    >
                                        05
                                    </Badge>
                                </sup>
                            </div>
                        }
                        icon={MdOutlineTextsms}>
                        <div>
                            <div className="flex items-center bg-white rounded-md w-full p-2 px-3 mb-1.5">
                                <ButtonGroup className="h-fit">
                                    <Button
                                        className=""
                                        size="xs"
                                        color="gray"
                                        theme={button_custom}
                                    // onClick={deleteRecords}
                                    >
                                        <div className="flex items-center gap-1">
                                            <MdOutlineMarkEmailRead className="text-slate-500" />
                                            <span className="text-xs">Sent</span>
                                        </div>
                                    </Button>
                                    <Button
                                        className=""
                                        size="xs"
                                        color="gray"
                                        theme={button_custom}
                                    // onClick={deleteRecords}
                                    >
                                        <div className="flex items-center gap-1 ms-1">
                                            <MdCallReceived className="text-slate-500" />
                                            <span className="text-xs">Received</span>
                                        </div>
                                    </Button>
                                    <Button
                                        className=""
                                        size="xs"
                                        color="gray"
                                        theme={button_custom}
                                    // onClick={deleteRecords}
                                    >
                                        <div className="flex items-center gap-1 ms-1">
                                            <MdPendingActions className="text-slate-500" />
                                            <span className="text-xs">Failed</span>
                                        </div>
                                    </Button>
                                    <Button
                                        className=""
                                        size="xs"
                                        color="gray"
                                        theme={button_custom}
                                    // onClick={deleteRecords}
                                    >
                                        <div className="flex items-center gap-1 ms-1">
                                            <MdPendingActions className="text-slate-500" />
                                            <span className="text-xs">Pending</span>
                                        </div>
                                    </Button>
                                </ButtonGroup>
                            </div>

                            {/* chat screen */}
                            <div>
                                <div className="bg-slate-100">
                                    <div
                                        className="p-4 space-y-4 overflow-auto scroll-smooth"
                                        id="chatsBox"
                                        // ref={divRef}
                                        style={{ height: "55vh" }}
                                    >
                                        {/* Date Label */}
                                        <div className="flex flex-col items-center">

                                            <Button
                                                theme={button_custom}
                                                size="xs"
                                                // color="green"
                                                className={`rounded-md`}
                                            // onClick={loadMore}
                                            >
                                                <span className="text-xs">
                                                    Load More
                                                </span>
                                            </Button>
                                        </div>

                                        <div>
                                            <div
                                                className="flex flex-col gap-2"
                                            >
                                                <div className="text-sm text-center text-gray-700 ">
                                                    <span className="px-2 py-1 rounded-md w-fit bg-slate-300">
                                                        Today
                                                    </span>
                                                </div>
                                                {/* Receiver's Message */}
                                                <div className="flex justify-start">
                                                    <div className="flex items-start">
                                                        <div
                                                            className=" bg-blue-600 p-2 rounded-lg shadow-md"
                                                        >
                                                            <span className="text-sm text-white">{"Sohan Sharma <+91 8560123450> [5630]"}</span>
                                                            <p className="text-white">
                                                                It is a long established fact that a reader will be
                                                                looking at its layout.
                                                            </p>
                                                            <span className="mt-1 text-xs text-gray-500">
                                                                <div className="flex items-center justify-end gap-1">
                                                                    <div className="flex items-center justify-end gap-2" >
                                                                        <div className="text-white">
                                                                            <span className="leading-2 mt-2">
                                                                                09:45
                                                                            </span>
                                                                        </div>
                                                                    </div>
                                                                </div>
                                                            </span>
                                                        </div>
                                                    </div>
                                                </div>

                                                {/* Sender's Message */}
                                                <div className="flex justify-end">
                                                    <div className="flex items-end">
                                                        <div
                                                            className=" bg-white p-2 rounded-lg shadow-md"
                                                        >
                                                            <span className="text-sm text-blue-600">{"Sohan Sharma <+91 8560123450> [5630]"}</span>
                                                            <p className="text-gray-800">
                                                                It is a long established fact that a reader will be
                                                                looking at its layout.
                                                            </p>
                                                            <span className="mt-1 text-xs text-gray-500">
                                                                <div className="flex items-center justify-end gap-1">
                                                                    <div className="flex items-center justify-end gap-2" >
                                                                        <div className="text-gray-600">
                                                                            <span className="leading-2 mt-2">
                                                                                09:45
                                                                            </span>
                                                                        </div>
                                                                    </div>
                                                                </div>
                                                            </span>
                                                        </div>
                                                    </div>
                                                </div>

                                            </div>
                                        </div>

                                    </div>
                                </div>
                                <div className="bg-[#F0EEED] p-2">
                                    {/*-------- Message -----------*/}
                                    <div className="">
                                        <div className="">
                                            {openEmoji && (
                                                <div
                                                    className="absolute z-50 "
                                                    style={{
                                                        top: "25%",
                                                        bottom: "25%",
                                                    }}
                                                >
                                                    <EmojiPicker
                                                        open={openEmoji}
                                                        lazyLoadEmojis={true}
                                                    />
                                                </div>
                                            )}

                                            <div className="p-2 border border-gray-300 rounded-md bg-gray-50">
                                                <div className="grid grid-cols-3 md:grid-cols-5 lg:grid-cols-5 xl:grid-cols-7">
                                                    <div className="flex justify-center"></div>
                                                </div>
                                                <Textarea
                                                    theme={textarea_custom}
                                                    name="message"
                                                    placeholder="Type Message here..."
                                                    rows={4}
                                                    id="myTextarea"
                                                    className="border-0 focus:ring-white"
                                                />

                                                <div className="flex flex-col flex-wrap justify-between gap-1 flex-center lg:flex-row">
                                                    <div className="flex flex-wrap items-center gap-1 space-x-1">
                                                        <div className="">
                                                            <Button
                                                                theme={
                                                                    button_custom
                                                                }
                                                                className="px-0"
                                                                size="xxs"
                                                                color="transparentForTab"
                                                            >
                                                                <FaRegFaceSmileBeam className="text-slate-500" />
                                                            </Button>
                                                        </div>
                                                        <div className="">
                                                            <label htmlFor="message-file-input">
                                                                <Button
                                                                    className="px-0"
                                                                    size="xs"
                                                                    color="gray"
                                                                    theme={
                                                                        button_custom
                                                                    }
                                                                >
                                                                    <div className="flex items-center gap-1">
                                                                        <CgAttachment className="text-slate-400 " />
                                                                        <span className="text-sm">
                                                                            Add File
                                                                        </span>
                                                                    </div>
                                                                </Button>
                                                            </label>
                                                            <FileInput
                                                                id="message-file-input"
                                                                className="hidden"
                                                            />
                                                        </div>
                                                        <div className="">
                                                            <Button
                                                                theme={button_custom}
                                                                size="xs"
                                                                color="gray"
                                                                onClick={() =>
                                                                    setIsOpenTemplates(!isOpenTemplates)
                                                                }
                                                            >
                                                                <div className="flex items-center gap-1">
                                                                    <div className="">
                                                                        <FaRegNoteSticky className="text-xs text-slate-700" />
                                                                    </div>
                                                                    <div className="text-sm">Select Template</div>
                                                                </div>
                                                            </Button>
                                                        </div>
                                                    </div>

                                                    <div className="flex flex-wrap gap-2">
                                                        <div className="">
                                                            <select
                                                                className="p-1 px-3 text-sm border-gray-300 rounded-md hover:bg-gray-50"
                                                                name="cars"
                                                                onChange={(e) => setData('gateway', e.target.value)}
                                                            >
                                                                <option value="">Gateway <span className="text-red-600">*</span></option>
                                                                <option>gateway1</option>
                                                            </select>
                                                        </div>
                                                        <div className="flex items-center gap-2">
                                                            <Checkbox
                                                                color="blue"
                                                                id="accept"
                                                            // defaultChecked={isScheduled}
                                                            // onChange={() => {
                                                            //     setIsScheduled(!isScheduled);
                                                            //     setData(
                                                            //         "isScheduled",
                                                            //         !isScheduled
                                                            //     );
                                                            // }}
                                                            />
                                                            <Label htmlFor="accept" className="flex">
                                                                Schedule&nbsp;
                                                            </Label>
                                                        </div>
                                                        <div className="">
                                                            <div className="">
                                                                <TextInput
                                                                    sizing={"xs"}
                                                                    theme={input_custom}
                                                                    id="startTime"
                                                                    name="startTime"
                                                                    type="datetime-local"
                                                                    defaultValue={new Date()
                                                                        .toLocaleString("sv-SE")
                                                                        .slice(0, 16)}
                                                                    required
                                                                    color="gray"
                                                                    className="w-full"
                                                                // onChange={(e) =>
                                                                //     setData(
                                                                //         "ScheduleTime",
                                                                //         e.target.value
                                                                //     )
                                                                // }
                                                                // helperText={
                                                                //     errors.startTime
                                                                //         ? errors.startTime
                                                                //         : null
                                                                // }
                                                                />
                                                            </div>
                                                        </div>


                                                        <Button
                                                            type="submit"
                                                            size="xs"
                                                            color="blue"
                                                            id="var1"
                                                        // onClick={sendMessage}
                                                        // disabled={data.gateway == null || data.gateway == ''}
                                                        >
                                                            <div className="flex items-center gap-1 text-sm">
                                                                <VscSend className="text-lg" />
                                                                Send Now
                                                            </div>
                                                        </Button>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <span className="text-sm text-red-600 ">
                                        Maximum upload file size: 512 MB | Selecting a gateway is mandatory.
                                    </span>
                                </div>

                            </div>
                        </div>
                    </Tabs.Item>
                    {/* Email */}
                    <Tabs.Item
                        className=""
                        active
                        title={
                            <div className="flex items-center gap-2">
                                Email
                                <sup>
                                    <Badge
                                        className="rounded-full bg-red-600 text-white"
                                        size="xs"
                                    >
                                        05
                                    </Badge>
                                </sup>
                            </div>
                        }
                        icon={RiMailOpenLine}>
                        <div>
                            {/* Buttons */}
                            <div className="flex items-center gap-2 justify-between bg-white rounded-md w-full p-2 px-3 mb-1.5">
                                <ButtonGroup className="h-fit">
                                    <Button
                                        className=""
                                        size="xs"
                                        color="gray"
                                        theme={button_custom}
                                    // onClick={deleteRecords}
                                    >
                                        <div className="flex items-center gap-1">
                                            <MdOutlineMarkEmailRead className="text-slate-400" />
                                            <span className="text-xs">Sent</span>
                                        </div>
                                    </Button>
                                    <Button
                                        className=""
                                        size="xs"
                                        color="gray"
                                        theme={button_custom}
                                    // onClick={deleteRecords}
                                    >
                                        <div className="flex items-center gap-1 ms-1">
                                            <MdCallReceived className="text-slate-400" />
                                            <span className="text-xs">Received</span>
                                        </div>
                                    </Button>
                                    <Button
                                        className=""
                                        size="xs"
                                        color="gray"
                                        theme={button_custom}
                                    // onClick={deleteRecords}
                                    >
                                        <div className="flex items-center gap-1 ms-1">
                                            <MdOutlineSmsFailed className="text-slate-400" />
                                            <span className="text-xs">Failed</span>
                                        </div>
                                    </Button>
                                    <Button
                                        className=""
                                        size="xs"
                                        color="gray"
                                        theme={button_custom}
                                    // onClick={deleteRecords}
                                    >
                                        <div className="flex items-center gap-1 ms-1">
                                            <MdPendingActions className="text-slate-400" />
                                            <span className="text-xs">Pending</span>
                                        </div>
                                    </Button>
                                    <Button
                                        className=""
                                        size="xs"
                                        color="gray"
                                        theme={button_custom}
                                    // onClick={deleteRecords}
                                    >
                                        <div className="flex items-center gap-1 ms-1">
                                            <MdBlock className="text-slate-400" />
                                            <span className="text-xs">Reject</span>
                                        </div>
                                    </Button>
                                </ButtonGroup>
                                <div>
                                    <Button
                                        className=""
                                        size="xs"
                                        color="gray"
                                        theme={button_custom}
                                    // onClick={deleteRecords}
                                    >
                                        <div className="flex items-center gap-1">
                                            <MdAdd className="text-slate-400" />
                                            <span className="text-xs">Create Template</span>
                                        </div>
                                    </Button>

                                </div>
                            </div>
                            {/* BG-White */}
                            <div className="bg-white rounded-md">
                                <div className="flex items-center justify-between p-2">
                                    <span className="text-base font-medium">Subject</span>
                                    <div className="flex items-center gap-3 text-gray-400 text-sm">
                                        <span>Gateway 1  |</span>
                                        <span>26/11/2024  |</span>
                                        <span>10:01AM</span>
                                        <TiStarOutline />
                                        <LuCornerUpLeft />
                                    </div>
                                </div>
                                <span className="text-xl font-medium p-2">🤩 Introducing our company services</span>
                                <hr className="my-2" />
                                <span className="text-base font-medium p-2">Body</span>
                                <div className="text-base font-medium px-2">Simran Malhotra </div>
                                <Dropdown
                                    arrowIcon={false}
                                    inline
                                    label={
                                        <div className="text-base flex items-center text-slate-400 px-2">to me <MdOutlineArrowDropDown className="mt-0.5" /></div>
                                    }
                                >
                                    {/* <DropdownHeader> */}
                                        <div className="flex items-start gap-2 p-1 px-2 text-sm font-normal">
                                            <div className="flex flex-col items-end text-slate-400">
                                                <span>from:</span>
                                                <span>to:</span>
                                                <span>Cc:</span>
                                                <span>Bcc:</span>
                                            </div>
                                            <div className="flex flex-col">
                                                <span className="">{"Simran Gupta <<EMAIL>>"}</span>
                                                <span className="">{"Dunes Factory <<EMAIL>>"}</span>
                                                <span>
                                                    dfdsf
                                                </span>
                                                <span>
                                                    ffdgfdg
                                                </span>
                                            </div>
                                        </div>

                                    {/* </DropdownHeader> */}
                                </Dropdown>
                                {/* Body */}
                                <div className="p-2 font-normal text-[15px]">
                                    Hello,Hope you're doing well!Dear Sir/Mam,On behalf of Dunes Factory Pvt Ltd, I'm taking this opportunity to introduce you to our company and the services that we offer. We have been on top of the Software selling company for the last 12+ years and we continue to strive to deliver the same uncompromising quality that has helped cement our reputation as one of the leading companies in Bikaner.We are specialized in Digital Marketing Tools & Services, SEO, SMM, Web Development, Application Development, and Business Management Software to help established businesses and start-ups to grow digitally. We offer a systematic and meticulous preconstruction process that would guarantee you solid and satisfying results. We will tailor our approach to help you find the best process to meet your goals.Our company keeps a strong portfolio that includes some well-known companies. It would be a great honour if your company will be added to our outstanding list of clients. We would be more than glad to assist you in this noble enterprise.RegardsTeam <EMAIL> for more details: www.rapbooster.com or login.rapbooster.com
                                </div>
                            </div>
                            {/* BG-Blue */}
                            <div className="bg-blue-50 rounded-md my-2">
                                <div className="flex items-center justify-between p-2">
                                    <span className="text-base font-medium">Subject</span>
                                    <div className="flex items-center gap-3 text-gray-400 text-sm">
                                        <span>Gateway 1  |</span>
                                        <span>26/11/2024  |</span>
                                        <span>10:01AM</span>
                                        <Badge color="info">Approved</Badge>
                                    </div>
                                </div>
                                <span className="text-xl font-medium p-2">🤩 Introducing our company services</span>
                                <hr className="my-2" />
                                <span className="text-base font-medium p-2">Body</span>
                                <div className="text-base font-medium px-2">Simran Malhotra </div>
                                <div className="text-base flex items-center text-slate-400 px-2">to me <MdOutlineArrowDropDown className="mt-0.5" /></div>
                                {/* Body */}
                                <div className="p-2 text-[15px]">
                                    Hello,Hope you're doing well!Dear Sir/Mam,On behalf of Dunes Factory Pvt Ltd, I'm taking this opportunity to introduce you to our company and the services that we offer. We have been on top of the Software selling company for the last 12+ years and we continue to strive to deliver the same uncompromising quality that has helped cement our reputation as one of the leading companies in Bikaner.We are specialized in Digital Marketing Tools & Services, SEO, SMM, Web Development, Application Development, and Business Management Software to help established businesses and start-ups to grow digitally. We offer a systematic and meticulous preconstruction process that would guarantee you solid and satisfying results. We will tailor our approach to help you find the best process to meet your goals.Our company keeps a strong portfolio that includes some well-known companies. It would be a great honour if your company will be added to our outstanding list of clients. We would be more than glad to assist you in this noble enterprise.RegardsTeam <EMAIL> for more details: www.rapbooster.com or login.rapbooster.com
                                </div>
                                <hr className="my-2" />

                                {/* Attachments */}
                                <div className="flex flex-col gap-1 px-2">
                                    <div>
                                        <span>Attachment: </span>
                                        <span className="text-blue-600">
                                            2
                                        </span>
                                    </div>
                                    <div className="flex items-center gap-2 flex-wrap">
                                        <div className="relative inline-block rounded-md bg-white p-1">
                                            <div className="border">
                                                <div className="flex items-center justify-center h-14">
                                                    <IoMdImage className="text-4xl text-gray-300" />
                                                </div>
                                                <div className="border-t p-2 flex items-start gap-1">
                                                    <IoMdImage className="text-lg text-red-600 " />
                                                    <div>
                                                        <div className="text-xs">
                                                            champion
                                                            12dec...
                                                        </div>
                                                        <div className="text-xs text-red-600">
                                                            Not Scanned for
                                                            viruses
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                            <button className="absolute top-1 right-1 text-slate-500 text-xl">
                                                <HiDownload />
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div className="bg-white px-2 rounded-md my-2">
                                {/* Subject */}
                                <div>
                                    <div className="mb-2 block">
                                        <Label htmlFor="email1" value="Subject" />
                                    </div>
                                    <TextInput theme={input_custom} color="gray" id="email1" type="email" placeholder="Subject" required />
                                </div>
                                <div className="my-2">
                                    {/*-------- Message -----------*/}
                                    <div className="">
                                        <div className="">
                                            <div className="text-nowrap flex items-center justify-between mb-2">
                                                <Label htmlFor="email4" value="Body" />
                                                <Button
                                                    className="px-0"
                                                    size="xs"
                                                    color="orange"
                                                    theme={button_custom}
                                                // onClick={() => handleClick()}
                                                >
                                                    <div className="flex items-center gap-1">
                                                        <MdOutlineHandyman className="" />
                                                        <span className="text-sm">
                                                            Use Builder
                                                        </span>
                                                    </div>
                                                </Button>
                                            </div>
                                            <div className="p-2 border border-gray-300 rounded-md bg-gray-50">
                                                <Textarea
                                                    theme={textarea_custom}
                                                    name="message"
                                                    placeholder="Type Message here..."
                                                    rows={4}
                                                    id="myTextarea"
                                                    onFocus={() => setOpenEmoji(false)}
                                                    className="border-0 focus:ring-white"
                                                />

                                                <div className="flex justify-between gap-1 flex-center lg:flex-row">
                                                    <div className="flex items-center gap-1 space-x-1">
                                                        <div className="pt-1">
                                                            <label htmlFor="message-file-input">
                                                                <Button
                                                                    className="px-0"
                                                                    size="xs"
                                                                    color="gray"
                                                                    theme={button_custom}
                                                                // onClick={() => handleClick()}
                                                                >
                                                                    <div className="flex items-center gap-1">
                                                                        <MdOutlineFingerprint className="text-slate-500" />
                                                                        <span className="text-sm">
                                                                            Add File
                                                                        </span>
                                                                    </div>
                                                                </Button>
                                                            </label>
                                                            <FileInput
                                                                id="message-file-input"
                                                                // ref={fileInputRef}
                                                                className="hidden"
                                                            />
                                                        </div>
                                                    </div>

                                                    <div className="flex flex-wrap gap-2">

                                                        <Button
                                                            type="submit"
                                                            size="xs"
                                                            color="blue"
                                                            id="var1"
                                                        >
                                                            <div className="flex items-center gap-1 text-sm">
                                                                <RiCheckDoubleFill className="text-lg" />
                                                                <span className="">Save</span>
                                                            </div>
                                                        </Button>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </Tabs.Item>
                </Tabs>
                <Modal
                    show={openMissedPunchModal}
                    onClose={() => setMissedPunchOpenModal(false)}
                >
                    <Modal.Header className="p-2">
                        <div className="flex items-center gap-2">
                            <MdCallSplit className="text-slate-400" />
                            <h4 className="text-lg">Lead Split</h4>
                        </div>
                    </Modal.Header>
                    <Modal.Body className="bg-slate-100 py-3 px-4 rounded-b-lg">
                        <LeadSplit></LeadSplit>
                    </Modal.Body>
                </Modal>
            </div>
        </>
    );
}
