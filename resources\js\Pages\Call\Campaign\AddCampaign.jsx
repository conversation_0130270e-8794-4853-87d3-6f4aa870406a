import React, { useEffect, useState } from "react";
import Box from "@mui/material/Box";
import Stepper from "@mui/material/Stepper";
import Step from "@mui/material/Step";
import StepLabel from "@mui/material/StepLabel";
// import Button from "@mui/material/Button";
import Typography from "@mui/material/Typography";
import {
    button_custom,
    card_custom,
    custom_datepicker_Bottom,
    input_custom,
    radio_custom,
    table_custom,
    textarea_custom,
    tooltip_custom,
} from "@/Pages/Helpers/DesignHelper";
import {
    Button,
    Card,
    Checkbox,
    Datepicker,
    FileInput,
    Label,
    Radio,
    Table,
    Textarea,
    TextInput,
    Tooltip,
} from "flowbite-react";
import { IoChevronBack } from "react-icons/io5";
import { AiOutlineReload } from "react-icons/ai";
import {
    MdAdd,
    MdDeleteOutline,
    MdOutlineAdd,
    MdOutlineAssignmentInd,
    MdOutlineCampaign,
    MdOutlineHandyman,
} from "react-icons/md";
import { IoMdAdd, IoMdClose, IoMdImage } from "react-icons/io";
import { fetchJson, showAsset } from "@/Pages/Helpers/Helper";
import { SlCalender } from "react-icons/sl";
import {
    FaBold,
    FaItalic,
    FaRegFaceSmileBeam,
    FaRegNoteSticky,
    FaStrikethrough,
} from "react-icons/fa6";
import EmojiPicker from "emoji-picker-react";
import { GrFormNext } from "react-icons/gr";
import { CgAttachment } from "react-icons/cg";
import NoRecord from "@/Components/HelperComponents/NoRecord";
import { LuRefreshCcw } from "react-icons/lu";
import Main from "@/Layouts/Main";
import { HiDownload, HiOutlineTemplate } from "react-icons/hi";
import TabBar from "../TabBar";

const steps = [
    "Basic Information",
    "Compose & Launch",
];

export default function AddCampaign({ categories, templates, category_id }) {
    // const [templateData, setTemplateData] = useState(templates.data);
    const [openEmoji, setOpenEmoji] = useState(false);
    const [channels, setChannelsData] = useState(null);
    const [contactList, setContactList] = useState(null);
    const [channelNextPage, setChannelNextPage] = useState();
    const [contactListNextPage, setContactListNextPage] = useState();

    const [activeStep, setActiveStep] = React.useState(0);

    const handleNext = () => {
        setActiveStep((prevActiveStep) => prevActiveStep + 1);
    };

    const handleBack = () => {
        setActiveStep((prevActiveStep) => prevActiveStep - 1);
    };

    const handleReset = () => {
        setActiveStep(0);
    };
    function fetchDataChannels() {
        Promise.all([
            fetchJson("helper.getChannels"),
            fetchJson("helper.getContactList"),
        ]).then(([channelsData, contactListData]) => {
            setChannelsData(channelsData.items.data);
            setChannelNextPage(channelsData.items.next_page_url);

            setContactList(contactListData.items.data);
            setContactListNextPage(contactListData.items.next_page_url);
        });
    }
    const loadMore = (nextPageUrl, setObject, object, nextPage) => {
        Promise.all([fetchJson(nextPageUrl, {}, true)]).then(([nextData]) => {
            if (nextData.items.next_page_url) {
                nextPage(nextData.items.next_page_url);
            } else {
                nextPage(null);
            }
            setObject((object) => [...object, ...nextData.items.data]);
        });
    };
    useEffect(() => {
        fetchDataChannels();
    }, []);
    const handleContactListChecked = (e) => {
        let id = e.target.value;
        if (e.target.checked) {
            if (data.contactList.length >= 5) {
                e.target.checked = false;
                alert("Max 5 Contact Lists allowed.");
                return false;
            }
            setData("contactList", [...data.contactList, id]);
        } else {
            setData(
                "contactList",
                data.contactList.filter((item) => {
                    return item !== id;
                })
            );
        }
    };

    const handleChannelsChecked = (e) => {
        let id = e.target.value;
        if (e.target.checked) {
            setData("channels", [...data.channels, id]);
        } else {
            setData(
                "channels",
                data.channels.filter((item) => {
                    return item !== id;
                })
            );
        }
    };
    const [showMiddleDiv, setShowMiddleDiv] = useState(false);

    const handleChildClick = () => {
        setShowMiddleDiv((prev) => !prev);
    };
    const renderStepContent = (step) => {
        switch (step) {
            case 0:
                return (
                    // Basic Information
                    <Typography className="p-0">
                        <div className="">
                            <div className="w-full p-2 bg-white border rounded-lg">
                                <h4 className="text-base">Basic Information</h4>
                                <p className="text-sm text-gray-400">
                                    Essential details for a strong campaign.
                                </p>
                            </div>
                            <div className="flex flex-col gap-4 p-2 px-3 mt-2 bg-white border rounded-lg">
                                {/* Campaign Name */}
                                <div className="max-w-screen-md">
                                    <div className="block mb-2">
                                        <Label htmlFor="countries">
                                            Campaign Name
                                            <span className="ms-1 text-red-600">
                                                *
                                            </span>
                                        </Label>
                                    </div>
                                    <Tooltip
                                        theme={tooltip_custom}
                                        placement="top"
                                        trigger="click"
                                        className="p-0"
                                        style="light"
                                        content={
                                            <div className="rounded w-60 text-wrap">
                                                <div className="bg-black text-white p-1.5 rounded-t text-center">
                                                    Play Campaign
                                                </div>
                                                <div className="p-1.5 text-center text-xs">
                                                    Export the data in CSV
                                                    format for offline use or
                                                    analysis.
                                                </div>
                                            </div>
                                        }
                                    >
                                        <TextInput
                                            name="campaignName"
                                            color={"gray"}
                                            theme={input_custom}
                                            type="text"
                                            placeholder="Ex. Good Friday Sale"
                                            required
                                        />
                                    </Tooltip>
                                </div>
                                {/* Choose gateway to send messages *  */}
                                <div className="">
                                    <div className="flex items-center gap-2 mb-2 ">
                                        <Label>
                                            Choose gateway to send messages
                                            <span className="ms-1 text-red-600">
                                                *
                                            </span>
                                        </Label>
                                        {channelNextPage ? (
                                            <Tooltip content="Load More">
                                                <Button
                                                    theme={button_custom}
                                                    size="xxs"
                                                    color="withoutBorder"
                                                    onClick={() =>
                                                        loadMore(
                                                            channelNextPage,
                                                            setChannelsData,
                                                            channels,
                                                            setChannelNextPage
                                                        )
                                                    }
                                                >
                                                    <span className="text-blue-600">
                                                        Load More...
                                                    </span>
                                                </Button>
                                            </Tooltip>
                                        ) : (
                                            <></>
                                        )}
                                    </div>
                                    <div className="flex flex-wrap  ">
                                        <div className="flex items-center flex-wrap lg:gap-6 md:gap-6 sm:gap-2 gap-3">
                                            {channels != null ? (
                                                channels &&
                                                    channels.length > 0 ? (
                                                    channels.map(
                                                        (channel, key) => (
                                                            <div
                                                                className="flex items-center gap-2"
                                                                key={
                                                                    "channels" +
                                                                    key
                                                                }
                                                            >
                                                                <Checkbox
                                                                    color={
                                                                        "blue"
                                                                    }
                                                                    id={
                                                                        "Channels" +
                                                                        key
                                                                    }
                                                                    name="channels[]"
                                                                    value={
                                                                        channel.id
                                                                    }
                                                                    onChange={
                                                                        handleChannelsChecked
                                                                    }
                                                                />
                                                                <Label
                                                                    htmlFor={
                                                                        "Channels" +
                                                                        key
                                                                    }
                                                                    className="flex"
                                                                >
                                                                    {
                                                                        channel.name
                                                                    }{" "}
                                                                    (
                                                                    {
                                                                        channel.status
                                                                    }
                                                                    )
                                                                </Label>
                                                            </div>
                                                        )
                                                    )
                                                ) : (
                                                    <div>
                                                        <NoRecord textSize="text-sm" />
                                                    </div>
                                                )
                                            ) : (
                                                <NoRecord
                                                    loading={true}
                                                    type="circle"
                                                />
                                            )}
                                        </div>
                                    </div>
                                    {/* {errors.channels ? (
                                        <p className="mt-2 text-sm text-red-600 dark:text-red-400">
                                            {errors.channels}
                                        </p>
                                    ) : (
                                        <></>
                                    )} */}
                                </div>

                                {/* Contact List */}
                                <div className="mb-2">
                                    <div className="flex items-center gap-2 mb-2 ">
                                        <Label>
                                            Contact List
                                            <span className="ms-1 text-red-600">
                                                *
                                            </span>
                                        </Label>
                                        {contactListNextPage ? (
                                            <Tooltip content="Load More">
                                                <Button
                                                    theme={button_custom}
                                                    size="xxs"
                                                    color="withoutBorder"
                                                    onClick={() =>
                                                        loadMore(
                                                            contactListNextPage,
                                                            setContactList,
                                                            contactList,
                                                            setContactListNextPage
                                                        )
                                                    }
                                                >
                                                    <span className="text-blue-600">
                                                        Load More...
                                                    </span>
                                                </Button>
                                            </Tooltip>
                                        ) : (
                                            <></>
                                        )}
                                    </div>
                                    <div className="">
                                        <div className="flex flex-wrap lg:gap-6 md:gap-6 sm:gap-2 items-center gap-2">
                                            {contactList != null ? (
                                                contactList.length > 0 ? (
                                                    contactList.map((cl, k) => (
                                                        <div
                                                            className="flex items-center gap-2 "
                                                            key={
                                                                "ContactListDiv" +
                                                                k
                                                            }
                                                        >
                                                            <Checkbox
                                                                color={"blue"}
                                                                id={
                                                                    "ContactListDiv" +
                                                                    k
                                                                }
                                                                name="contactList"
                                                                value={cl.id}
                                                                onChange={
                                                                    handleContactListChecked
                                                                }
                                                            />

                                                            <Label
                                                                className="text-nowrap"
                                                                htmlFor={
                                                                    "ContactListDiv" +
                                                                    k
                                                                }
                                                            >
                                                                {cl.name} (
                                                                {
                                                                    cl.totalContacts
                                                                }
                                                                )
                                                            </Label>
                                                        </div>
                                                    ))
                                                ) : (
                                                    <NoRecord textSize="text-sm" />
                                                )
                                            ) : (
                                                <NoRecord
                                                    loading={true}
                                                    type="circle"
                                                />
                                            )}
                                        </div>
                                    </div>
                                    {/* {errors.contactList ? (
                                        <p className="mt-2 text-sm text-red-600 dark:text-red-400">
                                            {errors.contactList}
                                        </p>
                                    ) : (
                                        <p className="mt-2 text-sm text-gray-500 dark:text-gray-500">
                                            Max 5 Contact list can be Selected.
                                        </p>
                                    )} */}
                                </div>
                            </div>
                        </div>
                        <div className="mt-2">
                            <div className="w-full p-2 px-4 bg-white border rounded-lg">
                                <h4 className="text-base">Schedule Campaign</h4>
                                <p className="text-sm text-gray-400">
                                    Set the perfect time to reach your audience
                                    for maximum impact.
                                </p>
                            </div>
                            <div className="flex flex-col gap-3 p-3 px-4 mt-2 bg-white border rounded-lg">
                                <div className="flex flex-wrap items-center gap-4">
                                    <div className="max-w-screen-md">
                                        <div className="block mb-2">
                                            <Label htmlFor="paymentMethod">
                                                Start Date
                                                <span className="text-red-600">
                                                    *
                                                </span>
                                            </Label>
                                        </div>
                                        <div className="relative">
                                            <div className="absolute inset-y-0 end-3.5 flex items-center pointer-events-none ps-4 pe-4 border-s"></div>
                                            <input
                                                type="date"
                                                id="timeAdd"
                                                className="w-72 border leading-none border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block p-2.5 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-blue-500 dark:focus:border-blue-500"
                                                // min="09:00"
                                                // max="18:00"
                                                // defaultValue="00:00"
                                                required
                                            />
                                        </div>
                                        {/* <input className="max-w-md rounded-md border-slate-300" type="date" id="birthdaytime" name="birthdaytime"/> */}
                                    </div>
                                    <div className="">
                                        <div className="block mb-2">
                                            <Label htmlFor="paymentMethod">
                                                Start Time{" "}
                                                <span className="text-red-600">
                                                    *
                                                </span>
                                            </Label>
                                        </div>
                                        <div className="relative">
                                            <div className="absolute inset-y-0 end-3.5 flex items-center pointer-events-none ps-3.5 border-s">
                                                <svg
                                                    className="w-5 h-5 dark:text-gray-400"
                                                    aria-hidden="true"
                                                    xmlns="http://www.w3.org/2000/svg"
                                                    fill="#93A2B7"
                                                    viewBox="0 0 24 24"
                                                >
                                                    <path d="M2 12C2 6.477 6.477 2 12 2s10 4.477 10 10-4.477 10-10 10S2 17.523 2 12Zm11-4a1 1 0 1 0-2 0v4a1 1 0 0 0 .293.707l3 3a1 1 0 0 0 1.414-1.414L13 11.586V8Z" />
                                                </svg>
                                            </div>
                                            <input
                                                type="time"
                                                id="timeAdd"
                                                className="w-72 border leading-none border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block p-2.5 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-blue-500 dark:focus:border-blue-500"
                                                min="09:00"
                                                max="18:00"
                                                defaultValue="00:00"
                                                required
                                            />
                                        </div>
                                    </div>
                                </div>
                                <div className="flex items-center gap-2">
                                    <Checkbox color="blue" id="promotion" />
                                    <Label htmlFor="promotion">
                                        Set the stop date and time settings
                                    </Label>
                                </div>
                                <div className="flex flex-wrap items-center gap-4">
                                    <div className="max-w-screen-md">
                                        <div className="block mb-2">
                                            <Label htmlFor="paymentMethod">
                                                Stop Date{" "}
                                                <span className="text-red-600">
                                                    *
                                                </span>
                                            </Label>
                                        </div>
                                        <div className="relative">
                                            <div className="absolute inset-y-0 end-3.5 flex items-center pointer-events-none ps-4 pe-4 border-s"></div>
                                            <input
                                                type="date"
                                                id="timeAdd"
                                                className="w-72 border leading-none border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block p-2.5 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-blue-500 dark:focus:border-blue-500"
                                                // min="09:00"
                                                // max="18:00"
                                                // defaultValue="00:00"
                                                required
                                            />
                                        </div>
                                    </div>
                                    <div className="">
                                        <div className="block mb-2">
                                            <Label htmlFor="paymentMethod">
                                                Stop Time{" "}
                                                <span className="text-red-600">
                                                    *
                                                </span>
                                            </Label>
                                        </div>
                                        <div className="relative">
                                            <div className="absolute inset-y-0 end-3.5 flex items-center pointer-events-none ps-3.5 border-s">
                                                <svg
                                                    className="w-5 h-5 dark:text-gray-400"
                                                    aria-hidden="true"
                                                    xmlns="http://www.w3.org/2000/svg"
                                                    fill="#93A2B7"
                                                    viewBox="0 0 24 24"
                                                >
                                                    <path d="M2 12C2 6.477 6.477 2 12 2s10 4.477 10 10-4.477 10-10 10S2 17.523 2 12Zm11-4a1 1 0 1 0-2 0v4a1 1 0 0 0 .293.707l3 3a1 1 0 0 0 1.414-1.414L13 11.586V8Z" />
                                                </svg>
                                            </div>
                                            <input
                                                type="time"
                                                id="timeAdd"
                                                className="w-72 border leading-none border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block p-2.5 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-blue-500 dark:focus:border-blue-500"
                                                min="09:00"
                                                max="18:00"
                                                defaultValue="00:00"
                                                required
                                            />
                                        </div>
                                    </div>
                                </div>
                                <div className="flex items-center gap-2 mt-3">
                                    <div>Sleep After </div>
                                    <div>
                                        <TextInput
                                            sizing="sm"
                                            theme={input_custom}
                                            id="PrincipalPaid"
                                            type="number"
                                            placeholder="0"
                                            required
                                            className="min-w-12 max-w-16"
                                        />
                                    </div>
                                    <div>Messages for</div>
                                    <div>
                                        <TextInput
                                            sizing="sm"
                                            theme={input_custom}
                                            id="PrincipalPaid"
                                            type="number"
                                            placeholder="0"
                                            required
                                            className="min-w-12 max-w-16"
                                        />
                                    </div>
                                    <div>Seconds.
                                        <span className="text-red-600 ms-1">*</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </Typography>
                );
            case 1:
                return (
                    <Typography className="p-0">
                        <div className="">
                            <div className="w-full p-2 bg-white border rounded-lg">
                                <h4 className="text-base">Audio Library</h4>
                                <p className="text-sm text-gray-400">
                                    Only one audio file can be selected at a time for the campaign.
                                </p>
                            </div>
                            <div className="overflow-x-auto mt-2 rounded-lg">
                                <Table theme={table_custom}>
                                    <TableHead>
                                        <TableHeadCell><div className="flex items-center gap-2">
                                            <Radio theme={radio_custom} color="blue" id="united-state" name="countries" value="USA" defaultChecked />
                                        </div></TableHeadCell>
                                        <TableHeadCell>Audio Name</TableHeadCell>
                                        <TableHeadCell>Duration</TableHeadCell>
                                        <TableHeadCell>Recording</TableHeadCell>
                                    </TableHead>
                                    <TableBody className="divide-y">
                                        <TableRow className="bg-white dark:border-gray-700 dark:bg-gray-800">
                                            <TableCell className="whitespace-nowrap font-medium text-gray-900 dark:text-white">
                                                <div className="flex items-center gap-2">
                                                    <Radio theme={radio_custom} color="blue" id="united-state" name="countries" value="USA" defaultChecked />
                                                </div>
                                            </TableCell>
                                            <TableCell>Sliver</TableCell>
                                            <TableCell>Laptop</TableCell>
                                            <TableCell>
                                                <audio className="h-12" autoplay="autoplay" controls="controls" id="player">
                                                    <source src="http://www.issilissinew.com/zindorg/1/mp3/2014/Mukunda/128/02%20-%20Daredumdadum%20%5bwww.AtoZmp3.in%5d.mp3.ogg" />
                                                    <source src="http://www.issilissinew.com/zindorg/1/mp3/2014/Mukunda/128/02%20-%20Daredumdadum%20%5bwww.AtoZmp3.in%5d.mp3" />
                                                    <p> Your browser doesn't support the audio tag </p>
                                                </audio>
                                                {/* <div>
                                                    <button onclick="document.getElementById('player').play()">Play</button>
                                                    <button onclick="document.getElementById('player').pause()">Pause</button>
                                                    <button onclick="document.getElementById('player').volume+ = 0.5">Vol+ </button>
                                                    <button onclick="document.getElementById('player').volume- = 0.5">Vol- </button>    
                                                </div>   */}
                                            </TableCell>
                                        </TableRow>
                                    </TableBody>
                                </Table>
                            </div>

                        </div>
                    </Typography>
                );

                return (
                    <Typography className="grid grid-flow-row-dense grid-cols-12 p-0 pt-2 border-none md:grid-cols-12 sm:grid-cols-1 lg:gap-2">
                        <div className="relative flex flex-col gap-2 pt-0 dark:text-gray-400 lg:p-0 xl:col-span-9 lg:col-span-8 md:col-span-12 col-span-full">
                            <div className="flex items-start w-full gap-5 p-2 px-4 bg-white border rounded-lg ">
                                <div>
                                    <h4 className="text-base ">
                                        Campaign Name
                                    </h4>
                                    <p className="text-sm text-gray-400">
                                        Weekend cash back offer
                                    </p>
                                </div>
                                <div>
                                    <h4 className="text-base ">Gateway Name</h4>
                                    <p className="text-sm text-gray-400">
                                        Gateway 2
                                    </p>
                                </div>
                            </div>{" "}
                            <div className="p-2 text-sm bg-white border rounded-lg">
                                <div
                                    // className="flex flex-wrap items-center w-full gap-2"
                                    className="grid grid-flow-row-dense grid-cols-12 gap-2 border-none md:grid-cols-12 sm:grid-cols-1"
                                >
                                    <div className="relative flex flex-col gap-2 p-2 border rounded-md lg:col-span-6 md:col-span-6 col-span-full">
                                        <div className="flex items-center justify-between w-full">
                                            <span>Template Name</span>
                                            <span className="text-gray-500">
                                                Cash Back Offer
                                            </span>
                                        </div>
                                    </div>
                                    <div className="relative flex flex-col gap-2 p-2 border rounded-md lg:col-span-6 md:col-span-6 col-span-full">
                                        <div className="flex items-center justify-between w-full">
                                            <span>Start Time</span>
                                            <span className="text-gray-500">
                                                28/10/24 | 02:45
                                            </span>
                                        </div>
                                    </div>
                                </div>
                                <div className="grid grid-flow-row-dense grid-cols-12 gap-2 mt-2 border-none md:grid-cols-12 sm:grid-cols-1">
                                    <div className="relative flex flex-col gap-2 p-2 border rounded-md lg:col-span-6 md:col-span-6 col-span-full">
                                        <div className="flex items-center justify-between w-full">
                                            <span>Stop Time</span>
                                            <span className="text-gray-500">
                                                27/10/24 | 02:45
                                            </span>
                                        </div>
                                    </div>
                                    <div className="relative flex flex-col gap-2 p-2 border rounded-md lg:col-span-6 md:col-span-6 col-span-full">
                                        <div className="flex items-center justify-between w-full">
                                            <span>Minimum Delay</span>
                                            <span className="text-gray-500">
                                                10 Seconds
                                            </span>
                                        </div>
                                    </div>
                                </div>
                                <div className="grid grid-flow-row-dense grid-cols-12 gap-2 mt-2 border-none md:grid-cols-12 sm:grid-cols-1">
                                    <div className="relative flex flex-col gap-2 p-2 border rounded-md lg:col-span-6 md:col-span-6 col-span-full">
                                        <div className="flex items-center justify-between w-full">
                                            <span>Maximum Delay</span>
                                            <span className="text-gray-500">
                                                2 Seconds
                                            </span>
                                        </div>
                                    </div>
                                    <div className="relative flex flex-col gap-2 p-2 border rounded-md lg:col-span-6 md:col-span-6 col-span-full">
                                        <div className="flex items-center justify-between w-full">
                                            <span>
                                                Want to try again 10 times
                                            </span>
                                            <span className="text-gray-500"></span>
                                        </div>
                                    </div>
                                </div>
                                <div className="relative flex flex-col gap-2 mt-2 p-2 border rounded-md lg:col-span-6 md:col-span-6 col-span-full">
                                    <div className="flex items-center justify-between w-full">
                                        <span>Sleep After 20 Emials for 30 Seconds.</span>
                                        <span className="text-gray-500">

                                        </span>
                                    </div>
                                </div>
                            </div>
                            <div className="py-2 px-4 text-sm bg-white border rounded-lg">
                                {/* Media Files */}
                                <div className="text-sm">
                                    <span className="text-sm">Contact Lists</span>
                                    <div className="grid grid-flow-row-dense grid-cols-12 gap-2 mt-2 border-none md:grid-cols-12 sm:grid-cols-1">
                                        <div className="relative flex flex-col gap-2 p-2 border rounded-md lg:col-span-6 md:col-span-6 col-span-full">
                                            <div className="flex items-center justify-between w-full">
                                                <span>RapBooster customers</span>
                                                <span className="text-gray-500">
                                                    2598
                                                </span>
                                            </div>
                                        </div>
                                        <div className="relative flex flex-col gap-2 p-2 border rounded-md lg:col-span-6 md:col-span-6 col-span-full">
                                            <div className="flex items-center justify-between w-full">
                                                <span>
                                                    Second contact list
                                                </span>
                                                <span className="text-gray-500">50</span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div
                            className="col-span-12 mb-2 xl:col-span-3 lg:col-span-4 md:col-span-12 sm:col-span-12 lg:flex"
                        // key={index}
                        >
                            <Card theme={card_custom} className="relative w-full ">
                                <div className="absolute -left-0 top-6 w-0 h-0 border-t-[1px] border-t-transparent border-b-[12px] border-b-transparent border-r-[18px] border-r-white"></div>
                                <div>
                                    <div className="p-2 bg-slate-200 overflow-auto h-fit">
                                        <div className="bg-white px-3 rounded-lg mx-2.5 pb-1.5 py-0.5">
                                            <>
                                                <div className="flex items-center justify-center">
                                                    <img
                                                        src={showAsset(
                                                            "/assets/img/mailviewopen.png"
                                                        )}
                                                        className="w-full my-2 rounded-md max-h-36 min-h-fit"
                                                    />
                                                </div>
                                            </>
                                            <div
                                                className=" text-center text-gray-300"
                                                style={{
                                                    fontSize: "7rem",
                                                }}
                                            ></div>
                                            <div className="my-2 rounded-lg">
                                                <div className="text-sm text-gray-400"></div>
                                                <div className="text-gray-700 text-sm">
                                                    Subject Your One-Stop Solution for
                                                    Digital Growth Body At RapBooster,
                                                    we offer a wide range of
                                                    cutting-edge digital tools and
                                                    services designed to propel your
                                                    business forward. Whether you are a
                                                    start-up or an established
                                                    enterprise, our platform provides
                                                    everything you need to enhance your
                                                    digital presence and optimize your
                                                    marketing strategies.From SEO
                                                    optimization and social media
                                                    management to advanced analytics and
                                                    content creation.
                                                </div>
                                            </div>
                                            <div className="flex flex-col gap-1">
                                                <div>
                                                    <span>Attachment: </span>
                                                    <span className="text-blue-600">
                                                        2
                                                    </span>
                                                </div>
                                                <div className="flex items-center gap-2 flex-wrap">
                                                    <div className="relative inline-block rounded-md ">
                                                        <div className="border">
                                                            <div className="flex items-center justify-center h-14">
                                                                <IoMdImage className="text-4xl text-gray-300" />
                                                            </div>
                                                            <div className="border-t p-2 flex items-start gap-1">
                                                                <IoMdImage className="text-lg text-red-600 " />
                                                                <div>
                                                                    <div className="text-xs">
                                                                        champion
                                                                        12dec...
                                                                    </div>
                                                                    <div className="text-xs">
                                                                        Not Scanned for
                                                                        viruses
                                                                    </div>
                                                                </div>
                                                            </div>
                                                        </div>
                                                        <button className="bg-white absolute top-1 right-1 text-slate-500 text-xl">
                                                            <HiDownload />
                                                        </button>
                                                    </div>
                                                </div>
                                            </div>
                                            <></>
                                        </div>
                                    </div>
                                </div>
                            </Card>
                        </div>
                    </Typography>
                );
        }
    };

    return (
        <Main>
            <div className="p-2">
                <TabBar></TabBar>
            </div>
            <div className="flex items-center gap-2 p-2 px-4">
                <MdOutlineCampaign className="text-2xl text-slate-400" />
                <span className="text-lg">Create Campaign</span>
            </div>
            <Box sx={{ width: "100%" }}>
                {/* Stepper */}
                <Stepper
                    activeStep={activeStep}
                    className="p-2 px-2 flex-wrap gap-2 text-nowrap font-medium w-96"
                >
                    {steps.map((label) => (
                        <Step key={label}>
                            <StepLabel className="font-bold ">
                                {label}
                            </StepLabel>
                        </Step>
                    ))}
                </Stepper>

                {/* Content */}

                <React.Fragment>
                    {/* Render content dynamically */}
                    <Box sx={{ mt: 0, mb: 0, px: 2 }}>
                        {renderStepContent(activeStep)}
                    </Box>
                    <Box
                        sx={{
                            display: "flex",
                            flexDirection: "row",
                            pt: 2,
                        }}
                    >
                        <Button
                            theme={button_custom}
                            color="withoutBorder"
                            className="text-blue-600"
                            disabled={activeStep === 0}
                            onClick={handleBack}
                            sx={{ mr: 1 }}
                        >
                            <div className="flex items-center">
                                <IoChevronBack />
                                Back
                            </div>
                        </Button>
                        <Box sx={{ flex: "1 1 auto" }} />
                        <div className="flex items-center gap-1">
                            <Button
                                size="sm"
                                className="me-4"
                                color="failure"
                                theme={button_custom}
                                onClick={handleNext}
                            >
                                <div className="flex items-center gap-1">
                                    <IoMdClose className="text-lg" />
                                    Cancel
                                </div>
                            </Button>
                            <Button
                                size="sm"
                                className="me-4"
                                color="blue"
                                theme={button_custom}
                                onClick={handleNext}
                            >
                                <div className="flex items-center gap-1">
                                    {activeStep === steps.length - 1
                                        ? "Finish"
                                        : "Next"}
                                    <GrFormNext className="text-lg" />
                                </div>
                            </Button>
                        </div>
                    </Box>
                </React.Fragment>
            </Box>
        </Main>
    );
}

