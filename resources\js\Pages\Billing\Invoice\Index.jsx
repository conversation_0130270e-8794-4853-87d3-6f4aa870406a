import { Head } from "@inertiajs/react";
import $ from "jquery";
import { Tooltip, Button, ButtonGroup, Table, Checkbox, Badge } from "flowbite-react";
import { TbColumns3, TbReportSearch } from "react-icons/tb";
import { useState } from "react";
import { IoClose } from "react-icons/io5";
import {
    MdOutlineFileDownload,
    MdOutlineInventory,
    MdOutlinePlagiarism,
    MdOutlineReceiptLong,
    MdOutlineSell,
} from "react-icons/md";
import Main from "@/Layouts/Main";
import { button_custom, customDrawer, table_custom } from "@/Pages/Helpers/DesignHelper";
import { IoIosArrowDown, IoMdAdd } from "react-icons/io";
import { RiDeleteBin6Line } from "react-icons/ri";
import { PiCaretUpDownBold, PiExportBold } from "react-icons/pi";
import Filter from "./Filter";
import TabBar from "../TabBar";
import Details from "./Details";
import Create from "./Create";
import InvoiceHistory from "./InvoiceHistory";
import EditBillingAddress from "./EditBillingAddress";

function Index() {
    const [isCheckAll, setIsCheckAll] = useState(false);
    // Lead Contact
    const [isOpen, setIsOpen] = useState(false);
    const handleClose = () => setIsOpen(false);
    // Details Drawer
    const [isOpenDetails, setIsOpenInvoiceHistory] = useState(false);
    // Edit Lead
    const [isOpenEditBillingAddress, setIsOpenEditBillingAddress] = useState(false);
    // Toggle Row
    const ToggleChild = (parent, child) => {
        // console.log(parent);
        var parentEle = $("#" + parent);
        var ele = $("#" + child);
        if (ele.hasClass("hidden")) {
            parentEle.addClass("rotate-180");
            ele.removeClass("hidden");
        } else {
            parentEle.removeClass("rotate-180");
            ele.addClass("hidden");
        }
    };

    // const ThTdPaddings = "px-2 py-1 text-nowrap";
    return (
        <Main>
            <Head title="User" />
            <div className="relative overflow-hidden p-2">
                <div className="grid grid-cols-12 md:grid-cols-12 sm:grid-cols-1 gap-2 grid-flow-row-dense bg-slate-100 p-0 border-none">
                    <div className="lg:col-span-2 md:col-span-3 sm:col-span-3 lg:flex hidden">
                        <div className="bg-white shadow-sm rounded-lg border dark:bg-gray-900 w-full overflow-auto min-h-[90vh] max-h-[90vh]">
                            <Filter />
                        </div>
                    </div>

                    <div className="dark:text-gray-400 lg:p-0 pt-0 lg:col-span-10 md:col-span-12 col-span-full relative">
                        <div className="col-span-full mb-2">
                            <TabBar />
                        </div>
                        <div className="min-h-[80vh] max-h-[80vh] bg-white border rounded-lg">
                            <div className="flex justify-between p-2">
                                <div className="flex items-center gap-1">
                                    <ButtonGroup>
                                        <Button
                                            theme={button_custom}
                                            size="xs"
                                            color="gray"
                                        >
                                            <div className="flex items-center gap-1">
                                                <RiDeleteBin6Line className="text-slate-500" />
                                                <span className="text-xs">
                                                    Delete
                                                </span>
                                            </div>
                                        </Button>

                                        <Button
                                            theme={button_custom}
                                            size="xs"
                                            color="gray"
                                            id="dropdownInformationButton"
                                            data-dropdown-toggle="dropdownNotification"
                                            type="button"
                                        >
                                            <div className="flex items-center gap-1">
                                                <TbColumns3 className="text-slate-500" />
                                                <span className="text-xs">
                                                    Columns
                                                </span>
                                            </div>
                                        </Button>
                                    </ButtonGroup>
                                    <Button
                                        theme={button_custom}
                                        size="xs"
                                        color="gray"
                                    >
                                        <div className="flex items-center gap-1">
                                            <PiExportBold className="text-slate-500" />
                                            <span className="text-xs">
                                                Export
                                            </span>
                                        </div>
                                    </Button>
                                </div>

                                <ButtonGroup className="">
                                    <Button
                                        theme={button_custom}
                                        size="xs"
                                        color="gray"
                                        onClick={() =>
                                            setIsOpen(
                                                true
                                            )
                                        }
                                    >
                                        <div className="flex items-center gap-1">
                                            <IoMdAdd className="text-slate-500" />
                                            <span className="text-xs">
                                                Create Invoice
                                            </span>
                                        </div>
                                    </Button>
                                </ButtonGroup>
                            </div>

                            <div className="overflow-x-auto bg-white border rounded-lg">
                                <Table hoverable theme={table_custom}>
                                    <TableHead className="bg-slate-100">
                                        <TableHeadCell>
                                            <Checkbox color="blue"
                                                checked={isCheckAll}
                                                onChange={() =>
                                                    setIsCheckAll(!isCheckAll)
                                                }
                                            />
                                        </TableHeadCell>

                                        <TableHeadCell>
                                            <div className="flex items-center justify-between gap-2">
                                                <h3>Date & Time</h3>
                                                <PiCaretUpDownBold />
                                            </div>
                                        </TableHeadCell>

                                        <TableHeadCell className="text-nowrap">PI / INV Number</TableHeadCell>

                                        <TableHeadCell>
                                            <div className="flex items-center justify-between gap-2">
                                                <h3>Customers</h3>
                                                <PiCaretUpDownBold />
                                            </div>
                                        </TableHeadCell>
                                        <TableHeadCell>Manager</TableHeadCell>
                                        <TableHeadCell>
                                            <div className="flex items-center justify-between gap-2">
                                                <h3>Status</h3>
                                                <PiCaretUpDownBold />
                                            </div>
                                        </TableHeadCell>

                                        <TableHeadCell>Actions</TableHeadCell>
                                    </TableHead>

                                    <TableBody className="divide-y">
                                        <TableRow className="">
                                            <TableCell>
                                                <Checkbox color="blue" className="rowCheckBox" />
                                            </TableCell>
                                            <TableCell>
                                                <div className="flex flex-col">
                                                    <div className="text-blue-600 text-sm ">
                                                        24/03/2024
                                                    </div>
                                                    <div className="text-xs">
                                                        02:45 PM
                                                    </div>
                                                </div>
                                            </TableCell>
                                            <TableCell className="cursor-pointer text-blue-600" onClick={() => setIsOpenInvoiceHistory(true)}>
                                                PI-2425-531
                                            </TableCell>
                                            <TableCell>
                                                <div className="flex flex-col">
                                                    <div className="text-blue-600 text-sm ">
                                                        Abhishek Bhattacharyya (8560)
                                                    </div>
                                                    <div className="text-xs">
                                                        Dunes Factory Pvt. Ltd.
                                                    </div>
                                                </div>
                                            </TableCell>
                                            <TableCell>
                                                <Tooltip
                                                    content="Abhishek"
                                                    className="bg-slate-700 p-1"
                                                >
                                                    <img
                                                        src="https://img.freepik.com/premium-photo/profile-icon-white-background_941097-160810.jpg"
                                                        alt=""
                                                        className="rounded-full size-10"
                                                    />
                                                </Tooltip>
                                            </TableCell>
                                            <TableCell>
                                                <Badge className="w-fit" color="warning">Unpaid</Badge>
                                            </TableCell>
                                            <TableCell>
                                                <div className="flex items-center justify-between">
                                                    <div className="flex gap-2 h-fit text-nowrap">
                                                        {/* <Tooltip
                                                            content="Documents"
                                                            className="bg-slate-700 p-1 px-2"
                                                        > */}
                                                        <Button
                                                            theme={
                                                                button_custom
                                                            }
                                                            color="failure"
                                                            size="xs"
                                                            onClick={() =>
                                                                setIsOpen(
                                                                    true
                                                                )
                                                            }
                                                        >
                                                            <div className="flex items-center gap-1">
                                                                <IoClose />
                                                                <span className="text-xs">Reject</span>
                                                            </div>
                                                        </Button>
                                                        <Button
                                                            theme={
                                                                button_custom
                                                            }
                                                            color="orange"
                                                            size="xs"
                                                            onClick={() =>
                                                                setIsOpenDocuments(
                                                                    true
                                                                )
                                                            }
                                                        >
                                                            <div className="flex items-center gap-1">
                                                                <MdOutlinePlagiarism />
                                                                <span className="text-xs">Approve</span>
                                                            </div>
                                                        </Button>
                                                        {/* </Tooltip> */}
                                                        <Button
                                                            theme={
                                                                button_custom
                                                            }
                                                            color="green"
                                                            size="xs"
                                                            onClick={() =>
                                                                setIsOpenEditBillingAddress(
                                                                    true
                                                                )
                                                            }
                                                        >
                                                            <div className="flex items-center gap-1">
                                                                <MdOutlinePlagiarism />
                                                                <span className="text-xs">Edit</span>
                                                            </div>
                                                        </Button>
                                                        <Button
                                                            theme={
                                                                button_custom
                                                            }
                                                            color="failure"
                                                            size="xs"
                                                            onClick={() =>
                                                                setIsOpenDocuments(
                                                                    true
                                                                )
                                                            }
                                                        >
                                                            <div className="flex items-center gap-1">
                                                                <RiDeleteBin6Line />
                                                                <span className="text-xs">Delete</span>
                                                            </div>
                                                        </Button>
                                                        <Button
                                                            theme={
                                                                button_custom
                                                            }
                                                            color="blue"
                                                            size="xs"
                                                            onClick={() =>
                                                                setIsOpenDocuments(
                                                                    true
                                                                )
                                                            }
                                                        >
                                                            <div className="flex items-center gap-1">
                                                                <MdOutlineSell />
                                                                <span className="text-xs">Payment Link</span>
                                                            </div>
                                                        </Button>
                                                        <Button
                                                            theme={
                                                                button_custom
                                                            }
                                                            color="Fuchsia_custom"
                                                            size="xs"
                                                            onClick={() =>
                                                                setIsOpenDocuments(
                                                                    true
                                                                )
                                                            }
                                                        >
                                                            <div className="flex items-center gap-1">
                                                                <MdOutlineFileDownload />
                                                                <span className="text-xs">Download</span>
                                                            </div>
                                                        </Button>
                                                    </div>
                                                    <div>
                                                        <button
                                                            className="p-1.5"
                                                            id="parentID"
                                                            onClick={() =>
                                                                ToggleChild(
                                                                    "parentID",
                                                                    "childTr"
                                                                )
                                                            }
                                                        >
                                                            <IoIosArrowDown />

                                                        </button>
                                                    </div>
                                                </div>
                                            </TableCell>
                                        </TableRow>
                                        <TableRow
                                            className="hidden bg-gray-100 "
                                            id="childTr"
                                        >
                                            <TableCell colSpan={9}>
                                                <Details></Details>
                                            </TableCell>
                                        </TableRow>

                                    </TableBody>
                                </Table>
                            </div>
                        </div>
                        <div className="float-end border absolute bottom-0 rounded-b-lg p-3 w-full bg-white ">
                            <div className="flex flex-wrap justify-between lg:gap-0 md:gap-0 gap-2">
                                <div className="flex gap-3 items-center">
                                    <div className="text-gray-400 text-sm ">
                                        Showing 1 to 25 of 62 entries
                                    </div>
                                    <div>
                                        <select
                                            id="countries"
                                            className="text-xs bg-gray-50 border border-gray-300 text-gray-900 rounded focus:ring-blue-500 focus:border-blue-500 block p-1 ps-2 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-blue-500 dark:focus:border-blue-500"
                                        >
                                            <option
                                                value={10}
                                                defaultValue={10}
                                            >
                                                10
                                            </option>
                                            <option value={15}>15</option>
                                            <option value={20}>20</option>
                                            <option value={25}>25</option>
                                            <option value={30}>30</option>
                                        </select>
                                    </div>
                                </div>
                                <div className="flex">
                                    <Button
                                        size="xs"
                                        color="gray"
                                        className="border-e-0 text-gray-400 px-2 rounded text-xs "
                                    >
                                        Previous
                                    </Button>
                                    <Button
                                        size="xs"
                                        color="blue"
                                        className="border text-white border-blue-600 bg-blue-600 px-2.5 rounded-none text-sm "
                                    >
                                        1
                                    </Button>
                                    <Button
                                        size="xs"
                                        color="gray"
                                        className="border text-blue-600 px-2.5 rounded-none text-xs "
                                    >
                                        2
                                    </Button>
                                    <Button
                                        size="xs"
                                        color="gray"
                                        className="border-s-0 text-blue-600 px-2 rounded text-xs "
                                    >
                                        Next
                                    </Button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            {/* Create Invoice */}
            <Drawer
                theme={customDrawer}
                open={isOpen}
                onClose={() => setIsOpen(false)}
                position="right"
                className="w-full xl:w-10/12 lg:w-full md:w-full"
            >
                <DrawerHeader
                    titleIcon={MdOutlineInventory}
                    title="Invoice Details"
                />
                <DrawerItems>
                    <Create></Create>
                </DrawerItems>
            </Drawer>
            {/* Invoice History */}
            <Drawer
                theme={customDrawer}
                open={isOpenDetails}
                onClose={() => setIsOpenInvoiceHistory(false)}
                position="right"
                className="w-full xl:w-10/12 lg:w-full md:w-full"
            >
                <DrawerHeader
                    titleIcon={TbReportSearch}
                    title="Invoice History"
                />
                <DrawerItems>
                    <InvoiceHistory />
                </DrawerItems>
            </Drawer>
            {/* Edit Billing Address */}
            <Drawer
                theme={customDrawer}
                open={isOpenEditBillingAddress}
                onClose={() => setIsOpenEditBillingAddress(false)}
                position="right"
                className="w-full xl:w-10/12 lg:w-full md:w-full"
            >
                <DrawerHeader
                    titleIcon={MdOutlineReceiptLong}
                    title="Edit Billing Address"
                />
                <DrawerItems>
                    <EditBillingAddress />
                </DrawerItems>
            </Drawer>
        </Main>
    );
}
export default Index;



