import { <PERSON><PERSON>, <PERSON><PERSON>, Spinner } from "flowbite-react";
import { useEffect, useRef, useState } from "react";

import {
    customDrawer,
} from "@/Pages/Helpers/DesignHelper";
import {
    convertDateFormat,
    fetchJson,
    showAsset,
} from "@/Pages/Helpers/Helper";
import { RiAccountBoxLine, RiCheckDoubleFill } from "react-icons/ri";

import { Head } from "@inertiajs/react";
import PeopleList from "./PeopleList";
import Profile from "./Profile";
import TextBox from "./TextBox";
import { LuListFilter } from "react-icons/lu";

function Index() {
    const [isOpenProfile, setIsOpenProfile] = useState(false);
    const handleCloseProfile = () => setIsOpenProfile(false);
    const [messageData, setMessageData] = useState([]);
    const [isLoading, setIsLoading] = useState(false);
    const divRef = useRef(null);
    const [selectedUser, setSelectedUser] = useState();
    const [nextPageUrl, setNextPageUrl] = useState(null);
    const [groupData, setGroupData] = useState([]);
    const [current, setCurrent] = useState();
    const [isListOpen, setIsListOpen] = useState(false);
    // useEffect(() => {
    //     console.log('nextPageUrl', nextPageUrl);
    // }, [nextPageUrl]);

    useEffect(() => {
        if (selectedUser) {
            resetMsgWindow();
        }
    }, [selectedUser]);
    const resetStates = async () => {
        try {
            setGroupData([]);
            setMessageData([]);
            setIsLoading(true);
            setNextPageUrl(null);
            return true;
        } catch (e) {
        }
    };
    const resetMsgWindow = async () => {
        try {
            await resetStates();
            fetchChat();
            divRef.current.scrollTop = divRef.current.scrollHeight;
            let divElement = document.getElementById("chatsBox");
            divElement.scrollTop = divElement.scrollHeight;
        } catch (error) {
            console.error("msg window function error :", error);
        }
    };

    const fetchChat = () => {
        setIsLoading(true);
        Promise.all([
            fetchJson(
                route("whatsappB.chats.show", { chat: selectedUser }),
                {},
                true
            ),
        ]).then(([chatData]) => {
            setIsLoading(false);

            setMessageData(chatData.data.data);
            setNextPageUrl(chatData.data.next_page_url);
        });
    };

    useEffect(() => {
        setGroupData(reverseGroupedObject(groupByDate(messageData)));
    }, [messageData]);

    const renderChange = () => {
        // setNextPageUrl(null);
        fetchChat();
        // divRef.current.scrollTop = divRef.current.scrollHeight;
        // let divElement = document.getElementById('chatsBox');
        // divElement.scrollTop = divElement.scrollHeight;
    };

    const loadMore = () => {
        setIsLoading(true);
        Promise.all([fetchJson(nextPageUrl, {}, true)]).then(([chatData]) => {
            setIsLoading(false);
            setMessageData([...messageData, ...chatData.data.data]);
            setNextPageUrl(chatData.data.next_page_url);
        });
    };

    const groupByDate = (data) => {
        return data.reduce((acc, item) => {
            const date = item.messageDateTime.split("T")[0];
            acc[date] = acc[date] || [];
            acc[date].push(item);
            return acc;
        }, {});
    };

    const reverseGroupedObject = (grouped) => {
        return Object.keys(grouped)
            .sort((a, b) => new Date(a) - new Date(b)) // Sort keys in ascending order
            .reduce((acc, key) => {
                acc[key] = grouped[key]; // Add the key-value pair in sorted order
                return acc;
            }, {});
    };

    return (
        <>
            {/* <TabBar /> */}
            <div className="absolute w-full h-32 bg-green-500"></div>
            <div className="lg:hidden">
                <div className="relative inline-block text-left">
                    {/* Dropdown Button */}
                    <button
                        onClick={() => setIsListOpen(!isListOpen)}
                        className="inline-flex justify-center w-full p-2 text-sm font-medium text-white bg-blue-700 rounded-md shadow-sm"
                    >
                        <LuListFilter className="text-lg" />
                    </button>
                    {/* Dropdown Menu */}
                    {/* {isListOpen && (
                        <div className="w-[70vw] absolute z-50 mt-2 origin-top-right bg-white rounded-md shadow-lg  ring-1 ring-black ring-opacity-5">
                            <div className="w-full bg-white shadow-sm rounded-lg border dark:bg-gray-900  max-h-[calc(100vh-12rem)] overflow-y-auto">
                                <PeopleList SelectedId={setSelectedUser} currentObject={setCurrent} />

                            </div>
                        </div>
                    )} */}
                </div>
            </div>
            <div className="relative h-screen p-2 inbox-screen">
                <Head title="Chats" />
                <div className="grid h-full grid-cols-12 gap-2 p-0 border-none grid-2 flow-row-dense md:grid-cols-12 sm:grid-cols-1 ">
                    <div className="hidden xl:col-span-3 lg:col-span-4 md:col-span-5 sm:col-span-3 lg:flex ">
                        <PeopleList
                            SelectedId={setSelectedUser}
                            currentObject={setCurrent}
                        />
                    </div>
                    <div className="relative border rounded-lg dark:text-gray-400 lg:p-0 xl:col-span-9 lg:col-span-8 md:col-span-12 col-span-full border-s-0">
                        {/* {selectedUser ? ( */}
                            <div className="border rounded-lg border-s-0">
                                <div className="bg-[#EEEEEE] rounded-t-lg w-full">
                                    <div className="flex items-center">
                                        <img
                                            className="p-2 h-14 w-14"
                                            src={showAsset(
                                                "/assets/img/dfprofile.png",''
                                            )}
                                            alt=""
                                        />
                                        <div className="flex flex-col text-start">
                                            <h5 className="font-medium text-blue-600">
                                                {selectedUser}
                                            </h5>
                                        </div>
                                    </div>
                                </div>
                                <div className="bg-[url('/assets/img/bgwtsp.png')]">
                                    <div
                                        className="p-4 space-y-4 overflow-auto scroll-smooth"
                                        id="chatsBox"
                                        ref={divRef}
                                        style={{ height: "71vh" }}
                                    >
                                        {/* Date Label */}
                                        <div className="flex flex-col items-center">
                                            {isLoading && <Spinner size="xl" />}
                                            {nextPageUrl ? (
                                                <Button
                                                    size="xs"
                                                    className={``}
                                                    onClick={loadMore}
                                                >
                                                    Load More
                                                </Button>
                                            ) : (
                                                "End of chat"
                                            )}
                                        </div>
                                        {groupData && (
                                            <div>
                                                {Object.keys(groupData).map(
                                                    (date) => (
                                                        <div
                                                            className="flex flex-col gap-2"
                                                            key={date}
                                                        >
                                                            <div className="text-sm text-center text-gray-700 ">
                                                                <span className="px-2 py-1 rounded-md w-fit bg-sky-200">
                                                                    {convertDateFormat(
                                                                        date,
                                                                        "date"
                                                                    )}
                                                                </span>
                                                            </div>
                                                            {groupData[date]
                                                                .sort(
                                                                    (a, b) =>
                                                                        a.id -
                                                                        b.id
                                                                )
                                                                .map(
                                                                    (
                                                                        msg,
                                                                        k
                                                                    ) => (
                                                                        <div
                                                                            className={`${
                                                                                msg.fromMe ==
                                                                                true
                                                                                    ? " justify-end "
                                                                                    : " justify-start "
                                                                            }  flex `}
                                                                            key={
                                                                                "chatItem-" +
                                                                                k
                                                                            }
                                                                        >
                                                                            <div
                                                                                className={`${
                                                                                    msg.fromMe ==
                                                                                    true
                                                                                        ? " justify-end "
                                                                                        : " justify-start "
                                                                                } flex w-3/4 text-sm lg:w-2/4 md:w-3/4   `}
                                                                            >
                                                                                <div
                                                                                    className={`${
                                                                                        msg.fromMe ==
                                                                                        true
                                                                                            ? " bg-[#DCF8C6]  "
                                                                                            : " bg-white "
                                                                                    } p-2 bg-[#DCF8C6] rounded-lg shadow-md`}
                                                                                >
                                                                                    <span className="text-sm text-blue-600">
                                                                                        Gateway 1
                                                                                    </span>

                                                                                    <p className="text-gray-800">
                                                                                        {
                                                                                            msg.msg
                                                                                        }
                                                                                        {/* --
                                                                        {msg.id} */}
                                                                                    </p>
                                                                                    <span className="mt-1 text-xs text-gray-500">
                                                                                        <div className="flex items-center justify-end gap-1">
                                                                                            {convertDateFormat(
                                                                                                msg.messageDateTime,
                                                                                                "time"
                                                                                            )}
                                                                                            <RiCheckDoubleFill className="text-base text-blue-700"/>
                                                                                            {/* {msg.fromMe ==
                                                                                            true ? (
                                                                                                <Dropdown
                                                                                                    theme={
                                                                                                        dropdownChats_custom
                                                                                                    }
                                                                                                    className=""
                                                                                                    arrowIcon={
                                                                                                        false
                                                                                                    }
                                                                                                    label={
                                                                                                        <BiErrorAlt className="text-sm text-red-600" />
                                                                                                    }
                                                                                                    // color={"white"}
                                                                                                    inline
                                                                                                >
                                                                                                    <DropdownItem>
                                                                                                        <div className="flex items-center gap-1">
                                                                                                            <FaCheck />
                                                                                                            Approve
                                                                                                        </div>
                                                                                                    </DropdownItem>
                                                                                                    <DropdownItem>
                                                                                                        <div className="flex items-center gap-1.5">
                                                                                                            <IoClose />
                                                                                                            Reject
                                                                                                        </div>
                                                                                                    </DropdownItem>
                                                                                                </Dropdown>
                                                                                            ) : (
                                                                                                <>

                                                                                                </>
                                                                                            )} */}
                                                                                        </div>
                                                                                    </span>
                                                                                </div>
                                                                            </div>
                                                                        </div>
                                                                    )
                                                                )}
                                                        </div>
                                                    )
                                                )}
                                            </div>
                                        )}
                                    </div>
                                </div>

                                <TextBox
                                    handleChange={renderChange}
                                    sendTo={12}
                                />
                            </div>
                        {/* ) : ( */}
                            {/* <div className="bg-[#EEEEEE] rounded-t-lg w-full h-full   p-2">
                                <div className="flex items-center justify-center h-full">
                                    <div className="text-4xl text-gray-400 ">
                                        Start your conversation Here ....
                                    </div>
                                </div>
                            </div>
                        )} */}
                    </div>
                </div>
                {/* profile details */}
                {isOpenProfile && (
                    <Drawer
                        theme={customDrawer}
                        open={isOpenProfile}
                        onClose={handleCloseProfile}
                        position="right"
                        className="w-full lg:w-5/6 md:w-4/5"
                    >
                        <Drawer.Header
                            titleIcon={RiAccountBoxLine}
                            title="Profile Details"
                        />
                        <Drawer.Items>
                            <Profile></Profile>
                        </Drawer.Items>
                    </Drawer>
                )}
            </div>
            <Drawer
                open={isListOpen}
                onClose={() => setIsListOpen(!isListOpen)}
            >
                <Drawer.Items>
                    <PeopleList
                        SelectedId={setSelectedUser}
                        currentObject={setCurrent}
                    />
                </Drawer.Items>
            </Drawer>
        </>
    );
}

export default Index;
