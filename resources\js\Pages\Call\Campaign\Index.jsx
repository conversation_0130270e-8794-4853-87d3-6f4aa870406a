import Main from "@/Layouts/Main";
import React, { useState } from "react";
import TabBar from "../TabBar";
import {
    Badge,
    Button,
    ButtonGroup,
    Checkbox,
    Drawer,
    Progress,
    Table,
} from "flowbite-react";
import {
    button_custom,
    customDrawer,
    table_custom,
} from "../../Helpers/DesignHelper";
import { TbColumns3 } from "react-icons/tb";
import { IoMdAdd } from "react-icons/io";
import {
    MdDeleteOutline,
    MdFilterList,
    MdOutlineCampaign,
    MdOutlineIndeterminateCheckBox,
} from "react-icons/md";
import { Link } from "@inertiajs/react";
import { PiCaretUpDownBold } from "react-icons/pi";
import { IoPauseOutline } from "react-icons/io5";
import { FiPlay } from "react-icons/fi";
import View from "../../WhatsApp/Campaign/View";
import Details from "./Details";
import { showAsset } from "@/Pages/Helpers/Helper";

export default function Campaign() {
    const [isAddOpen, setIsAddOpen] = useState(false);
    // ----------------Edit --------------------
    const [isDetailsOpen, setIsDetailsOpen] = useState(false);
    const [isOpenDetail, setIsOpenDetail] = useState(false);

    return (
        <Main>
            <div className="p-2 overflow-hidden">
                <TabBar />
                <div className="px-2 pb-2 rounded-lg">
                    <div className="h-fit bg-white rounded border overflow-auto w-full mt-2.5">
                        <div className="flex items-center justify-between ps-2">
                            <ButtonGroup className="h-fit">
                                <Button
                                    className=""
                                    size="xs"
                                    color="gray"
                                    theme={button_custom}
                                // onClick={deleteRecords}
                                >
                                    <div className="flex items-center gap-1">
                                        <MdDeleteOutline className="text-slate-500" />
                                        <span className="text-xs">Delete</span>
                                    </div>
                                </Button>
                                <Button
                                    theme={button_custom}
                                    size="xs"
                                    color="gray"
                                    id="dropdownInformationButton"
                                    data-dropdown-toggle="dropdownNotification"
                                    type="button"
                                >
                                    <div className="flex items-center gap-1 text-xs">
                                        <TbColumns3 className="text-slate-500 ms-1" />
                                        <span className="text-xs">Columns</span>
                                    </div>
                                </Button>
                            </ButtonGroup>
                            <div className="flex items-center gap-2 px-2">
                                <div className="flex items-center gap-1">
                                    <img src={showAsset(
                                        "/assets/img/flag_india.png")} alt="" />
                                    <span className="text-sm">+91 8561258952</span>
                                </div>
                                <div className="flex flex-col border-l ps-3 py-1">
                                    <span className="text-xs">Aval. Balance</span>
                                    <span className="text-sm">₹5,000</span>
                                </div>
                                <Button
                                    // className="pe-1"
                                    as={Link}
                                    size="xs"
                                    color="gray"
                                    theme={button_custom}

                                    href={route("call.campaign.addcampaign")}
                                // onClick={() => setIsAddOpen(true)}
                                >
                                    <div className="flex items-center gap-1 text-xs">
                                        <IoMdAdd className="text-sm text-slate-500" />
                                        <span>Add</span>
                                    </div>
                                </Button>

                                {/* Dropdown menu */}
                                <div
                                    id="dropdownNotification"
                                    className="z-20 hidden w-full max-w-sm bg-white border divide-y divide-gray-100 rounded-lg shadow-lg dark:bg-gray-800 dark:divide-gray-700"
                                    aria-labelledby="dropdownNotificationButton"
                                >
                                    <div className="block px-4 py-2 font-medium text-center text-gray-700 rounded-t-lg bg-gray-50 dark:bg-gray-800 dark:text-white">
                                        <div className="flex justify-between">
                                            <div className="">
                                                <div className="flex items-center gap-1 p-1 text-gray-400 group">
                                                    <svg
                                                        xmlns="http://www.w3.org/2000/svg"
                                                        className=" fill-slate-400"
                                                        height="24px"
                                                        viewBox="0 0 24 24"
                                                        width="24px"
                                                    >
                                                        <rect
                                                            fill="none"
                                                            height="24"
                                                            width="24"
                                                        />
                                                        <path d="M20,4H4C2.9,4,2,4.9,2,6v12c0,1.1,0.9,2,2,2h16c1.1,0,2-0.9,2-2V6C22,4.9,21.1,4,20,4z M8,18H4V6h4V18z M14,18h-4V6h4V18z M20,18h-4V6h4V18z" />
                                                    </svg>
                                                    Column
                                                </div>
                                            </div>
                                            <div className="">
                                                <button className="flex p-1 text-blue-600">
                                                    <svg
                                                        xmlns="http://www.w3.org/2000/svg"
                                                        height="24px"
                                                        viewBox="0 0 24 24"
                                                        width="24px"
                                                        className="fill-blue-600"
                                                    >
                                                        <g>
                                                            <path
                                                                d="M0,0h24v24H0V0z"
                                                                fill="none"
                                                            />
                                                        </g>
                                                        <g>
                                                            <g>
                                                                <path d="M6,13c0-1.65,0.67-3.15,1.76-4.24L6.34,7.34C4.9,8.79,4,10.79,4,13c0,4.08,3.05,7.44,7,7.93v-2.02 C8.17,18.43,6,15.97,6,13z M20,13c0-4.42-3.58-8-8-8c-0.06,0-0.12,0.01-0.18,0.01l1.09-1.09L11.5,2.5L8,6l3.5,3.5l1.41-1.41 l-1.08-1.08C11.89,7.01,11.95,7,12,7c3.31,0,6,2.69,6,6c0,2.97-2.17,5.43-5,5.91v2.02C16.95,20.44,20,17.08,20,13z" />
                                                            </g>
                                                        </g>
                                                    </svg>
                                                    Reset
                                                </button>
                                            </div>
                                        </div>
                                    </div>
                                    <div className="divide-y divide-gray-100 dark:divide-gray-700">
                                        <div className="flex flex-col">
                                            <div className="relative overflow-x-auto shadow-md sm:rounded-lg ">
                                                <table className="w-full text-sm text-left text-gray-500 rtl:text-right dark:text-gray-400">
                                                    <thead className="text-xs text-gray-700 uppercase dark:text-gray-400">
                                                        <tr>
                                                            <th className="p-2 text-center bg-gray-50 dark:bg-gray-800 w-14 ">
                                                                List
                                                            </th>
                                                            <th className="p-2 text-center bg-gray-50 dark:bg-gray-800 w-14">
                                                                Details
                                                            </th>
                                                            <th className="p-2"></th>
                                                        </tr>
                                                    </thead>
                                                    <tbody className="overflow-auto ">
                                                        <tr className="border-b border-gray-200 dark:border-gray-700">
                                                            <td className="p-2 font-medium text-center text-gray-900 max-w-max whitespace-nowrap bg-gray-50 dark:text-white dark:bg-gray-800">
                                                                <Checkbox></Checkbox>
                                                            </td>
                                                            <td className="p-2 font-medium text-center text-gray-900 whitespace-nowrap bg-gray-50 dark:text-white dark:bg-gray-800">
                                                                <Checkbox></Checkbox>
                                                            </td>
                                                            <td className="px-6 py-4 max-w-max">
                                                                Name
                                                            </td>
                                                        </tr>
                                                        <tr className="border-b border-gray-200 dark:border-gray-700">
                                                            <td className="p-2 font-medium text-center text-gray-900 max-w-max whitespace-nowrap bg-gray-50 dark:text-white dark:bg-gray-800">
                                                                <Checkbox></Checkbox>
                                                            </td>
                                                            <td className="p-2 font-medium text-center text-gray-900 whitespace-nowrap bg-gray-50 dark:text-white dark:bg-gray-800">
                                                                <Checkbox></Checkbox>
                                                            </td>
                                                            <td className="px-6 py-4 max-w-max">
                                                                Name
                                                            </td>
                                                        </tr>
                                                        <tr className="border-b border-gray-200 dark:border-gray-700">
                                                            <td className="p-2 font-medium text-center text-gray-900 max-w-max whitespace-nowrap bg-gray-50 dark:text-white dark:bg-gray-800">
                                                                <Checkbox></Checkbox>
                                                            </td>
                                                            <td className="p-2 font-medium text-center text-gray-900 whitespace-nowrap bg-gray-50 dark:text-white dark:bg-gray-800">
                                                                <Checkbox></Checkbox>
                                                            </td>
                                                            <td className="px-6 py-4 max-w-max">
                                                                Name
                                                            </td>
                                                        </tr>
                                                        <tr className="border-b border-gray-200 dark:border-gray-700">
                                                            <td className="p-2 font-medium text-center text-gray-900 max-w-max whitespace-nowrap bg-gray-50 dark:text-white dark:bg-gray-800">
                                                                <Checkbox></Checkbox>
                                                            </td>
                                                            <td className="p-2 font-medium text-center text-gray-900 whitespace-nowrap bg-gray-50 dark:text-white dark:bg-gray-800">
                                                                <Checkbox></Checkbox>
                                                            </td>
                                                            <td className="px-6 py-4 max-w-max">
                                                                Name
                                                            </td>
                                                        </tr>
                                                    </tbody>
                                                </table>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div className="h-full">
                            {/* documents table */}
                            <div className="overflow-x-auto bg-white border rounded-lg text-nowrap">
                                <Table hoverable theme={table_custom}>
                                    <Table.Head className="bg-slate-100">
                                        <Table.HeadCell>
                                            <Checkbox color={"blue"} />
                                        </Table.HeadCell>

                                        <Table.HeadCell>
                                            <Link
                                            // href={route(
                                            //     "whatsapp.campaign.index",
                                            //     {
                                            //         column: "startTime",
                                            //         sort:
                                            //             getData.sort ==
                                            //             "asc"
                                            //                 ? "desc"
                                            //                 : "asc",
                                            //         perPage:
                                            //             getData.perPage,
                                            //     }
                                            // )}
                                            >
                                                <div className="flex items-center justify-between gap-2">
                                                    <h3>Start Time      </h3>
                                                    <PiCaretUpDownBold
                                                    // className={
                                                    //     (getData.column ==
                                                    //     "startTime"
                                                    //         ? "  text-gray-900 dark:text-gray-200 "
                                                    //         : "text-gray-600 dark:text-gray-400 ") +
                                                    //     "  cursor-pointer "
                                                    // }
                                                    />
                                                </div>
                                            </Link>
                                        </Table.HeadCell>

                                        <Table.HeadCell>
                                            <Link
                                            // href={route(
                                            //     "whatsapp.campaign.index",
                                            //     {
                                            //         column: "name",
                                            //         sort:
                                            //             getData.sort ==
                                            //             "asc"
                                            //                 ? "desc"
                                            //                 : "asc",
                                            //         perPage:
                                            //             getData.perPage,
                                            //     }
                                            // )}
                                            >
                                                <div className="flex items-center justify-between gap-2">
                                                    <h3>Campaign Name</h3>
                                                    <PiCaretUpDownBold
                                                    // className={
                                                    //     (getData.column ==
                                                    //     "name"
                                                    //         ? "  text-gray-900 dark:text-gray-200 "
                                                    //         : "text-gray-600 dark:text-gray-400 ") +
                                                    //     "  cursor-pointer "
                                                    // }
                                                    />
                                                </div>
                                            </Link>
                                        </Table.HeadCell>
                                        <Table.HeadCell>
                                            Progress
                                        </Table.HeadCell>

                                        <Table.HeadCell>
                                            <Link
                                            // href={route(
                                            //     "whatsapp.campaign.index",
                                            //     {
                                            //         column: "status",
                                            //         sort:
                                            //             getData.sort ==
                                            //             "asc"
                                            //                 ? "desc"
                                            //                 : "asc",
                                            //         perPage:
                                            //             getData.perPage,
                                            //     }
                                            // )}
                                            >
                                                <div className="flex items-center justify-between gap-2">
                                                    <h3>Status</h3>
                                                    <PiCaretUpDownBold
                                                    // className={
                                                    //     (getData.column ==
                                                    //     "status"
                                                    //         ? "  text-gray-900 dark:text-gray-200 "
                                                    //         : "text-gray-600 dark:text-gray-400 ") +
                                                    //     "  cursor-pointer "
                                                    // }
                                                    />
                                                </div>
                                            </Link>
                                        </Table.HeadCell>

                                        <Table.HeadCell>
                                            <Link
                                            // href={route(
                                            //     "whatsapp.campaign.index",
                                            //     {
                                            //         column: "sleepAfterMsgs",
                                            //         sort:
                                            //             getData.sort ==
                                            //             "asc"
                                            //                 ? "desc"
                                            //                 : "asc",
                                            //         perPage:
                                            //             getData.perPage,
                                            //     }
                                            // )}
                                            >
                                                <div className="flex items-center justify-between gap-2">
                                                    <h3>Sleep After</h3>
                                                    <PiCaretUpDownBold
                                                    // className={
                                                    //     (getData.column ==
                                                    //     "sleepAfterMsgs"
                                                    //         ? "  text-gray-900 dark:text-gray-200 "
                                                    //         : "text-gray-600 dark:text-gray-400 ") +
                                                    //     "  cursor-pointer "
                                                    // }
                                                    />
                                                </div>
                                            </Link>
                                        </Table.HeadCell>
                                        <Table.HeadCell>
                                            <Link
                                            // href={route(
                                            //     "whatsapp.campaign.index",
                                            //     {
                                            //         column: "sleepForSeconds",
                                            //         sort:
                                            //             getData.sort ==
                                            //             "asc"
                                            //                 ? "desc"
                                            //                 : "asc",
                                            //         perPage:
                                            //             getData.perPage,
                                            //     }
                                            // )}
                                            >
                                                <div className="flex items-center justify-between gap-2">
                                                    <h3>Sleep For</h3>
                                                    <PiCaretUpDownBold
                                                    // className={
                                                    //     (getData.column ==
                                                    //     "sleepForSeconds"
                                                    //         ? "  text-gray-900 dark:text-gray-200 "
                                                    //         : "text-gray-600 dark:text-gray-400 ") +
                                                    //     "  cursor-pointer "
                                                    // }
                                                    />
                                                </div>
                                            </Link>
                                        </Table.HeadCell>
                                        <Table.HeadCell>
                                            <div className="flex items-center justify-between gap-2">
                                                <h3>User</h3>
                                                <MdFilterList />
                                            </div>
                                        </Table.HeadCell>
                                        <Table.HeadCell>
                                            <h3>Actions</h3>
                                        </Table.HeadCell>
                                    </Table.Head>
                                    <Table.Body className="divide-y">
                                        <Table.Row
                                            className="items-center bg-white dark:border-gray-700 dark:bg-gray-800"
                                        // key={"campaign" + k}
                                        >
                                            <Table.Cell>
                                                <Checkbox color={"blue"} />
                                            </Table.Cell>
                                            <Table.Cell>
                                                11/1/2024 5:21
                                                {/* {convertDateFormat(
                                                    campaign.startTime
                                                )} */}
                                            </Table.Cell>

                                            <Table.Cell
                                                onClick={() => {
                                                    setIsOpenDetail(true);
                                                    setCampaignID({
                                                        id: "73",
                                                        name: "Dana Gates",
                                                        status: "Terminated",
                                                    });
                                                }}
                                                className="cursor-pointer "
                                            >
                                                Dana Gates
                                                {/* {campaign.name} */}
                                            </Table.Cell>
                                            <Table.Cell>
                                                <div className="flex flex-col gap-1 py-1">
                                                    <Progress
                                                        color="blue"
                                                    // progress={
                                                    //     (campaign.completedContacts /
                                                    //         campaign.totalContacts) *
                                                    //     100
                                                    // }
                                                    />
                                                    <span className="">
                                                        {/* {
                                                            campaign.completedContacts
                                                        }
                                                        &nbsp;Out of&nbsp;
                                                        {campaign.totalContacts} */}
                                                        0 Out of 450501
                                                        Completed
                                                    </span>
                                                </div>
                                            </Table.Cell>
                                            <Table.Cell>
                                                <div className="flex">
                                                    <Badge
                                                        className="cursor-default"
                                                    // color={
                                                    //     statusCodes[6]
                                                    //         .bg
                                                    // }
                                                    >
                                                        Terminated
                                                        {
                                                            // statusCodes[6]
                                                            //     .text
                                                        }
                                                    </Badge>
                                                </div>
                                            </Table.Cell>

                                            <Table.Cell>
                                                41
                                                {/* {campaign.sleepAfterMsgs} */}
                                            </Table.Cell>
                                            <Table.Cell>
                                                26
                                                {/* {campaign.sleepForSeconds} */}
                                            </Table.Cell>
                                            <Table.Cell>
                                                flslsd
                                                {/* {campaign.user.username} */}
                                            </Table.Cell>
                                            <Table.Cell>
                                                <div className="relative flex items-center gap-2">
                                                    <Button
                                                        theme={button_custom}
                                                        color="success"
                                                        size="xs"
                                                    >
                                                        <IoPauseOutline className="text-sm" />
                                                        <span className="text-xs ms-1">
                                                            Pause
                                                        </span>
                                                    </Button>

                                                    <Button
                                                        theme={button_custom}
                                                        color="orange"
                                                        size="xs"
                                                    >
                                                        <FiPlay className="text-sm" />
                                                        <span className="text-xs ms-1">
                                                            Play
                                                        </span>
                                                    </Button>
                                                    <Button
                                                        theme={button_custom}
                                                        color="failure"
                                                        size="xs"
                                                    >
                                                        <MdOutlineIndeterminateCheckBox className="text-sm" />
                                                        <span className="text-xs ms-1">
                                                            Stop
                                                        </span>
                                                    </Button>
                                                </div>
                                            </Table.Cell>
                                        </Table.Row>
                                    </Table.Body>
                                </Table>
                            </div>
                        </div>

                        <div className="bottom-0 w-full p-3 bg-white border rounded-b-lg float-end">
                            <div className="flex flex-wrap justify-between gap-2 lg:gap-0 md:gap-0">
                                <div className="flex items-center gap-3">
                                    <div className="text-sm text-gray-400 ">
                                        Showing 1 to 25 of 62 entries
                                    </div>
                                    <div>
                                        <select
                                            id="countries"
                                            className="block p-1 text-xs text-gray-900 border border-gray-300 rounded bg-gray-50 focus:ring-blue-500 focus:border-blue-500 ps-2 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-blue-500 dark:focus:border-blue-500"
                                        >
                                            <option
                                                value={10}
                                                defaultValue={10}
                                            >
                                                10
                                            </option>
                                            <option value={15}>15</option>
                                            <option value={20}>20</option>
                                            <option value={25}>25</option>
                                            <option value={30}>30</option>
                                        </select>
                                    </div>
                                </div>

                            </div>
                        </div>
                    </div>
                </div>
            </div>
            {isAddOpen ? (
                <Drawer
                    theme={customDrawer}
                    className="w-full lg:w-4/6 md:w-4/5"
                    open={isAddOpen}
                    onClose={() => setIsAddOpen(false)}
                    position="right"
                >
                    <DrawerHeader
                        titleIcon={MdOutlineCampaign}
                        title={<span>Add Campaign</span>}
                    />
                    <DrawerItems className="py-2">
                        <Add onClose={() => setIsAddOpen(false)}></Add>
                    </DrawerItems>
                </Drawer>
            ) : (
                <></>
            )}
            {isDetailsOpen ? (
                <Drawer
                    theme={customDrawer}
                    className="w-full lg:w-4/6 md:w-4/5"
                    open={isDetailsOpen}
                    onClose={() => setIsDetailsOpen(false)}
                    position="right"
                >
                    <DrawerHeader
                        titleIcon={MdOutlineCampaign}
                        title={<span>Details ({"Dana Gates"})</span>}
                    />
                    <DrawerItems className="py-2">
                        <View campaign={"73"}></View>
                    </DrawerItems>
                </Drawer>
            ) : (
                <></>
            )}
            {isOpenDetail ? (
                <Drawer
                    theme={customDrawer}
                    className="w-full lg:w-5/6 md:w-4/5"
                    open={isOpenDetail}
                    onClose={() => setIsOpenDetail(false)}
                    position="right"
                >
                    <DrawerHeader
                        titleIcon={MdOutlineCampaign}
                        title={
                            <div className="flex items-center gap-2">
                                Campaign Details ({"Dana Gates"})
                                <Badge color={"blue"}>Terminated</Badge>
                            </div>
                        }
                    />
                    <DrawerItems className="py-2">
                        {/* <View ></View> */}
                        <Details campaign={"73"}></Details>
                    </DrawerItems>
                </Drawer>
            ) : (
                <></>
            )}
        </Main>
    );
}
