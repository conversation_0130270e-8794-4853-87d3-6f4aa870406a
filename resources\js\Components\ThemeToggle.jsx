import React, { memo } from 'react';
import { Button } from 'flowbite-react';
import { HiSun, HiMoon } from 'react-icons/hi2';
import { useTheme } from '@/Contexts/ThemeContext';

const ThemeToggle = memo(function ThemeToggle({ className = "", size = "xs" }) {
    const { isDarkMode, toggleTheme } = useTheme();

    const buttonTheme = {
        color: {
            themeToggle: `
                border border-gray-300 bg-white text-gray-700 
                hover:bg-gray-50 focus:ring-2 focus:ring-gray-200 
                dark:border-gray-600 dark:bg-gray-800 dark:text-gray-300 
                dark:hover:bg-gray-700 dark:focus:ring-gray-600
                transition-all duration-200 ease-in-out
            `
        },
        pill: {
            off: "rounded-lg",
            on: "rounded-full",
        },
        size: {
            xs: "p-2 text-lg",
            sm: "p-2.5 text-xl",
            md: "p-3 text-xl",
            lg: "p-3.5 text-2xl",
        },
    };

    return (
        <Button
            color="themeToggle"
            size={size}
            theme={buttonTheme}
            onClick={toggleTheme}
            className={`relative transition-all duration-200 ${className}`}
            aria-label={isDarkMode ? 'Switch to light mode' : 'Switch to dark mode'}
            title={isDarkMode ? 'Switch to light mode' : 'Switch to dark mode'}
        >
            <div className="relative">
                {/* Sun icon for light mode */}
                <HiSun 
                    className={`
                        absolute inset-0 transition-all duration-300 transform
                        ${isDarkMode 
                            ? 'opacity-0 rotate-90 scale-0' 
                            : 'opacity-100 rotate-0 scale-100'
                        }
                    `}
                />
                {/* Moon icon for dark mode */}
                <HiMoon 
                    className={`
                        transition-all duration-300 transform
                        ${isDarkMode 
                            ? 'opacity-100 rotate-0 scale-100' 
                            : 'opacity-0 -rotate-90 scale-0'
                        }
                    `}
                />
            </div>
        </Button>
    );
});

export default ThemeToggle;
