import { <PERSON><PERSON>, <PERSON>ton<PERSON><PERSON>, Checkbox, Drawer, DrawerHeader, DrawerItems, Table } from "flowbite-react";
import { useState } from "react";
import { IoMdAdd, IoMdRepeat } from "react-icons/io";
import {
    MdChecklistRtl,
    MdDeleteOutline,
    MdOutlineModeEdit,
} from "react-icons/md";
import { TbColumns3 } from "react-icons/tb";

import { TruncatedCell } from "@/Components/ExpandMsg";
import NoRecord from "@/Components/HelperComponents/NoRecord";
import Paginate from "@/Components/HelperComponents/Paginate";
import PerPageDropdown from "@/Components/HelperComponents/PerPageDropdown";
import WaMain from "@/Pages/WhatsApp/WaMain";
import { Head, Link, useForm } from "@inertiajs/react";
import $ from "jquery";
import {
    button_custom,
    customDrawer,
    table_custom,
} from "../../Helpers/DesignHelper";
import Add from "./Add";
import AddKeyword from "./AddKeyword";
import Edit from "./Edit";
import View from "./View";

export default function Index({ autoReplyData }) {
    const [isCheckAll, setIsCheckAll] = useState(false);
    const [isAddOpen, setIsAddOpen] = useState(false);
    const [isViewOpen, setIsViewOpen] = useState(false);
    const [isEditAutoReplyOpen, setIsEditAutoReply] = useState(false);
    const [isAddKeywordOpen, setIsAddKeyword] = useState(false);
    const [autoReplyEditData, setautoReplyEditData] = useState({});
    const [checkValue, setCheckValue] = useState([]);
    const {
        data,
        setData,
        delete: destroy,
        processing,
        errors,
    } = useForm({
        id: [],
    });
    // useEffect(() => {
    //     let idsAry = [];
    //     if (isCheckAll) {
    //         $(".rowCheckBox").prop("checked", true);
    //         let ids = $(".rowCheckBox");
    //         ids.each((index, element) => {
    //             idsAry.push(element.id);
    //         });
    //     } else {
    //         $(".rowCheckBox").prop("checked", false);
    //         idsAry = [];
    //     }
    //     setCheckValue(idsAry);
    // }, [isCheckAll]);

    // ----------------Edit --------------------
    const [isOpen, setIsOpen] = useState(false);

    function getCheckedIds(e) {
        let previousIds = checkValue;
        if (e.target.checked) {
            if (!previousIds.includes(e.target.id)) {
                previousIds.push(e.target.id);
                setCheckValue(previousIds);
            }
        } else {
            const newIds = previousIds.filter((item) => item !== e.target.id);
            setCheckValue(newIds);
        }
    }

    // executes when user click table header checkbox
    function headerCheckBoxChecked(e) {
        let previousIds = [];
        if (e.target.checked && e.target.id == 0 && autoReplyData.autoReplyData.data.length > 0) {
            autoReplyData.autoReplyData.data.map((auto, key) => {
                if (!previousIds.includes(auto.id)) {
                    previousIds.push(auto.id);
                    setCheckValue(previousIds);
                }
            });
            setIsCheckAll(true);
            $(".rowCheckBox").prop("checked", true);
        } else {
            setCheckValue(previousIds);
            setIsCheckAll(false);
            $(".rowCheckBox").prop("checked", false);
        }
    }

    // handle checkBox Check or uncheck
    function checkAddcheckBoxChecked() {
        let allCheckBoxes = $(".rowCheckBox");
        let checkedCheckBoxes = $(".rowCheckBox:checked");

        if (allCheckBoxes.length == checkedCheckBoxes.length) {
            setIsCheckAll(true);
        } else {
            setIsCheckAll(false);
        }
    }


    function deleteRecords() {
        if (checkValue.length > 0) {
            setData({ id: checkValue });
            destroy(
                route("whatsapp.bot.destroy", { bot: checkValue })
            );
            setCheckValue([]);
            setIsCheckAll(false);
        }
    }

    const handleClose = () => setIsOpen(false);

    const customDrawerEdit = {
        root: {
            base: "fixed z-50 overflow-y-auto bg-white p-4 transition-transform dark:bg-gray-800 bg-slate-100 ",
            backdrop: "fixed inset-0 z-40 bg-gray-900/50 dark:bg-gray-900/80",
            edge: "bottom-16",
            position: {
                top: {
                    on: "left-0 right-0 top-0 w-full transform-none",
                    off: "left-0 right-0 top-0 w-full -translate-y-full",
                },
                right: {
                    on: "right-0 top-0 h-screen w-80 transform-none",
                    off: "right-0 top-0 h-screen w-80 translate-x-full",
                },
                bottom: {
                    on: "bottom-0 left-0 right-0 w-full transform-none",
                    off: "bottom-0 left-0 right-0 w-full translate-y-full",
                },
                left: {
                    on: "left-0 top-0 h-screen w-80 transform-none",
                    off: "left-0 top-0 h-screen w-80 -translate-x-full",
                },
            },
        },
        header: {
            inner: {
                closeButton:
                    "absolute end-2.5 top-2.5 flex h-8 w-8 items-center justify-center rounded-lg bg-transparent text-sm text-gray-400 hover:bg-gray-200 hover:text-gray-900 dark:hover:bg-gray-600 dark:hover:text-white",
                closeIcon: "h-6 w-6",
                titleIcon: "me-2.5 h-6 w-6 text-slate-400",
                titleText:
                    "mb-4 inline-flex items-center text-xl font-semibold text-slate-800 dark:text-gray-400",
            },
            collapsed: {
                on: "hidden",
                off: "block",
            },
        },
        items: {
            base: "rounded-lg bg-white dark:!bg-gray-700",
        },
    };
    return (
        <WaMain>
            <Head title="Bots" />
            {/* <TabBar /> */}
            <div className="px-2 pb-2 rounded-lg">
                <div className="h-fit bg-white rounded border overflow-auto w-full mt-2.5">
                    <div className="flex justify-between p-2">
                        <div className="flex ">
                            <Button
                                className="border"
                                theme={button_custom}
                                size="xs"
                                color="gray"
                                id="dropdownInformationButton"
                                data-dropdown-toggle="dropdownNotification"
                                type="button"
                            >
                                <div className="flex items-center gap-1">
                                    <TbColumns3 className="text-xs text-slate-500" />
                                    <span className="text-xs">Columns</span>
                                </div>
                            </Button>
                        </div>
                        <div className="flex">
                            <Button
                                className="border pe-1"
                                size="xs"
                                color="gray"
                                theme={button_custom}
                                onClick={() => setIsAddOpen(true)}
                            >
                                <div className="flex items-center gap-1">
                                    <IoMdAdd className="text-slate-500" />
                                    <span className="text-xs">Add</span>
                                </div>
                            </Button>

                            {/* Dropdown menu */}
                            <div
                                id="dropdownNotification"
                                className="z-20 hidden w-full max-w-sm bg-white border divide-y divide-gray-100 rounded-lg shadow-lg dark:bg-gray-800 dark:divide-gray-700"
                                aria-labelledby="dropdownNotificationButton"
                            >
                                <div className="block px-4 py-2 font-medium text-center text-gray-700 rounded-t-lg bg-gray-50 dark:bg-gray-800 dark:text-white">
                                    <div className="flex justify-between">
                                        <div className="">
                                            <div className="flex items-center gap-1 p-1 text-gray-400 group">
                                                <svg
                                                    xmlns="http://www.w3.org/2000/svg"
                                                    className=" fill-slate-400"
                                                    height="24px"
                                                    viewBox="0 0 24 24"
                                                    width="24px"
                                                >
                                                    <rect
                                                        fill="none"
                                                        height="24"
                                                        width="24"
                                                    />
                                                    <path d="M20,4H4C2.9,4,2,4.9,2,6v12c0,1.1,0.9,2,2,2h16c1.1,0,2-0.9,2-2V6C22,4.9,21.1,4,20,4z M8,18H4V6h4V18z M14,18h-4V6h4V18z M20,18h-4V6h4V18z" />
                                                </svg>
                                                Column
                                            </div>
                                        </div>
                                        <div className="">
                                            <button className="flex p-1 text-blue-600">
                                                <svg
                                                    xmlns="http://www.w3.org/2000/svg"
                                                    height="24px"
                                                    viewBox="0 0 24 24"
                                                    width="24px"
                                                    className="fill-blue-600"
                                                >
                                                    <g>
                                                        <path
                                                            d="M0,0h24v24H0V0z"
                                                            fill="none"
                                                        />
                                                    </g>
                                                    <g>
                                                        <g>
                                                            <path d="M6,13c0-1.65,0.67-3.15,1.76-4.24L6.34,7.34C4.9,8.79,4,10.79,4,13c0,4.08,3.05,7.44,7,7.93v-2.02 C8.17,18.43,6,15.97,6,13z M20,13c0-4.42-3.58-8-8-8c-0.06,0-0.12,0.01-0.18,0.01l1.09-1.09L11.5,2.5L8,6l3.5,3.5l1.41-1.41 l-1.08-1.08C11.89,7.01,11.95,7,12,7c3.31,0,6,2.69,6,6c0,2.97-2.17,5.43-5,5.91v2.02C16.95,20.44,20,17.08,20,13z" />
                                                        </g>
                                                    </g>
                                                </svg>
                                                Reset
                                            </button>
                                        </div>
                                    </div>
                                </div>
                                <div className="divide-y divide-gray-100 dark:divide-gray-700">
                                    <div className="flex flex-col">
                                        <div className="relative overflow-x-auto shadow-md sm:rounded-lg ">
                                            <table className="w-full text-sm text-left text-gray-500 rtl:text-right dark:text-gray-400">
                                                <thead className="text-xs text-gray-700 uppercase dark:text-gray-400">
                                                    <tr>
                                                        <th className="p-2 text-center bg-gray-50 dark:bg-gray-800 w-14 ">
                                                            List
                                                        </th>
                                                        <th className="p-2 text-center bg-gray-50 dark:bg-gray-800 w-14">
                                                            Details
                                                        </th>
                                                        <th className="p-2"></th>
                                                    </tr>
                                                </thead>
                                                <tbody className="overflow-auto ">
                                                    <tr className="border-b border-gray-200 dark:border-gray-700">
                                                        <td className="p-2 font-medium text-center text-gray-900 max-w-max whitespace-nowrap bg-gray-50 dark:text-white dark:bg-gray-800">
                                                            <Checkbox></Checkbox>
                                                        </td>
                                                        <td className="p-2 font-medium text-center text-gray-900 whitespace-nowrap bg-gray-50 dark:text-white dark:bg-gray-800">
                                                            <Checkbox></Checkbox>
                                                        </td>
                                                        <td className="px-6 py-4 max-w-max">
                                                            Name
                                                        </td>
                                                    </tr>
                                                    <tr className="border-b border-gray-200 dark:border-gray-700">
                                                        <td className="p-2 font-medium text-center text-gray-900 max-w-max whitespace-nowrap bg-gray-50 dark:text-white dark:bg-gray-800">
                                                            <Checkbox></Checkbox>
                                                        </td>
                                                        <td className="p-2 font-medium text-center text-gray-900 whitespace-nowrap bg-gray-50 dark:text-white dark:bg-gray-800">
                                                            <Checkbox></Checkbox>
                                                        </td>
                                                        <td className="px-6 py-4 max-w-max">
                                                            Name
                                                        </td>
                                                    </tr>
                                                    <tr className="border-b border-gray-200 dark:border-gray-700">
                                                        <td className="p-2 font-medium text-center text-gray-900 max-w-max whitespace-nowrap bg-gray-50 dark:text-white dark:bg-gray-800">
                                                            <Checkbox></Checkbox>
                                                        </td>
                                                        <td className="p-2 font-medium text-center text-gray-900 whitespace-nowrap bg-gray-50 dark:text-white dark:bg-gray-800">
                                                            <Checkbox></Checkbox>
                                                        </td>
                                                        <td className="px-6 py-4 max-w-max">
                                                            Name
                                                        </td>
                                                    </tr>
                                                    <tr className="border-b border-gray-200 dark:border-gray-700">
                                                        <td className="p-2 font-medium text-center text-gray-900 max-w-max whitespace-nowrap bg-gray-50 dark:text-white dark:bg-gray-800">
                                                            <Checkbox></Checkbox>
                                                        </td>
                                                        <td className="p-2 font-medium text-center text-gray-900 whitespace-nowrap bg-gray-50 dark:text-white dark:bg-gray-800">
                                                            <Checkbox></Checkbox>
                                                        </td>
                                                        <td className="px-6 py-4 max-w-max">
                                                            Name
                                                        </td>
                                                    </tr>
                                                </tbody>
                                            </table>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div className="overflow-auto bg-white h-ull">
                        {/* documents table */}
                        <div className="overflow-x-auto border rounded-lg">
                            <Table hoverable theme={table_custom}>
                                <Table.Head className=" bg-slate-100">
                                    <Table.HeadCell className="w-10">
                                        <Checkbox
                                            color="blue"
                                            checked={isCheckAll}
                                            id={0}
                                            onChange={(e) =>
                                                headerCheckBoxChecked(e)
                                            }
                                        />
                                    </Table.HeadCell>
                                    <Table.HeadCell>
                                        <Link
                                            href={route(
                                                "whatsapp.bot.index",
                                                {
                                                    column: "id",
                                                    sort:
                                                        autoReplyData.getData
                                                            .sort == "asc"
                                                            ? "desc"
                                                            : "asc",
                                                }
                                            )}
                                        >
                                            <div className="flex items-center justify-between gap-2">
                                                <span>Id</span>
                                                <svg
                                                    xmlns="http://www.w3.org/2000/svg"
                                                    height="16px"
                                                    viewBox="0 0 24 24"
                                                    width="16px"
                                                    className="fill-gray-600"
                                                >
                                                    <path
                                                        d="M0 0h24v24H0V0z"
                                                        fill="none"
                                                    />
                                                    <path d="M12 5.83L15.17 9l1.41-1.41L12 3 7.41 7.59 8.83 9 12 5.83zm0 12.34L8.83 15l-1.41 1.41L12 21l4.59-4.59L15.17 15 12 18.17z" />
                                                </svg>
                                            </div>
                                        </Link>
                                    </Table.HeadCell>

                                    <Table.HeadCell>
                                        <Link
                                            href={route(
                                                "whatsapp.bot.index",
                                                {
                                                    column: "user_id",
                                                    sort:
                                                        autoReplyData.getData
                                                            .sort == "asc"
                                                            ? "desc"
                                                            : "asc",
                                                }
                                            )}
                                        >
                                            <div className="flex items-center justify-between gap-1">
                                                <span>User</span>
                                                <svg
                                                    xmlns="http://www.w3.org/2000/svg"
                                                    height="16px"
                                                    viewBox="0 0 24 24"
                                                    width="16px"
                                                    className="fill-gray-600"
                                                >
                                                    <path
                                                        d="M0 0h24v24H0V0z"
                                                        fill="none"
                                                    />
                                                    <path d="M12 5.83L15.17 9l1.41-1.41L12 3 7.41 7.59 8.83 9 12 5.83zm0 12.34L8.83 15l-1.41 1.41L12 21l4.59-4.59L15.17 15 12 18.17z" />
                                                </svg>
                                            </div>
                                        </Link>
                                    </Table.HeadCell>

                                    <Table.HeadCell>
                                        <Link
                                            href={route(
                                                "whatsapp.bot.index",
                                                {
                                                    column: "name",
                                                    sort:
                                                        autoReplyData.getData
                                                            .sort == "asc"
                                                            ? "desc"
                                                            : "asc",
                                                }
                                            )}
                                        >
                                            <div className="flex items-center justify-between">
                                                <span>Name</span>
                                                <svg
                                                    xmlns="http://www.w3.org/2000/svg"
                                                    height="16px"
                                                    viewBox="0 0 24 24"
                                                    width="16px"
                                                    className="fill-gray-600"
                                                >
                                                    <path
                                                        d="M0 0h24v24H0V0z"
                                                        fill="none"
                                                    />
                                                    <path d="M12 5.83L15.17 9l1.41-1.41L12 3 7.41 7.59 8.83 9 12 5.83zm0 12.34L8.83 15l-1.41 1.41L12 21l4.59-4.59L15.17 15 12 18.17z" />
                                                </svg>
                                            </div>
                                        </Link>
                                    </Table.HeadCell>
                                    <Table.HeadCell className="w-80">
                                        <Link
                                            href={route(
                                                "whatsapp.bot.index",
                                                {
                                                    column: "isWelcomeMsg",
                                                    sort:
                                                        autoReplyData.getData
                                                            .sort == "asc"
                                                            ? "desc"
                                                            : "asc",
                                                }
                                            )}
                                        >
                                            <div className="flex items-center justify-between gap-1">
                                                <span>
                                                    Welcome Msg / Wrong Keyword
                                                </span>
                                                <svg
                                                    xmlns="http://www.w3.org/2000/svg"
                                                    height="16px"
                                                    viewBox="0 0 24 24"
                                                    width="16px"
                                                    className="fill-gray-600"
                                                >
                                                    <path
                                                        d="M0 0h24v24H0V0z"
                                                        fill="none"
                                                    />
                                                    <path d="M12 5.83L15.17 9l1.41-1.41L12 3 7.41 7.59 8.83 9 12 5.83zm0 12.34L8.83 15l-1.41 1.41L12 21l4.59-4.59L15.17 15 12 18.17z" />
                                                </svg>
                                            </div>
                                        </Link>
                                    </Table.HeadCell>
                                    {/* <Table.HeadCell>
                                        <div className="flex items-center justify-between gap-1">
                                            <span>Keywords</span>
                                            <svg
                                                xmlns="http://www.w3.org/2000/svg"
                                                height="16px"
                                                viewBox="0 0 24 24"
                                                width="16px"
                                                className="fill-gray-600"
                                            >
                                                <path
                                                    d="M0 0h24v24H0V0z"
                                                    fill="none"
                                                />
                                                <path d="M12 5.83L15.17 9l1.41-1.41L12 3 7.41 7.59 8.83 9 12 5.83zm0 12.34L8.83 15l-1.41 1.41L12 21l4.59-4.59L15.17 15 12 18.17z" />
                                            </svg>
                                        </div>
                                    </Table.HeadCell> */}
                                    <Table.HeadCell>Actions</Table.HeadCell>
                                </Table.Head>

                                <Table.Body className="divide-y">
                                    {autoReplyData.autoReplyData.data.length >
                                        0 ? (
                                        autoReplyData.autoReplyData.data.map(
                                            (auto, key) => {
                                                return (
                                                    <Table.Row
                                                        className="bg-white dark:border-gray-700 dark:bg-gray-800"
                                                        key={key}
                                                    >
                                                        <Table.Cell>
                                                            <Checkbox
                                                                color={"blue"}
                                                                className="rowCheckBox"
                                                                id={auto.id}
                                                                onChange={(e) => {
                                                                    getCheckedIds(e);
                                                                    checkAddcheckBoxChecked();
                                                                }
                                                                }
                                                            />
                                                        </Table.Cell>

                                                        <Table.Cell className="font-medium text-gray-900 whitespace-nowrap dark:text-white">
                                                            {auto.id}
                                                        </Table.Cell>
                                                        <Table.Cell className="font-medium text-gray-900 whitespace-nowrap dark:text-white">
                                                            {auto.user.username}
                                                        </Table.Cell>

                                                        <Table.Cell>
                                                            {auto.name}
                                                        </Table.Cell>
                                                        <Table.Cell className="max-w-40">
                                                            <TruncatedCell
                                                                content={
                                                                    auto.msg ??
                                                                    "-"
                                                                }
                                                            />

                                                            {/* jsdkbsdkbsdkgkdbgksdbvksbhkbvhsdbvhdvhdskvkjsvjsdvhjsdvhjsvdhjvjdsvfsdvfvsdjfsdjfvsdvfvsdvfjdsvfdvfvsdfjvsdjfvsjdfvjsdvfffffffffffffffffffffffffffffdsjvffyafqwhasssssssvnf */}
                                                            {/* {auto.isWelcomeMsg ==
                                                            1
                                                                ? "Yes"
                                                                : "No"} */}
                                                        </Table.Cell>
                                                        {/* <Table.Cell>
                                                            {auto.rules.length}
                                                        </Table.Cell> */}
                                                        <Table.Cell>
                                                            <div className="flex items-center gap-3 pt-1">
                                                                <Button
                                                                    className=""
                                                                    theme={
                                                                        button_custom
                                                                    }
                                                                    color="success"
                                                                    size="xs"
                                                                    onClick={() => {
                                                                        setIsEditAutoReply(
                                                                            true
                                                                        );
                                                                        setautoReplyEditData(
                                                                            {
                                                                                id: auto.id,
                                                                                name: auto.name,
                                                                                message:
                                                                                    auto.msg,
                                                                                isWelcomeMsg:
                                                                                    auto.isWelcomeMsg,
                                                                                media: auto.wa_media,
                                                                            }
                                                                        );
                                                                    }}
                                                                >
                                                                    <MdOutlineModeEdit className="text-sm" />
                                                                    <span className="text-xs ms-1">
                                                                        Edit
                                                                    </span>
                                                                </Button>

                                                                {/* <Button
                                                                    theme={
                                                                        button_custom
                                                                    }
                                                                    color="gray"
                                                                    size="xs"
                                                                    onClick={() => {
                                                                        setIsViewOpen(
                                                                            true
                                                                        ),
                                                                            setautoReplyEditData(
                                                                                {
                                                                                    id: auto.id,
                                                                                    name: auto.name,
                                                                                    user: auto
                                                                                        .user
                                                                                        .username,
                                                                                    message:
                                                                                        auto.msg,
                                                                                    isWelcomeMsg:
                                                                                        auto.isWelcomeMsg,
                                                                                    total: auto
                                                                                        .rules
                                                                                        .length,
                                                                                    media: auto.wa_media,
                                                                                }
                                                                            );
                                                                    }}
                                                                >
                                                                    <IoEyeOutline className="text-sm" />
                                                                    <span className="text-xs ms-1">
                                                                        View
                                                                    </span>
                                                                </Button> */}

                                                                <div className="relative flex me-1.5">
                                                                    <ButtonGroup
                                                                        outline
                                                                    >
                                                                        <Button
                                                                            className="items-center"
                                                                            onClick={() => {
                                                                                setIsAddKeyword(
                                                                                    true
                                                                                );
                                                                                setautoReplyEditData(
                                                                                    {
                                                                                        id: auto.id,
                                                                                        name: auto.name,
                                                                                    }
                                                                                );
                                                                            }}
                                                                            theme={
                                                                                button_custom
                                                                            }
                                                                            size={
                                                                                "xs"
                                                                            }
                                                                            color="gray"
                                                                        >
                                                                            <IoMdAdd className="text-sm" />
                                                                        </Button>
                                                                        <Button
                                                                            className="items-center"
                                                                            as={
                                                                                Link
                                                                            }
                                                                            href={route(
                                                                                "whatsapp.bot.listKeyword",
                                                                                {
                                                                                    id: auto.id,
                                                                                }
                                                                            )}
                                                                            theme={
                                                                                button_custom
                                                                            }
                                                                            size={
                                                                                "xs"
                                                                            }
                                                                            color="blue"
                                                                        >
                                                                            <MdChecklistRtl className="text-sm ms-1" />
                                                                            <span className="text-xs ms-1">
                                                                                Keyword
                                                                            </span>
                                                                        </Button>
                                                                        {auto
                                                                            .rules
                                                                            .length >
                                                                            0 ? (
                                                                            <div className="absolute inline-flex items-center justify-center mt-0.5 w-4 h-4 min-w-max min-h-max text-xs font-bold text-white bg-red-600 rounded-full -top-2 -end-3 dark:border-gray-900">
                                                                                {
                                                                                    auto
                                                                                        .rules
                                                                                        .length
                                                                                }
                                                                            </div>
                                                                        ) : (
                                                                            <>

                                                                            </>
                                                                        )}
                                                                    </ButtonGroup>
                                                                    {/* <sup>
                                                                        <Badge theme={badge_custom} color={"failure"} size={"xs"} className="rounded-full">
                                                                            2
                                                                        </Badge>
                                                                    </sup> */}
                                                                </div>
                                                            </div>
                                                        </Table.Cell>
                                                    </Table.Row>
                                                );
                                            }
                                        )
                                    ) : (
                                        <Table.Row className="bg-white dark:border-gray-700 dark:bg-gray-800">
                                            <Table.Cell
                                                colSpan={11}
                                                className="text-center"
                                            >
                                                <NoRecord />
                                            </Table.Cell>
                                        </Table.Row>
                                    )}
                                </Table.Body>
                            </Table>
                        </div>
                    </div>

                    <div className="bottom-0 w-full p-3 bg-white rounded-lg float-end dark:border-gray-500 dark:rounded-lg dark:bg-gray-700">
                        <div className="flex flex-wrap justify-between gap-2 lg:justify-between lg:gap-0 md:gap-0">
                            <div className="flex items-center gap-4">
                                <Button
                                    className="px-2 py-1 border rounded-md"
                                    size="xs"
                                    color="gray"
                                    theme={button_custom}
                                    onClick={() =>
                                        window.confirm(
                                            "Do you really want to delete AutoReply ?"
                                        ) && deleteRecords()
                                    }
                                >
                                    <div className="flex items-center gap-1">
                                        <MdDeleteOutline className="text-slate-500" />
                                        <span className="text-xs">Delete</span>
                                    </div>
                                </Button>
                                <PerPageDropdown
                                    getDataFields={
                                        autoReplyData.getData ?? null
                                    }
                                    routeName={"whatsapp.bot.index"}
                                    data={autoReplyData.autoReplyData}
                                />
                            </div>

                            <Paginate tableData={autoReplyData.autoReplyData} />
                        </div>
                    </div>
                </div>
            </div>
            {/*--------Add AutoReply---------*/}
            {isAddOpen === true ? (
                <Drawer
                    theme={customDrawerEdit}
                    className="w-full lg:w-2/5 md:w-3/5"
                    open={isAddOpen}
                    onClose={() => setIsAddOpen(false)}
                    position="right"
                >
                    <DrawerHeader
                        titleIcon={IoMdRepeat}
                        title="Add Autoreply"
                    />
                    <DrawerItems className="py-2 bg-white rounded">
                        <Add onClose={() => setIsAddOpen(false)}></Add>
                    </DrawerItems>
                </Drawer>
            ) : (
                <></>
            )}

            {/*--------Add Keyword---------*/}
            {isAddKeywordOpen === true ? (
                <Drawer
                    theme={customDrawer}
                    className="w-full xl:w-1/2 lg:w-2/3 md:w-4/5"
                    open={isAddKeywordOpen}
                    onClose={() => setIsAddKeyword(false)}
                    position="right"
                >
                    <DrawerHeader
                        titleIcon={IoMdRepeat}
                        title={"Add Keyword (" + autoReplyEditData.name + ")"}
                    />
                    <DrawerItems className="px-4 py-2 mt-4 bg-white rounded">
                        <AddKeyword
                            autoReplyData={autoReplyEditData}
                            onClose={() => setIsAddKeyword(false)}
                        ></AddKeyword>
                    </DrawerItems>
                </Drawer>
            ) : (
                <></>
            )}

            {/*-------- edit autoreply ---------*/}
            {isEditAutoReplyOpen === true ? (
                <Drawer
                    theme={customDrawerEdit}
                    className="w-full lg:w-4/6 md:w-4/5"
                    open={isEditAutoReplyOpen}
                    onClose={() => setIsEditAutoReply(false)}
                    position="right"
                >
                    <DrawerHeader
                        titleIcon={IoMdRepeat}
                        title={
                            "Edit Autoreply (" + autoReplyEditData.name + ")"
                        }
                    />
                    <DrawerItems className="px-4 py-2">
                        <Edit
                            autoReplyEditData={autoReplyEditData}
                            onClose={() => setIsEditAutoReply(false)}
                        ></Edit>
                    </DrawerItems>
                </Drawer>
            ) : (
                <></>
            )}

            {/*--------view autoreply---------*/}
            {isViewOpen === true ? (
                <Drawer
                    theme={customDrawerEdit}
                    className="w-full lg:w-2/5 md:w-3/5"
                    open={isViewOpen}
                    onClose={() => setIsViewOpen(false)}
                    position="right"
                >
                    <DrawerHeader
                        titleIcon={IoMdRepeat}
                        title="View Auto-Reply"
                    />
                    <DrawerItems className="px-4 pt-2 pb-4 bg-white rounded-lg">
                        <View autoReplyEditData={autoReplyEditData} />
                    </DrawerItems>
                </Drawer>
            ) : (
                <></>
            )}
        </WaMain>
    );
}
