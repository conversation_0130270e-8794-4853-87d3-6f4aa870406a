import { table_custom } from "@/Pages/Helpers/DesignHelper";
import { Table, Tooltip } from "flowbite-react";
import React from "react";
import { CgDetailsMore } from "react-icons/cg";

export default function Details() {
    return (
        <div className=" w-full">
            <div className="w-full">
                <div className="flex items-center gap-2">
                    <CgDetailsMore className="text-xl" />
                    <span className="text-base font-medium">Details</span>
                </div>

                <div className="flex gap-7 px-2 text-base  text-gray-600 w-full">
                    <div className="flex w-full gap-16 flex-wrap">
                        <div className="flex items-center gap-6">
                            <div>
                                <div className="flex gap-3 ">
                                    <div className="font-medium text-sm">
                                        Taxable:
                                    </div>
                                    <div className="text-sm">Yes</div>
                                </div>
                                <div className="flex gap-3 ">
                                    <div className="font-medium text-sm">
                                        Including GST:
                                    </div>
                                    <div className="text-sm">
                                        Yes
                                    </div>
                                </div>
                                <div className="flex gap-3 ">
                                    <div className="font-medium text-sm">
                                        Billing Address:
                                    </div>
                                    <div className="text-sm">Rani Bazar, Bikaner, India</div>
                                </div>
                                <div className="flex gap-3 ">
                                    <div className="font-medium text-sm">
                                        Billing Address To:
                                    </div>
                                    <div className="text-sm">India</div>
                                </div>
                                <div className="flex gap-3 ">
                                    <div className="font-medium text-sm">
                                        Currency:
                                    </div>
                                    <div className="text-sm">Indian Rupees</div>
                                </div>
                            </div>
                        </div>
                        <div>
                            <div className="flex gap-3 ">
                                <div className="font-medium text-sm">
                                    Business:
                                </div>
                                <div className="text-sm">Gold Store</div>
                            </div>
                            <div className="flex gap-3 ">
                                <div className="font-medium text-sm">
                                    Mobile:
                                </div>
                                <div className="text-sm">+91 8561045220</div>
                            </div>
                            <div className="flex gap-3 ">
                                <div className="font-medium text-sm">
                                    Email:
                                </div>
                                <div className="text-sm"><EMAIL></div>
                            </div>
                            <div className="flex gap-3 ">
                                <div className="font-medium text-sm">
                                    Place of Supply:
                                </div>
                                <div className="text-sm"> India</div>
                            </div>
                        </div>
                        <div>
                            <div className="flex gap-3   ">
                                <div className="font-medium text-sm">
                                    Address:
                                </div>
                                <div className="text-sm">Rani Bazar, Bikaner, India</div>
                            </div>
                            <div className="flex gap-3 ">
                                <div className="font-medium text-sm">
                                    City:
                                </div>
                                <div className="text-sm">
                                    Bikaner
                                </div>
                            </div>
                            <div className="flex gap-3 ">
                                <div className="font-medium text-sm">
                                    State:
                                </div>
                                <div className="text-sm">
                                    Rajasthan
                                </div>
                            </div><div className="flex gap-3 ">
                                <div className="font-medium text-sm">
                                    Pin Code:
                                </div>
                                <div className="text-sm">
                                    334001
                                </div>
                            </div>
                        </div>
                        <div>
                            <div className="flex gap-3   ">
                                <div className="font-medium text-sm">
                                    Approved by user:
                                </div>
                                <div className="text-sm">Bhawna Tanwar</div>
                            </div>
                            <div className="flex gap-3 ">
                                <div className="font-medium text-sm">
                                    Coupon:
                                </div>
                                <div className="text-sm">
                                    -
                                </div>
                            </div>
                            <div className="flex gap-3 ">
                                <div className="font-medium text-sm">
                                    Payment Date:
                                </div>
                                <div className="text-sm">
                                    24/03/24 | 05:32 PM
                                </div>
                            </div><div className="flex gap-3 ">
                                <div className="font-medium text-sm">
                                    Refund Amount:
                                </div>
                                <div className="text-sm">
                                    ₹0
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div className="overflow-x-auto mt-2">
                    <Table theme={table_custom}>
                        <TableHead>
                            <TableHeadCell>Items (2)</TableHeadCell>
                            <TableHeadCell>Time</TableHeadCell>
                            <TableHeadCell>Quantity</TableHeadCell>
                            <TableHeadCell>Rate</TableHeadCell>
                            <TableHeadCell>
                                Total
                            </TableHeadCell>
                        </TableHead>
                        <TableBody className="divide-y">
                            <TableRow className="bg-white dark:border-gray-700 dark:bg-gray-800">
                                <TableCell>
                                    Rapbooster Pro
                                </TableCell>
                                <TableCell>1 Year</TableCell>
                                <TableCell>2</TableCell>
                                <TableCell>₹ 5,750</TableCell>
                                <TableCell>
                                    ₹ 5,750
                                </TableCell>
                            </TableRow>
                            <TableRow className="bg-white dark:border-gray-700 dark:bg-gray-800">
                                <TableCell>
                                    Rapbooster Pro
                                </TableCell>
                                <TableCell>1 Year</TableCell>
                                <TableCell>2</TableCell>
                                <TableCell>₹ 5,750</TableCell>
                                <TableCell>
                                    ₹ 5,750
                                </TableCell>
                            </TableRow>
                        </TableBody>
                    </Table>
                    <div className="float-end p-2">
                        Final Total:
                        ₹ 5,750
                    </div>
                </div>
            </div>
        </div>
    );
}

