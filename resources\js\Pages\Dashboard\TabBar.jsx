import { inHouseNavIcons } from "@/Global/Icons";
import { Link } from "@inertiajs/react";
import { Badge } from "flowbite-react";
import { memo, useState } from "react";
import { hasPermission, menuCounts } from "../Helpers/Global";

export function TabBar() {
    
    const [tabCounts, setTabCounts] = useState(menuCounts());

    const Items = [
        {
            name: "Dashboard",
            route: "dashboard",
            icon: "MdOutlineDashboard",
            requiredPermission: "dashboard.view",
            activeRoute: "dashboard",
            label: "DashBoard",
            badgeCount: null,
            badgeColor: "bg-orange-500"
        },
        {
            name: "Sales Performance",
            route: "dashboard.salesPerformance",
            icon: "ImStatsDots",
            requiredPermission: "salesPerformance.view",
            activeRoute: "dashboard.salesPerformance",
            label: "Sales Performance",
            badgeCount: 0,
            badgeColor: "bg-orange-500"
        },
    ];

    const tabItems = Items.filter(item => hasPermission(item.requiredPermission));

    return (
        <div className="flex items-center w-full gap-2 px-2 pt-2">
            <div className="flex justify-between w-full gap-2 overflow-auto bg-white rounded-lg">
                <div className="flex">
                    {tabItems.map((item, k) => {
                        return <Link key={k + item.activeRoute}
                            href={route(item.route)}
                            className={
                                "p-2 flex text-black " +
                                (
                                    route().current(item.activeRoute)
                                        ? "bg-blue-700 text-white"
                                        : "hover:bg-blue-100 "
                                )
                            }
                        >
                            <div className="flex ">
                                <span className="me-1.5 self-center">

                                    {
                                        inHouseNavIcons(item.icon, `text-xl text-slate-400 ${route().current(item.activeRoute)
                                            ? "text-white "
                                            : " text-slate-400 "
                                            }`)
                                    }

                                </span>
                                <div className="self-center text-sm">{item.label}</div>
                                {tabCounts && item.name in tabCounts && tabCounts[item.name] != 0 && (
                                    <Badge className={`${item.badgeColor} text-white rounded-full py-0 px-1.5 ms-0.5 text-[10px]`}>
                                        {tabCounts[item.name]}
                                    </Badge>
                                )}
                            </div>
                        </Link>
                    })
                    }
                </div>
            </div>
        </div>
    );
}

export default memo(TabBar);
