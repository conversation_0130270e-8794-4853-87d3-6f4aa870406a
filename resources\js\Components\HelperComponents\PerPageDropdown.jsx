import { Dropdown, DropdownItem } from "flowbite-react";
import React from "react";
import { Link } from "@inertiajs/react";
import { pagination_dropdown_theme } from "@/Pages/Helpers/DesignHelper";

function PerPageDropdown({
    getDataFields = null,
    routeName,
    id = null,
    idName = "id",
    data = null,
    customPerPage = 10,
    routeParams,
    showPerPageDropdown = true,
}) {
    const perPages = [5, 15, 20, 25, customPerPage].sort((a, b) => a - b);

    const getDynamicParams = (perPage) => {
        const params = {
            perPage: perPage,
            column: (getDataFields) ? getDataFields.column : null,
            sort: (getDataFields) ? getDataFields.sort : null,
            [idName]: id,
        };
        return { ...params, ...routeParams };
    };

    let url = window.location.search;
    let perPageGet = (new URLSearchParams(url).get("perPage") != null) ? new URLSearchParams(url).get("perPage") : customPerPage;

    return (
        <div className="flex gap-4">
            {data != null ? (
                <div className="mt-1.5">
                    <div className="text-gray-400 dark:text-gray-500 text-[14px] transition-colors duration-200">
                        Showing {data.from} to {data.to} of {data.total} entries
                    </div>
                </div>
            ) : (
                <></>
            )}

            {showPerPageDropdown && <div>
                <Dropdown
                    className="text-xs"
                    label={<span className="px-0 text-xs text-gray-700 dark:text-gray-300 transition-colors duration-200">{perPageGet}</span>}
                    dismissOnClick={false}
                    size="xs"
                    color={"light"}
                    theme={pagination_dropdown_theme}
                >
                    {perPages.map((perPage, k) => (
                        <DropdownItem
                            className="text-xs hover:bg-gray-100 dark:hover:bg-gray-700 text-gray-700 dark:text-gray-200 transition-colors duration-200"
                            as={Link}
                            href={route(routeName, getDynamicParams(perPage))}
                            key={"perPage" + k}
                        >
                            {perPage}
                        </DropdownItem>
                    ))}
                </Dropdown>
            </div>}
        </div>
    );
}

export default PerPageDropdown;
