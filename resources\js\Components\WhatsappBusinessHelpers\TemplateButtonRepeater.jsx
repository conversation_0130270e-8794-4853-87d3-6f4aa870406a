import { Button, Dropdown, DropdownItem, Label, Select, TextInput, Tooltip } from "flowbite-react";
import React, { useState } from "react";
import { useEffect } from "react";
import { IoMdAdd, IoMdClose } from "react-icons/io";
import { BsInfoCircleFill } from "react-icons/bs";

const TemplateButtonRepeater = ({ setData, variablePreference = "number", previousButtons = [] }) => {

    const [buttons, setButtons] = useState(previousButtons);
    const [warnings, setWarnings] = useState({ URL: "", PHONE_NUMBER: "", COPY_CODE: "" });

    useEffect(() => {
        const urlCount = buttons.filter(button => button.type === "URL").length;
        const phoneCount = buttons.filter(button => button.type === "PHONE_NUMBER").length;
        const copyCodeCount = buttons.filter(button => button.type === "COPY_CODE").length;

        setWarnings({
            URL: urlCount > 2 ? "⚠️ You have exceeded the maximum of 2 URL buttons." : "",
            PHONE_NUMBER: phoneCount > 1 ? "⚠️ You have exceeded the maximum of 1 Phone Number button." : "",
            COPY_CODE: copyCodeCount > 1 ? "⚠️ You have exceeded the maximum of 1 Copy Code button." : "",
        });

        setData('buttons', buttons);
    }, [buttons]);

    const addButton = () => {
        setButtons([
            ...buttons,
            { type: "QUICK_REPLY", text: "", url: "", phoneNumber: "", countryCode: "", code: "", isStatic: 1, sample: "" },
        ]);
    };

    const removeButton = (index) => {
        setButtons(buttons.filter((_, i) => i !== index));
    };

    const updateButton = (index, updatedButton) => {
        const newButtons = [...buttons];
        newButtons[index] = updatedButton;
        setButtons(newButtons);
    };

    const cleanButtons = buttons.map(({ type, text, url, phoneNumber, countryCode, code }) => {
        const button = { type };
        if (text) button.text = text;
        if (url) button.url = url;
        if (phoneNumber) button.phone_number = phoneNumber;
        if (countryCode) button.country_code = countryCode;
        if (code) button.code = code;
        return button;
    });

    return (
        <div className="w-full py-1.5 mx-auto ">
            <div className="mb-3">
                <Button onClick={addButton} size="sm" color="gray">
                    <IoMdAdd className="text-lg" />
                    Add Button
                </Button>
            </div>
            <div className="text-sm text-red-500">
                {warnings.URL && <p className="mb-2">{warnings.URL}</p>}
                {warnings.PHONE_NUMBER && <p className="mb-2">{warnings.PHONE_NUMBER}</p>}
                {warnings.COPY_CODE && <p className="mb-2">{warnings.COPY_CODE}</p>}
            </div>
            <div className="">
                {buttons.map((button, index) => (
                    <div key={index} className="flex items-start gap-3 p-3 mb-4 border rounded">
                        <ButtonTypeForm
                            button={button}
                            onUpdate={(updatedButton) => updateButton(index, updatedButton)}
                            index={index}
                            variablePreference={variablePreference}
                            setData={setData}
                        />
                        <div className="">
                            <Button onClick={() => removeButton(index)} size="xs" color="white">
                                <IoMdClose className="text-lg text-gray-600" />
                            </Button>
                        </div>
                    </div>
                ))}
            </div>
        </div>
    );
};

const ButtonTypeForm = ({ button, onUpdate, index, variablePreference, setData }) => {
    const [warnings, setWarnings] = useState({ URL: "", PHONE_NUMBER: "", COPY_CODE: "", URL_VARIABLE: "" });
    const [variableList, setVariableList] = useState([]);

    const handleInputChange = (e) => {
        if (e.target.name == "url") {
            const matchCount = e.target.value.split("{{1}}").length - 1;
            if (matchCount == 1) {
                setVariableList([{ "{{1}}": "" }]);
                setWarnings((prev) => ({
                    ...prev,
                    URL: ""
                }));
            } else {
                if (matchCount > 1) {
                    setVariableList([]);
                    setWarnings((prev) => ({
                        ...prev,
                        URL: "Only one variable allowed in URL."
                    }));
                } else {
                    setVariableList([]);
                }
            }
        }
        onUpdate({ ...button, [e.target.name]: e.target.value });
    };

    const buttonOptions = [
        { value: "QUICK_REPLY", label: "Quick Reply", 'subText': '' },
        { value: "URL", label: "Visit Website", 'subText': '2 Buttons Max' },
        { value: "PHONE_NUMBER", label: "Phone Number", 'subText': '1 Buttons Max' },
        { value: "COPY_CODE", label: "Copy Offer Code", 'subText': '1 Buttons Max' },
    ];

    return (
        <div className="flex w-full gap-3 md:flex-col xl:flex-row">
            <div className="mb-3">
                <div className="mb-2">
                    <Label htmlFor={`type-${index}`} value="Type:" />
                </div>
                <Dropdown label={buttonOptions.find(option => option.value === button.type)?.label || "Select Type"} inline
                    size="sm" theme={{ "inlineWrapper": "flex items-center text-sm border p-1 rounded text-nowrap border-gray-300 bg-gray-100" }}
                >
                    {buttonOptions.map((option) => (
                        <DropdownItem key={option.value} onClick={() => handleInputChange({ target: { name: 'type', value: option.value } })}>
                            <div className="text-start">
                                <div className="block">
                                    {option.label}
                                </div>
                                {option.subText && <span className="text-xs text-gray-500">{option.subText}</span>}
                            </div>
                        </DropdownItem>
                    ))}
                </Dropdown>
            </div>

            <div className="grid w-full mb-3 gap-x-4 gap-y-2 sm:grid-cols-1 md:grid-cols-3 lg:grid-cols-3">
                {button.type === "QUICK_REPLY" && (
                    <div className="">
                        <div className="block mb-2">
                            <Label htmlFor={`text-${index}`} value="Quick Reply Text:" />
                        </div>
                        <TextInput
                            sizing={"sm"}
                            type="text"
                            name="text" maxLength={25}
                            placeholder="Enter quick reply text"
                            value={button.text}
                            onChange={handleInputChange}
                            id={`text-${index}`} // Unique id
                        />
                        <div className={`text-xs font-sans text-end mt-0.5 ${button.text.length > 25 ? 'text-red-400' : 'text-gray-400'}`}>{button.text.length} / 25 </div>
                    </div>
                )}
                {button.type === "URL" && (
                    <>
                        <div className="">
                            <div className="block mb-2">
                                <Label htmlFor={`url-text-${index}`} value="Button Text:" />
                            </div>
                            <TextInput
                                sizing={"sm"}
                                type="text"
                                name="text" maxLength={25}
                                placeholder="Button Text"
                                value={button.text}
                                onChange={handleInputChange}
                                id={`url-text-${index}`} // Unique id
                            />
                            <div className={`text-xs font-sans text-end mt-0.5 ${button.text.length > 25 ? 'text-red-400' : 'text-gray-400'}`}>{button.text.length} / 25 </div>
                        </div>
                        <div className="">
                            <div className="block mb-2">
                                <Label htmlFor={`isStatic-${index}`} value="Url Type:" />
                            </div>
                            <Select name="isStatic" value={button.isStatic} onChange={handleInputChange} sizing="sm" id={`isStatic-${index}`}>
                                <option value={1}>Static</option>
                                <option value={0}>Dynamic</option>
                            </Select>
                        </div>
                        <div className="">
                            <div className="block mb-2">
                                <Label htmlFor={`url-${index}`} value="Enter URL:" />
                            </div>
                            <TextInput
                                sizing={"sm"}
                                type="url"
                                name="url"
                                placeholder="Enter URL"
                                value={button.url}
                                onChange={handleInputChange}
                                id={`url-${index}`}
                            />
                            <div className="text-sm text-red-500">
                                {warnings.URL && <p className="mb-2">{warnings.URL}</p>}
                            </div>
                        </div>

                        {
                            (variableList.length == 1) && (
                                <div>
                                    <div className="block mb-2">
                                        <Label htmlFor={`url-dynamic-${index}`} value="Add sample URL" />
                                    </div>
                                    <TextInput
                                        sizing={"sm"}
                                        type="url"
                                        helperText={"To help us review your message template, please add an example of the website URL. Do not use real customer information."}
                                        name="sample"
                                        placeholder="Enter full URL for https://www.example.com/%7B%7B1%7D%7D{{1}}"
                                        onChange={handleInputChange}
                                        id={`url-dynamic-${index}`}
                                    />
                                    <div className="text-sm text-red-500">
                                        {warnings.URL_VARIABLE && <p className="mb-2">{warnings.URL_VARIABLE}</p>}
                                    </div>
                                </div>
                            )
                        }

                    </>
                )}
                {button.type === "PHONE_NUMBER" && (
                    <>
                        <div className="">
                            <div className="block mb-2">
                                <Label htmlFor={`phone-text-${index}`} value="Button Text:" />
                            </div>
                            <TextInput
                                sizing={"sm"}
                                type="text"
                                name="text" maxLength={25}
                                placeholder="Call"
                                value={button.text}
                                onChange={handleInputChange}
                                id={`phone-text-${index}`} // Unique id
                            />
                            <div className={`text-xs font-sans text-end mt-0.5 ${button.text.length > 25 ? 'text-red-400' : 'text-gray-400'}`}>{button.text.length} / 25 </div>
                        </div>
                        <div className="">
                            <div className="block mb-2">
                                <Label htmlFor={`countryCode-${index}`} value="Country Code:" />
                            </div>
                            <TextInput
                                sizing={"sm"}
                                type="text"
                                name="countryCode"
                                placeholder="91"
                                value={button.countryCode}
                                onChange={handleInputChange}
                                id={`countryCode-${index}`} // Unique id
                            />
                        </div>
                        <div className="">
                            <div className="block mb-2">
                                <Label htmlFor={`phoneNumber-${index}`} value="Enter phone number:" />
                            </div>
                            <TextInput
                                sizing={"sm"}
                                type="text"
                                name="phoneNumber" maxLength={20}
                                placeholder="Enter phone number"
                                value={button.phoneNumber}
                                onChange={handleInputChange}
                                id={`phoneNumber-${index}`} // Unique id
                            />
                        </div>
                    </>
                )}
                {button.type === "COPY_CODE" && (
                    <>
                        <div className="">
                            <div className="block mb-2">
                                <Label htmlFor={`code-text-${index}`} value="Button Text:" />
                            </div>
                            <TextInput
                                sizing={"sm"}
                                type="text"
                                name="text" maxLength={25}
                                placeholder="Enter button text"
                                value={button.text}
                                onChange={handleInputChange}
                                id={`code-text-${index}`} // Unique id
                            />
                            <div className={`text-xs font-sans text-end mt-0.5 ${button.text.length > 25 ? 'text-red-400' : 'text-gray-400'}`}>{button.text.length} / 25 </div>
                        </div>
                        <div className="">
                            <div className="block mb-2">
                                <Label htmlFor={`code-${index}`} value="Offer Code:" />
                            </div>
                            <TextInput
                                sizing={"sm"}
                                type="text"
                                name="code" maxLength={15}
                                placeholder="Offer Code"
                                value={button.code}
                                onChange={handleInputChange}
                                id={`code-${index}`} // Unique id
                            />
                        </div>
                    </>
                )}
            </div>
            {button.type === "URL" && (
                <div className="flex items-center">
                    <Tooltip content="Adding a variable creates a personalised link for the customer to view their info. Only one variable can be added to the end of a URL.">
                        <BsInfoCircleFill />
                    </Tooltip>
                </div>
            )}


        </div>

    );
};

export default TemplateButtonRepeater;
