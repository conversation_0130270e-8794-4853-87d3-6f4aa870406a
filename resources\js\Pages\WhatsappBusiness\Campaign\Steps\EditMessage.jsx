import { Head, router, useForm } from "@inertiajs/react";
import { useEffect, useState } from "react";
import CreateMain from "./CreateMain";
import NoRecord from "@/Components/HelperComponents/NoRecord";
import TemplateLibrary from "@/Components/WhatsappBusinessHelpers/TemplateLibrary";
import { button_custom } from "@/Pages/Helpers/DesignHelper";
import { Button, FileInput, Select, TextInput } from "flowbite-react";
import { GrFormNext } from "react-icons/gr";

function EditMessage({ collection }) {

    const oldTemplateJson = collection.campaign?.template?.templateJson ?? null;
    const oldTemplateVariables = collection.campaign?.template_variables ?? null;

    const [selectedTemplate, setSelectedTemplate] = useState({ id: oldTemplateVariables?.[0]?.waba_template_id ?? null, json: oldTemplateJson ?? null });
    const [totalVariableCount, setTotalVariableCount] = useState({ Header: 0, Body: 0 });
    const variable_option = { name: 'name', mobile: 'mobile', email: 'email', var1: 'var1', var2: 'var2', va3: 'va3', var4: 'var4', var5: 'var5' };
    const [variableList, setVariableList] = useState({});

    // Initialize form with old values from template variables
    const initialVarObject = {};
    if (oldTemplateVariables) {
        oldTemplateVariables.forEach(variable => {
            const section = variable.variableFor[0].toUpperCase() + variable.variableFor.slice(1).toLowerCase();

            initialVarObject[section] = {
                ...initialVarObject[section],
                [variable.key]: {
                    fixed: variable.valueFixed || "",
                    column: variable.valueField || ""
                }
            };
        });
    }

    const { data, setData, post, processing, errors } = useForm({
        templateId: selectedTemplate.id,
        totalVariableCount: totalVariableCount,
        varObject: initialVarObject,
        campaignId: collection.id,
    });

    const extractTemplateVariables = (template) => {
        let extractedVariables = {
            Header: [],
            Body: []
        };

        // Function to extract variables from text
        const extractVariables = (text) => {

            let variables = [];
            if (!text) return variables;

            const regex = /{{(\d+|[a-zA-Z_][a-zA-Z0-9_]*)}}/g;
            let match;
            while ((match = regex.exec(text)) !== null) {
                let key = match[1];
                variables.push({ [key]: "" });
            }
            return variables;
        };

        if (template.components) {
            template.components.forEach((component) => {
                if (component.type == "HEADER" && component.format == "TEXT" && component.text) {
                    extractedVariables.Header = {
                        type: "text",
                        variables: extractVariables(component.text)
                    };
                }
                if (component.type == "HEADER" && component.format == "IMAGE") {
                    extractedVariables.Header = {
                        type: "image",
                        variables: []
                    };
                }
                if (component.type == "BODY" && component.text) {
                    extractedVariables.Body = {
                        type: "text",
                        variables: extractVariables(component.text)
                    }
                }
            });

        }
        return extractedVariables;
    };

    const handleVariableChange = (parentKey, key, sub_key, value) => {
        setData(prevState => ({
            ...prevState,
            varObject: {
                ...prevState.varObject,
                [parentKey]: {
                    ...prevState.varObject[parentKey],
                    [key]: {
                        [sub_key === 'column' ? 'column' : 'fixed']: value,
                        [sub_key === 'column' ? 'fixed' : 'column']: ""
                    }
                }
            }
        }));
    }

    const handleTemplateSubmit = (e) => {
        e.preventDefault();
        post(route("whatsappB.campaign.message.edit"));
    }


    useEffect(() => {
        if (selectedTemplate.id && selectedTemplate.json) {
            const parsedTemplateJson = JSON.parse(selectedTemplate.json);
            const extractedVariables = extractTemplateVariables(parsedTemplateJson);
            setVariableList(extractedVariables);

            const newTotalVariableCount = {
                Header: (extractedVariables.Header.type == "text") ? extractedVariables.Header.variables.length : (extractedVariables.Header.type == "image" ? 1 : 0),
                Body: extractedVariables.Body.variables.length ?? 0
            };

            setTotalVariableCount(newTotalVariableCount);

            // Only reset varObject if template changes
            if (selectedTemplate.id !== oldTemplateVariables?.[0]?.waba_template_id) {
                setData(prevData => ({
                    ...prevData,
                    varObject: {},
                    templateId: selectedTemplate.id,
                    totalVariableCount: newTotalVariableCount
                }));
            }
        }
    }, [selectedTemplate]);

    return (
        <CreateMain
            active={1}
            mode={"Edit"}
        >
            <Head title="Campaign Message" />
            <div>
                <form id="template-variables-form" onSubmit={handleTemplateSubmit} className="">
                    <div className="w-full p-2 bg-white border rounded-lg">
                        <h4 className="text-base">Compose Message</h4>
                        <p className="text-sm text-gray-400">
                            Write, edit, and personalize your message quickly.
                        </p>
                    </div>

                    <div className="grid grid-flow-row-dense grid-cols-12 gap-2 p-0 mt-2 border-none md:grid-cols-12 sm:grid-cols-1 bg-slate-100">
                        <div className="col-span-12 p-2 bg-white rounded-lg h-fit lg:col-span-8 md:col-span-12">
                            <div className="h-full p-2 bg-white rounded-lg">
                                <TemplateLibrary setSelectedTemplate={setSelectedTemplate} oldSelectedTemplate={oldTemplateVariables?.[0]?.waba_template_id ?? null} />
                            </div>
                        </div>
                        <div className="col-span-12 p-1 bg-white rounded-lg lg:col-span-4 md:col-span-12  ps-2">
                            <h2 className="text-2xl text-center text-gray-500 mt-2">Variables</h2>
                            {variableList && (
                                (variableList.Header && Object.keys(variableList.Header).length > 0) ||
                                (variableList.Body && Object.keys(variableList.Body.variables).length > 0)
                            ) ? (
                                Object.entries(variableList).map(([parentkey, { type, variables }], index) => (
                                    <div key={`${parentkey}-${index}`}>
                                        {((parentkey == "Header" && (variables && Object.keys(variables).length > 0)) || (parentkey == "Header" && type == "image")) && (
                                            <h3 className="text-xl font-medium mt-2">Header</h3>
                                        )}
                                        {parentkey == "Body" && variables && Object.keys(variables).length > 0 && (
                                            <h3 className="text-xl font-medium mt-2">Body</h3>
                                        )}
                                        {type === "text" && variables && Object.keys(variables).length > 0 && (
                                            Object.entries(variables).map(([, variableObject], subIndex) => {
                                                const variableName = Object.keys(variableObject)[0];
                                                return (
                                                    <div className="flex gap-3 mb-2" key={`${parentkey}-${variableName}-${subIndex}`}>
                                                        <TextInput
                                                            className="w-full"
                                                            id={`var-id-${variableName}`}
                                                            type="text"
                                                            sizing="sm"
                                                            addon={variableName}
                                                            placeholder="Enter Variable Value"
                                                            value={data.varObject?.[parentkey]?.[variableName]?.fixed || ""}
                                                            disabled={data.varObject?.[parentkey]?.[variableName]?.column?.length > 0}
                                                            onChange={(e) => handleVariableChange(parentkey, variableName, "fixed", e.target.value)}
                                                        />
                                                        <Select
                                                            value={data.varObject?.[parentkey]?.[variableName]?.column || ""}
                                                            onChange={(e) => handleVariableChange(parentkey, variableName, "column", e.target.value)}
                                                            sizing="sm"
                                                            disabled={data.varObject?.[parentkey]?.[variableName]?.fixed?.length > 0}
                                                        >
                                                            <option value="">Select Column</option>
                                                            {Object.entries(variable_option).map(([key, value]) => (
                                                                <option key={key} value={value}>
                                                                    {value}
                                                                </option>
                                                            ))}
                                                        </Select>
                                                    </div>
                                                );
                                            })
                                        )}
                                        {type === "image" && (
                                            <div className="flex gap-2 mb-2 mt-2">
                                                <FileInput
                                                    sizing="sm"
                                                    onChange={(e) => setData((prevData) => ({
                                                        ...prevData,
                                                        varObject: {
                                                            ...prevData.varObject,
                                                            [parentkey]: { "image": e.target.files[0] }
                                                        }
                                                    }))}
                                                    accept="image/*"
                                                />
                                            </div>
                                        )}
                                    </div>
                                ))
                            ) : (
                                <div className="mt-5">
                                    <NoRecord message="No Variables Found" />
                                </div>
                            )}
                            {
                                errors && (
                                    <div className="text-red-500 text-center">
                                        {errors.varObject}
                                        {errors.templateId}
                                    </div>
                                )
                            }
                        </div>
                    </div>
                    <div className='flex justify-end mt-3 rounded-lg p-3'>
                        <div className="flex items-center gap-1">
                            <Button
                                // disabled={(data.varObject && Object.entries(data.varObject).length != 0) ? false : true}
                                size="sm"
                                type="submit"
                                className="me-4"
                                color="blue"
                                theme={button_custom}
                                isProcessing={processing}
                            >
                                <div className="flex items-center gap-1">
                                    {"Continue"}
                                    <GrFormNext className="text-lg" />
                                </div>
                            </Button>
                        </div>
                    </div>


                </form>
            </div>
        </CreateMain>
    );
}

export default EditMessage;
