import React from "react";
import { GrCurrency } from "react-icons/gr";
import { <PERSON><PERSON>, Checkbox, Drawer, Table } from "flowbite-react";
import { CiExport } from "react-icons/ci";
import { useState } from "react";
import { Tooltip } from "flowbite-react";
import { CgMail } from "react-icons/cg";
import { IoEyeOutline, IoArrowDownOutline } from "react-icons/io5";
import { RiMoneyRupeeCircleLine } from "react-icons/ri";
import {
    MdDeleteOutline,
    MdOutlineContactPage,
    MdOutlineModeEdit,
} from "react-icons/md";
import { TbColumns3 } from "react-icons/tb";

export default function Loan() {
    const [isCheckAll, setIsCheckAll] = useState(false);
    // ----------------Edit --------------------
    const [isOpen, setIsOpen] = useState(false);
    const handleClose = () => setIsOpen(false);
    const table_custom = {
        root: {
            base: "w-full text-left text-sm text-gray-500 dark:text-gray-400 shadow-lg",
            shadow: "absolute left-0 top-0 -z-10 h-full w-full rounded-lg bg-white drop-shadow-md dark:bg-black",
            wrapper: "relative",
        },
        head: {
            base: "group/head text-gray-700 dark:text-gray-400 ",
            cell: {
                base: "text-slate-900 dark:text-slate-300 font-medium border dark:border-slate-500 bg-slate-50 text-nowrap px-3 py-2 group-first/head:first:rounded-tl-lg group-first/head:last:rounded-tr-lg dark:bg-gray-700",
            },
        },
        body: {
            base: "group/body",
            cell: {
                base: "border-b px-3 py-2.5 text-gray-800 group-first/body:group-first/row:first:rounded-tl-lg group-first/body:group-first/row:last:rounded-tr-lg group-last/body:group-last/row:first:rounded-bl-lg group-last/body:group-last/row:last:rounded-br-lg",
            },
        },
    };

    const button_custom = {
        color: {
            orange: "border border-transparent bg-orange-500 text-white focus:ring-4 focus:ring-orange-300 enabled:hover:bg-orange-500 dark:bg-orange-500 dark:hover:bg-orange-500 dark:focus:ring-orange-300",
        },
        pill: {
            off: "rounded",
            on: "rounded-full",
        },
        size: {
            xs: "p-0.5 text-sm",
            sm: "px-3 py-1.5 text-sm",
            md: "px-4 py-2 text-sm",
            lg: "px-5 py-2.5 text-base",
            xl: "px-6 py-3 text-base",
        },
    };

    const customTheme = {
        root: {
            position: {
                right: {
                    on: "right-0 top-0 h-screen w-[70vw] transform-none bg-slate-100",
                    off: "right-0 top-0 h-screen w-80 translate-x-full",
                },
            },
        },
        header: {
            inner: {
                closeButton:
                    "absolute end-2.5 top-2.5 flex h-8 w-8 items-center justify-center rounded-lg bg-transparent text-sm text-gray-400 hover:bg-gray-200 hover:text-gray-900 dark:hover:bg-gray-600 dark:hover:text-white",
                closeIcon: "h-4 w-4",
                titleIcon: "me-2.5 h-6 w-6 text-gray-400",
                titleText:
                    "mb-4 inline-flex items-center text-lg font-semibold text-sky-950 dark:text-gray-400",
            },
        },
    };
    return (
        <>
            <div className="px-4 pb-2 rounded-lg">
                <div className="flex items-center text-xl p-2 gap-2">
                    <RiMoneyRupeeCircleLine className="text-slate-400 text-2xl" />
                    <span className=" font-medium">Loan</span>
                </div>
                <div className="h-fit bg-white rounded border overflow-auto w-full">
                    <div className="flex justify-between p-2">
                        <div className="flex ">
                            <Button
                                theme={button_custom}
                                size="xs"
                                color="gray"
                                className="border-e-0 rounded-e-none"
                            >
                                <div className="flex items-center gap-1">
                                    <CiExport className="text-slate-500" />
                                    <span className="text-xs">Export</span>
                                </div>
                            </Button>
                            <Button
                                className="border rounded-s-none"
                                size="xs"
                                color="gray"
                                theme={button_custom}
                            >
                                <div className="flex items-center gap-1">
                                    <MdDeleteOutline className="text-slate-500" />
                                    <span className="text-xs">Delete</span>
                                </div>
                            </Button>
                        </div>
                        <div className="">
                            <Button
                                theme={button_custom}
                                size="xs"
                                color="gray"
                                id="dropdownInformationButton"
                                data-dropdown-toggle="dropdownNotification"
                                type="button"
                            >
                                <div className="flex items-center gap-1">
                                    <TbColumns3 className="text-slate-500" />
                                    <span className="text-xs">Columns</span>
                                </div>
                            </Button>
                            {/* Dropdown menu */}
                            <div
                                id="dropdownNotification"
                                className="z-20 border shadow-lg hidden w-full max-w-sm bg-white divide-y divide-gray-100 rounded-lg  dark:bg-gray-800 dark:divide-gray-700"
                                aria-labelledby="dropdownNotificationButton"
                            >
                                <div className="block px-4 py-2 font-medium text-center text-gray-700 rounded-t-lg bg-gray-50 dark:bg-gray-800 dark:text-white">
                                    <div className="flex justify-between">
                                        <div className="">
                                            <div className="group  text-gray-400 p-1 flex items-center gap-1">
                                                <svg
                                                    xmlns="http://www.w3.org/2000/svg"
                                                    className=" fill-slate-400  "
                                                    height="24px"
                                                    viewBox="0 0 24 24"
                                                    width="24px"
                                                >
                                                    <rect
                                                        fill="none"
                                                        height="24"
                                                        width="24"
                                                    />
                                                    <path d="M20,4H4C2.9,4,2,4.9,2,6v12c0,1.1,0.9,2,2,2h16c1.1,0,2-0.9,2-2V6C22,4.9,21.1,4,20,4z M8,18H4V6h4V18z M14,18h-4V6h4V18z M20,18h-4V6h4V18z" />
                                                </svg>
                                                Column
                                            </div>
                                        </div>
                                        <div className="">
                                            <button className="p-1 flex text-blue-600">
                                                <svg
                                                    xmlns="http://www.w3.org/2000/svg"
                                                    height="24px"
                                                    viewBox="0 0 24 24"
                                                    width="24px"
                                                    className="fill-blue-600"
                                                >
                                                    <g>
                                                        <path
                                                            d="M0,0h24v24H0V0z"
                                                            fill="none"
                                                        />
                                                    </g>
                                                    <g>
                                                        <g>
                                                            <path d="M6,13c0-1.65,0.67-3.15,1.76-4.24L6.34,7.34C4.9,8.79,4,10.79,4,13c0,4.08,3.05,7.44,7,7.93v-2.02 C8.17,18.43,6,15.97,6,13z M20,13c0-4.42-3.58-8-8-8c-0.06,0-0.12,0.01-0.18,0.01l1.09-1.09L11.5,2.5L8,6l3.5,3.5l1.41-1.41 l-1.08-1.08C11.89,7.01,11.95,7,12,7c3.31,0,6,2.69,6,6c0,2.97-2.17,5.43-5,5.91v2.02C16.95,20.44,20,17.08,20,13z" />
                                                        </g>
                                                    </g>
                                                </svg>
                                                Reset
                                            </button>
                                        </div>
                                    </div>
                                </div>
                                <div className="divide-y divide-gray-100 dark:divide-gray-700">
                                    <div className="flex flex-col">
                                        <div className="relative overflow-x-auto shadow-md sm:rounded-lg ">
                                            <table className=" w-full text-sm text-left rtl:text-right text-gray-500 dark:text-gray-400">
                                                <thead className="text-xs text-gray-700 uppercase dark:text-gray-400">
                                                    <tr>
                                                        <th className="p-2 bg-gray-50 dark:bg-gray-800 w-14 text-center ">
                                                            List
                                                        </th>
                                                        <th className="p-2  bg-gray-50 dark:bg-gray-800 w-14 text-center">
                                                            Details
                                                        </th>
                                                        <th className="p-2"></th>
                                                    </tr>
                                                </thead>
                                                <tbody className=" overflow-auto">
                                                    <tr className="border-b border-gray-200 dark:border-gray-700">
                                                        <td className="text-center max-w-max p-2 font-medium text-gray-900 whitespace-nowrap bg-gray-50 dark:text-white dark:bg-gray-800">
                                                            <Checkbox></Checkbox>
                                                        </td>
                                                        <td className="text-center p-2 font-medium text-gray-900 whitespace-nowrap bg-gray-50 dark:text-white dark:bg-gray-800">
                                                            <Checkbox></Checkbox>
                                                        </td>
                                                        <td className=" max-w-max px-6 py-4">
                                                            Name
                                                        </td>
                                                    </tr>
                                                    <tr className="border-b border-gray-200 dark:border-gray-700">
                                                        <td className="text-center  max-w-max p-2 font-medium text-gray-900 whitespace-nowrap bg-gray-50 dark:text-white dark:bg-gray-800">
                                                            <Checkbox></Checkbox>
                                                        </td>
                                                        <td className="text-center p-2 font-medium text-gray-900 whitespace-nowrap bg-gray-50 dark:text-white dark:bg-gray-800">
                                                            <Checkbox></Checkbox>
                                                        </td>
                                                        <td className=" max-w-max px-6 py-4">
                                                            Name
                                                        </td>
                                                    </tr>
                                                    <tr className="border-b border-gray-200 dark:border-gray-700">
                                                        <td className="text-center  max-w-max p-2 font-medium text-gray-900 whitespace-nowrap bg-gray-50 dark:text-white dark:bg-gray-800">
                                                            <Checkbox></Checkbox>
                                                        </td>
                                                        <td className="text-center p-2 font-medium text-gray-900 whitespace-nowrap bg-gray-50 dark:text-white dark:bg-gray-800">
                                                            <Checkbox></Checkbox>
                                                        </td>
                                                        <td className="max-w-max px-6 py-4">
                                                            Name
                                                        </td>
                                                    </tr>
                                                    <tr className="border-b border-gray-200 dark:border-gray-700">
                                                        <td className="text-center max-w-max p-2 font-medium text-gray-900 whitespace-nowrap bg-gray-50 dark:text-white dark:bg-gray-800">
                                                            <Checkbox></Checkbox>
                                                        </td>
                                                        <td className="text-center p-2 font-medium text-gray-900 whitespace-nowrap bg-gray-50 dark:text-white dark:bg-gray-800">
                                                            <Checkbox></Checkbox>
                                                        </td>
                                                        <td className="max-w-max px-6 py-4">
                                                            Name
                                                        </td>
                                                    </tr>
                                                </tbody>
                                            </table>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div className="bg-white h-[73vh] overflow-auto">
                        {/* documents table */}
                        <div className="overflow-x-auto bg-white border rounded-lg">
                            <Table hoverable theme={table_custom}>
                                <TableHead className=" bg-slate-100">
                                    <TableHeadCell>
                                        Principal Amount
                                    </TableHeadCell>

                                    <TableHeadCell>
                                        Returned Amount
                                    </TableHeadCell>

                                    <TableHeadCell>Monthly EMI</TableHeadCell>

                                    <TableHeadCell>Status</TableHeadCell>

                                    <TableHeadCell>
                                        Interest Rate
                                    </TableHeadCell>

                                    <TableHeadCell>Start Date</TableHeadCell>
                                    <TableHeadCell>End Date</TableHeadCell>
                                </TableHead>

                                <TableBody className="divide-y">
                                    <TableRow className="bg-white dark:border-gray-700 dark:bg-gray-800">
                                        <TableCell>50,000</TableCell>

                                        <TableCell className="font-medium text-gray-900 whitespace-nowrap dark:text-white">
                                            40,000
                                        </TableCell>
                                        <TableCell>5,000</TableCell>
                                        <TableCell>
                                            <h3 className="text-blue-500 font-medium">
                                                Running
                                            </h3>
                                        </TableCell>
                                        <TableCell>12%</TableCell>
                                        <TableCell>
                                            <div className="flex flex-col">
                                                <span className="text-blue-500 font-medium">
                                                    24/03/2024
                                                </span>
                                                <span className="text-xs">02:45 PM</span>
                                            </div>
                                        </TableCell>
                                        <TableCell>
                                            <div className="flex flex-col">
                                                <span className="text-blue-500 font-medium">
                                                    24/03/2024
                                                </span>
                                                <span className="text-xs">02:45 PM</span>
                                            </div>
                                        </TableCell>
                                    </TableRow>
                                    <TableRow className="bg-white dark:border-gray-700 dark:bg-gray-800">
                                        <TableCell>50,000</TableCell>

                                        <TableCell className="font-medium text-gray-900 whitespace-nowrap dark:text-white">
                                            40,000
                                        </TableCell>
                                        <TableCell>5,000</TableCell>
                                        <TableCell>
                                            <h3 className="text-red-500 font-medium">
                                                Due
                                            </h3>
                                        </TableCell>
                                        <TableCell>12%</TableCell>
                                        <TableCell>
                                            <div className="flex flex-col">
                                                <span className="text-blue-500 font-medium">
                                                    24/03/2024
                                                </span>
                                                <span className="text-xs">02:45 PM</span>
                                            </div>
                                        </TableCell>
                                        <TableCell>
                                            <div className="flex flex-col">
                                                <span className="text-blue-500 font-medium">
                                                    24/03/2024
                                                </span>
                                                <span className="text-xs">02:45 PM</span>
                                            </div>
                                        </TableCell>
                                    </TableRow>
                                    <TableRow className="bg-white dark:border-gray-700 dark:bg-gray-800">
                                        <TableCell>50,000</TableCell>

                                        <TableCell className="font-medium text-gray-900 whitespace-nowrap dark:text-white">
                                            40,000
                                        </TableCell>
                                        <TableCell>5,000</TableCell>
                                        <TableCell>
                                            <h3 className="text-gray-400 font-medium">
                                                Closed
                                            </h3>
                                        </TableCell>
                                        <TableCell>12%</TableCell>
                                        <TableCell>
                                            <div className="flex flex-col">
                                                <span className="text-blue-500 font-medium">
                                                    24/03/2024
                                                </span>
                                                <span className="text-xs">02:45 PM</span>
                                            </div>
                                        </TableCell>
                                        <TableCell>
                                            <div className="flex flex-col">
                                                <span className="text-blue-500 font-medium">
                                                    24/03/2024
                                                </span>
                                                <span className="text-xs">02:45 PM</span>
                                            </div>
                                        </TableCell>
                                    </TableRow>
                                </TableBody>
                            </Table>
                        </div>
                    </div>

                    <div className="border-t p-3 w-full bg-white ">
                        <div className="flex flex-wrap justify-between lg:gap-0 md:gap-0 gap-2">
                            <div className="flex gap-3 items-center">
                                <div className="text-gray-400 text-sm ">
                                    Showing 1 to 25 of 62 entries
                                </div>
                                <div>
                                    <select
                                        id="countries"
                                        className="text-xs bg-gray-50 border border-gray-300 text-gray-900 rounded focus:ring-blue-500 focus:border-blue-500 block p-1 ps-2 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-blue-500 dark:focus:border-blue-500"
                                    >
                                        <option value={10} defaultValue={10}>
                                            10
                                        </option>
                                        <option value={15}>15</option>
                                        <option value={20}>20</option>
                                        <option value={25}>25</option>
                                        <option value={30}>30</option>
                                    </select>
                                </div>
                            </div>
                            <div className="flex">
                                <Button
                                    size="xs"
                                    color="gray"
                                    className="border-e-0 text-gray-400 px-2 rounded text-xs "
                                >
                                    Previous
                                </Button>
                                <Button
                                    size="xs"
                                    color="blue"
                                    className="border text-white border-blue-600 bg-blue-600 px-2.5 rounded-none text-sm "
                                >
                                    1
                                </Button>
                                <Button
                                    size="xs"
                                    color="gray"
                                    className="border text-blue-600 px-2.5 rounded-none text-xs "
                                >
                                    2
                                </Button>
                                <Button
                                    size="xs"
                                    color="gray"
                                    className="border-s-0 text-blue-600 px-2 rounded text-xs "
                                >
                                    Next
                                </Button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            {/* <div className="col-span-full bg-white dark:bg-gray-700 p-2.5 mx-3.5 mb-2 rounded bottom-0">
                <h6 className="text-sm dark:text-white">
                    Copyright © 2024 Rapbooster Cloud by Dunes Factory Pvt Ltd
                </h6>
            </div> */}
            <Drawer
                theme={customTheme}
                open={isOpen}
                onClose={handleClose}
                position="right"
                className="w-full xl:w-3/5 lg:w-4/5 md:w-4/5"
            >
                <DrawerHeader title="Salary Slip" titleIcon={GrCurrency} />
                <DrawerItems>
                    <div className="bg-white rounded">
                        <div className="flex items-start justify-between p-2 px-4">
                            <div className="h-36 w-36">
                                <img src="assets/img/df.png" alt="" />
                            </div>
                            <div className="text-right text-sm font-medium">
                                <h6>Opp. Bajaj RE Motors, Road No. 5,</h6>
                                <h6>Bikaner-334001</h6>
                                <h6>+91-9680440011</h6>
                                <h6><EMAIL></h6>
                                <h6>www.dunesfactory.com</h6>
                            </div>
                        </div>
                        {/* Table */}
                        <div className="overflow-x-auto">
                            <table className="w-full text-sm text-left rtl:text-right text-gray-500 dark:text-gray-400">
                                <thead className="text-sm text-white bg-blue-500 dark:bg-gray-700 dark:text-gray-400">
                                    <tr>
                                        <th
                                            scope="col"
                                            className="px-6 py-3 font-normal"
                                        >
                                            Salary Slip
                                        </th>
                                        <th
                                            scope="col"
                                            className="px-6 py-3 font-normal"
                                        ></th>
                                        <th
                                            scope="col"
                                            className="px-6 py-3 font-normal"
                                        ></th>
                                        <th
                                            scope="col"
                                            className="px-6 py-3 font-normal"
                                        >
                                            Month
                                        </th>
                                        <th
                                            scope="col"
                                            className="px-6 py-3 font-normal"
                                        >
                                            August-2024
                                        </th>
                                    </tr>
                                </thead>
                                <tbody className="">
                                    <tr className="bg-white p-2">
                                        <td className="whitespace-nowrap px-6 py-3 font-medium text-gray-900 dark:text-white">
                                            {"Employee Name"}
                                        </td>
                                        <td className="px-6 py-3">
                                            Gaurav Kachhawa
                                        </td>
                                        <td className="px-6 py-3 font-medium text-gray-900">
                                            Total Working Days
                                        </td>
                                        <td className="px-6 py-3">26</td>
                                        <td></td>
                                    </tr>
                                    <tr className="bg-white dark:border-gray-700 dark:bg-gray-800">
                                        <td className="whitespace-nowrap font-medium px-6 py-3 text-gray-900 dark:text-white">
                                            Employee ID
                                        </td>
                                        <td className="px-6 py-3">DF85</td>
                                        <td className="font-medium px-6 py-3 text-gray-900">
                                            No. of Working Days
                                        </td>
                                        <td className="px-6 py-3">24</td>
                                        <td></td>
                                    </tr>
                                    <tr className="bg-white dark:border-gray-700 dark:bg-gray-800">
                                        <td className="px-6 py-3 whitespace-nowrap font-medium text-gray-900 dark:text-white">
                                            Designation
                                        </td>
                                        <td className="px-6 py-3">
                                            IT Sales Executive
                                        </td>
                                        <td className="font-medium px-6 py-3 text-gray-900">
                                            Leaves
                                        </td>
                                        <td className="px-6 py-3">
                                            Present: 25
                                        </td>
                                        <td className="px-6 py-3">Absent:1</td>
                                    </tr>
                                    <tr className="bg-white dark:border-gray-700 dark:bg-gray-800">
                                        <td className="px-6 py-3 whitespace-nowrap font-medium text-gray-900 dark:text-white">
                                            PAN
                                        </td>
                                        <td className="px-6 py-3">
                                            ASC152500000
                                        </td>
                                        <td className="font-medium text-gray-900 px-6 py-3">
                                            Balance Leave
                                        </td>
                                        <td className="px-6 py-3">0</td>
                                        <td className="px-6 py-3">Absent:1</td>
                                    </tr>
                                    <tr className="bg-white dark:border-gray-700 dark:bg-gray-800">
                                        <td className="px-6 py-3 whitespace-nowrap font-medium text-gray-900 dark:text-white">
                                            Bank Account Number
                                        </td>
                                        <td className="px-6 py-3">
                                            XYZ785202458
                                        </td>
                                        <td className="font-medium px-6 py-3"></td>
                                        <td className="px-6 py-3"></td>
                                        <td className="px-6 py-3"></td>
                                    </tr>
                                    <tr className="bg-white dark:border-gray-700 dark:bg-gray-800">
                                        <td className="px-6 py-3 whitespace-nowrap font-medium text-gray-900 dark:text-white">
                                            IFSC Code
                                        </td>
                                        <td className="px-6 py-3">
                                            hi452012SBI
                                        </td>
                                        <td className="font-medium"></td>
                                        <td></td>
                                        <td></td>
                                    </tr>
                                    <tr className="text-sm text-white bg-blue-500 dark:bg-gray-700 dark:text-gray-400">
                                        <th
                                            scope="col"
                                            className="px-6 py-3"
                                        ></th>
                                        <th
                                            scope="col"
                                            className="px-6 py-3"
                                        ></th>
                                        <th
                                            scope="col"
                                            className="px-6 py-3"
                                        ></th>
                                        <th
                                            scope="col"
                                            className="px-6 py-3 font-normal"
                                        >
                                            Deductions
                                        </th>
                                        <th
                                            scope="col"
                                            className="px-6 py-3"
                                        ></th>
                                    </tr>

                                    <tr className="text-sm text-white bg-slate-400 dark:bg-gray-700 dark:text-gray-400">
                                        <th
                                            scope="col"
                                            className="px-6 py-3 font-normal"
                                        >
                                            Particulars
                                        </th>
                                        <th
                                            scope="col"
                                            className="px-6 py-3 font-normal"
                                        ></th>
                                        <th
                                            scope="col"
                                            className="px-6 py-3 font-normal"
                                        >
                                            Particulars
                                        </th>
                                        <th
                                            scope="col"
                                            className="px-6 py-3 font-normal"
                                        >
                                            Amount
                                        </th>
                                        <th
                                            scope="col"
                                            className="px-6 py-3 font-normal"
                                        ></th>
                                    </tr>
                                    <tr className="bg-white p-2">
                                        <td className="whitespace-nowrap px-6 py-3 font-medium text-gray-900 dark:text-white">
                                            {"Basic Salary"}
                                        </td>
                                        <td className="px-6 py-3">₹ 15,000/</td>
                                        <td className="px-6 py-3 font-medium text-gray-900">
                                            Leave
                                        </td>
                                        <td className="px-6 py-3">₹ 0/</td>
                                        <td></td>
                                    </tr>
                                    <tr className="bg-white dark:border-gray-700 dark:bg-gray-800">
                                        <td className="whitespace-nowrap font-medium px-6 py-3 text-gray-900 dark:text-white">
                                            HRA
                                        </td>
                                        <td className="px-6 py-3">₹ 1,000/</td>
                                        <td className="font-medium px-6 py-3 text-gray-900">
                                            ESI
                                        </td>
                                        <td className="px-6 py-3">0</td>
                                        <td></td>
                                    </tr>
                                    <tr className="bg-white dark:border-gray-700 dark:bg-gray-800">
                                        <td className="px-6 py-3 whitespace-nowrap font-medium text-gray-900 dark:text-white">
                                            Other
                                        </td>
                                        <td className="px-6 py-3"></td>
                                        <td className="font-medium px-6 py-3 text-gray-900">
                                            PF
                                        </td>
                                        <td className="px-6 py-3">0</td>
                                        <td className="px-6 py-3"></td>
                                    </tr>
                                    <tr className="bg-white dark:border-gray-700 dark:bg-gray-800">
                                        <td className="px-6 py-3 whitespace-nowrap font-medium text-gray-900 dark:text-white">
                                            Incentive
                                        </td>
                                        <td className="px-6 py-3"></td>
                                        <td className="font-medium text-gray-900 px-6 py-3">
                                            Tax
                                        </td>
                                        <td className="px-6 py-3">0</td>
                                        <td className="px-6 py-3"></td>
                                    </tr>
                                    <tr className="text-sm text-white bg-blue-500 dark:bg-gray-700 dark:text-gray-400">
                                        <th
                                            scope="col"
                                            className="px-6 py-3"
                                        ></th>
                                        <th
                                            scope="col"
                                            className="px-6 py-3 font-normal"
                                        >
                                            Net Salary
                                        </th>
                                        <th
                                            scope="col"
                                            className="px-6 py-3"
                                        ></th>
                                        <th
                                            scope="col"
                                            className="px-6 py-3"
                                        ></th>
                                        <th
                                            scope="col"
                                            className="px-6 py-3"
                                        ></th>
                                    </tr>
                                </tbody>
                            </table>
                            <div className="p-3 text-center texr-sm">
                                This is a computer generated document, no need
                                for a signature.
                            </div>
                        </div>
                        <div className="float-end p-2 py-4">
                            <Button
                                size="xs"
                                theme={button_custom}
                                color="blue"
                            >
                                <div className="flex gap-1 items-center">
                                    <IoArrowDownOutline />
                                    Download
                                </div>
                            </Button>
                        </div>
                    </div>
                </DrawerItems>
            </Drawer>
        </>
    );
}



