import { Head } from "@inertiajs/react";
import $ from "jquery";
import { Button, ButtonGroup, Table, Checkbox, TableHead, TableHeadCell, TableBody, TableRow, TableCell } from "flowbite-react";
import Details from "./Details";
import { TbColumns3 } from "react-icons/tb";
import { PiExport, PiExportBold } from "react-icons/pi";
import { useState } from "react";
import {
    MdOutlineContactPage,
    MdOutlineModeEditOutline,
    MdOutlineNotificationImportant,
    MdOutlineNotificationsOff,
    MdOutlineRemoveRedEye,
} from "react-icons/md";

import Main from "@/Layouts/Main";
import SideMenu from "./SideMenu";
import { button_custom, table_custom } from "@/Pages/Helpers/DesignHelper";
import { IoMdNotificationsOutline } from "react-icons/io";
import { RiDeleteBin6Line } from "react-icons/ri";

function Index() {
    const [isCheckAll, setIsCheckAll] = useState(false);
    const [switch2, setSwitch2] = useState(false);

    // ----------------Edit --------------------
    const [isOpen, setIsOpen] = useState(false);
    const handleClose = () => setIsOpen(false);
    // ----------------Edit --------------------
    const [isOpenDocuments, setIsOpenDocuments] = useState(false);
    const handleCloseDocuments = () => setIsOpenDocuments(false);

    const customTheme = {
        root: {
            position: {
                right: {
                    on: "right-0 top-0 h-screen w-[70vw] transform-none bg-slate-100",
                    off: "right-0 top-0 h-screen w-80 translate-x-full",
                },
            },
        },
        header: {
            inner: {
                closeButton:
                    "absolute end-2.5 top-2.5 flex h-8 w-8 items-center justify-center rounded-lg bg-transparent text-sm text-gray-400 hover:bg-gray-200 hover:text-gray-900 dark:hover:bg-gray-600 dark:hover:text-white",
                closeIcon: "h-4 w-4",
                titleIcon: "me-2.5 h-6 w-6 text-gray-400",
                titleText:
                    "mb-4 inline-flex items-center text-lg font-semibold text-sky-950 dark:text-gray-400",
            },
        },
    };
    const ToggleChild = (parent, child) => {
        // console.log(parent);
        var parentEle = $("#" + parent);
        var ele = $("#" + child);
        if (ele.hasClass("hidden")) {
            parentEle.addClass("rotate-180");
            ele.removeClass("hidden");
        } else {
            parentEle.removeClass("rotate-180");
            ele.addClass("hidden");
        }
    };

    // const ThTdPaddings = "px-2 py-1 text-nowrap";
    return (
        <Main>
            <Head title="User" />

            <div className="relative overflow-hidden p-2">
                <div className="grid grid-cols-12 md:grid-cols-12 sm:grid-cols-1 gap-2 grid-flow-row-dense bg-slate-100 p-0 border-none">
                    <div className="lg:col-span-2 md:col-span-3 sm:col-span-3 lg:flex hidden">
                        <div className="bg-white shadow-sm rounded-lg border dark:bg-gray-900 w-full overflow-auto min-h-[90vh] max-h-[90vh]">
                            <SideMenu />
                        </div>
                    </div>

                    <div className="dark:text-gray-400 lg:p-0 pt-0 lg:col-span-10 md:col-span-12 col-span-full relative">
                        <div className="min-h-[80vh] max-h-[80vh] bg-white border rounded-lg">
                            <div className="flex justify-between p-2">
                                <div className="flex items-center gap-1">
                                    <ButtonGroup>
                                        <Button
                                            theme={button_custom}
                                            size="xs"
                                            color="gray"
                                        >
                                            <div className="flex items-center gap-1">
                                                <RiDeleteBin6Line className="text-slate-500" />
                                                <span className="text-xs">
                                                    Delete
                                                </span>
                                            </div>
                                        </Button>

                                        <Button
                                            theme={button_custom}
                                            size="xs"
                                            color="gray"
                                            id="dropdownInformationButton"
                                            data-dropdown-toggle="dropdownNotification"
                                            type="button"
                                        >
                                            <div className="flex items-center gap-1">
                                                <TbColumns3 className="text-slate-500" />
                                                <span className="text-xs">
                                                    Columns
                                                </span>
                                            </div>
                                        </Button>
                                    </ButtonGroup>
                                    <Button
                                        theme={button_custom}
                                        size="xs"
                                        color="gray"
                                    >
                                        <div className="flex items-center gap-1">
                                            <PiExportBold className="text-slate-500" />
                                            <span className="text-xs">
                                                Export
                                            </span>
                                        </div>
                                    </Button>
                                </div>
                            </div>

                            <div className="overflow-x-auto bg-white border rounded-lg">
                                <Table hoverable theme={table_custom}>
                                    <TableHead className="bg-slate-100">
                                        <TableHeadCell>
                                            <Checkbox color="blue"
                                                checked={isCheckAll}
                                                onChange={() =>
                                                    setIsCheckAll(!isCheckAll)
                                                }
                                            />
                                        </TableHeadCell>

                                        <TableHeadCell>
                                            <div className="flex items-center justify-between">
                                                <h3>Activation</h3>
                                                <svg
                                                    xmlns="http://www.w3.org/2000/svg"
                                                    height="16px"
                                                    viewBox="0 0 24 24"
                                                    width="16px"
                                                    className="fill-gray-600"
                                                >
                                                    <path
                                                        d="M0 0h24v24H0V0z"
                                                        fill="none"
                                                    />
                                                    <path d="M12 5.83L15.17 9l1.41-1.41L12 3 7.41 7.59 8.83 9 12 5.83zm0 12.34L8.83 15l-1.41 1.41L12 21l4.59-4.59L15.17 15 12 18.17z" />
                                                </svg>
                                            </div>
                                        </TableHeadCell>

                                        <TableHeadCell>Customer</TableHeadCell>

                                        <TableHeadCell>
                                            <div className="flex items-center justify-between">
                                                <h3>Services</h3>
                                                <svg
                                                    xmlns="http://www.w3.org/2000/svg"
                                                    height="16px"
                                                    viewBox="0 0 24 24"
                                                    width="16px"
                                                    className="fill-gray-600"
                                                >
                                                    <path
                                                        d="M0 0h24v24H0V0z"
                                                        fill="none"
                                                    />
                                                    <path d="M12 5.83L15.17 9l1.41-1.41L12 3 7.41 7.59 8.83 9 12 5.83zm0 12.34L8.83 15l-1.41 1.41L12 21l4.59-4.59L15.17 15 12 18.17z" />
                                                </svg>
                                            </div>
                                        </TableHeadCell>

                                        <TableHeadCell>
                                            <h3>Expiry Date</h3>
                                        </TableHeadCell>


                                    </TableHead>

                                    <TableBody className="divide-y">
                                        <TableRow className="">
                                            <TableCell>
                                                <Checkbox color="blue" className="rowCheckBox" />
                                            </TableCell>
                                            <TableCell className="whitespace-nowrap dark:text-white">
                                                <div className="flex justify-between ">
                                                    <div className="flex flex-col">
                                                        <div className="text-blue-500 text-sm ">
                                                            12/08/24
                                                        </div>
                                                        <div className="text-xs">
                                                            2:24 PM
                                                        </div>
                                                    </div>
                                                </div>
                                            </TableCell>
                                            <TableCell className="whitespace-nowrap dark:text-white">
                                                <div className="flex justify-between ">
                                                    <div className="flex flex-col">
                                                        <div className="text-blue-500 text-sm ">
                                                            Kanhaiya Lal (5620)
                                                        </div>
                                                        <div className="text-xs">
                                                            Dunes Factory Pvt. Ltd.
                                                        </div>
                                                    </div>
                                                </div>
                                            </TableCell>
                                            <TableCell className="whitespace-nowrap dark:text-white">
                                                <div className="flex justify-between ">
                                                    <div className="flex items-center gap-2">
                                                        <div className="text-blue-500 text-sm ">
                                                            Email Marketing
                                                        </div>
                                                        <div className="flex items-center gap-1">
                                                            <Button theme={button_custom} size="xxs" color="blue">
                                                                <div className="flex items-center gap-1">
                                                                    <IoMdNotificationsOutline className="text-base" />
                                                                    <span>
                                                                        2
                                                                    </span>
                                                                </div>
                                                            </Button>
                                                            <Button theme={button_custom} size="xxs" color="orange">
                                                                <div className="flex items-center gap-1">
                                                                    <MdOutlineNotificationImportant className="text-base" />
                                                                    <span>
                                                                        2
                                                                    </span>
                                                                </div>
                                                            </Button>
                                                            <Button theme={button_custom} size="xxs" color="failure">
                                                                <div className="flex items-center gap-1">
                                                                    <MdOutlineNotificationsOff className="text-base" />
                                                                    <span>
                                                                        2
                                                                    </span>
                                                                </div>
                                                            </Button>
                                                        </div>
                                                    </div>
                                                </div>
                                            </TableCell>
                                            <TableCell className="text-nowrap">
                                                <div className="flex gap-2 justify-between">
                                                    <div className="flex justify-between ">
                                                        <div className="flex flex-col">
                                                            <div className="text-red-500 text-sm ">
                                                                12/08/24
                                                            </div>
                                                            <div className="text-xs">
                                                                2:24 PM
                                                            </div>
                                                        </div>
                                                    </div>
                                                    <div className="flex gap-2 h-fit">
                                                        {/* <Tooltip
                                                            content="Documents"
                                                            className="bg-slate-700 p-1 px-2"
                                                        > */}
                                                        <Button
                                                            theme={
                                                                button_custom
                                                            }
                                                            color="Fuchsia_custom"
                                                            size="xs"
                                                            onClick={() =>
                                                                setIsOpenDocuments(
                                                                    true
                                                                )
                                                            }
                                                        >
                                                            <div className="flex items-center gap-1">
                                                                <MdOutlineContactPage />
                                                                <span className="text-xs">DWID Reset</span>
                                                            </div>
                                                        </Button>
                                                        {/* </Tooltip> */}
                                                        <Button
                                                            theme={
                                                                button_custom
                                                            }
                                                            color="blue"
                                                            size="xs"
                                                            onClick={() =>
                                                                setIsOpenDocuments(
                                                                    true
                                                                )
                                                            }
                                                        >
                                                            <div className="flex items-center gap-1">
                                                                <PiExport />
                                                                <span className="text-xs">Product Upgrade</span>
                                                            </div>
                                                        </Button>
                                                        <Button
                                                            theme={
                                                                button_custom
                                                            }
                                                            color="green"
                                                            size="xs"
                                                            onClick={() =>
                                                                setIsOpenDocuments(
                                                                    true
                                                                )
                                                            }
                                                        >
                                                            <div className="flex items-center gap-1">
                                                                <MdOutlineRemoveRedEye />
                                                                <span className="text-xs">View</span>
                                                            </div>
                                                        </Button>
                                                        <Button
                                                            theme={
                                                                button_custom
                                                            }
                                                            color="blue"
                                                            size="xs"
                                                            onClick={() =>
                                                                setIsOpenDocuments(
                                                                    true
                                                                )
                                                            }
                                                        >
                                                            <div className="flex items-center gap-1">
                                                                <MdOutlineModeEditOutline />
                                                                <span className="text-xs">Edit</span>
                                                            </div>
                                                        </Button>
                                                    </div>
                                                    <div>
                                                        <button
                                                            className="p-1.5"
                                                            id="parentID"
                                                            onClick={() =>
                                                                ToggleChild(
                                                                    "parentID",
                                                                    "childTr"
                                                                )
                                                            }
                                                        >
                                                            <svg
                                                                xmlns="http://www.w3.org/2000/svg"
                                                                height="23px"
                                                                viewBox="0 0 24 24"
                                                                width="23px"
                                                                className="fill-gray-500"
                                                            >
                                                                <path
                                                                    d="M0 0h24v24H0V0z"
                                                                    fill="none"
                                                                />
                                                                <path d="M7.41 8.59 12 13.17l4.59-4.58L18 10l-6 6-6-6 1.41-1.41z" />
                                                            </svg>
                                                        </button>
                                                    </div>
                                                </div>
                                            </TableCell>
                                        </TableRow>
                                        <TableRow
                                            className="hidden bg-gray-100"
                                            id="childTr"
                                        >
                                            <TableCell colSpan={9}>
                                                <Details></Details>
                                            </TableCell>
                                        </TableRow>
                                    </TableBody>
                                </Table>
                            </div>
                        </div>
                        <div className="float-end border absolute bottom-0 rounded-b-lg p-3 w-full bg-white ">
                            <div className="flex flex-wrap justify-between lg:gap-0 md:gap-0 gap-2">
                                <div className="flex gap-3 items-center">
                                    <div className="text-gray-400 text-sm ">
                                        Showing 1 to 25 of 62 entries
                                    </div>
                                    <div>
                                        <select
                                            id="countries"
                                            className="text-xs bg-gray-50 border border-gray-300 text-gray-900 rounded focus:ring-blue-500 focus:border-blue-500 block p-1 ps-2 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-blue-500 dark:focus:border-blue-500"
                                        >
                                            <option
                                                value={10}
                                                defaultValue={10}
                                            >
                                                10
                                            </option>
                                            <option value={15}>15</option>
                                            <option value={20}>20</option>
                                            <option value={25}>25</option>
                                            <option value={30}>30</option>
                                        </select>
                                    </div>
                                </div>
                                <div className="flex">
                                    <Button
                                        size="xs"
                                        color="gray"
                                        className="border-e-0 text-gray-400 px-2 rounded text-xs "
                                    >
                                        Previous
                                    </Button>
                                    <Button
                                        size="xs"
                                        color="blue"
                                        className="border text-white border-blue-600 bg-blue-600 px-2.5 rounded-none text-sm "
                                    >
                                        1
                                    </Button>
                                    <Button
                                        size="xs"
                                        color="gray"
                                        className="border text-blue-600 px-2.5 rounded-none text-xs "
                                    >
                                        2
                                    </Button>
                                    <Button
                                        size="xs"
                                        color="gray"
                                        className="border-s-0 text-blue-600 px-2 rounded text-xs "
                                    >
                                        Next
                                    </Button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </Main>
    );
}

export default Index;

