import useFetch from "@/Global/useFetch";
import { button_custom } from "@/Pages/Helpers/DesignHelper";
import { Link } from "@inertiajs/react";
import { Badge, Button, Dropdown, DropdownItem, Modal } from "flowbite-react";
import { lazy, useState, useMemo } from "react";
import { BiChevronLeft } from "react-icons/bi";
import { CiEdit } from "react-icons/ci";
import { LuMailOpen } from "react-icons/lu";
import { MdBlockFlipped, MdEdit, MdOutlineMarkEmailRead, MdOutlineMoveToInbox } from "react-icons/md";
import { RiSendPlane2Line } from "react-icons/ri";
const Compose = lazy(() => import('./Compose'));



const dropdownTheme = {
    "floating": {
        "target": "w-full"
    },

}


function SideMenu({ currentPageRoute = route().current() }) {

    const queryParams = (route().queryParams);
    const routeParam = (route().params);

    // console.log(routeParam.type == 'all');



    const [openComposeModal, setOpenComposeModal] = useState(false);

    const { data: gates, loading: gatesLoading, error } = useFetch(route('mail.gateways.index', { all: true }));

    const menuDropdown = useMemo(() => ({
        arrowIcon: "ml-2 h-4 w-4",
        content: "py-1 focus:outline-none ",
        floating: {
            animation: "transition-opacity",
            arrow: {
                base: "absolute z-10 h-2 w-2 rotate-45",
                style: {
                    dark: "bg-gray-900 dark:bg-gray-700",
                    light: "bg-white",
                    auto: "bg-white dark:bg-gray-700",
                },
                placement: "-4px",
            },
            base: "z-10  divide-y divide-gray-100 rounded shadow focus:outline-none w-full",
            content: "py-1 text-sm text-gray-700 dark:text-gray-200",
            divider: "my-1 h-px bg-gray-100 dark:bg-gray-600",
            header: "block px-4 py-2 text-sm text-gray-700 dark:text-gray-200",
            hidden: "invisible opacity-0",
            item: {
                container: "w-full",
                base: "flex w-full cursor-pointer items-center justify-start px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 hover:bg-gray-100 focus:outline-none dark:text-gray-200 dark:hover:bg-gray-600 dark:hover:text-white dark:focus:bg-gray-600 dark:focus:text-white",
                icon: "mr-2 h-4 w-4",
            },
            style: {
                dark: "bg-gray-900 text-white dark:bg-gray-700",
                light: "border border-gray-200 bg-white text-gray-900",
                auto: "border border-gray-200 bg-white text-gray-900 dark:border-none dark:bg-gray-700 dark:text-white",
            },
            target: "w-full ",
        },
        inlineWrapper: "flex items-center",
    }), []);

    const items = useMemo(() => [
        {
            name: "All",
            url: route("mail.inbound.index", { type: 'all' }),
            active: (routeParam.type == 'all') ? "mail.inbound.index" : "",
            icon: <LuMailOpen className="text-blue-500" />,
            classes: {
                groupHover: " group-hover:text-blue-500 ",
                group: " hover:bg-blue-50 ",
                active: " bg-blue-50 ",
            },
        },


        {
            name: "Inbox",
            url: route("mail.inbound.index"),
            active: (route().current('mail.inbound.index') && routeParam.type == undefined) ? "mail.inbound.index" : "",
            icon: <MdOutlineMarkEmailRead className="text-green-500" />,
            classes: {
                groupHover: "group-hover:text-green-500 ",
                group: " hover:bg-green-50 ",
                active: " bg-green-50 ",
            },
        },

        {
            name: "Sent",
            url: route("mail.sendmail.index"),
            active: "mail.sendmail.index",
            icon: <RiSendPlane2Line className="text-cyan-500" />,
            classes: {
                groupHover: " group-hover:text-cyan-500 ",
                group: " hover:bg-cyan-100 ",
                active: " bg-cyan-50 ",
            },
        },

        {
            name: "Spam",
            url: route("mail.inbound.index", { type: 'spam' }),
            active: (routeParam.type == 'spam') ? "mail.inbound.index" : "",
            icon: <MdBlockFlipped className="text-red-500" />,
            classes: {
                groupHover: " group-hover:text-red-500 ",
                group: " hover:bg-red-50 ",
                active: " bg-red-50 ",
            },
        },

        {
            name: "Archive",
            url: route("mail.inbound.index", { type: 'archived' }),
            active: (routeParam.type == 'archived') ? "mail.inbound.index" : "",
            icon: <MdOutlineMoveToInbox className="text-fuchsia-500" />,
            classes: {
                groupHover: " group-hover:text-fuchsia-500 ",
                group: " hover:bg-fuchsia-50 ",
                active: " bg-fuchsia-50 ",
            },
        },
    ], []);

    return (
        <div className="w-full pb-4 bg-white border rounded-lg h-fit">
            <div className="flex p-2">
                <Button theme={button_custom} size="xxs" color="Fuchsia_custom"
                    as={Link} href={route(route().current("mail.inbound.index") ? "mail.dashboard" : "mail.inbound.index")}
                >
                    <div className="flex items-center">
                        <BiChevronLeft className="text-xl" />
                        <span>Back</span>
                    </div>
                </Button>
            </div>
            <div className="flex flex-col gap-3 p-2">
                <Button className="w-full text-white rounded-lg bg-gradient-to-r from-blue-500 via-blue-600 to-blue-700 hover:bg-gradient-to-br focus:ring-blue-300 dark:focus:ring-blue-800"
                    theme={button_custom} size="xs"
                    as={Link}
                    href={route('mail.compose')}
                >
                    <div className="flex items-center gap-1 text-base">
                        <MdEdit />
                        <span>Compose</span>
                    </div>
                </Button>


                <Dropdown label="Dropdown button" color="blue"
                    theme={dropdownTheme}
                    size="sm"
                    isProcessing={gatesLoading}>
                    {gates?.collection?.gateways.map((gw, kye) => {
                        return (<DropdownItem as={Link} href={route(currentPageRoute, { gateway: gw.id })}>{gw.name}</DropdownItem>)
                    })}
                </Dropdown>
            </div>
            <div className="lg:hidden md:hidden">
                <Dropdown
                    label="Menu"
                    size="sm"
                    className="w-full"
                    theme={menuDropdown}
                    color="light"
                >
                    {items.map((item, k) => (
                        <Dropdown.Item
                            key={`navItems-${k}`}
                            as={Link}
                            href={item.url}
                        >
                            {item.name}
                        </Dropdown.Item>
                    ))}
                </Dropdown>
            </div>
            <div className="hidden w-full lg:block md:block">
                {items.map((item, k) => (
                    <Link href={item.url} key={`items-${k}`}>
                        <div
                            className={`border-b hover:border-transparent p-1.5 px-2 flex items-center justify-between gap-3  rounded rounded-b-none hover:rounded-b group ${item.classes.group} ${(route().current() == item.active) ? item.classes.active : " bg-white "}`}
                        >
                            <div className="flex items-center gap-3">
                                {item.icon}
                                <span className={item.classes.groupHover}>
                                    {item.name}
                                </span>
                            </div>
                            <Badge>5</Badge>
                        </div>
                    </Link>
                ))}
            </div>
            {openComposeModal &&
                <Modal
                    size="5xl"
                    show={openComposeModal}
                    onClose={() => setOpenComposeModal(false)}
                >
                    <Modal.Header className="p-2">
                        <div className="flex items-center gap-2">
                            <CiEdit />
                            <h4 className="text-lg">Compose</h4>
                        </div>
                    </Modal.Header>
                    <Modal.Body className="px-4 py-3 rounded-b-lg">
                        <Compose onClose={() => setOpenComposeModal(false)} />
                    </Modal.Body>
                </Modal>
            }
        </div>
    );
}

export default SideMenu;
