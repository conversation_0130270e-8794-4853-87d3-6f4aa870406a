import Main from "@/Layouts/Main";
import React, { useState } from "react";
import TabBar from "../TabBar";
import {
    Button,
    ButtonGroup,
    Checkbox,
    Drawer,
    Table,
} from "flowbite-react";
import {
    button_custom,
    customTheme_drawer,
    table_custom,
} from "@/Pages/Helpers/DesignHelper";
import {
    MdDeleteOutline,
    MdOutlinePreview,
} from "react-icons/md";
import { TruncatedCell } from "@/Components/ExpandMsg";
import { HiOutlineViewColumns } from "react-icons/hi2";
import { PiClockCountdownBold } from "react-icons/pi";
import ReviewMail from "./ReviewMail";

export default function Inbox() {
    const [isAddOpen, setIsAddOpen] = useState(false);
    const handleAddClose = () => setIsAddOpen(false);
    return (
        <Main>
            <div className="p-2 overflow-hidden">
                <TabBar />
                <div
                    className="grid grid-cols-12 gap-2 p-0 border-none grid-2 flow-row-dense md:grid-cols-12 sm:grid-cols-1 
                bg-slate-100 mt-2"
                >
                    <div className="relative pt-0 dark:text-gray-400 lg:p-0  col-span-full">
                        <div className=" rounded-md  h-full">
                            <div className="min-h-[80vh] max-h-[80vh] bg-white border rounded-lg">
                                <div className="flex justify-between p-2">
                                    <div className="flex gap-2">
                                        <ButtonGroup>
                                            <Button
                                                className="ps-1"
                                                size="xs"
                                                color="gray"
                                                theme={button_custom}
                                            >
                                                <div className="flex items-center gap-1">
                                                    <MdDeleteOutline className="text-slate-400" />
                                                    <span className="text-xs">
                                                        Delete
                                                    </span>
                                                </div>
                                            </Button>
                                            <Button
                                                className="ps-1"
                                                size="xs"
                                                color="gray"
                                                theme={button_custom}
                                            >
                                                <div className="flex items-center gap-1">
                                                    <HiOutlineViewColumns className="text-slate-400" />
                                                    <span className="text-xs">
                                                        Columns
                                                    </span>
                                                </div>
                                            </Button>
                                        </ButtonGroup>
                                    </div>
                                    <div className="">
                                        {/* <Button
                                            theme={button_custom}
                                            size="xs"
                                            color="gray"
                                            type="button"
                                            onClick={() => setOpenModal(true)}
                                        >
                                            <div className="flex items-center gap-1">
                                                <CiEdit className="text-slate-500" />
                                                <span className="text-xs">
                                                    Compose
                                                </span>
                                            </div>
                                        </Button> */}
                                    </div>
                                </div>

                                <div className="overflow-x-auto bg-white border rounded-lg">
                                    <Table hoverable theme={table_custom}>
                                        <Table.Head className="bg-slate-100">
                                            <Table.HeadCell className="w-8">
                                                <Checkbox
                                                    color="blue"
                                                    // checked={isCheckAll}
                                                    // onChange={() =>
                                                    //     setIsCheckAll(
                                                    //         !isCheckAll
                                                    //     )
                                                    // }
                                                />
                                            </Table.HeadCell>
                                            <Table.HeadCell>
                                                <div className="flex items-center justify-between gap-2">
                                                    Date & Time
                                                    <svg
                                                        xmlns="http://www.w3.org/2000/svg"
                                                        height="16px"
                                                        viewBox="0 0 24 24"
                                                        width="16px"
                                                        className="fill-gray-600"
                                                    >
                                                        <path
                                                            d="M0 0h24v24H0V0z"
                                                            fill="none"
                                                        />
                                                        <path d="M12 5.83L15.17 9l1.41-1.41L12 3 7.41 7.59 8.83 9 12 5.83zm0 12.34L8.83 15l-1.41 1.41L12 21l4.59-4.59L15.17 15 12 18.17z" />
                                                    </svg>
                                                </div>
                                            </Table.HeadCell>
                                            <Table.HeadCell>
                                                <div className="flex items-center justify-between gap-2">
                                                    Name
                                                    <svg
                                                        xmlns="http://www.w3.org/2000/svg"
                                                        height="16px"
                                                        viewBox="0 0 24 24"
                                                        width="16px"
                                                        className="fill-gray-600"
                                                    >
                                                        <path
                                                            d="M0 0h24v24H0V0z"
                                                            fill="none"
                                                        />
                                                        <path d="M12 5.83L15.17 9l1.41-1.41L12 3 7.41 7.59 8.83 9 12 5.83zm0 12.34L8.83 15l-1.41 1.41L12 21l4.59-4.59L15.17 15 12 18.17z" />
                                                    </svg>
                                                </div>
                                            </Table.HeadCell>
                                            <Table.HeadCell>
                                                Mails
                                            </Table.HeadCell>
                                            <Table.HeadCell>
                                                Actions
                                            </Table.HeadCell>
                                        </Table.Head>
                                        <Table.Body className="divide-y">
                                            <Table.Row className="bg-white dark:border-gray-700 dark:bg-gray-800">
                                                <Table.Cell>
                                                    <div className="flex items-center">
                                                        <Checkbox
                                                            color="blue"
                                                            className="rowCheckBox"
                                                        />
                                                    </div>
                                                </Table.Cell>
                                                <Table.Cell className="font-medium text-gray-900 whitespace-nowrap dark:text-white">
                                                    <div className="flex justify-between ">
                                                        <div className="flex flex-col w-20">
                                                            <div className="text-blue-500 text-sm ">
                                                                12/08/24
                                                            </div>
                                                            <div className="text-xs">
                                                                2:24 PM
                                                            </div>
                                                        </div>
                                                    </div>
                                                </Table.Cell>
                                                <Table.Cell>
                                                    <div className="flex justify-between ">
                                                        <div className="">
                                                            <div className=" text-sm font-medium flex items-center gap-2">
                                                                <span>
                                                                    Rudra
                                                                    Choudhary
                                                                </span>
                                                                <PiClockCountdownBold className="text-red-600 text-lg" />
                                                            </div>
                                                            <div className="text-xs">
                                                                <EMAIL>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </Table.Cell>
                                                <Table.Cell className="max-w-40">
                                                    <TruncatedCell
                                                        content={
                                                            " Welcome to RapBooster - your  ultimate partner in supercharging gregdfgdg ggr ret reret re ret retre tre tgffdgdfgd fdgfhhtrrtry yyeryry eryreye ettututru rututuurt..."
                                                        }
                                                    />
                                                </Table.Cell>
                                                <Table.Cell>
                                                    <div className="flex items-center gap-2">
                                                        <Button
                                                            // as={Link}
                                                            // href={route(
                                                            //     "mail.mails.openmail"
                                                            // )}
                                                            theme={
                                                                button_custom
                                                            }
                                                            onClick={() =>
                                                                setIsAddOpen(true)
                                                            }
                                                            size="xs"
                                                            color="green"
                                                            type="button"
                                                        >
                                                            <div className="flex items-center gap-1">
                                                                <MdOutlinePreview className="" />
                                                                <span className="text-xs">
                                                                    Review
                                                                </span>
                                                            </div>
                                                        </Button>
                                                    </div>
                                                </Table.Cell>
                                            </Table.Row>
                                        </Table.Body>
                                    </Table>
                                </div>
                            </div>
                            <div className="absolute bottom-0 w-full p-3 bg-white border rounded-b-lg float-end">
                                <div className="flex flex-wrap justify-between gap-2 lg:gap-0 md:gap-0">
                                    <div className="flex items-center gap-3">
                                        <div className="text-sm text-gray-400 ">
                                            Showing 1 to 25 of 62 entries
                                        </div>
                                        <div>
                                            <select
                                                id="countries"
                                                className="block p-1 text-xs text-gray-900 border border-gray-300 rounded bg-gray-50 focus:ring-blue-500 focus:border-blue-500 ps-2 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-blue-500 dark:focus:border-blue-500"
                                            >
                                                <option
                                                    value={10}
                                                    defaultValue={10}
                                                >
                                                    10
                                                </option>
                                                <option value={15}>15</option>
                                                <option value={20}>20</option>
                                                <option value={25}>25</option>
                                                <option value={30}>30</option>
                                            </select>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <Drawer
                    theme={customTheme_drawer}
                    open={isAddOpen}
                    onClose={handleAddClose}
                    position="right"
                    className="w-full xl:w-4/5 lg:w-4/5 md:w-4/5"
                >
                    <DrawerHeader
                        title="Review Mail"
                        titleIcon={MdOutlinePreview}
                    />
                    <DrawerItems>
                        <ReviewMail></ReviewMail>
                    </DrawerItems>
                </Drawer>
            </div>
        </Main>
    );
}
