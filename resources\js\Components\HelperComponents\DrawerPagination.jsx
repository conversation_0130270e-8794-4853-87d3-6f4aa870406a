import { Button, ButtonGroup } from "flowbite-react";
import {
    FaChevronLeft,
    FaChevronRight,
    FaAnglesLeft,
    FaAnglesRight,
} from "react-icons/fa6";
import { useMemo, useState } from "react";
import { pagination_button_theme, pagination_buttongroup_theme } from "@/Pages/Helpers/DesignHelper";

function DrawerPagination({ tableData, setData }) {
    const [isLoading, setIsLoading] = useState(false);
    const paginationData = tableData?.links || [];

    const result = useMemo(() => {
        const getObjectsAroundActive = (objects, activeProperty) => {
            if (!objects || !Array.isArray(objects)) return [];
            const newOne = objects.slice(1, -1);
            const activeIndex = newOne.findIndex((obj) => obj && obj[activeProperty]);
            if (activeIndex === -1) return [];

            const startIndex = Math.max(activeIndex - 2, 0);
            const endIndex = Math.min(activeIndex + 3, newOne.length);

            return newOne.slice(startIndex, endIndex);
        };

        return getObjectsAroundActive(paginationData, "active");
    }, [paginationData]);

    const handleClick = (url) => {
        if (!url || isLoading) return;
        setIsLoading(true);
        fetch(url, {
            headers: {
                "Content-Type": "application/json",
                "Accept": "application/json",
            },
        })
            .then((response) => response.json())
            .then((data) => {
                setData(data);
            })
            .catch((error) => {
                console.error("Error fetching data:", error);
            })
            .finally(() => {
                setIsLoading(false);
            });
    };

    const renderPageButton = (lnk, k) => {
        if (!lnk || !lnk.label) return null;
        return (
            <Button
                key={`paginationBtn${k}`}
                color={lnk.active ? "blue" : "gray"}
                size="xs"
                onClick={() => handleClick(lnk.url)}
                disabled={!lnk.url || isLoading || lnk.active}
                theme={pagination_button_theme}
            >
                <div
                    className="px-1 text-xs h-fit border-e-0 rounded-e-0 text-nowrap"
                    dangerouslySetInnerHTML={{ __html: lnk.label }}
                />
            </Button>
        );
    };

    return (
        <div className="flex items-center overflow-auto">
            <ButtonGroup theme={pagination_buttongroup_theme}>
                <Button
                    size="xs"
                    color="gray"
                    className="focus:ring-0"
                    onClick={() => handleClick(tableData.first_page_url)}
                    disabled={!tableData?.first_page_url || tableData?.current_page === 1 || isLoading}
                    theme={pagination_button_theme}
                >
                    <FaAnglesLeft />
                </Button>
                <Button
                    size="xs"
                    color="gray"
                    className="focus:ring-0"
                    onClick={() => handleClick(tableData.prev_page_url)}
                    disabled={!tableData?.prev_page_url || tableData?.current_page === 1 || isLoading}
                    theme={pagination_button_theme}
                >
                    <FaChevronLeft />
                </Button>
                {result.map(renderPageButton)}
                <Button
                    size="xs"
                    className="focus:ring-0"
                    color="gray"
                    onClick={() => handleClick(tableData?.next_page_url)}
                    disabled={
                        !tableData?.next_page_url ||
                        tableData?.current_page === tableData?.last_page ||
                        isLoading
                    }
                    theme={pagination_button_theme}
                >
                    <FaChevronRight />
                </Button>
                <Button
                    size="xs"
                    className="focus:ring-0"
                    color="gray"
                    onClick={() => handleClick(tableData?.last_page_url)}
                    disabled={
                        !tableData?.last_page_url ||
                        tableData?.current_page === tableData?.last_page ||
                        isLoading
                    }
                    theme={pagination_button_theme}
                >
                    <FaAnglesRight />
                </Button>
            </ButtonGroup>
        </div>
    );
}

export default DrawerPagination;
