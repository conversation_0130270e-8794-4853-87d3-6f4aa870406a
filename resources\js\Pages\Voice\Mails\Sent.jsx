import Main from "@/Layouts/Main";
import React, { useState } from "react";
import SideMenu from "./SideMenu";
import {
    Button,
    ButtonGroup,
    Checkbox,
    Modal,
    Table,
} from "flowbite-react";
import {
    button_custom,
    table_custom,
} from "@/Pages/Helpers/DesignHelper";
import { CiEdit } from "react-icons/ci";
import {
    MdAutorenew,
    MdBlock,
    MdOutlineMoveToInbox,
    MdOutlineRemoveRedEye,
} from "react-icons/md";
import { BiHide } from "react-icons/bi";
import { TiStarOutline } from "react-icons/ti";
import { RiDeleteBin6Line } from "react-icons/ri";
import Compose from "./Compose";
import { Link } from "@inertiajs/react";

export default function Sent() {
    const [openModal, setOpenModal] = useState(false);

    return (
        <>
            <div className="p-2 overflow-hidden">
                <div
                    className="grid grid-cols-12 gap-2 p-0 border-none grid-2 flow-row-dense md:grid-cols-12 sm:grid-cols-1 
                bg-slate-100 mt-2"
                >
                    <div className="hidden xl:col-span-2 lg:col-span-2 md:col-span-3 sm:col-span-3 lg:flex ">
                        <SideMenu />
                    </div>
                    <div className="relative pt-0 dark:text-gray-400 lg:p-0 xl:col-span-10 lg:col-span-10 md:col-span-12 col-span-full">
                        <div className=" rounded-md  h-full">
                            <div className="min-h-[80vh] max-h-[80vh] bg-white border rounded-lg">
                                <div className="flex justify-between p-2">
                                    <div className="flex gap-2">
                                        <Button
                                            className="ps-1"
                                            size="xs"
                                            color="gray"
                                            theme={button_custom}
                                        >
                                            <MdAutorenew className="text-slate-400" />
                                        </Button>
                                        <ButtonGroup>
                                            <Button
                                                theme={button_custom}
                                                size="xs"
                                                color="gray"
                                            // className="border-e-0 rounded-e-none"
                                            >
                                                <div className="flex items-center gap-1">
                                                    <BiHide className="text-slate-400" />
                                                    <span className="text-xs">
                                                        Mark Unread
                                                    </span>
                                                </div>
                                            </Button>
                                            <Button
                                                className="ps-1"
                                                size="xs"
                                                color="gray"
                                                theme={button_custom}
                                            >
                                                <div className="flex items-center gap-1">
                                                    <MdOutlineMoveToInbox className="text-slate-400" />
                                                    <span className="text-xs">
                                                        Archive
                                                    </span>
                                                </div>
                                            </Button>
                                            <Button
                                                className="ps-1"
                                                size="xs"
                                                color="gray"
                                                theme={button_custom}
                                            >
                                                <div className="flex items-center gap-1">
                                                    <TiStarOutline className="text-slate-400" />
                                                    <span className="text-xs">
                                                        Pinned
                                                    </span>
                                                </div>
                                            </Button>
                                            <Button
                                                className="ps-1"
                                                size="xs"
                                                color="gray"
                                                theme={button_custom}
                                            >
                                                <div className="flex items-center gap-1">
                                                    <MdBlock className="text-slate-400" />
                                                    <span className="text-xs">
                                                        Spam
                                                    </span>
                                                </div>
                                            </Button>
                                            <Button
                                                className="ps-1"
                                                size="xs"
                                                color="gray"
                                                theme={button_custom}
                                            >
                                                <div className="flex items-center gap-1">
                                                    <RiDeleteBin6Line className="text-slate-400" />
                                                    <span className="text-xs">
                                                        Delete
                                                    </span>
                                                </div>
                                            </Button>
                                        </ButtonGroup>
                                    </div>
                                    <div className="">
                                        <Button
                                            theme={button_custom}
                                            size="xs"
                                            color="gray"
                                            type="button"
                                            onClick={() => setOpenModal(true)}
                                        >
                                            <div className="flex items-center gap-1">
                                                <CiEdit className="text-slate-500" />
                                                <span className="text-xs">
                                                    Compose
                                                </span>
                                            </div>
                                        </Button>
                                    </div>
                                </div>

                                <div className="overflow-x-auto bg-white border rounded-lg">
                                    <Table hoverable theme={table_custom}>
                                        <TableHead className="bg-slate-100">
                                            <TableHeadCell className="w-8">
                                                <Checkbox
                                                    color="blue"
                                                // checked={isCheckAll}
                                                // onChange={() =>
                                                //     setIsCheckAll(
                                                //         !isCheckAll
                                                //     )
                                                // }
                                                />
                                            </TableHeadCell>
                                            <TableHeadCell>
                                                <div className="flex items-center justify-between gap-2">
                                                    Date & Time
                                                    <svg
                                                        xmlns="http://www.w3.org/2000/svg"
                                                        height="16px"
                                                        viewBox="0 0 24 24"
                                                        width="16px"
                                                        className="fill-gray-600"
                                                    >
                                                        <path
                                                            d="M0 0h24v24H0V0z"
                                                            fill="none"
                                                        />
                                                        <path d="M12 5.83L15.17 9l1.41-1.41L12 3 7.41 7.59 8.83 9 12 5.83zm0 12.34L8.83 15l-1.41 1.41L12 21l4.59-4.59L15.17 15 12 18.17z" />
                                                    </svg>
                                                </div>
                                            </TableHeadCell>
                                            <TableHeadCell>
                                                <div className="flex items-center justify-between gap-2">
                                                    Name
                                                    <svg
                                                        xmlns="http://www.w3.org/2000/svg"
                                                        height="16px"
                                                        viewBox="0 0 24 24"
                                                        width="16px"
                                                        className="fill-gray-600"
                                                    >
                                                        <path
                                                            d="M0 0h24v24H0V0z"
                                                            fill="none"
                                                        />
                                                        <path d="M12 5.83L15.17 9l1.41-1.41L12 3 7.41 7.59 8.83 9 12 5.83zm0 12.34L8.83 15l-1.41 1.41L12 21l4.59-4.59L15.17 15 12 18.17z" />
                                                    </svg>
                                                </div>
                                            </TableHeadCell>
                                            <TableHeadCell>
                                                Mails
                                            </TableHeadCell>
                                            <TableHeadCell>
                                                Actions
                                            </TableHeadCell>
                                        </TableHead>
                                        <TableBody className="divide-y">
                                            <TableRow className="bg-white dark:border-gray-700 dark:bg-gray-800">
                                                <TableCell>
                                                    <div className="flex items-center">
                                                        <Checkbox
                                                            color="blue"
                                                            className="rowCheckBox"
                                                        />
                                                        <Button
                                                            className="ps-1"
                                                            size="xs"
                                                            color="withoutBorder"
                                                            theme={
                                                                button_custom
                                                            }
                                                        >
                                                            <TiStarOutline className="text-slate-400" />
                                                        </Button>
                                                    </div>
                                                </TableCell>
                                                <TableCell className="font-medium text-gray-900 whitespace-nowrap dark:text-white">
                                                    <div className="flex justify-between ">
                                                        <div className="flex flex-col w-20">
                                                            <div className="text-blue-500 text-sm ">
                                                                12/08/24
                                                            </div>
                                                            <div className="text-xs">
                                                                2:24 PM
                                                            </div>
                                                        </div>
                                                    </div>
                                                </TableCell>
                                                <TableCell>
                                                    <div className="flex justify-between ">
                                                        <div className="">
                                                            <div className=" text-sm font-medium">
                                                                Rudra Choudhary
                                                            </div>
                                                            <div className="text-xs">
                                                                <EMAIL>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </TableCell>
                                                <TableCell>
                                                    Welcome to RapBooster - your
                                                    ultimate partner in
                                                    supercharging...
                                                </TableCell>
                                                <TableCell>
                                                    Gateway 1
                                                </TableCell>
                                            </TableRow>
                                        </TableBody>
                                    </Table>
                                </div>
                            </div>
                            <div className="absolute bottom-0 w-full p-3 bg-white border rounded-b-lg float-end">
                                <div className="flex flex-wrap justify-between gap-2 lg:gap-0 md:gap-0">
                                    <div className="flex items-center gap-3">
                                        <div className="text-sm text-gray-400 ">
                                            Showing 1 to 25 of 62 entries
                                        </div>
                                        <div>
                                            <select
                                                id="countries"
                                                className="block p-1 text-xs text-gray-900 border border-gray-300 rounded bg-gray-50 focus:ring-blue-500 focus:border-blue-500 ps-2 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-blue-500 dark:focus:border-blue-500"
                                            >
                                                <option
                                                    value={10}
                                                    defaultValue={10}
                                                >
                                                    10
                                                </option>
                                                <option value={15}>15</option>
                                                <option value={20}>20</option>
                                                <option value={25}>25</option>
                                                <option value={30}>30</option>
                                            </select>
                                        </div>
                                    </div>
                                    <div className="flex">
                                        <Button className="px-2 text-xs text-gray-400 border rounded ">
                                            Previous
                                        </Button>
                                        <Button className="border text-white border-blue-600 bg-blue-600 px-2.5 rounded-1 rounded-0 text-sm ">
                                            1
                                        </Button>
                                        <Button className="border text-blue-600 px-2.5 rounded-1 rounded-0 text-xs ">
                                            2
                                        </Button>
                                        <Button className="px-2 text-xs text-blue-600 border rounded ">
                                            Next
                                        </Button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <Modal
                    size="5xl"
                    show={openModal}
                    onClose={() => setOpenModal(false)}
                >
                    <ModalHeader className="bg-slate-100 p-2">
                        <div className="flex items-center gap-2">
                            <CiEdit />
                            <h4 className="text-lg">Compose</h4>
                        </div>
                    </ModalHeader>
                    <ModalBody className="px-4 py-3 rounded-b-lg bg-slate-100">
                        <Compose></Compose>
                    </ModalBody>
                </Modal>
            </div>
        </>
    );
}

