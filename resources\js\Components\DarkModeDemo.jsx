import React from 'react';
import { Card, Button, Badge } from 'flowbite-react';
import { useTheme } from '@/Contexts/ThemeContext';

const DarkModeDemo = () => {
    const { isDarkMode, theme } = useTheme();

    return (
        <div className="p-6 space-y-6">
            <div className="text-center">
                <h2 className="text-2xl font-bold text-gray-900 dark:text-white mb-2">
                    Dark Mode Demo
                </h2>
                <p className="text-gray-600 dark:text-gray-300">
                    Current theme: <Badge color={isDarkMode ? 'dark' : 'info'}>{theme}</Badge>
                </p>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <Card className="dark:bg-gray-800">
                    <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
                        Light Mode Features
                    </h3>
                    <ul className="space-y-2 text-gray-600 dark:text-gray-300">
                        <li>• Clean, bright interface</li>
                        <li>• Better for daytime use</li>
                        <li>• Traditional appearance</li>
                        <li>• High contrast text</li>
                    </ul>
                    <Button color="blue" size="sm">
                        Light Mode Button
                    </Button>
                </Card>

                <Card className="dark:bg-gray-800">
                    <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
                        Dark Mode Features
                    </h3>
                    <ul className="space-y-2 text-gray-600 dark:text-gray-300">
                        <li>• Reduced eye strain</li>
                        <li>• Better for low-light environments</li>
                        <li>• Modern appearance</li>
                        <li>• Battery saving on OLED screens</li>
                    </ul>
                    <Button color="gray" size="sm">
                        Dark Mode Button
                    </Button>
                </Card>
            </div>

            <div className="bg-white dark:bg-gray-800 p-4 rounded-lg border border-gray-200 dark:border-gray-700">
                <h4 className="font-medium text-gray-900 dark:text-white mb-2">
                    Theme Information
                </h4>
                <div className="text-sm text-gray-600 dark:text-gray-300 space-y-1">
                    <p>Theme preference is automatically saved to localStorage</p>
                    <p>System preference is detected on first visit</p>
                    <p>Toggle button is available in the navbar</p>
                    <p>All Flowbite components support dark mode</p>
                </div>
            </div>
        </div>
    );
};

export default DarkModeDemo;
