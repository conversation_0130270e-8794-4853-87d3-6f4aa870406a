export const table_custom = {
    head: {
        base: "group/head text-gray-700 dark:text-gray-400 ",
        cell: {
            base: "text-slate-900 dark:text-slate-300 font-medium border border-t-0 border-s-0 last:border-r-0 dark:border-slate-500 bg-[#f8fafc]  px-3 py-2 group-first/head:first:rounded-tl-lg group-first/head:last:rounded-tr-lg dark:bg-gray-700",
        },
    },
    body: {
        base: "group/body",
        cell: {
            base: " px-3 py-1.5 group-first/body:group-first/row:first:rounded-tl-lg group-first/body:group-first/row:last:rounded-tr-lg group-last/body:group-last/row:first:rounded-bl-lg group-last/body:group-last/row:last:rounded-br-lg",
        },
    },
};
export const radio_custom = {
    root: {
        base: "flex",
    },
    color: {
        gray: ":ring-cyan-700 border border-gray-200 bg-white text-gray-900 f enabled:hover:bg-gray-100 dark:border-gray-600 dark:bg-transparent dark:text-gray-400 dark:enabled:hover:bg-gray-700 dark:enabled:hover:text-white",
    },
    field: {
        base: "relative w-full",
        input: {
            base: "w-full cursor-pointer appearance-none rounded-lg bg-gray-200 dark:bg-gray-700",
            sizes: {
                sm: "h-1",
                md: "h-2",
                lg: "h-3",
            },
        },
    },
};

export const customDrawer = {
    root: {
        base: "fixed z-50 overflow-y-auto bg-white p-4 transition-transform dark:bg-gray-800 bg-slate-100 ",
        backdrop: "fixed inset-0 z-40 bg-gray-900/50 dark:bg-gray-900/80",
        edge: "bottom-16",
        position: {
            top: {
                on: "left-0 right-0 top-0 w-full transform-none",
                off: "left-0 right-0 top-0 w-full -translate-y-full",
            },
            right: {
                on: "right-0 top-0 h-screen w-80 transform-none",
                off: "right-0 top-0 h-screen w-80 translate-x-full",
            },
            bottom: {
                on: "bottom-0 left-0 right-0 w-full transform-none",
                off: "bottom-0 left-0 right-0 w-full translate-y-full",
            },
            left: {
                on: "left-0 top-0 h-screen w-80 transform-none",
                off: "left-0 top-0 h-screen w-80 -translate-x-full",
            },
        },
    },
    header: {
        inner: {
            closeButton:
                "absolute end-2.5 top-2.5 flex h-8 w-8 items-center justify-center rounded-lg bg-transparent text-sm text-gray-400 hover:bg-gray-200 hover:text-gray-900 dark:hover:bg-gray-600 dark:hover:text-white",
            closeIcon: "h-6 w-6",
            titleIcon: "me-2.5 h-6 w-6 text-slate-400",
            titleText:
                " inline-flex items-center text-xl font-semibold text-slate-800 dark:text-gray-400",
        },
        collapsed: {
            on: "hidden",
            off: "block",
        },
    },
    items: {
        base: "rounded-lg dark:!bg-gray-700",
    },
};

export const button_custom2 = {
    pill: {
        off: "rounded-lg",
        on: "rounded-full",
    },
    size: {
        xs: "p-0.5 text-[18px]",
        sm: "p-2 text-[22px]",
        md: "px-4 py-2 text-sm",
        lg: "px-5 py-2.5 text-base",
        xl: "px-6 py-3 text-base",
    },
};

export const customTheme_drawer = {
    root: {
        position: {
            right: {
                on: "right-0 top-0 h-screen w-[70vw] transform-none bg-slate-100",
                off: "right-0 top-0 h-screen w-80 translate-x-full",
            },
        },
    },
    header: {
        inner: {
            closeButton:
                "absolute end-2.5 top-2.5 flex h-8 w-8 items-center justify-center rounded-lg bg-transparent text-sm text-gray-400 hover:bg-gray-200 hover:text-gray-900 dark:hover:bg-gray-600 dark:hover:text-white",
            closeIcon: "h-4 w-4",
            titleIcon: "me-2.5 h-6 w-6 text-gray-400",
            titleText:
                " inline-flex items-center text-lg font-semibold text-sky-950 dark:text-gray-400",
        },
    },
};

export const button_custom = {
    color: {
        transparentForTab:
            "border-0 bg-transparent text-gray-900 enabled:hover:bg-transparent-100 dark:border-gray-600 dark:bg-transparent dark:text-gray-400 dark:enabled:hover:bg-gray-700 dark:enabled:hover:text-white rounded-0",
        transparent:
            "border-b border-blue-400 bg-transparent text-gray-900 f enabled:hover:bg-transparent-100 dark:border-gray-600 dark:bg-transparent dark:text-gray-400 dark:enabled:hover:bg-gray-700 dark:enabled:hover:text-white rounded-0",
        withoutBorder:
            "focus:ring-0 bg-transparent text-gray-900 hover:bg-gray-100 rounded enabled:hover:bg-transparent-100 dark:border-gray-600 dark:bg-transparent dark:text-gray-400 dark:enabled:hover:bg-gray-700 dark:enabled:hover:text-white rounded-0",
        gray: "border border-gray-200 bg-white text-gray-900 f enabled:hover:bg-gray-100 dark:border-gray-600 dark:bg-transparent dark:text-gray-400 dark:enabled:hover:bg-gray-700 dark:enabled:hover:text-white",

        pink: "bg-pink-600 text-white  enabled:hover:bg-pink-700 dark:border-pink-600 dark:bg-transparent dark:text-pink-400 dark:enabled:hover:bg-pink-700 dark:enabled:hover:text-white",

        green: "border border-transparent bg-[#28a745] text-white focus:ring-4 focus:ring-[#d4ffde] enabled:hover:bg-[#28a745] dark:bg-[#28a745] dark:hover:bg-[#28a745] dark:focus:ring-[#d4ffde]",
        slate: "border border-transparent bg-slate-600 text-white focus:ring-4 focus:ring-slate-500 enabled:hover:bg-slate-600 dark:bg-slate-600 dark:hover:bg-slate-600 dark:focus:ring-slate-500",

        slate_border:
            "border border-slate-600 bg-slate-100 text-black focus:ring-1 focus:ring-slate-500 enabled:hover:bg-slate-200 dark:bg-slate-600 dark:hover:bg-slate-600 dark:focus:ring-slate-500 ",
        custom_blue:
            "border border-transparent bg-white text-gray-700 hover:bg-blue enabled:hover:bg-blue-700 dark:bg-slate-600 dark:hover:bg-blue-600 active:bg-blue-700",
        orange: "border border-transparent bg-orange-500 text-white focus:ring-4 focus:ring-orange-300 enabled:hover:bg-orange-500 dark:bg-orange-500 dark:hover:bg-orange-500 dark:focus:ring-orange-300",
        Fuchsia_custom:
            "border border-transparent bg-fuchsia-700 text-white focus:ring-4 focus:ring-fuchsia-500 enabled:hover:bg-fuchsia-700 dark:bg-fuchsia-700 dark:hover:bg-fuchsia-700 dark:focus:ring-fuchsia-500",
        Pink_custom:
            "border border-transparent bg-pink-600 text-white focus:ring-4 focus:ring-pink-500 enabled:hover:bg-pink-700 dark:bg-pink-600 dark:hover:bg-pink-600 dark:focus:ring-pink-500",
    },
    pill: {
        off: "rounded",
        on: "rounded-full",
    },
    size: {
        xxs: "text-sm",
        xs: "p-0.5 text-lg ",
        sm: "px-3 py-1.5 text-sm",
        md: "px-4 py-2 text-sm",
        lg: "px-5 py-2.5 text-base",
        xl: "px-6 py-3 text-base",
    },
    outline: {
        color: {
            gray: "border border-gray-600 dark:border-white",
            default: "border-0",
            light: "",
        },
    },
};

export const button_custom_for_tab = {
    color: {
        transparentForTab:
            "border-0 bg-transparent text-gray-900 enabled:hover:bg-transparent-100 dark:border-gray-600 dark:bg-transparent dark:text-gray-400 dark:enabled:hover:bg-gray-700 dark:enabled:hover:text-white rounded-0",
        transparent:
            "border-b border-blue-400 bg-transparent text-gray-900 f enabled:hover:bg-transparent-100 dark:border-gray-600 dark:bg-transparent dark:text-gray-400 dark:enabled:hover:bg-gray-700 dark:enabled:hover:text-white rounded-0",
        withoutBorder:
            "bg-transparent text-gray-900 hover:bg-gray-100 rounded enabled:hover:bg-transparent-100 dark:border-gray-600 dark:bg-transparent dark:text-gray-400 dark:enabled:hover:bg-gray-700 dark:enabled:hover:text-white rounded-0",
        gray: "border border-gray-200 bg-white text-gray-900 f enabled:hover:bg-gray-100 dark:border-gray-600 dark:bg-transparent dark:text-gray-400 dark:enabled:hover:bg-gray-700 dark:enabled:hover:text-white",

        green: "border border-transparent bg-[#28a745] text-white focus:ring-4 focus:ring-[#d4ffde] enabled:hover:bg-[#28a745] dark:bg-[#28a745] dark:hover:bg-[#28a745] dark:focus:ring-[#d4ffde]",
        slate: "border border-transparent bg-slate-600 text-white focus:ring-4 focus:ring-slate-500 enabled:hover:bg-slate-600 dark:bg-slate-600 dark:hover:bg-slate-600 dark:focus:ring-slate-500",
        Fuchsia_custom:
            "border border-transparent bg-fuchsia-600 text-white focus:ring-4 focus:ring-fuchsia-500 enabled:hover:bg-fuchsia-600 dark:bg-fuchsia-600 dark:hover:bg-fuchsia-600 dark:focus:ring-fuchsia-500",
        custom_blue:
            "border border-transparent bg-white text-gray-700 hover:bg-blue enabled:hover:bg-blue-700 dark:bg-slate-600 dark:hover:bg-blue-600 active:bg-blue-700",
        orange: "border border-transparent bg-orange-500 text-white focus:ring-4 focus:ring-orange-300 enabled:hover:bg-orange-500 dark:bg-orange-500 dark:hover:bg-orange-500 dark:focus:ring-orange-300",
    },
    pill: {
        off: "rounded-0",
        on: "rounded-0",
    },
    size: {
        xxs: "text-sm",
        xs: "p-0.5 text-lg ",
        sm: "px-3 py-1.5 text-sm",
        md: "px-4 py-2 text-sm",
        lg: "px-5 py-2.5 text-base",
        xl: "px-6 py-3 text-base",
    },
    outline: {
        color: {
            gray: "border border-gray-600 dark:border-white",
            default: "border-0",
            light: "",
        },
    },
};

export const input_custom = {
    base: "flex",
    addon: "inline-flex items-center rounded-l-md border border-r-0 border-gray-300 bg-gray-200 px-3 text-xs text-gray-900 dark:border-gray-600 dark:bg-gray-600 dark:text-gray-400",
    field: {
        base: "relative w-full",
        icon: {
            base: "pointer-events-none absolute inset-y-0 left-0 flex items-center pl-3",
            svg: "h-5 w-5 text-gray-500 dark:text-gray-400",
        },
        rightIcon: {
            base: "pointer-events-none absolute inset-y-0 right-0 flex items-center pr-3",
            svg: "h-5 w-5 text-gray-500 dark:text-gray-400",
        },
        input: {
            base: "block w-full border disabled:cursor-not-allowed disabled:opacity-50",
            sizes: {
                xs: "px-2 py-1.5 text-xs",
                sm: "p-2 sm:text-xs",
                md: "p-2.5 text-sm",
                lg: "p-4 sm:text-base",
            },
            colors: {
                gray: "border-gray-300 bg-gray-50 text-gray-900 focus:border-blue-500 focus:ring-blue-500 dark:border-gray-600 dark:bg-gray-700 dark:text-white dark:placeholder-gray-400 dark:focus:border-blue-500 dark:focus:ring-blue-500",

                onlyBorder_b:
                    "border-b border-gray-300 bg-gray-50 text-gray-900 focus:border-blue-500 focus:ring-blue-500 dark:border-gray-600 dark:bg-gray-700 dark:text-white dark:placeholder-gray-400 dark:focus:border-blue-500 dark:focus:ring-blue-500",

                info: "border-cyan-500 bg-cyan-50 text-cyan-900 placeholder-cyan-700 focus:border-cyan-500 focus:ring-cyan-500 dark:border-cyan-400 dark:bg-cyan-100 dark:focus:border-cyan-500 dark:focus:ring-cyan-500",
                failure:
                    "border-red-500 bg-red-50 text-red-900 placeholder-red-700 focus:border-red-500 focus:ring-red-500 dark:border-red-400 dark:bg-red-100 dark:focus:border-red-500 dark:focus:ring-red-500",
                warning:
                    "border-yellow-500 bg-yellow-50 text-yellow-900 placeholder-yellow-700 focus:border-yellow-500 focus:ring-yellow-500 dark:border-yellow-400 dark:bg-yellow-100 dark:focus:border-yellow-500 dark:focus:ring-yellow-500",
                success:
                    "border-green-500 bg-green-50 text-green-900 placeholder-green-700 focus:border-green-500 focus:ring-green-500 dark:border-green-400 dark:bg-green-100 dark:focus:border-green-500 dark:focus:ring-green-500",
            },
            withRightIcon: {
                on: "pr-10",
                off: "",
            },
            withIcon: {
                on: "pl-10",
                off: "",
            },
            withAddon: {
                on: "rounded-r-lg",
                off: "rounded-lg",
            },
            withShadow: {
                on: "shadow-sm dark:shadow-sm-light",
                off: "",
            },
        },
    },
};

export const input_custom_for_sleep = {
    base: "flex",
    addon: "inline-flex items-center rounded-l-md border border-r-0 border-gray-300 bg-gray-200 px-3 text-sm text-gray-900 dark:border-gray-600 dark:bg-gray-600 dark:text-gray-400",
    field: {
        base: "relative w-full",
        icon: {
            base: "pointer-events-none absolute inset-y-0 left-0 flex items-center pl-3",
            svg: "h-5 w-5 text-gray-500 dark:text-gray-400",
        },
        rightIcon: {
            base: "pointer-events-none absolute inset-y-0 right-0 flex items-center pr-3",
            svg: "h-5 w-5 text-gray-500 dark:text-gray-400",
        },
        input: {
            base: "block w-full border disabled:cursor-not-allowed disabled:opacity-50",
            sizes: {
                xs: "px-2 py-1.5 text-sm",
                sm: "p-2 sm:text-xs",
                md: "p-2.5 text-sm",
                lg: "p-4 sm:text-base",
            },
            colors: {
                gray: "border-gray-300 bg-gray-50 text-gray-900 focus:border-blue-500 focus:ring-blue-500 dark:border-gray-600 dark:bg-gray-700 dark:text-white dark:placeholder-gray-400 dark:focus:border-blue-500 dark:focus:ring-blue-500",

                onlyBorder_b:
                    "border-b border-gray-300 bg-gray-50 text-gray-900 focus:border-blue-500 focus:ring-blue-500 dark:border-gray-600 dark:bg-gray-700 dark:text-white dark:placeholder-gray-400 dark:focus:border-blue-500 dark:focus:ring-blue-500",

                info: "border-cyan-500 bg-cyan-50 text-cyan-900 placeholder-cyan-700 focus:border-cyan-500 focus:ring-cyan-500 dark:border-cyan-400 dark:bg-cyan-100 dark:focus:border-cyan-500 dark:focus:ring-cyan-500",
                failure:
                    "border-red-500 bg-red-50 text-red-900 placeholder-red-700 focus:border-red-500 focus:ring-red-500 dark:border-red-400 dark:bg-red-100 dark:focus:border-red-500 dark:focus:ring-red-500",
                warning:
                    "border-yellow-500 bg-yellow-50 text-yellow-900 placeholder-yellow-700 focus:border-yellow-500 focus:ring-yellow-500 dark:border-yellow-400 dark:bg-yellow-100 dark:focus:border-yellow-500 dark:focus:ring-yellow-500",
                success:
                    "border-green-500 bg-green-50 text-green-900 placeholder-green-700 focus:border-green-500 focus:ring-green-500 dark:border-green-400 dark:bg-green-100 dark:focus:border-green-500 dark:focus:ring-green-500",
            },
            withRightIcon: {
                on: "pr-10",
                off: "",
            },
            withIcon: {
                on: "pl-10",
                off: "",
            },
            withAddon: {
                on: "rounded-r-lg",
                off: "rounded-lg",
            },
            withShadow: {
                on: "shadow-sm dark:shadow-sm-light",
                off: "",
            },
        },
    },
};

export const inputButtton_custom = {
    base: "flex",
    addon: "inline-flex items-center rounded-l-md border border-r-0 border-gray-300 bg-gray-200 px-3 text-sm text-gray-900 dark:border-gray-600 dark:bg-gray-600 dark:text-gray-400",
    field: {
        base: "relative w-full",
        icon: {
            base: "pointer-events-none absolute inset-y-0 left-0 flex items-center pl-3",
            svg: "h-5 w-5 text-gray-500 dark:text-gray-400",
        },
        rightIcon: {
            base: "pointer-events-none absolute inset-y-0 right-0 flex items-center pr-3",
            svg: "h-5 w-5 text-gray-500 dark:text-gray-400",
        },
        input: {
            base: "block w-full border disabled:cursor-not-allowed disabled:opacity-50",
            sizes: {
                sm: "p-2 sm:text-xs",
                md: "p-2.5 text-sm",
                lg: "p-4 sm:text-base",
            },
            colors: {
                gray: "border-gray-300 bg-gray-50 text-gray-900 focus:border-blue-500 focus:ring-blue-500 dark:border-gray-600 dark:bg-gray-700 dark:text-white dark:placeholder-gray-400 dark:focus:border-blue-500 dark:focus:ring-blue-500",
                info: "border-cyan-500 bg-cyan-50 text-cyan-900 placeholder-cyan-700 focus:border-cyan-500 focus:ring-cyan-500 dark:border-cyan-400 dark:bg-cyan-100 dark:focus:border-cyan-500 dark:focus:ring-cyan-500",
                failure:
                    "border-red-500 bg-red-50 text-red-900 placeholder-red-700 focus:border-red-500 focus:ring-red-500 dark:border-red-400 dark:bg-red-100 dark:focus:border-red-500 dark:focus:ring-red-500",
                warning:
                    "border-yellow-500 bg-yellow-50 text-yellow-900 placeholder-yellow-700 focus:border-yellow-500 focus:ring-yellow-500 dark:border-yellow-400 dark:bg-yellow-100 dark:focus:border-yellow-500 dark:focus:ring-yellow-500",
                success:
                    "border-green-500 bg-green-50 text-green-900 placeholder-green-700 focus:border-green-500 focus:ring-green-500 dark:border-green-400 dark:bg-green-100 dark:focus:border-green-500 dark:focus:ring-green-500",
            },
            withRightIcon: {
                on: "pr-10",
                off: "",
            },
            withIcon: {
                on: "pl-10",
                off: "",
            },
            withAddon: {
                on: "rounded-r-lg",
                off: "rounded-lg",
            },
            withShadow: {
                on: "shadow-sm dark:shadow-sm-light",
                off: "",
            },
        },
    },
};

export const textarea_custom = {
    base: "block w-full rounded-lg border text-sm disabled:cursor-not-allowed disabled:opacity-50",
    colors: {
        gray: "border-gray-300 bg-gray-50 text-gray-900 focus:border-blue-500 focus:ring-transparent dark:border-gray-600 dark:bg-gray-700 dark:text-white dark:placeholder-gray-400 dark:focus:border-blue-500 dark:focus:ring-blue-500",
        info: "border-cyan-500 bg-cyan-50 text-cyan-900 placeholder-cyan-700 focus:border-cyan-500 focus:ring-cyan-500 dark:border-cyan-400 dark:bg-cyan-100 dark:focus:border-cyan-500 dark:focus:ring-cyan-500",
        failure:
            "border-red-500 bg-red-50 text-red-900 placeholder-red-700 focus:border-red-500 focus:ring-red-500 dark:border-red-400 dark:bg-red-100 dark:focus:border-red-500 dark:focus:ring-red-500",
        warning:
            "border-yellow-500 bg-yellow-50 text-yellow-900 placeholder-yellow-700 focus:border-yellow-500 focus:ring-yellow-500 dark:border-yellow-400 dark:bg-yellow-100 dark:focus:border-yellow-500 dark:focus:ring-yellow-500",
        success:
            "border-green-500 bg-green-50 text-green-900 placeholder-green-700 focus:border-green-500 focus:ring-green-500 dark:border-green-400 dark:bg-green-100 dark:focus:border-green-500 dark:focus:ring-green-500",
    },
    withShadow: {
        on: "shadow-sm dark:shadow-sm-light",
        off: "",
    },
};

export const custom_tabs = {
    base: "flex flex-col gap-2",
    tablist: {
        base: "flex text-center bg-white rounded-lg",
        variant: {
            default: "flex-wrap border-b border-gray-200 dark:border-gray-700",
            underline:
                "-mb-px flex-wrap border-b border-gray-200 dark:border-gray-700",
            pills: "flex-wrap space-x-2 text-sm font-medium text-gray-500 dark:text-gray-400",
            fullWidth:
                "grid w-full grid-flow-col divide-x divide-gray-200 rounded-none text-sm font-medium shadow dark:divide-gray-700 dark:text-gray-400",
        },
        tabitem: {
            base: "flex gap-1 items-center justify-center p-3 text-sm font-medium first:ml-0 disabled:cursor-not-allowed disabled:text-gray-400 disabled:dark:text-gray-500",
            variant: {
                default: {
                    base: "",
                    active: {
                        on: "bg-blue-700 text-white dark:bg-gray-800 dark:text-cyan-500 first:rounded-s-lg",
                        off: "text-gray-500 hover:bg-gray-50 hover:text-gray-600 dark:text-gray-400 dark:hover:bg-gray-800 dark:hover:text-gray-300",
                    },
                },
            },
            icon: "h-5 w-5",
            // icon: {
            //     base: "mr-2 h-7 w-7 text-slate-300",
            //     active: {
            //         on: "text-white mr-2 text-lg",
            //         off: "mr-2",
            //     },
            // },
        },
    },
    tabitemcontainer: {
        base: "",
        variant: {
            default: "",
            underline: "",
            pills: "",
            fullWidth: "",
        },
    },
    tabpanel: "py-1",
};

export const toggle_custom = {
    root: {
        base: "group flex rounded-lg focus:outline-none",
        active: {
            on: "cursor-pointer",
            off: "cursor-not-allowed opacity-50",
        },
        label: "ms-3 mt-0.5 text-start text-sm font-medium text-gray-900 dark:text-gray-300",
    },
    toggle: {
        base: "relative rounded-full border after:absolute after:rounded-full after:bg-white after:transition-all group-focus:ring-4 group-focus:ring-cyan-500/25",
        checked: {
            on: "after:translate-x-full after:border-white rtl:after:-translate-x-full",
            off: "border-gray-200 bg-gray-200 dark:border-gray-600 dark:bg-gray-700",
            color: {
                blue: "border-blue-400 bg-blue-700",
            },
        },
        sizes: {
            xs: "h-4 w-7 min-w-7 after:left-px after:top-px after:h-3 after:w-3 rtl:after:right-px",
            sm: "h-5 w-9 min-w-9 after:left-px after:top-px after:h-4 after:w-4 rtl:after:right-px",
            md: "h-6 w-11 min-w-11 after:left-px after:top-px after:h-5 after:w-5 rtl:after:right-px",
            lg: "h-7 w-14 min-w-14 after:left-1 after:top-0.5 after:h-6 after:w-6 rtl:after:right-1",
        },
    },
};

export const buttongroup_custom = {
    base: "inline-flex",
    position: {
        none: "",
        start: "rounded-r-none",
        middle: "rounded-none border-l-0 pl-0",
        end: "rounded-l-none border-l-0 pl-0",
    },
};

export const badge_custom = {
    root: {
        base: "flex h-fit items-center gap-1 font-semibold",
        color: {
            info: "bg-cyan-500 text-white group-hover:bg-cyan-200 dark:bg-cyan-200 dark:text-cyan-800 dark:group-hover:bg-cyan-300",
            gray: "bg-gray-500 text-white group-hover:bg-gray-200 dark:bg-gray-700 dark:text-gray-300 dark:group-hover:bg-gray-600",
            failure:
                "bg-red-600 text-white group-hover:bg-red-200 dark:bg-red-200 dark:text-red-900 dark:group-hover:bg-red-300",
            success:
                "bg-green-500 text-white group-hover:bg-green-200 dark:bg-green-200 dark:text-green-900 dark:group-hover:bg-green-300",
            warning:
                "bg-yellow-500 text-white group-hover:bg-yellow-200 dark:bg-yellow-200 dark:text-yellow-900 dark:group-hover:bg-yellow-300",
            indigo: "bg-indigo-500 text-white group-hover:bg-indigo-200 dark:bg-indigo-200 dark:text-indigo-900 dark:group-hover:bg-indigo-300",
            purple: "bg-purple-500 text-white group-hover:bg-purple-200 dark:bg-purple-200 dark:text-purple-900 dark:group-hover:bg-purple-300",
            pink: "bg-pink-500 texwhite group-hover:bg-pink-200 dark:bg-pink-200 dark:text-pink-900 dark:group-hover:bg-pink-300",
            blue: "bg-blue-500 texwhite group-hover:bg-blue-200 dark:bg-blue-200 dark:text-blue-900 dark:group-hover:bg-blue-300",
            cyan: "bg-cyan-500 texwhite group-hover:bg-cyan-200 dark:bg-cyan-200 dark:text-cyan-900 dark:group-hover:bg-cyan-300",
            dark: "bg-gray-600 text-gray-100 group-hover:bg-gray-500 dark:bg-gray-900 dark:text-gray-200 dark:group-hover:bg-gray-700",
            light: "bg-gray-500 texwhite group-hover:bg-gray-300 dark:bg-gray-400 dark:text-gray-900 dark:group-hover:bg-gray-500",
            green: "bg-green-500 textwhite group-hover:bg-green-200 dark:bg-green-200 dark:text-green-900 dark:group-hover:bg-green-300",
            lime: "bg-lime-500 texwhite group-hover:bg-lime-200 dark:bg-lime-200 dark:text-lime-900 dark:group-hover:bg-lime-300",
            red: "bg-red-500 tewhite group-hover:bg-red-200 dark:bg-red-200 dark:text-red-900 dark:group-hover:bg-red-300",
            teal: "bg-teal-500 texwhite group-hover:bg-teal-200 dark:bg-teal-200 dark:text-teal-900 dark:group-hover:bg-teal-300",
            yellow: "bg-yellow-500 text-white group-hover:bg-yellow-200 dark:bg-yellow-200 dark:text-yellow-900 dark:group-hover:bg-yellow-300",
        },
        href: "group",
        size: {
            xs: "p-0 text-xs",
            sm: "p-1.5 text-sm",
        },
    },
    icon: {
        off: "rounded px-2 py-0.5",
        on: "rounded-full p-1.5",
        size: {
            xs: "h-3 w-3",
            sm: "h-3.5 w-3.5",
        },
    },
};

export const copyButton_custom = {
    button: {
        base: "inline-flex w-full items-center justify-center rounded-lg px-5 py-3 bg-slate-500 hover:bg-blue-800 focus:outline-none focus:ring-4 focus:ring-blue-300 dark:bg-blue-600 dark:hover:bg-blue-700 dark:focus:ring-blue-800",
        label: "text-center text-sm font-medium text-white sm:w-auto",
    },
    withIcon: {
        base: "absolute end-2 top-1/2 inline-flex -translate-y-1/2 items-center justify-center rounded-lg p-2 text-white bg-slate-500 hover:bg-gray-100 dark:text-gray-400 dark:hover:bg-gray-800",
        icon: {
            defaultIcon: "h-4 w-4 bg-blue-600",
            successIcon: "h-4 w-4 text-blue-700 dark:text-blue-500",
        },
    },
    withIconText: {
        base: "absolute end-2.5 top-1/2 inline-flex -translate-y-1/2 items-center justify-center rounded-lg border border-gray-200 bg-white px-2.5 py-2 text-gray-900 hover:bg-gray-100 dark:border-gray-600 dark:bg-gray-800 dark:text-gray-400 dark:hover:bg-gray-700",
        icon: {
            defaultIcon: "me-1.5 h-3 w-3",
            successIcon: "me-1.5 h-3 w-3 text-blue-700 dark:text-blue-500",
        },
        label: {
            base: "inline-flex items-center",
            defaultText: "text-xs font-semibold",
            successText:
                "text-xs font-semibold text-blue-700 dark:text-blue-500",
        },
    },
};

export const avatar_custom = {
    root: {
        base: "flex items-center justify-center space-x-4 rounded",
        bordered: "p-1 ring-2",
        rounded: "rounded-full",
        color: {
            dark: "ring-gray-800 dark:ring-gray-800",
            failure: "ring-red-500 dark:ring-red-700",
            gray: "ring-gray-500 dark:ring-gray-400",
            info: "ring-cyan-400 dark:ring-cyan-800",
            light: "ring-gray-300 dark:ring-gray-500",
            purple: "ring-purple-500 dark:ring-purple-600",
            success: "ring-green-500 dark:ring-green-500",
            warning: "ring-yellow-300 dark:ring-yellow-500",
            pink: "ring-pink-500 dark:ring-pink-500",
        },
        img: {
            base: "rounded",
            off: "relative overflow-hidden bg-gray-100 dark:bg-gray-600",
            on: "",
            placeholder: "absolute -bottom-1 h-auto w-auto text-gray-400",
        },
        size: {
            xs: "h-6 w-6",
            sm: "h-8 w-8",
            md: "h-10 w-10",
            lg: "h-16 w-16",
            xl: "h-36 w-36",
        },
        stacked: "ring-2 ring-gray-300 dark:ring-gray-500",
        statusPosition: {
            "bottom-left": "-bottom-1 -left-1",
            "bottom-center": "-bottom-1",
            "bottom-right": "-bottom-1 -right-1",
            "top-left": "-left-1 -top-1",
            "top-center": "-top-1",
            "top-right": "-right-1 -top-1",
            "center-right": "-right-1",
            center: "",
            "center-left": "-left-1",
        },
        status: {
            away: "bg-yellow-400",
            base: "absolute h-3.5 w-3.5 rounded-full border-2 border-white dark:border-gray-800",
            busy: "bg-red-400",
            offline: "bg-gray-400",
            online: "bg-green-400",
        },
        initials: {
            text: "font-medium text-gray-600 dark:text-gray-300",
            base: "relative inline-flex items-center justify-center overflow-hidden bg-gray-100 dark:bg-gray-600",
        },
    },
    group: {
        base: "flex -space-x-4",
    },
    groupCounter: {
        base: "relative flex h-10 w-10 items-center justify-center rounded-full bg-gray-700 text-xs font-medium text-white ring-2 ring-gray-300 hover:bg-gray-600 dark:ring-gray-500",
    },
};

export const chatList_custom = {
    root: {
        base: "list-none rounded-lg border border-gray-200 bg-white text-left text-sm font-medium text-gray-900 dark:border-gray-600 dark:bg-gray-700 dark:text-white",
    },
    item: {
        base: "[&>*]:first:rounded-t-lg [&>*]:last:rounded-b-lg [&>*]:last:border-b-0 p-0",
        link: {
            base: "flex w-full items-center border-b border-gray-200 px-0 py-0 dark:border-gray-600",
            active: {
                off: "bg-white ",
                on: "bg-gray-100 text-white",
            },
            disabled: {
                off: "",
                on: "cursor-not-allowed bg-gray-100 text-gray-900 hover:bg-gray-100 hover:text-gray-900 focus:text-gray-900",
            },
            href: {
                off: "",
                on: "",
            },
            icon: "mr-2 h-4 w-4 fill-current",
        },
    },
};

export const listchat_custom = {
    root: {
        base: "list-inside text-gray-500 dark:text-gray-400",
        ordered: {
            off: "list-disc",
            on: "list-decimal",
        },
        horizontal:
            "flex list-none flex-wrap items-center justify-center space-y-0",
        unstyled: "list-none",
        nested: "mt-2 ps-5",
    },
    item: {
        withIcon: {
            off: "",
            on: "flex items-center",
        },
        icon: "me-2 h-3.5 w-3.5 flex-shrink-0",
    },
};

export const dropdownChats_custom = {
    arrowIcon: "ml-0 h-4 w-4",
    content: "py-1 focus:outline-none",
    floating: {
        animation: "transition-opacity",
        arrow: {
            base: "absolute z-10 h-2 w-2 rotate-45",
            style: {
                dark: "bg-gray-900 dark:bg-gray-700",
                light: "bg-white",
                auto: "bg-white dark:bg-gray-700",
            },
            placement: "-4px",
        },
        base: "z-10 w-fit divide-y divide-gray-100 rounded shadow focus:outline-none",
        content: "py-1 text-sm text-gray-700 dark:text-gray-200",
        divider: "my-1 h-px bg-gray-100 dark:bg-gray-600",
        header: "block px-4 py-2 text-sm text-gray-700 dark:text-gray-200",
        hidden: "invisible opacity-0",
        item: {
            container: "",
            base: "flex w-full cursor-pointer items-center justify-start px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 focus:bg-gray-100 focus:outline-none dark:text-gray-200 dark:hover:bg-gray-600 dark:hover:text-white dark:focus:bg-gray-600 dark:focus:text-white",
            icon: "mr-2 h-4 w-4",
        },
        style: {
            dark: "bg-gray-900 text-white dark:bg-gray-700",
            light: "border border-gray-200 bg-white text-gray-900",
            auto: "border border-gray-200 bg-white text-gray-900 dark:border-none dark:bg-gray-700 dark:text-white",
        },
        target: "w-fit",
    },
    inlineWrapper: "flex items-center",
};

export const card_custom = {
    root: {
        base: "h-full max-w-full flex rounded-lg border border-gray-200 shadow-sm dark:border-gray-700 dark:bg-gray-800",
        children: "flex h-full max-w-full flex-col  justify-between",
        horizontal: {
            off: "flex-col",
            on: "flex-col md:max-w-xl md:flex-row",
        },
        href: "hover:bg-gray-100 dark:hover:bg-gray-700",
    },
    img: {
        base: "",
        horizontal: {
            off: "rounded-t-lg",
            on: "h-96 w-full rounded-t-lg object-cover md:h-auto md:w-48 md:rounded-none md:rounded-l-lg",
        },
    },
};

export const custom_datepicker = {
    root: {
        base: "relative",
    },
    field: {
        base: "relative w-full",
        icon: {
            base: "pointer-events-none absolute inset-y-0 end-3.5 flex items-center pl-3",
            svg: "h-5 w-5 text-slate-400 dark:text-gray-400",
        },
        rightIcon: {
            base: "pointer-events-none absolute border-s inset-y-0 right-0 flex items-center ps-2 pr-3  text-slate-300",
            svg: "h-5 w-5 text-slate-400 dark:text-gray-400 ",
        },
    },
    input: {
        base: "block w-full border disabled:cursor-not-allowed disabled:opacity-50",
        sizes: {
            sm: "p-2 sm:text-xs",
            md: "p-2.5 text-sm",
            lg: "p-4 sm:text-base",
        },
        gray: "border-blue-300 bg-blue-50 text-blue-900 placeholder-blue-700 focus:border-blue-500 focus:ring-blue-500 dark:border-blue-400 dark:bg-blue-100 dark:focus:border-blue-500 dark:focus:ring-blue-500",
    },

    popup: {
        root: {
            base: "absolute bottom-20 z-50 block pt-2",
            inline: "relative top-0 z-auto",
            inner: "inline-block rounded-lg bg-white p-4 shadow-lg dark:bg-gray-700",
        },
        header: {
            base: "",
            title: "px-2 py-3 text-center font-semibold text-gray-900 dark:text-white",
            selectors: {
                base: "mb-2 flex justify-between",
                button: {
                    base: "rounded-lg bg-white px-5 py-2.5 text-sm font-semibold text-gray-900 hover:bg-gray-100 focus:outline-none focus:ring-2 focus:ring-gray-200 dark:bg-gray-700 dark:text-white dark:hover:bg-gray-600",
                    prev: "",
                    next: "",
                    view: "",
                },
            },
        },
        view: {
            base: "p-1",
        },
        footer: {
            base: "mt-2 flex space-x-2",
            button: {
                base: "w-full rounded-lg px-5 py-2 text-center text-sm font-medium focus:ring-4 focus:ring-blue-300",
                today: "bg-blue-700 text-white hover:bg-blue-800 dark:bg-blue-600 dark:hover:bg-blue-700",
                clear: "border border-gray-300 bg-white text-gray-900 hover:bg-gray-100 dark:border-gray-600 dark:bg-gray-700 dark:text-white dark:hover:bg-gray-600",
            },
        },
    },
    views: {
        days: {
            header: {
                base: "mb-1 grid grid-cols-7",
                title: "h-6 text-center text-sm font-medium leading-6 text-gray-500 dark:text-gray-400",
            },
            items: {
                base: "grid w-64 grid-cols-7",
                item: {
                    base: "block flex-1 cursor-pointer rounded-lg border-0 text-center text-sm font-semibold leading-9 text-gray-900 hover:bg-gray-100 dark:text-white dark:hover:bg-gray-600",
                    selected: "bg-blue-700 text-white hover:bg-blue-600",
                    disabled: "text-gray-500",
                },
            },
        },
        months: {
            items: {
                base: "grid w-64 grid-cols-4",
                item: {
                    base: "block flex-1 cursor-pointer rounded-lg border-0 text-center text-sm font-semibold leading-9 text-gray-900 hover:bg-gray-100 dark:text-white dark:hover:bg-gray-600",
                    selected: "bg-cyan-700 text-white hover:bg-cyan-600",
                    disabled: "text-gray-500",
                },
            },
        },
        years: {
            items: {
                base: "grid w-64 grid-cols-4",
                item: {
                    base: "block flex-1 cursor-pointer rounded-lg border-0 text-center text-sm font-semibold leading-9 text-gray-900 hover:bg-gray-100 dark:text-white dark:hover:bg-gray-600",
                    selected: "bg-cyan-700 text-white hover:bg-cyan-600",
                    disabled: "text-gray-500",
                },
            },
        },
        decades: {
            items: {
                base: "grid w-64 grid-cols-4",
                item: {
                    base: "block flex-1 cursor-pointer rounded-lg border-0 text-center text-sm font-semibold leading-9 text-gray-900 hover:bg-gray-100 dark:text-white dark:hover:bg-gray-600",
                    selected: "bg-cyan-700 text-white hover:bg-cyan-600",
                    disabled: "text-gray-500",
                },
            },
        },
    },
};

export const custom_datepicker_Bottom = {
    root: {
        base: "relative",
    },
    field: {
        base: "relative w-full",
        icon: {
            base: "pointer-events-none absolute inset-y-0 end-3.5 flex items-center pl-3",
            svg: "h-5 w-5 text-slate-400 dark:text-gray-400",
        },
        rightIcon: {
            base: "pointer-events-none absolute border-s inset-y-0 right-0 flex items-center ps-2 pr-3  text-slate-300",
            svg: "h-5 w-5 text-slate-400 dark:text-gray-400 ",
        },
    },
    input: {
        base: "block w-full border disabled:cursor-not-allowed disabled:opacity-50 ",
        sizes: {
            sm: "p-2 sm:text-xs",
            md: "p-2.5 text-sm",
            lg: "p-4 sm:text-base",
        },
        gray: "border-blue-300 bg-blue-50 text-blue-900 placeholder-blue-700 focus:border-blue-500 focus:ring-blue-500 dark:border-blue-400 dark:bg-blue-100 dark:focus:border-blue-500 dark:focus:ring-blue-500",
    },

    popup: {
        root: {
            base: "absolute z-50 block pt-2",
            inline: "relative top-0 z-auto",
            inner: "inline-block rounded-lg bg-white p-4 shadow-lg dark:bg-gray-700",
        },
        header: {
            base: "",
            title: "px-2 py-3 text-center font-semibold text-gray-900 dark:text-white",
            selectors: {
                base: "mb-2 flex justify-between",
                button: {
                    base: "rounded-lg bg-white px-5 py-2.5 text-sm font-semibold text-gray-900 hover:bg-gray-100 focus:outline-none focus:ring-2 focus:ring-gray-200 dark:bg-gray-700 dark:text-white dark:hover:bg-gray-600",
                    prev: "",
                    next: "",
                    view: "",
                },
            },
        },
        view: {
            base: "p-1",
        },
        footer: {
            base: "mt-2 flex space-x-2",
            button: {
                base: "w-full rounded-lg px-5 py-2 text-center text-sm font-medium focus:ring-4 focus:ring-blue-300",
                today: "bg-blue-700 text-white hover:bg-blue-800 dark:bg-blue-600 dark:hover:bg-blue-700",
                clear: "border border-gray-300 bg-white text-gray-900 hover:bg-gray-100 dark:border-gray-600 dark:bg-gray-700 dark:text-white dark:hover:bg-gray-600",
            },
        },
    },
    views: {
        days: {
            header: {
                base: "mb-1 grid grid-cols-7",
                title: "h-6 text-center text-sm font-medium leading-6 text-gray-500 dark:text-gray-400",
            },
            items: {
                base: "grid w-64 grid-cols-7",
                item: {
                    base: "block flex-1 cursor-pointer rounded-lg border-0 text-center text-sm font-semibold leading-9 text-gray-900 hover:bg-gray-100 dark:text-white dark:hover:bg-gray-600",
                    selected: "bg-blue-700 text-white hover:bg-blue-600",
                    disabled: "text-gray-500",
                },
            },
        },
        months: {
            items: {
                base: "grid w-64 grid-cols-4",
                item: {
                    base: "block flex-1 cursor-pointer rounded-lg border-0 text-center text-sm font-semibold leading-9 text-gray-900 hover:bg-gray-100 dark:text-white dark:hover:bg-gray-600",
                    selected: "bg-cyan-700 text-white hover:bg-cyan-600",
                    disabled: "text-gray-500",
                },
            },
        },
        years: {
            items: {
                base: "grid w-64 grid-cols-4",
                item: {
                    base: "block flex-1 cursor-pointer rounded-lg border-0 text-center text-sm font-semibold leading-9 text-gray-900 hover:bg-gray-100 dark:text-white dark:hover:bg-gray-600",
                    selected: "bg-cyan-700 text-white hover:bg-cyan-600",
                    disabled: "text-gray-500",
                },
            },
        },
        decades: {
            items: {
                base: "grid w-64 grid-cols-4",
                item: {
                    base: "block flex-1 cursor-pointer rounded-lg border-0 text-center text-sm font-semibold leading-9 text-gray-900 hover:bg-gray-100 dark:text-white dark:hover:bg-gray-600",
                    selected: "bg-cyan-700 text-white hover:bg-cyan-600",
                    disabled: "text-gray-500",
                },
            },
        },
    },
};

export const custom_carousal = {
    root: {
        base: "relative h-full ",
        leftControl:
            "absolute left-0 top-0 flex h-full items-center justify-center px-4 focus:outline-none",
        rightControl:
            "absolute right-0 top-0 flex h-full items-center justify-center px-4 focus:outline-none",
    },
    indicators: {
        active: {
            off: "bg-gray hover:bg-white dark:bg-gray-800/50 dark:hover:bg-gray-800",
            on: "bg-gray dark:bg-gray-800",
        },
        base: "h-3 w-3 rounded-full",
        wrapper: "absolute bottom-5 left-1/2 flex -translate-x-1/2 space-x-3",
    },
    item: {
        base: "h-auto absolute left-1/2 top-1/2 block -translate-x-1/2 -translate-y-1/2 m-1",
        wrapper: {
            off: "w-60 flex-shrink-0 gap-3 transform cursor-default snap-center",
            on: "w-60 flex-shrink-0 gap-3 transform cursor-grab snap-center",
        },
    },
    control: {
        base: "inline-flex h-8 w-8 items-center justify-center rounded-full bg-gray-500 group-hover:bg--gray-500 group-focus:outline-none group-focus:ring-4 group-focus:ring-white dark:bg-gray-800/30 dark:group-hover:bg-gray-800/60 dark:group-focus:ring-gray-800/70 sm:h-10 sm:w-10",
        icon: "h-5 w-5 text-white dark:text-gray-800 sm:h-6 sm:w-6",
    },
    scrollContainer: {
        base: "flex h-full snap-mandatory overflow-y-hidden overflow-x-scroll scroll-smooth rounded-lg",
        snap: "snap-x",
    },
};

export const tooltip_custom = {
    target: "w-full",
    animation: "transition-opacity",
    arrow: {
        base: "absolute z-10 h-2 w-2 rotate-45",
        style: {
            dark: "bg-gray-900 dark:bg-gray-700",
            light: "bg-slate-100",
            auto: "bg-white dark:bg-gray-700",
        },
        placement: "-4px",
    },
    base: "absolute z-10 inline-block rounded-lg px-3 py-2 text-sm font-medium shadow-sm",
    hidden: "invisible opacity-0",
    style: {
        dark: "bg-gray-900 text-white dark:bg-gray-700",
        light: "border border-gray-200 bg-slate-100 text-gray-900",
        auto: "border border-gray-200 bg-white text-gray-900 dark:border-none dark:bg-gray-700 dark:text-white",
    },
    content: "relative z-20",
};

export const tabBar_underline = {
    base: "flex flex-col gap-2",
    tablist: {
        base: "flex text-center",
        variant: {
            default: "flex-wrap border-b border-gray-200 dark:border-gray-700",
            underline: "-mb-px flex-wrap bg-white rounded-md",
            pills: "flex-wrap space-x-2 text-sm font-medium text-gray-500 dark:text-gray-400",
            fullWidth:
                "grid w-full grid-flow-col divide-x divide-gray-200 rounded-none text-sm font-medium shadow dark:divide-gray-700 dark:text-gray-400",
        },
        tabitem: {
            base: "flex items-center justify-center rounded-t-lg py-2 px-4 text-sm font-medium first:ml-0 disabled:cursor-not-allowed disabled:text-gray-400 disabled:dark:text-gray-500",
            variant: {
                default: {
                    base: "rounded-t-lg",
                    active: {
                        on: "bg-gray-100 text-blue-500 dark:bg-gray-800 dark:text-blue-500",
                        off: "text-gray-500 hover:bg-gray-50 hover:text-gray-600 dark:text-gray-400 dark:hover:bg-gray-800 dark:hover:text-gray-300",
                    },
                },
                underline: {
                    base: "rounded-t-lg",
                    active: {
                        on: "active rounded-t-lg border-b-2 border-blue-500 text-blue-500 dark:border-blue-500 dark:text-blue-500",
                        off: "border-b-2 border-transparent text-gray-500 hover:border-gray-300 hover:text-gray-600 dark:text-gray-400 dark:hover:text-gray-300",
                    },
                },
                pills: {
                    base: "",
                    active: {
                        on: "rounded-lg bg-cyan-600 text-white",
                        off: "rounded-lg hover:bg-gray-100 hover:text-gray-900 dark:hover:bg-gray-800 dark:hover:text-white",
                    },
                },
                fullWidth: {
                    base: "ml-0 flex w-full rounded-none first:ml-0",
                    active: {
                        on: "active rounded-none bg-gray-100 p-4 text-gray-900 dark:bg-gray-700 dark:text-white",
                        off: "rounded-none bg-white hover:bg-gray-50 hover:text-gray-700 dark:bg-gray-800 dark:hover:bg-gray-700 dark:hover:text-white",
                    },
                },
            },
            icon: "mr-2 h-5 w-5",
        },
    },
    tabitemcontainer: {
        base: "",
        variant: {
            default: "",
            underline: "",
            pills: "",
            fullWidth: "",
        },
    },
    tabpanel: "py-0",
};

export const spinner_custom = {
    base: "inline animate-spin text-gray-200",
    color: {
        failure: "fill-red-600",
        gray: "fill-gray-600",
        info: "fill-cyan-600",
        pink: "fill-pink-600",
        purple: "fill-purple-600",
        success: "fill-green-500",
        warning: "fill-yellow-400",
        blue: "fill-blue-600",
    },
    light: {
        off: {
            base: "dark:text-gray-600",
            color: {
                failure: "",
                gray: "dark:fill-gray-300",
                info: "",
                pink: "",
                purple: "",
                success: "",
                warning: "",
            },
        },
        on: {
            base: "",
            color: {
                failure: "",
                gray: "",
                info: "",
                pink: "",
                purple: "",
                success: "",
                warning: "",
            },
        },
    },
    size: {
        xs: "h-3 w-3",
        sm: "h-4 w-4",
        md: "h-6 w-6",
        lg: "h-8 w-8",
        xl: "h-10 w-10",
    },
};

export const customDrawerEdit = {
    root: {
        base: "fixed z-50 overflow-y-auto bg-white p-4 transition-transform dark:bg-gray-800 bg-slate-100 ",
        backdrop: "fixed inset-0 z-40 bg-gray-900/50 dark:bg-gray-900/80",
        edge: "bottom-16",
        position: {
            top: {
                on: "left-0 right-0 top-0 w-full transform-none",
                off: "left-0 right-0 top-0 w-full -translate-y-full",
            },
            right: {
                on: "right-0 top-0 h-screen w-80 transform-none",
                off: "right-0 top-0 h-screen w-80 translate-x-full",
            },
            bottom: {
                on: "bottom-0 left-0 right-0 w-full transform-none",
                off: "bottom-0 left-0 right-0 w-full translate-y-full",
            },
            left: {
                on: "left-0 top-0 h-screen w-80 transform-none",
                off: "left-0 top-0 h-screen w-80 -translate-x-full",
            },
        },
    },
    header: {
        inner: {
            closeButton:
                "absolute end-2.5 top-2.5 flex h-8 w-8 items-center justify-center rounded-lg bg-transparent text-sm text-gray-400 hover:bg-gray-200 hover:text-gray-900 dark:hover:bg-gray-600 dark:hover:text-white",
            closeIcon: "h-6 w-6",
            titleIcon: "me-2.5 h-6 w-6 text-slate-400",
            titleText:
                "mb-4 inline-flex items-center text-xl font-semibold text-slate-800 dark:text-gray-400",
        },
        collapsed: {
            on: "hidden",
            off: "block",
        },
    },
    items: {
        base: "rounded-lg dark:!bg-gray-700",
    },
};

export const customModal = {
    "root": {
        "base": "fixed inset-x-0 top-0 z-50 h-screen overflow-y-auto overflow-x-hidden md:inset-0 md:h-full",
        "show": {
            "on": "flex bg-gray-900 bg-opacity-50 dark:bg-opacity-80",
            "off": "hidden"
        },
        "sizes": {
            "sm": "max-w-sm",
            "md": "max-w-md",
            "lg": "max-w-lg",
            "xl": "max-w-xl",
            "2xl": "max-w-2xl",
            "3xl": "max-w-3xl",
            "4xl": "max-w-4xl",
            "5xl": "max-w-5xl",
            "6xl": "max-w-6xl",
            "7xl": "max-w-7xl"
        },
        "positions": {
            "top-left": "items-start justify-start",
            "top-center": "items-start justify-center",
            "top-right": "items-start justify-end",
            "center-left": "items-center justify-start",
            "center": "items-center justify-center",
            "center-right": "items-center justify-end",
            "bottom-right": "items-end justify-end",
            "bottom-center": "items-end justify-center",
            "bottom-left": "items-end justify-start"
        }
    },
    "content": {
        "base": "relative h-full w-full p-4 md:h-auto",
        "inner": "relative flex max-h-[90dvh] flex-col rounded-lg bg-white shadow dark:bg-gray-700"
    },
    "body": {
        "base": "flex-1 overflow-auto p-6",
        "popup": "pt-0"
    },
    "header": {
        "base": "flex items-start justify-between rounded-t border-b p-2 dark:border-gray-600",
        "popup": "border-b-0 p-2",
        "title": "text-xl font-medium text-gray-900 dark:text-white",
        "close": {
            "base": "ml-auto inline-flex items-center rounded-lg bg-transparent p-1.5 text-sm text-gray-400 hover:bg-gray-200 hover:text-gray-900 dark:hover:bg-gray-600 dark:hover:text-white",
            "icon": "h-5 w-5"
        }
    },
    "footer": {
        "base": "flex items-center space-x-2 rounded-b border-gray-200 p-6 dark:border-gray-600",
        "popup": "border-t"
    }
}

export const custom_dropdown = {
    root: {
        base: "fixed z-40 overflow-y-auto bg-slate-800 p-4 transition-transform dark:bg-gray-800",
        backdrop: "fixed inset-0 z-30 bg-gray-900/50 dark:bg-gray-900/80",
        edge: "bottom-16",
        position: {
            top: {
                on: "left-0 right-0 top-0 w-full transform-none",
                off: "left-0 right-0 top-0 w-full -translate-y-full",
            },
            right: {
                on: "right-0 top-0 h-screen w-80 transform-none",
                off: "right-0 top-0 h-screen w-80 translate-x-full",
            },
            bottom: {
                on: "bottom-0 left-0 right-0 w-full transform-none",
                off: "bottom-0 left-0 right-0 w-full translate-y-full",
            },
            left: {
                on: "left-0 top-0 h-screen w-80 transform-none",
                off: "left-0 top-0 h-screen w-80 -translate-x-full",
            },
        },
    },
    header: {
        inner: {
            closeButton:
                "absolute end-2.5 top-2.5 flex h-8 w-8 items-center justify-center rounded-lg bg-transparent text-sm text-gray-400 hover:bg-gray-200 hover:text-gray-900 dark:hover:bg-gray-600 dark:hover:text-white",
            closeIcon: "h-4 w-4",
            titleIcon: "me-2.5 h-4 w-4",
            titleText:
                "mb-4 inline-flex items-center text-base font-semibold text-gray-500 dark:text-gray-400",
        },
        collapsed: {
            on: "hidden",
            off: "block",
        },
    },
    items: {
        base: "",
    },
};

export const navbar_custom = {
    root: {
        base: "bg-slate-800  dark:border-gray-700 dark:bg-gray-800 p-0 px-0 py-0 bg-slate-800 dark:bg-gray-800 dark:border-gray-700 lg:h-14 md:h-14 grow z-40",
        rounded: {
            on: "",
            off: "",
        },
        bordered: {
            on: "border",
            off: "",
        },
        inner: {
            base: "mx-auto flex gap-2 items-center justify-between",
            fluid: {
                on: "",
                off: "",
            },
        },
    },
    brand: {
        base: "flex items-center text-white",
    },
    collapse: {
        base: "w-full md:block md:w-auto",
        list: "mt-4 flex flex-col md:mt-0 md:flex-row md:space-x-8 md:text-sm md:font-medium",
        hidden: {
            on: "hidden",
            off: "hidden",
        },
    },
    link: {
        base: "block py-2 pl-3 pr-4 md:p-0",
        active: {
            on: "bg-cyan-700 text-white dark:text-white md:bg-transparent md:text-cyan-700",
            off: "border-b border-gray-100 text-gray-700 hover:bg-gray-50 dark:border-gray-700 dark:text-gray-400 dark:hover:bg-gray-700 dark:hover:text-white md:border-0 md:hover:bg-transparent md:hover:text-cyan-700 md:dark:hover:bg-transparent md:dark:hover:text-white",
        },
        disabled: {
            on: "text-gray-400 hover:cursor-not-allowed dark:text-gray-600",
            off: "",
        },
    },
    toggle: {
        base: "inline-flex items-center rounded-lg p-2 text-sm text-gray-500 hover:bg-gray-100 focus:outline-none focus:ring-2 focus:ring-gray-200 dark:text-gray-400 dark:hover:bg-gray-700 dark:focus:ring-gray-600 md:hidden",
        icon: "h-6 w-6 shrink-0",
    },
};

export const custom_global_card = {
    "root": {
        "base": "flex rounded-lg border border-gray-200 bg-white shadow-md dark:border-gray-700 dark:bg-gray-800 m-4",
        "children": "flex h-full flex-col justify-center gap-4 p-0",
        "horizontal": {
            "off": "flex-col",
            "on": "flex-col md:max-w-xl md:flex-row"
        },
        "href": "hover:bg-gray-100 dark:hover:bg-gray-700"
    },
    "img": {
        "base": "",
        "horizontal": {
            "off": "rounded-t-lg",
            "on": "h-96 w-full rounded-t-lg object-cover md:h-auto md:w-48 md:rounded-none md:rounded-l-lg"
        }
    }
};

export const accordion_custom = {
    root: {
        base: "divide-y divide-gray-200 border-gray-200 dark:divide-gray-700 dark:border-gray-700 w-full",
        flush: {
            off: "",
            on: "border-b",
        },
    },
    content: {
        base: "p-5 last:rounded-b-lg dark:bg-gray-900",
    },
    title: {
        h2: "w-full",
        arrow: {
            base: "h-6 w-6 shrink-0 p-2 w-full hover:bg-white",
            open: {
                off: "",
                on: "rotate-180",
            },
        },
        base: "flex w-full items-center justify-between p-2 text-left font-medium text-gray-500 dark:text-gray-400",
        flush: {
            off: "hover:bg-white focus:ring-4 focus:ring-gray-200 dark:hover:bg-gray-800 dark:focus:ring-gray-800",
            on: "bg-transparent dark:bg-transparent",
        },
        heading: {},
        open: {
            off: "",
            on: "bg-white text-gray-900 dark:bg-gray-800 dark:text-white w-full",
        },
    },
};

export const page_badge_theme = {
    "root": {
        "base": "flex h-fit items-center gap-1 font-semibold ",

        "href": "group",
        "size": {
            "xs": "p-1 text-xs",
            "sm": "p-1 text-sm"
        }
    },
};

// Pagination themes for dark mode support
export const pagination_button_theme = {
    color: {
        gray: "border border-gray-300 bg-white text-gray-500 enabled:hover:bg-gray-100 enabled:hover:text-gray-700 disabled:opacity-50 dark:border-gray-600 dark:bg-gray-800 dark:text-gray-400 dark:enabled:hover:bg-gray-700 dark:enabled:hover:text-white dark:disabled:opacity-50 transition-colors duration-200",
        blue: "border border-blue-300 bg-blue-600 text-white enabled:hover:bg-blue-700 dark:border-blue-600 dark:bg-blue-600 dark:enabled:hover:bg-blue-700 transition-colors duration-200"
    },
    size: {
        xs: "px-2 py-1 text-xs"
    }
};

export const pagination_dropdown_theme = {
    arrowIcon: "ml-2 h-4 w-4 text-gray-400 dark:text-gray-500 transition-colors duration-200",
    content: "py-1 focus:outline-none bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-600 rounded-lg shadow-lg",
    floating: {
        base: "z-10 w-fit rounded divide-y divide-gray-100 dark:divide-gray-600 shadow",
        content: "py-1 text-sm text-gray-700 dark:text-gray-200",
        target: "w-fit"
    },
    inlineWrapper: "flex items-center"
};

export const pagination_buttongroup_theme = {
    position: {
        start: "rounded-r-none border-r-0",
        middle: "rounded-none border-r-0",
        end: "rounded-l-none"
    }
};
