import NoRecord from "@/Components/HelperComponents/NoRecord";
import { Table, TableBody, TableCell, TableHead, TableHeadCell, TableRow } from "flowbite-react";
import { useEffect, useState } from "react";
import { FaSpinner } from "react-icons/fa6";
import { convertDateFormat } from "@/Pages/Helpers/Helper";
import { table_custom } from "@/Pages/Helpers/DesignHelper";

export default function ViewSavedChats({ data }) {
    // const [isCheckAll, setIsCheckAll] = useState(false);
    const [switch3, setSwitch3] = useState(true);
    const [tableData, setTableData] = useState(null);
    const [isLoading, setIsLoading] = useState(true);

    useEffect(() => {
        // fetch data
        const dataFetch = async () => {
            const newData = await (
                await fetch(
                    route("Integration.whatsapp.whatsappweb.getChat", {
                        channel: data.channel,
                    })
                )
            ).json();
            setIsLoading(false);
            if (newData.count !== 0) {
                setTableData(newData.chats);
            }
        };

        dataFetch();
    }, []);

    return (
        <>
            <div className="bg-white border rounded-lg overflow-x-auto">
                <Table hoverable theme={table_custom}>
                    <TableHead className="text-center">
                        <TableHeadCell>Id</TableHeadCell>
                        <TableHeadCell>Mobile</TableHeadCell>
                        <TableHeadCell>Msg</TableHeadCell>

                        <TableHeadCell>User</TableHeadCell>
                        <TableHeadCell>Datetime</TableHeadCell>
                    </TableHead>
                    <TableBody className="divide-y">
                        {tableData ? (
                            tableData.map((item, key) => (
                                <TableRow
                                    key={"saveChatRow" + key}
                                    className="bg-white dark:border-gray-700 dark:bg-gray-800"
                                >
                                    <TableCell className="font-medium text-gray-900 whitespace-nowrap dark:text-white">
                                        {item.id ? item.id : <>-</>}
                                    </TableCell>
                                    <TableCell>
                                        {item.remoteJid ? (
                                            item.remoteJid
                                        ) : (
                                            <>-</>
                                        )}
                                    </TableCell>

                                    <TableCell className="text-center">
                                        {item.msg ? item.msg : <>-</>}
                                    </TableCell>
                                    <TableCell>
                                        {item.user_id ? (
                                            item.user.username
                                        ) : (
                                            <>-</>
                                        )}
                                    </TableCell>
                                    <TableCell>
                                        {item.messageDateTime ? (
                                            convertDateFormat(
                                                item.messageDateTime
                                            )
                                        ) : (
                                            <>-</>
                                        )}
                                    </TableCell>
                                </TableRow>
                            ))
                        ) : (
                            <TableRow className="bg-white dark:border-gray-700 dark:bg-gray-800">
                                <TableCell colSpan={5} className="text-center">
                                    <NoRecord loading={isLoading} />
                                </TableCell>
                            </TableRow>
                        )}
                    </TableBody>
                </Table>
            </div>
        </>
    );
}

