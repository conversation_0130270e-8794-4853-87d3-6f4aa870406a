import React, { useEffect, useState } from "react";
// import WaMain from "../WaMain";
import Box from "@mui/material/Box";
import Step from "@mui/material/Step";
import StepLabel from "@mui/material/StepLabel";
import Stepper from "@mui/material/Stepper";
// import Button from "@mui/material/Button";
import {
    button_custom,
    card_custom,
    input_custom,
    radio_custom,
    textarea_custom,
    toggle_custom
} from "@/Pages/Helpers/DesignHelper";
import { fetchJson, showAsset } from "@/Pages/Helpers/Helper";
import Typography from "@mui/material/Typography";
import EmojiPicker from "emoji-picker-react";
import {
    Button,
    Card,
    Checkbox,
    Dropdown,
    DropdownDivider,
    DropdownItem,
    Label,
    List,
    Radio,
    Textarea,
    TextInput,
    ToggleSwitch,
    Tooltip
} from "flowbite-react";
import { AiOutlineReload } from "react-icons/ai";
import { BiErrorAlt } from "react-icons/bi";
import {
    FaBold,
    FaItalic,
    FaRegFaceSmileBeam,
    FaStrikethrough
} from "react-icons/fa6";
import { GrFormNext } from "react-icons/gr";
import { HiOutlineTemplate } from "react-icons/hi";
import {
    IoMdAdd,
    IoMdClose,
    IoMdInformationCircleOutline,
    IoMdOpen,
} from "react-icons/io";
import { IoChevronBack, IoText } from "react-icons/io5";
import {
    MdClose,
    MdDeleteOutline,
    MdInfoOutline,
    MdOutlineAdd,
    MdOutlineCampaign,
    MdOutlineContentCopy,
    MdOutlineInfo,
    MdOutlineNotificationsActive,
    MdOutlineOpenInNew,
    MdOutlineVpnKey
} from "react-icons/md";
import { TbCheckbox } from "react-icons/tb";
import Category from "./Category";
import WaMain from "@/Pages/WhatsappBusiness/WaMain";

const steps = [
    "Set up Template",
    "Edit Template",
    "Edit-Utility Template",
    "Edit-Authentication Template",
];

export default function AddCampaign({ categories, templates, category_id }) {
    // const [templateData, setTemplateData] = useState(templates.data);
    const [openEmoji, setOpenEmoji] = useState(false);
    const [channels, setChannelsData] = useState(null);
    const [contactList, setContactList] = useState(null);
    const [channelNextPage, setChannelNextPage] = useState();
    const [contactListNextPage, setContactListNextPage] = useState();

    const [activeStep, setActiveStep] = React.useState(0);

    const handleNext = () => {
        setActiveStep((prevActiveStep) => prevActiveStep + 1);
    };

    const handleBack = () => {
        setActiveStep((prevActiveStep) => prevActiveStep - 1);
    };

    const handleReset = () => {
        setActiveStep(0);
    };
    function fetchDataChannels() {
        Promise.all([
            fetchJson("helper.getChannels"),
            fetchJson("helper.getContactList"),
        ]).then(([channelsData, contactListData]) => {
            setChannelsData(channelsData.items.data);
            setChannelNextPage(channelsData.items.next_page_url);

            setContactList(contactListData.items.data);
            setContactListNextPage(contactListData.items.next_page_url);
        });
    }
    const loadMore = (nextPageUrl, setObject, object, nextPage) => {
        Promise.all([fetchJson(nextPageUrl, {}, true)]).then(([nextData]) => {
            if (nextData.items.next_page_url) {
                nextPage(nextData.items.next_page_url);
            } else {
                nextPage(null);
            }
            setObject((object) => [...object, ...nextData.items.data]);
        });
    };
    useEffect(() => {
        fetchDataChannels();
    }, []);
    const handleContactListChecked = (e) => {
        let id = e.target.value;
        if (e.target.checked) {
            if (data.contactList.length >= 5) {
                e.target.checked = false;
                alert("Max 5 Contact Lists allowed.");
                return false;
            }
            setData("contactList", [...data.contactList, id]);
        } else {
            setData(
                "contactList",
                data.contactList.filter((item) => {
                    return item !== id;
                })
            );
        }
    };

    const handleChannelsChecked = (e) => {
        let id = e.target.value;
        if (e.target.checked) {
            setData("channels", [...data.channels, id]);
        } else {
            setData(
                "channels",
                data.channels.filter((item) => {
                    return item !== id;
                })
            );
        }
    };
    const [showMiddleDiv, setShowMiddleDiv] = useState(false);

    const handleChildClick = () => {
        setShowMiddleDiv((prev) => !prev);
    };
    const [selectedCountry, setSelectedCountry] = useState("USA");

    const handleButtonClick = (value) => {
        setSelectedCountry(value);
    };
    const renderStepContent = (step) => {
        switch (step) {
            case 0:
                return (
                    // Basic Information
                    <Typography className="grid grid-flow-row-dense grid-cols-12 p-0 pt-2 border-none md:grid-cols-12 sm:grid-cols-1 lg:gap-2">
                        <div className="relative flex flex-col gap-2 pt-0 dark:text-gray-400 lg:p-0 xl:col-span-9 lg:col-span-8 md:col-span-12 col-span-full">
                            <div className="w-full p-2 bg-white border rounded-lg">
                                <h4 className="text-base">Set up Template</h4>
                                <p className="text-sm text-gray-400">
                                    Choose the category that best describes your
                                    message template. Then, select the type of
                                    message that you want to send.
                                </p>
                            </div>
                            <div className="p-2 px-3 bg-white border rounded-lg ">
                                {/* Campaign Name */}
                                <div className="flex max-w-screen-md gap-3">
                                    <div className="w-full">
                                        <div className="block mb-2">
                                            <Label htmlFor="default-range">
                                                Template Name
                                                <span className="text-red-600">
                                                    *
                                                </span>
                                            </Label>
                                        </div>
                                        <TextInput
                                            theme={input_custom}
                                            id="templateName"
                                            type="text"
                                            // value={data.templateName}
                                            // onChange={(e) =>
                                            //     setData(
                                            //         "templateName",
                                            //         e.target.value
                                            //     )
                                            // }
                                            placeholder="E.g. Welcome New Customers"
                                            color={"gray"}
                                            // helperText={
                                            //     errors.templateName &&
                                            //     errors.templateName
                                            // }
                                        />
                                    </div>
                                    <div className="w-full">
                                        <div className="block mb-2">
                                            <Label htmlFor="default-range">
                                                Category Name
                                                <span className="text-red-600">
                                                    *
                                                </span>
                                            </Label>
                                        </div>
                                        <select
                                            className="w-full rounded-lg text-sm border-gray-300 p-2.5 hover:bg-gray-50"
                                            name="cars"
                                            id="cars"
                                        >
                                            <option value="volvo">
                                                Select Category
                                            </option>
                                            <option value="saab">Name</option>
                                            <option value="opel">
                                                Mobile Number
                                            </option>
                                            <option value="audi">City</option>
                                            <option value="audi">
                                                Address
                                            </option>
                                        </select>
                                    </div>
                                </div>
                            </div>
                            <div className="flex flex-col gap-3 p-2 bg-white rounded-lg">
                                <div className="">
                                    <h4 className="text-base">
                                        Set up Template
                                    </h4>
                                    <p className="text-sm text-gray-400">
                                        Choose the category that best describes
                                        your message template. Then, select the
                                        type of message that you want to send.
                                    </p>
                                </div>
                                <div className="flex items-center gap-4">
                                    <button className="px-4 py-1 border border-gray-300 rounded w-fit focus:outline-none focus:ring-1 focus:ring-blue-500 group focus:bg-blue-50">
                                        <div className="flex items-center text-sm group-focus:text-blue-700 text-slate-600">
                                            <MdOutlineCampaign className="text-2xl " />
                                            Marketing
                                        </div>
                                    </button>
                                    <button className="px-4 py-1 border border-gray-300 rounded w-fit focus:outline-none focus:ring-1 focus:ring-blue-500 group focus:bg-blue-50">
                                        <div className="flex items-center gap-1 text-sm group-focus:text-blue-700 text-slate-600">
                                            <MdOutlineNotificationsActive className="text-xl " />
                                            Utility
                                        </div>
                                    </button>
                                    <button className="px-4 py-1 border border-gray-300 rounded w-fit focus:outline-none focus:ring-1 focus:ring-blue-500 group focus:bg-blue-50">
                                        <div className="flex items-center gap-1 text-sm group-focus:text-blue-700 text-slate-600">
                                            <MdOutlineVpnKey className="text-xl " />
                                            Authentication
                                        </div>
                                    </button>
                                </div>
                                {/* <div>
                                    <button className="flex items-center py-1.5 px-3 gap-2 border border-gray-300 focus:outline-none focus:ring-1 focus:ring-blue-500 group rounded focus:bg-blue-50">
                                        <Radio
                                            id="united-state"
                                            name="countries"
                                            value="USA"
                                            defaultChecked
                                        />
                                        <div className="flex flex-col">
                                            <Label htmlFor="united-state">
                                                United States
                                            </Label>
                                            <span className="text-sm">
                                                Send promotions or announcements
                                                to increase awareness and
                                                engagement.
                                            </span>
                                        </div>
                                    </button>
                                </div> */}
                                <div className="flex flex-col gap-3">
                                    <button
                                        className="flex items-center py-1.5 px-3 gap-2 border border-gray-300 focus:outline-none focus:ring-1 focus:ring-blue-500 group rounded focus:bg-blue-50"
                                        onClick={() => handleButtonClick("USA")}
                                    >
                                        <input
                                            type="radio"
                                            id="united-state"
                                            name="countries"
                                            value="USA"
                                            checked={selectedCountry === "USA"}
                                            onChange={() =>
                                                setSelectedCountry("USA")
                                            }
                                        />
                                        <div className="flex flex-col text-start">
                                            <label>Custom</label>
                                            <span className="text-sm">
                                                Send promotions or announcements
                                                to increase awareness and
                                                engagement.
                                            </span>
                                        </div>
                                    </button>

                                    <button
                                        className="flex items-center py-1.5 px-3 gap-2 border border-gray-300 focus:outline-none focus:ring-1 focus:ring-blue-500 group rounded focus:bg-blue-50"
                                        onClick={() => handleButtonClick("USA")}
                                    >
                                        <input
                                            type="radio"
                                            id="united-state"
                                            name="countries"
                                            value="USA"
                                            checked={selectedCountry === "USA"}
                                            onChange={() =>
                                                setSelectedCountry("USA")
                                            }
                                        />
                                        <div className="flex flex-col text-start">
                                            <label>Catalogue</label>
                                            <span className="text-sm">
                                                Send messages about your entire
                                                catalogue or multiple products
                                                from it.
                                            </span>
                                        </div>
                                    </button>
                                </div>
                            </div>
                            <div className="flex flex-col gap-3 p-2 bg-white rounded-lg">
                                <div className="">
                                    <h4 className="text-base">
                                        Set up Template
                                    </h4>
                                    <p className="text-sm text-gray-400">
                                        Choose the category that best describes
                                        your message template. Then, select the
                                        type of message that you want to send.
                                    </p>
                                </div>
                                <div className="flex items-center gap-4">
                                    <button className="px-4 py-1 border border-gray-300 rounded w-fit focus:outline-none focus:ring-1 focus:ring-blue-500 group focus:bg-blue-50">
                                        <div className="flex items-center text-sm group-focus:text-blue-700 text-slate-600">
                                            <MdOutlineCampaign className="text-2xl " />
                                            Marketing
                                        </div>
                                    </button>
                                    <button className="px-4 py-1 border border-gray-300 rounded w-fit focus:outline-none focus:ring-1 focus:ring-blue-500 group focus:bg-blue-50">
                                        <div className="flex items-center gap-1 text-sm group-focus:text-blue-700 text-slate-600">
                                            <MdOutlineNotificationsActive className="text-xl " />
                                            Utility
                                        </div>
                                    </button>
                                    <button className="px-4 py-1 border border-gray-300 rounded w-fit focus:outline-none focus:ring-1 focus:ring-blue-500 group focus:bg-blue-50">
                                        <div className="flex items-center gap-1 text-sm group-focus:text-blue-700 text-slate-600">
                                            <MdOutlineVpnKey className="text-xl " />
                                            Authentication
                                        </div>
                                    </button>
                                </div>
                                <div className="flex flex-col gap-3">
                                    <button
                                        className="flex items-center py-1.5 px-3 gap-2 border border-gray-300 focus:outline-none focus:ring-1 focus:ring-blue-500 group rounded focus:bg-blue-50"
                                        onClick={() => handleButtonClick("USA")}
                                    >
                                        <input
                                            type="radio"
                                            id="united-state"
                                            name="countries"
                                            value="USA"
                                            checked={selectedCountry === "USA"}
                                            onChange={() =>
                                                setSelectedCountry("USA")
                                            }
                                        />
                                        <div className="flex flex-col text-start">
                                            <label>Custom</label>
                                            <span className="text-sm">
                                                Send messages about an existing
                                                order or account.
                                            </span>
                                        </div>
                                    </button>
                                </div>
                            </div>
                            <div className="flex items-start gap-2 bg-white">
                                <div className="h-full">
                                    <img
                                        className="h-full"
                                        src={showAsset(
                                            "/assets/img/templateLibrary.png"
                                        )}
                                        alt=""
                                    />
                                </div>
                                <div className="pt-1">
                                    <div>Start with pre-approved templates</div>
                                    <div className="mb-2 font-normal text-gray-500">
                                        Save time and effort by using
                                        pre-approved templates from our Utility
                                        Library. You can quickly build
                                        professional-looking templates without
                                        starting from scratch.
                                    </div>
                                    <Button
                                        className="m-2 float-end"
                                        color="blue"
                                        theme={button_custom}
                                        size="xs"
                                    >
                                        <div className="flex items-center gap-1">
                                            <TbCheckbox className="" />
                                            <span className="text-sm">
                                                Go to Template Library
                                            </span>
                                        </div>
                                    </Button>
                                </div>
                            </div>
                            <div className="flex flex-col gap-3 p-2 bg-white rounded-lg">
                                <div className="">
                                    <h4 className="text-base">
                                        Set up Template
                                    </h4>
                                    <p className="text-sm text-gray-400">
                                        Choose the category that best describes
                                        your message template. Then, select the
                                        type of message that you want to send.
                                    </p>
                                </div>
                                <div className="flex items-center gap-4">
                                    <button className="px-4 py-1 border border-gray-300 rounded w-fit focus:outline-none focus:ring-1 focus:ring-blue-500 group focus:bg-blue-50">
                                        <div className="flex items-center text-sm group-focus:text-blue-700 text-slate-600">
                                            <MdOutlineCampaign className="text-2xl " />
                                            Marketing
                                        </div>
                                    </button>
                                    <button className="px-4 py-1 border border-gray-300 rounded w-fit focus:outline-none focus:ring-1 focus:ring-blue-500 group focus:bg-blue-50">
                                        <div className="flex items-center gap-1 text-sm group-focus:text-blue-700 text-slate-600">
                                            <MdOutlineNotificationsActive className="text-xl " />
                                            Utility
                                        </div>
                                    </button>
                                    <button className="px-4 py-1 border border-gray-300 rounded w-fit focus:outline-none focus:ring-1 focus:ring-blue-500 group focus:bg-blue-50">
                                        <div className="flex items-center gap-1 text-sm group-focus:text-blue-700 text-slate-600">
                                            <MdOutlineVpnKey className="text-xl " />
                                            Authentication
                                        </div>
                                    </button>
                                </div>
                                <div className="flex flex-col gap-3">
                                    <button
                                        className="flex items-center py-1.5 px-3 gap-2 border border-gray-300 focus:outline-none focus:ring-1 focus:ring-blue-500 group rounded focus:bg-blue-50"
                                        onClick={() => handleButtonClick("USA")}
                                    >
                                        <input
                                            type="radio"
                                            id="united-state"
                                            name="countries"
                                            value="USA"
                                            checked={selectedCountry === "USA"}
                                            onChange={() =>
                                                setSelectedCountry("USA")
                                            }
                                        />
                                        <div className="flex flex-col text-start">
                                            <label>One-time passcode</label>
                                            <span className="text-sm">
                                                Send codes to verify a
                                                transaction or login.
                                            </span>
                                        </div>
                                    </button>
                                </div>
                            </div>
                            <div className="flex flex-col gap-2 p-2 bg-white rounded-lg">
                                <div>
                                    <Label>
                                        <div className="flex items-center gap-2">
                                            Choose a gateway to design your
                                            template.{" "}
                                            <span className="text-red-600">
                                                *
                                            </span>
                                            <Tooltip content="Load More">
                                                <Button
                                                    theme={button_custom}
                                                    size="xxs"
                                                    color="blue"
                                                    // onClick={() =>
                                                    //     loadMore(
                                                    //         channelNextPage,
                                                    //         setChannelsData,
                                                    //         channels,
                                                    //         setChannelNextPage
                                                    //     )
                                                    // }
                                                >
                                                    <AiOutlineReload />
                                                </Button>
                                            </Tooltip>
                                        </div>
                                    </Label>
                                </div>
                                <div className="flex flex-wrap gap-5">
                                    <div className="flex items-center gap-2">
                                        <Radio
                                            color="blue"
                                            theme={radio_custom}
                                            id="united-state"
                                            name="countries"
                                            value="USA"
                                            defaultChecked
                                        />
                                        <Label htmlFor="united-state">
                                            Gateway 1
                                        </Label>
                                    </div>
                                    <div className="flex items-center gap-2">
                                        <Radio
                                            color="blue"
                                            theme={radio_custom}
                                            id="germany"
                                            name="countries"
                                            value="Germany"
                                        />
                                        <Label htmlFor="germany">
                                            Gateway 2
                                        </Label>
                                    </div>
                                    <div className="flex items-center gap-2">
                                        <Radio
                                            color="blue"
                                            theme={radio_custom}
                                            id="spain"
                                            name="countries"
                                            value="Spain"
                                        />
                                        <Label htmlFor="spain">Gateway 3</Label>
                                    </div>
                                    <div className="flex items-center gap-2">
                                        <Radio
                                            color="blue"
                                            theme={radio_custom}
                                            id="uk"
                                            name="countries"
                                            value="United Kingdom"
                                        />
                                        <Label htmlFor="uk">Gateway 4</Label>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div
                            className="col-span-12 mb-2 xl:col-span-3 lg:col-span-4 md:col-span-12 sm:col-span-12 lg:flex"
                            // key={index}
                        >
                            <Card
                                theme={card_custom}
                                className="relative w-full bg-[url('/assets/img/bgwtsp.png')]"
                            >
                                <div className="absolute -left-0 top-6 w-0 h-0 border-t-[1px] border-t-transparent border-b-[12px] border-b-transparent border-r-[18px] border-r-white"></div>
                                <div>
                                    <div
                                        className="p-2 overflow-auto"
                                        // style={{
                                        //     height: "250px",
                                        // }}
                                    >
                                        <div className="bg-white px-3 rounded-lg mx-2.5 py-0.5">
                                            <>
                                                <img
                                                    src={showAsset("/storage/")}
                                                    className="w-full my-2 rounded-md "
                                                />
                                            </>
                                            <div
                                                className="p-3 text-center text-gray-300"
                                                style={{
                                                    fontSize: "7rem",
                                                }}
                                            ></div>
                                            <div className="flex gap-2 p-3 my-2 bg-gray-100 rounded-lg">
                                                <div className="text-3xl text-gray-400"></div>
                                                <div className="text-sm text-gray-700">
                                                    Thank you for using your
                                                    credit card at CS Mutual.
                                                    Your receipt is attached as
                                                    a PDF.
                                                </div>
                                            </div>

                                            <></>
                                            <div className="text-xs text-end text-slate-500">
                                                09:45
                                            </div>
                                            <div className="flex justify-center py-2 mt-2 border-t">
                                                <button>
                                                    <div className="flex items-center justify-center gap-2 font-medium text-cyan-700">
                                                        <MdOutlineOpenInNew />
                                                        Shop now
                                                    </div>
                                                </button>
                                            </div>
                                            <div className="flex justify-center py-2 mt-2 border-t">
                                                <button>
                                                    <div className="flex items-center justify-center gap-2 font-medium text-cyan-700">
                                                        <MdOutlineContentCopy />
                                                        Copy Code
                                                    </div>
                                                </button>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </Card>
                        </div>
                    </Typography>
                );
            case 1:
                return (
                    <Typography>
                        <div className="grid grid-flow-row-dense grid-cols-12 p-0 pt-2 border-none md:grid-cols-12 sm:grid-cols-1 lg:gap-2">
                            <div className="relative flex flex-col gap-2 pt-0 dark:text-gray-400 lg:p-0 xl:col-span-9 lg:col-span-8 md:col-span-12 col-span-full">
                                <div className="w-full p-2 bg-white border rounded-lg">
                                    <h4 className="text-base">
                                        Choose Language
                                    </h4>
                                    <p className="text-sm text-gray-400">
                                        Select your preferred language for the
                                        template.
                                    </p>
                                    <div className="w-full mt-3">
                                        <select
                                            className="w-full rounded-lg text-sm border-gray-300 p-2.5 hover:bg-gray-50"
                                            name="cars"
                                            id="cars"
                                        >
                                            <option value="volvo">
                                                Choose Language
                                            </option>
                                            <option value="saab">Name</option>
                                            <option value="opel">
                                                Mobile Number
                                            </option>
                                            <option value="audi">City</option>
                                            <option value="audi">
                                                Address
                                            </option>
                                        </select>
                                    </div>
                                </div>
                                <div className="w-full p-2 bg-white border rounded-lg">
                                    <h4 className="text-base">Header </h4>
                                    <p className="text-sm text-gray-400">
                                        Select your preferred language for the
                                        template.
                                    </p>
                                    <div className="w-full mt-3">
                                        <select
                                            className="w-full rounded-lg text-sm border-gray-300 p-2.5 hover:bg-gray-50"
                                            name="cars"
                                            id="cars"
                                        >
                                            <option value="volvo">
                                                Choose Language
                                            </option>
                                            <option value="saab">
                                                <div className="flex items-center">
                                                    <IoText />
                                                    Text
                                                </div>
                                            </option>
                                            <option value="opel">Image</option>
                                            <option value="audi">Video</option>
                                            <option value="audi">
                                                Document
                                            </option>
                                            <option value="audi">
                                                Location
                                            </option>
                                        </select>
                                    </div>
                                    {/* Text */}
                                    <div>
                                        <div className="mt-2">
                                            <TextInput
                                                theme={input_custom}
                                                id="email1"
                                                type="email"
                                                placeholder="Enter text here..."
                                                required
                                            />
                                            <div className="flex items-center gap-1 mt-2 w-fit float-end">
                                                <Button
                                                    theme={button_custom}
                                                    size="xxs"
                                                    color="gray"
                                                >
                                                    <div className="flex items-center gap-1">
                                                        <IoMdAdd />
                                                        <span>Variables</span>
                                                    </div>
                                                </Button>
                                                <Tooltip
                                                    className="p-0"
                                                    style="light"
                                                    content={
                                                        <div className="rounded min-w-52 text-wrap">
                                                            <div className="bg-black text-white p-1.5 rounded-t text-center">
                                                                Stop Campaign
                                                            </div>
                                                            <div className="p-1.5 text-center">
                                                                Export the data
                                                                in CSV format
                                                                for offline use
                                                                or analysis.
                                                            </div>
                                                        </div>
                                                    }
                                                >
                                                    <IoMdInformationCircleOutline className="text-xl text-slate-500" />
                                                </Tooltip>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div className="p-2 px-3 bg-white border rounded-lg ">
                                    <div className="flex items-center gap-2">
                                        <h4 className="text-base">Body </h4>
                                        <IoMdInformationCircleOutline className="text-xl text-slate-500" />
                                    </div>
                                    <div className="bg-[#F0EEED] p-1.5 mt-1 rounded-md">
                                        {/*-------- Message -----------*/}
                                        <div className="">
                                            <div className="">
                                                {openEmoji && (
                                                    <div
                                                        className="absolute z-50 "
                                                        style={{
                                                            top: "25%",
                                                            bottom: "25%",
                                                        }}
                                                    >
                                                        <EmojiPicker
                                                            open={openEmoji}
                                                            lazyLoadEmojis={
                                                                true
                                                            }
                                                        />
                                                    </div>
                                                )}

                                                <div className="p-2 rounded-md bg-gray-50">
                                                    <div className="grid grid-cols-3 md:grid-cols-5 lg:grid-cols-5 xl:grid-cols-7">
                                                        <div className="flex justify-center"></div>
                                                    </div>

                                                    <Textarea
                                                        theme={textarea_custom}
                                                        name="message"
                                                        placeholder="Type Message here..."
                                                        rows={4}
                                                        id="myTextarea"
                                                        className="border-0 focus:ring-white"
                                                    />

                                                    <div className="flex flex-wrap justify-between gap-1 flex-center lg:flex-row">
                                                        <div className="flex flex-wrap items-center gap-1 space-x-1">
                                                            <div className="">
                                                                <Button
                                                                    className="px-0"
                                                                    size="xs"
                                                                    color="gray"
                                                                    // onClick={() =>
                                                                    //     setOpenEmoji(
                                                                    //         !openEmoji
                                                                    //     )
                                                                    // }
                                                                >
                                                                    <FaRegFaceSmileBeam />
                                                                </Button>
                                                            </div>
                                                            <div className="">
                                                                <Button
                                                                    className="px-0"
                                                                    size="xs"
                                                                    color="gray"
                                                                    id="boldButton"
                                                                    // onClick={() =>
                                                                    //     textFormat(
                                                                    //         "*"
                                                                    //     )
                                                                    // }
                                                                >
                                                                    <FaBold />
                                                                </Button>
                                                            </div>
                                                            <div className="">
                                                                <Button
                                                                    className="px-0"
                                                                    size="xs"
                                                                    color="gray"
                                                                    id="italicButton"
                                                                    // onClick={() =>
                                                                    //     textFormat(
                                                                    //         "_"
                                                                    //     )
                                                                    // }
                                                                >
                                                                    <FaItalic />
                                                                </Button>
                                                            </div>
                                                            <div className="">
                                                                <Button
                                                                    className="px-0"
                                                                    size="xs"
                                                                    color="gray"
                                                                    id="strike"
                                                                    // onClick={() =>
                                                                    //     textFormat(
                                                                    //         "~"
                                                                    //     )
                                                                    // }
                                                                >
                                                                    <FaStrikethrough />
                                                                </Button>
                                                            </div>
                                                        </div>

                                                        <div className="flex flex-wrap gap-2">
                                                            <Button
                                                                theme={
                                                                    button_custom
                                                                }
                                                                size="xs"
                                                                // outline
                                                                className="rounded-lg "
                                                                color="gray"
                                                                id="varName"
                                                            >
                                                                <div className="flex items-center gap-1 text-sm">
                                                                    <MdOutlineAdd className="text-lg text-slate-400" />
                                                                    Add
                                                                    Variables
                                                                </div>
                                                            </Button>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div className="flex flex-col gap-3 p-2 bg-white rounded-lg">
                                    <div>
                                        <div className="block mb-2">
                                            <Label htmlFor="email1">
                                                <div>Footer</div>
                                                <div className="font-normal text-gray-400">
                                                    Footers are great to add any
                                                    disclaimers or to add a
                                                    thoughtful PS.
                                                </div>
                                            </Label>
                                        </div>
                                        <TextInput
                                            theme={input_custom}
                                            color="gray"
                                            id="email1"
                                            type="email"
                                            // placeholder="<EMAIL>"
                                            required
                                        />
                                    </div>
                                </div>
                                <div className="flex flex-col gap-3 p-2 px-3 bg-white border rounded-lg">
                                    <div>
                                        <h4 className="text-base">Buttons</h4>
                                        <p className="text-sm text-gray-400">
                                            Create buttons that let customers
                                            respond to your message or take
                                            action. You can add up to ten
                                            buttons. If you add more than three
                                            buttons, they will appear in a list.
                                        </p>
                                    </div>
                                    <div className="w-fit">
                                        <Dropdown
                                            className="w-fit"
                                            arrowIcon={false}
                                            inline
                                            label={
                                                <Button
                                                    theme={button_custom}
                                                    size="xs"
                                                    // outline
                                                    className="rounded-lg w-fit "
                                                    color="gray"
                                                    id="varName"
                                                >
                                                    <div className="flex items-center gap-1 text-sm">
                                                        <MdOutlineAdd className="text-lg text-slate-400" />
                                                        Add Button
                                                    </div>
                                                </Button>
                                            }
                                        >
                                            <DropdownItem>
                                                <div className="text-start">
                                                    <div className="text-sm font-medium ">
                                                        Quick Reply Buttons
                                                    </div>
                                                    <div className="text-xs ">
                                                        Custom
                                                    </div>
                                                </div>
                                            </DropdownItem>
                                            <DropdownDivider />

                                            <DropdownItem>
                                                <div className="text-start">
                                                    <div className="text-sm font-medium ">
                                                        Call-to-action Buttons
                                                    </div>
                                                    <div className="text-xs ">
                                                        Visit Website
                                                        <br />2 buttons maximum
                                                    </div>
                                                </div>
                                            </DropdownItem>
                                            <DropdownDivider />
                                            <DropdownItem>
                                                <div className="text-start">
                                                    <div className="text-xs ">
                                                        Call Phone Number
                                                        <br />1 button maximum
                                                    </div>
                                                </div>
                                            </DropdownItem>
                                            <DropdownDivider />
                                            <DropdownItem>
                                                <div className="text-start">
                                                    <div className="text-xs ">
                                                        Copy Offer Code
                                                        <br />1 button maximum
                                                    </div>
                                                </div>
                                            </DropdownItem>
                                        </Dropdown>
                                    </div>
                                    {/* Quick Reply */}
                                    <div>
                                        <h1 className="mb-2 text-base">
                                            Quick Reply
                                        </h1>
                                        <div className="flex items-center gap-4 p-2 border rounded bg-blue-50">
                                            <div className="w-fit">
                                                <div className="mb-1">
                                                    <Label>Type</Label>
                                                </div>
                                                <div className="bg-white border py-2.5 px-3 rounded-md">
                                                    <Dropdown
                                                        className="w-fit"
                                                        arrowIcon={true}
                                                        inline
                                                        label={
                                                            <div className="text-sm">
                                                                Custom
                                                            </div>
                                                        }
                                                    >
                                                        <DropdownItem>
                                                            Visit website
                                                        </DropdownItem>
                                                        <DropdownDivider />

                                                        <DropdownItem>
                                                            Call phone number
                                                        </DropdownItem>
                                                        <DropdownDivider />
                                                        <DropdownItem>
                                                            <div className="text-start">
                                                                <div className="text-xs ">
                                                                    Call Phone
                                                                    Number
                                                                    <br />1
                                                                    button
                                                                    maximum
                                                                </div>
                                                            </div>
                                                        </DropdownItem>
                                                        <DropdownDivider />
                                                        <DropdownItem>
                                                            <div className="text-start">
                                                                <div className="text-xs ">
                                                                    Copy Offer
                                                                    Code
                                                                    <br />1
                                                                    button
                                                                    maximum
                                                                </div>
                                                            </div>
                                                        </DropdownItem>
                                                    </Dropdown>
                                                </div>
                                            </div>
                                            <div>
                                                <div className="block mb-1">
                                                    <Label
                                                        htmlFor="email1"
                                                        value="Button Text "
                                                    />
                                                </div>
                                                <div className="flex items-center w-full">
                                                    <TextInput
                                                        className="w-full"
                                                        theme={input_custom}
                                                        id="email1"
                                                        type="email"
                                                        placeholder="Quick Reply"
                                                        required
                                                    />
                                                    <Button
                                                        theme={button_custom}
                                                        color="transparentForTab"
                                                    >
                                                        <MdClose className="text-xl" />
                                                    </Button>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div
                                className="col-span-12 mb-2 xl:col-span-3 lg:col-span-4 md:col-span-12 sm:col-span-12 lg:flex"
                                // key={index}
                            >
                                <Card
                                    theme={card_custom}
                                    className="relative w-full bg-[url('/assets/img/bgwtsp.png')]"
                                >
                                    <div className="absolute -left-0 top-6 w-0 h-0 border-t-[1px] border-t-transparent border-b-[12px] border-b-transparent border-r-[18px] border-r-white"></div>
                                    <div>
                                        <div
                                            className="p-2 overflow-auto"
                                            // style={{
                                            //     height: "250px",
                                            // }}
                                        >
                                            <div className="bg-white px-3 rounded-lg mx-2.5 py-0.5">
                                                <>
                                                    <img
                                                        src={showAsset(
                                                            "/storage/"
                                                        )}
                                                        className="w-full my-2 rounded-md "
                                                    />
                                                </>
                                                <div
                                                    className="p-3 text-center text-gray-300"
                                                    style={{
                                                        fontSize: "7rem",
                                                    }}
                                                ></div>
                                                <div className="flex gap-2 p-3 my-2 bg-gray-100 rounded-lg">
                                                    <div className="text-3xl text-gray-400"></div>
                                                    <div className="text-sm text-gray-700">
                                                        Thank you for using your
                                                        credit card at CS
                                                        Mutual. Your receipt is
                                                        attached as a PDF.
                                                    </div>
                                                </div>

                                                <></>
                                                <div className="text-xs text-end text-slate-500">
                                                    09:45
                                                </div>
                                                <div className="flex justify-center py-2 mt-2 border-t">
                                                    <button>
                                                        <div className="flex items-center justify-center gap-2 font-medium text-cyan-700">
                                                            <MdOutlineOpenInNew />
                                                            Shop now
                                                        </div>
                                                    </button>
                                                </div>
                                                <div className="flex justify-center py-2 mt-2 border-t">
                                                    <button>
                                                        <div className="flex items-center justify-center gap-2 font-medium text-cyan-700">
                                                            <MdOutlineContentCopy />
                                                            Copy Code
                                                        </div>
                                                    </button>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </Card>
                            </div>
                        </div>
                    </Typography>
                );
            case 2:
                return (
                    <Typography>
                        <div className="grid grid-flow-row-dense grid-cols-12 p-0 pt-2 border-none md:grid-cols-12 sm:grid-cols-1 lg:gap-2">
                            <div className="relative flex flex-col gap-2 pt-0 dark:text-gray-400 lg:p-0 xl:col-span-9 lg:col-span-8 md:col-span-12 col-span-full">
                                <div className="w-full p-2 bg-white border rounded-lg">
                                    <h4 className="text-base">
                                        Choose Language
                                    </h4>
                                    <p className="text-sm text-gray-400">
                                        Select your preferred language for the
                                        template.
                                    </p>
                                    <div className="w-full mt-3">
                                        <select
                                            className="w-full rounded-lg text-sm border-gray-300 p-2.5 hover:bg-gray-50"
                                            name="cars"
                                            id="cars"
                                        >
                                            <option value="volvo">
                                                Choose Language
                                            </option>
                                            <option value="saab">Name</option>
                                            <option value="opel">
                                                Mobile Number
                                            </option>
                                            <option value="audi">City</option>
                                            <option value="audi">
                                                Address
                                            </option>
                                        </select>
                                    </div>
                                </div>
                                <div className="w-full p-2 bg-white border rounded-lg">
                                    <h4 className="text-base">Header </h4>
                                    <p className="text-sm text-gray-400">
                                        Select your preferred language for the
                                        template.
                                    </p>
                                    <div className="w-full mt-3">
                                        <select
                                            className="w-full rounded-lg text-sm border-gray-300 p-2.5 hover:bg-gray-50"
                                            name="cars"
                                            id="cars"
                                        >
                                            <option value="volvo">
                                                Choose Language
                                            </option>
                                            <option value="saab">
                                                <div className="flex items-center">
                                                    <IoText />
                                                    Text
                                                </div>
                                            </option>
                                            <option value="opel">Image</option>
                                            <option value="audi">Video</option>
                                            <option value="audi">
                                                Document
                                            </option>
                                            <option value="audi">
                                                Location
                                            </option>
                                        </select>
                                    </div>
                                    {/* Text */}
                                    <div>
                                        <div className="mt-2">
                                            <TextInput
                                                theme={input_custom}
                                                id="email1"
                                                type="email"
                                                placeholder="Enter text here..."
                                                required
                                            />
                                            <div className="flex items-center gap-1 mt-2 w-fit float-end">
                                                <Button
                                                    theme={button_custom}
                                                    size="xxs"
                                                    color="gray"
                                                >
                                                    <div className="flex items-center gap-1">
                                                        <IoMdAdd />
                                                        <span>Variables</span>
                                                    </div>
                                                </Button>
                                                <Tooltip
                                                    className="p-0"
                                                    style="light"
                                                    content={
                                                        <div className="rounded min-w-52 text-wrap">
                                                            <div className="bg-black text-white p-1.5 rounded-t text-center">
                                                                Stop Campaign
                                                            </div>
                                                            <div className="p-1.5 text-center">
                                                                Export the data
                                                                in CSV format
                                                                for offline use
                                                                or analysis.
                                                            </div>
                                                        </div>
                                                    }
                                                >
                                                    <IoMdInformationCircleOutline className="text-xl text-slate-500" />
                                                </Tooltip>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div className="p-2 px-3 bg-white border rounded-lg ">
                                    <div className="flex items-center gap-2">
                                        <h4 className="text-base">Body </h4>
                                        <IoMdInformationCircleOutline className="text-xl text-slate-500" />
                                    </div>
                                    <div className="bg-[#F0EEED] p-1.5 mt-1 rounded-md">
                                        {/*-------- Message -----------*/}
                                        <div className="">
                                            <div className="">
                                                {openEmoji && (
                                                    <div
                                                        className="absolute z-50 "
                                                        style={{
                                                            top: "25%",
                                                            bottom: "25%",
                                                        }}
                                                    >
                                                        <EmojiPicker
                                                            open={openEmoji}
                                                            lazyLoadEmojis={
                                                                true
                                                            }
                                                        />
                                                    </div>
                                                )}

                                                <div className="p-2 rounded-md bg-gray-50">
                                                    <div className="grid grid-cols-3 md:grid-cols-5 lg:grid-cols-5 xl:grid-cols-7">
                                                        <div className="flex justify-center"></div>
                                                    </div>

                                                    <Textarea
                                                        theme={textarea_custom}
                                                        name="message"
                                                        placeholder="Type Message here..."
                                                        rows={4}
                                                        id="myTextarea"
                                                        className="border-0 focus:ring-white"
                                                    />

                                                    <div className="flex flex-wrap justify-between gap-1 flex-center lg:flex-row">
                                                        <div className="flex flex-wrap items-center gap-1 space-x-1">
                                                            <div className="">
                                                                <Button
                                                                    className="px-0"
                                                                    size="xs"
                                                                    color="gray"
                                                                    // onClick={() =>
                                                                    //     setOpenEmoji(
                                                                    //         !openEmoji
                                                                    //     )
                                                                    // }
                                                                >
                                                                    <FaRegFaceSmileBeam />
                                                                </Button>
                                                            </div>
                                                            <div className="">
                                                                <Button
                                                                    className="px-0"
                                                                    size="xs"
                                                                    color="gray"
                                                                    id="boldButton"
                                                                    // onClick={() =>
                                                                    //     textFormat(
                                                                    //         "*"
                                                                    //     )
                                                                    // }
                                                                >
                                                                    <FaBold />
                                                                </Button>
                                                            </div>
                                                            <div className="">
                                                                <Button
                                                                    className="px-0"
                                                                    size="xs"
                                                                    color="gray"
                                                                    id="italicButton"
                                                                    // onClick={() =>
                                                                    //     textFormat(
                                                                    //         "_"
                                                                    //     )
                                                                    // }
                                                                >
                                                                    <FaItalic />
                                                                </Button>
                                                            </div>
                                                            <div className="">
                                                                <Button
                                                                    className="px-0"
                                                                    size="xs"
                                                                    color="gray"
                                                                    id="strike"
                                                                    // onClick={() =>
                                                                    //     textFormat(
                                                                    //         "~"
                                                                    //     )
                                                                    // }
                                                                >
                                                                    <FaStrikethrough />
                                                                </Button>
                                                            </div>
                                                        </div>

                                                        <div className="flex flex-wrap gap-2">
                                                            <Button
                                                                theme={
                                                                    button_custom
                                                                }
                                                                size="xs"
                                                                // outline
                                                                className="rounded-lg "
                                                                color="gray"
                                                                id="varName"
                                                            >
                                                                <div className="flex items-center gap-1 text-sm">
                                                                    <MdOutlineAdd className="text-lg text-slate-400" />
                                                                    Add
                                                                    Variables
                                                                </div>
                                                            </Button>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div className="flex flex-col gap-3 p-2 bg-white rounded-lg">
                                    <div>
                                        <div className="block mb-2">
                                            <Label htmlFor="email1">
                                                <div>Footer</div>
                                                <div className="font-normal text-gray-400">
                                                    Footers are great to add any
                                                    disclaimers or to add a
                                                    thoughtful PS.
                                                </div>
                                            </Label>
                                        </div>
                                        <TextInput
                                            theme={input_custom}
                                            color="gray"
                                            id="email1"
                                            type="email"
                                            // placeholder="<EMAIL>"
                                            required
                                        />
                                    </div>
                                </div>
                                <div className="flex flex-col gap-3 p-2 px-3 bg-white border rounded-lg">
                                    <div>
                                        <h4 className="text-base">Buttons</h4>
                                        <p className="text-sm text-gray-400">
                                            Create buttons that let customers
                                            respond to your message or take
                                            action. You can add up to ten
                                            buttons. If you add more than three
                                            buttons, they will appear in a list.
                                        </p>
                                    </div>
                                    <div className="w-fit">
                                        <Dropdown
                                            className="w-fit"
                                            arrowIcon={false}
                                            inline
                                            label={
                                                <Button
                                                    theme={button_custom}
                                                    size="xs"
                                                    // outline
                                                    className="rounded-lg w-fit "
                                                    color="gray"
                                                    id="varName"
                                                >
                                                    <div className="flex items-center gap-1 text-sm">
                                                        <MdOutlineAdd className="text-lg text-slate-400" />
                                                        Add Button
                                                    </div>
                                                </Button>
                                            }
                                        >
                                            <DropdownItem>
                                                <div className="text-start">
                                                    <div className="text-sm font-medium ">
                                                        Quick Reply Buttons
                                                    </div>
                                                    <div className="text-xs ">
                                                        Custom
                                                    </div>
                                                </div>
                                            </DropdownItem>
                                            <DropdownDivider />

                                            <DropdownItem>
                                                <div className="text-start">
                                                    <div className="text-sm font-medium ">
                                                        Call-to-action Buttons
                                                    </div>
                                                    <div className="text-xs ">
                                                        Visit Website
                                                        <br />2 buttons maximum
                                                    </div>
                                                </div>
                                            </DropdownItem>
                                            <DropdownDivider />
                                            <DropdownItem>
                                                <div className="text-start">
                                                    <div className="text-xs ">
                                                        Call Phone Number
                                                        <br />1 button maximum
                                                    </div>
                                                </div>
                                            </DropdownItem>
                                            <DropdownDivider />
                                            <DropdownItem>
                                                <div className="text-start">
                                                    <div className="text-xs ">
                                                        Copy Offer Code
                                                        <br />1 button maximum
                                                    </div>
                                                </div>
                                            </DropdownItem>
                                        </Dropdown>
                                    </div>
                                    {/* Quick Reply */}
                                    <div>
                                        <h1 className="mb-2 text-base">
                                            Quick Reply
                                        </h1>
                                        <div className="flex items-center gap-4 p-2 border rounded bg-blue-50">
                                            <div className="w-fit">
                                                <div className="mb-1">
                                                    <Label>Type</Label>
                                                </div>
                                                <div className="bg-white border py-2.5 px-3 rounded-md">
                                                    <Dropdown
                                                        className="w-fit"
                                                        arrowIcon={true}
                                                        inline
                                                        label={
                                                            <div className="text-sm">
                                                                Custom
                                                            </div>
                                                        }
                                                    >
                                                        <DropdownItem>
                                                            Visit website
                                                        </DropdownItem>
                                                        <DropdownDivider />

                                                        <DropdownItem>
                                                            Call phone number
                                                        </DropdownItem>
                                                        <DropdownDivider />
                                                        <DropdownItem>
                                                            <div className="text-start">
                                                                <div className="text-xs ">
                                                                    Call Phone
                                                                    Number
                                                                    <br />1
                                                                    button
                                                                    maximum
                                                                </div>
                                                            </div>
                                                        </DropdownItem>
                                                        <DropdownDivider />
                                                        <DropdownItem>
                                                            <div className="text-start">
                                                                <div className="text-xs ">
                                                                    Copy Offer
                                                                    Code
                                                                    <br />1
                                                                    button
                                                                    maximum
                                                                </div>
                                                            </div>
                                                        </DropdownItem>
                                                    </Dropdown>
                                                </div>
                                            </div>
                                            <div>
                                                <div className="block mb-1">
                                                    <Label
                                                        htmlFor="email1"
                                                        value="Button Text "
                                                    />
                                                </div>
                                                <div className="flex items-center w-full">
                                                    <TextInput
                                                        className="w-full"
                                                        theme={input_custom}
                                                        id="email1"
                                                        type="email"
                                                        placeholder="Quick Reply"
                                                        required
                                                    />
                                                    <Button
                                                        theme={button_custom}
                                                        color="transparentForTab"
                                                    >
                                                        <MdClose className="text-xl" />
                                                    </Button>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <div className="flex flex-col gap-3 p-2 px-3 bg-white border rounded-lg">
                                    <div>
                                        <div className="flex items-center gap-2 mt-1 rounded-md">
                                            <h4 className="text-base">
                                                Message validity period
                                            </h4>
                                            <IoMdInformationCircleOutline className="text-xl text-slate-500" />
                                        </div>
                                        <span className="text-sm text-gray-500">
                                            You can set a custom validity period
                                            that your utility message must be
                                            delivered by before it expires. If a
                                            message has not been delivered
                                            within this time frame, you will not
                                            be charged and your customer will
                                            not see the message.
                                        </span>
                                    </div>
                                    <div>
                                        <div className="flex items-center justify-between gap-2 mt-1 rounded-md">
                                            <h4 className="text-base">
                                                Set custom validity period for
                                                your message
                                            </h4>
                                            <ToggleSwitch
                                                theme={toggle_custom}
                                                sizing="xs"
                                            />
                                        </div>
                                        <span className="text-sm text-gray-500">
                                            If you don't set a custom validity
                                            period, the standard 30 days
                                            WhatsApp message validity period
                                            will be applied.
                                        </span>
                                    </div>
                                </div>
                            </div>
                            <div
                                className="col-span-12 mb-2 xl:col-span-3 lg:col-span-4 md:col-span-12 sm:col-span-12 lg:flex"
                                // key={index}
                            >
                                <Card
                                    theme={card_custom}
                                    className="relative w-full bg-[url('/assets/img/bgwtsp.png')]"
                                >
                                    <div className="absolute -left-0 top-6 w-0 h-0 border-t-[1px] border-t-transparent border-b-[12px] border-b-transparent border-r-[18px] border-r-white"></div>
                                    <div>
                                        <div
                                            className="p-2 overflow-auto"
                                            // style={{
                                            //     height: "250px",
                                            // }}
                                        >
                                            <div className="bg-white px-3 rounded-lg mx-2.5 py-0.5">
                                                <>
                                                    <img
                                                        src={showAsset(
                                                            "/storage/"
                                                        )}
                                                        className="w-full my-2 rounded-md "
                                                    />
                                                </>
                                                <div
                                                    className="p-3 text-center text-gray-300"
                                                    style={{
                                                        fontSize: "7rem",
                                                    }}
                                                ></div>
                                                <div className="flex gap-2 p-3 my-2 bg-gray-100 rounded-lg">
                                                    <div className="text-3xl text-gray-400"></div>
                                                    <div className="text-sm text-gray-700">
                                                        Thank you for using your
                                                        credit card at CS
                                                        Mutual. Your receipt is
                                                        attached as a PDF.
                                                    </div>
                                                </div>

                                                <></>
                                                <div className="text-xs text-end text-slate-500">
                                                    09:45
                                                </div>
                                                <div className="flex justify-center py-2 mt-2 border-t">
                                                    <button>
                                                        <div className="flex items-center justify-center gap-2 font-medium text-cyan-700">
                                                            <MdOutlineOpenInNew />
                                                            Shop now
                                                        </div>
                                                    </button>
                                                </div>
                                                <div className="flex justify-center py-2 mt-2 border-t">
                                                    <button>
                                                        <div className="flex items-center justify-center gap-2 font-medium text-cyan-700">
                                                            <MdOutlineContentCopy />
                                                            Copy Code
                                                        </div>
                                                    </button>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </Card>
                            </div>
                        </div>
                        <h1 className="mt-3 text-xl">Template Library</h1>
                        <div className="grid grid-flow-row-dense grid-cols-12 p-0 pt-2 border-none md:grid-cols-12 sm:grid-cols-1 lg:gap-2">
                            <div
                                className="col-span-12 mb-2 xl:col-span-2 lg:col-span-3 md:col-span-12 sm:col-span-12 lg:flex"
                                // key={index}
                            >
                                <Category></Category>
                            </div>
                            <div className="relative flex flex-col gap-2 pt-0 dark:text-gray-400 lg:p-0 xl:col-span-10 lg:col-span-9 md:col-span-12 col-span-full">
                                <div className="w-full p-2 bg-white border rounded-lg">
                                    <h4 className="text-base">
                                        Choose Language
                                    </h4>
                                    <p className="text-sm text-gray-400">
                                        Select your preferred language for the
                                        template.
                                    </p>
                                    <div className="w-full mt-3">
                                        <select
                                            className="w-full rounded-lg text-sm border-gray-300 p-2.5 hover:bg-gray-50"
                                            name="cars"
                                            id="cars"
                                        >
                                            <option value="volvo">
                                                Choose Language
                                            </option>
                                            <option value="saab">Name</option>
                                            <option value="opel">
                                                Mobile Number
                                            </option>
                                            <option value="audi">City</option>
                                            <option value="audi">
                                                Address
                                            </option>
                                        </select>
                                    </div>
                                </div>
                                {/* First Div */}
                                <div className="p-2 bg-white rounded-md xl:col-span-9 lg:col-span-9 md:col-span-12 col-span-full">
                                    <div className="grid grid-flow-row-dense grid-cols-12 gap-2 border-none md:grid-cols-12 sm:grid-cols-1 ">
                                        <div className="xl:col-span-3 lg:col-span-4 md:col-span-6 col-span-full">
                                            <Card
                                                onClick={handleChildClick}
                                                theme={card_custom}
                                                className="cursor-pointer relative w-full bg-[url('/assets/img/bgwtsp.png')]"
                                            >
                                                <div className="absolute -left-0 top-6 w-0 h-0 border-t-[1px] border-t-transparent border-b-[12px] border-b-transparent border-r-[18px] border-r-white"></div>
                                                <div>
                                                    <div
                                                        className="p-2 overflow-auto"
                                                        style={{
                                                            height: "250px",
                                                        }}
                                                    >
                                                        <div className="bg-white h-full px-2 rounded-lg mx-2.5 py-0.5 flex flex-col  justify-between">
                                                            <div className="flex gap-2 my-2 rounded-lg">
                                                                <div className="text-3xl text-gray-400"></div>
                                                                <div className="text-sm text-gray-700">
                                                                    <div className="font-medium">
                                                                        Statement
                                                                        available
                                                                    </div>
                                                                    <div>
                                                                        This is
                                                                        to
                                                                        notify
                                                                        you that
                                                                        your
                                                                        latest
                                                                        statement
                                                                        for your
                                                                        Market
                                                                        checking
                                                                        plus
                                                                        account
                                                                        is now
                                                                        available.
                                                                        Please
                                                                        log into
                                                                        your
                                                                        account
                                                                        to view
                                                                        your
                                                                        statement.
                                                                    </div>
                                                                </div>
                                                            </div>
                                                            <div className="flex items-center justify-center gap-1 py-1 border-t text-sky-600">
                                                                <IoMdOpen className="text-sky-600" />
                                                                <span>
                                                                    View
                                                                    Statement
                                                                </span>
                                                            </div>
                                                        </div>
                                                    </div>
                                                    <div className="p-1 bg-slate-100">
                                                        <div className="flex items-start justify-between">
                                                            <div className="text-sm">
                                                                <div>
                                                                    Welcome New
                                                                    Customer
                                                                </div>

                                                                <span className="text-xs">
                                                                    Gateway 1
                                                                </span>
                                                            </div>
                                                            <Button
                                                                theme={
                                                                    button_custom
                                                                }
                                                                size="xxs"
                                                                color="withoutBorder"
                                                            >
                                                                <MdDeleteOutline className="text-lg text-slate-400" />
                                                            </Button>
                                                        </div>
                                                    </div>
                                                </div>
                                            </Card>
                                        </div>
                                        <div className="xl:col-span-3 lg:col-span-4 md:col-span-6 col-span-full">
                                            <Card
                                                onClick={handleChildClick}
                                                theme={card_custom}
                                                className="cursor-pointer relative w-full bg-[url('/assets/img/bgwtsp.png')]"
                                            >
                                                <div className="absolute -left-0 top-6 w-0 h-0 border-t-[1px] border-t-transparent border-b-[12px] border-b-transparent border-r-[18px] border-r-white"></div>
                                                <div>
                                                    <div
                                                        className="p-2 overflow-auto"
                                                        style={{
                                                            height: "250px",
                                                        }}
                                                    >
                                                        <div className="bg-white h-full px-2 rounded-lg mx-2.5 py-0.5 flex flex-col  justify-between">
                                                            <div className="flex gap-2 my-2 rounded-lg">
                                                                <div className="text-3xl text-gray-400"></div>
                                                                <div className="text-sm text-gray-700">
                                                                    <div className="font-medium">
                                                                        Statement
                                                                        available
                                                                    </div>
                                                                    <div>
                                                                        This is
                                                                        to
                                                                        notify
                                                                        you that
                                                                        your
                                                                        latest
                                                                        statement
                                                                        for your
                                                                        Market
                                                                        checking
                                                                        plus
                                                                        account
                                                                        is now
                                                                        available.
                                                                        Please
                                                                        log into
                                                                        your
                                                                        account
                                                                        to view
                                                                        your
                                                                        statement.
                                                                    </div>
                                                                </div>
                                                            </div>
                                                            <div className="flex items-center justify-center gap-1 py-1 border-t text-sky-600">
                                                                <IoMdOpen className="text-sky-600" />
                                                                <span>
                                                                    View
                                                                    Statement
                                                                </span>
                                                            </div>
                                                        </div>
                                                    </div>
                                                    <div className="p-1 bg-slate-100">
                                                        <div className="flex items-start justify-between">
                                                            <div className="text-sm">
                                                                <div>
                                                                    Welcome New
                                                                    Customer
                                                                </div>

                                                                <span className="text-xs">
                                                                    Gateway 1
                                                                </span>
                                                            </div>
                                                            <Button
                                                                theme={
                                                                    button_custom
                                                                }
                                                                size="xxs"
                                                                color="withoutBorder"
                                                            >
                                                                <MdDeleteOutline className="text-lg text-slate-400" />
                                                            </Button>
                                                        </div>
                                                    </div>
                                                </div>
                                            </Card>
                                        </div>
                                        <div className="xl:col-span-3 lg:col-span-4 md:col-span-6 col-span-full">
                                            <Card
                                                onClick={handleChildClick}
                                                theme={card_custom}
                                                className="cursor-pointer relative w-full bg-[url('/assets/img/bgwtsp.png')]"
                                            >
                                                <div className="absolute -left-0 top-6 w-0 h-0 border-t-[1px] border-t-transparent border-b-[12px] border-b-transparent border-r-[18px] border-r-white"></div>
                                                <div>
                                                    <div
                                                        className="p-2 overflow-auto"
                                                        style={{
                                                            height: "250px",
                                                        }}
                                                    >
                                                        <div className="bg-white h-full px-2 rounded-lg mx-2.5 py-0.5 flex flex-col  justify-between">
                                                            <div className="flex gap-2 my-2 rounded-lg">
                                                                <div className="text-3xl text-gray-400"></div>
                                                                <div className="text-sm text-gray-700">
                                                                    <div className="font-medium">
                                                                        Statement
                                                                        available
                                                                    </div>
                                                                    <div>
                                                                        This is
                                                                        to
                                                                        notify
                                                                        you that
                                                                        your
                                                                        latest
                                                                        statement
                                                                        for your
                                                                        Market
                                                                        checking
                                                                        plus
                                                                        account
                                                                        is now
                                                                        available.
                                                                        Please
                                                                        log into
                                                                        your
                                                                        account
                                                                        to view
                                                                        your
                                                                        statement.
                                                                    </div>
                                                                </div>
                                                            </div>
                                                            <div className="flex items-center justify-center gap-1 py-1 border-t text-sky-600">
                                                                <IoMdOpen className="text-sky-600" />
                                                                <span>
                                                                    View
                                                                    Statement
                                                                </span>
                                                            </div>
                                                        </div>
                                                    </div>
                                                    <div className="p-1 bg-slate-100">
                                                        <div className="flex items-start justify-between">
                                                            <div className="text-sm">
                                                                <div>
                                                                    Welcome New
                                                                    Customer
                                                                </div>

                                                                <span className="text-xs">
                                                                    Gateway 1
                                                                </span>
                                                            </div>
                                                            <Button
                                                                theme={
                                                                    button_custom
                                                                }
                                                                size="xxs"
                                                                color="withoutBorder"
                                                            >
                                                                <MdDeleteOutline className="text-lg text-slate-400" />
                                                            </Button>
                                                        </div>
                                                    </div>
                                                </div>
                                            </Card>
                                        </div>
                                        <div className="xl:col-span-3 lg:col-span-4 md:col-span-6 col-span-full">
                                            <Card
                                                onClick={handleChildClick}
                                                theme={card_custom}
                                                className="cursor-pointer relative w-full bg-[url('/assets/img/bgwtsp.png')]"
                                            >
                                                <div className="absolute -left-0 top-6 w-0 h-0 border-t-[1px] border-t-transparent border-b-[12px] border-b-transparent border-r-[18px] border-r-white"></div>
                                                <div>
                                                    <div
                                                        className="p-2 overflow-auto"
                                                        style={{
                                                            height: "250px",
                                                        }}
                                                    >
                                                        <div className="bg-white h-full px-2 rounded-lg mx-2.5 py-0.5 flex flex-col  justify-between">
                                                            <div className="flex gap-2 my-2 rounded-lg">
                                                                <div className="text-3xl text-gray-400"></div>
                                                                <div className="text-sm text-gray-700">
                                                                    <div className="font-medium">
                                                                        Statement
                                                                        available
                                                                    </div>
                                                                    <div>
                                                                        This is
                                                                        to
                                                                        notify
                                                                        you that
                                                                        your
                                                                        latest
                                                                        statement
                                                                        for your
                                                                        Market
                                                                        checking
                                                                        plus
                                                                        account
                                                                        is now
                                                                        available.
                                                                        Please
                                                                        log into
                                                                        your
                                                                        account
                                                                        to view
                                                                        your
                                                                        statement.
                                                                    </div>
                                                                </div>
                                                            </div>
                                                            <div className="flex items-center justify-center gap-1 py-1 border-t text-sky-600">
                                                                <IoMdOpen className="text-sky-600" />
                                                                <span>
                                                                    View
                                                                    Statement
                                                                </span>
                                                            </div>
                                                        </div>
                                                    </div>
                                                    <div className="p-1 bg-slate-100">
                                                        <div className="flex items-start justify-between">
                                                            <div className="text-sm">
                                                                <div>
                                                                    Welcome New
                                                                    Customer
                                                                </div>

                                                                <span className="text-xs">
                                                                    Gateway 1
                                                                </span>
                                                            </div>
                                                            <Button
                                                                theme={
                                                                    button_custom
                                                                }
                                                                size="xxs"
                                                                color="withoutBorder"
                                                            >
                                                                <MdDeleteOutline className="text-lg text-slate-400" />
                                                            </Button>
                                                        </div>
                                                    </div>
                                                </div>
                                            </Card>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </Typography>
                );
            case 3:
                return (
                    <Typography>
                        <div className="grid grid-flow-row-dense grid-cols-12 p-0 pt-2 border-none md:grid-cols-12 sm:grid-cols-1 lg:gap-2">
                            <div className="relative flex flex-col gap-2 pt-0 dark:text-gray-400 lg:p-0 xl:col-span-9 lg:col-span-8 md:col-span-12 col-span-full">
                                <div className="w-full p-2 bg-white border rounded-lg">
                                    <h4 className="text-base">
                                        Choose Language
                                    </h4>
                                    <p className="text-sm text-gray-400">
                                        Select your preferred language for the
                                        template.
                                    </p>
                                    <div className="w-full mt-3">
                                        <select
                                            className="w-full rounded-lg text-sm border-gray-300 p-2.5 hover:bg-gray-50"
                                            name="cars"
                                            id="cars"
                                        >
                                            <option value="volvo">
                                                Choose Language
                                            </option>
                                            <option value="saab">Name</option>
                                            <option value="opel">
                                                Mobile Number
                                            </option>
                                            <option value="audi">City</option>
                                            <option value="audi">
                                                Address
                                            </option>
                                        </select>
                                    </div>
                                </div>
                                <div className="flex flex-col gap-3 p-2 bg-white rounded-lg">
                                    <div className="">
                                        <h4 className="text-base">
                                            Code Delivery Setup
                                        </h4>
                                        <p className="text-sm font-normal text-gray-400">
                                            Choose how customers send the code
                                            from WhatsApp to your app. Edits to
                                            this section won't require review or
                                            count towards edit limits. Learn how
                                            to send authentication message
                                            templates.
                                        </p>
                                        {/* <IoMdInformationCircleOutline className="text-xl text-slate-500 " /> */}
                                    </div>
                                    <div className="flex flex-col gap-3">
                                        <button
                                            className="flex items-start py-1.5 px-3 gap-2 border border-gray-300 focus:outline-none focus:ring-1 focus:ring-blue-500 group rounded focus:bg-blue-50"
                                            onClick={() =>
                                                handleButtonClick("USA")
                                            }
                                        >
                                            <input
                                                className="mt-1"
                                                type="radio"
                                                id="united-state"
                                                name="countries"
                                                value="USA"
                                                checked={
                                                    selectedCountry === "USA"
                                                }
                                                onChange={() =>
                                                    setSelectedCountry("USA")
                                                }
                                            />
                                            <div className="flex flex-col text-start">
                                                <div className="flex items-center gap-2">
                                                    <label>
                                                        Zero-tap auto-fill
                                                    </label>
                                                    <IoMdInformationCircleOutline className="text-xl text-slate-400 " />
                                                </div>
                                                <span className="mt-1 text-sm text-gray-400">
                                                    This is recommended as the
                                                    easiest option for your
                                                    customers. Zero-tap will
                                                    automatically send code
                                                    without requiring your
                                                    customer to tap a button. An
                                                    auto-fill or copy code
                                                    message will be sent if
                                                    zero-tap and auto-fill
                                                    aren't possible.
                                                </span>
                                                <div className="p-2 mt-2 bg-gray-100 rounded-md">
                                                    <div className="flex items-start gap-2">
                                                        <Checkbox
                                                            color="blue"
                                                            id="remember"
                                                            className="mt-0.5"
                                                        />
                                                        <Label htmlFor="remember">
                                                            By selecting
                                                            zero-tap, I
                                                            understand that
                                                            MGC's use of
                                                            zero-tap
                                                            authentication is
                                                            subject to
                                                            the WhatsApp
                                                            Business Terms of
                                                            Service . It's MGC
                                                            's responsibility to
                                                            ensure that its
                                                            customers expect
                                                            that the code will
                                                            be automatically
                                                            filled in on their
                                                            behalf when they
                                                            choose to receive
                                                            the zero-tap code
                                                            through WhatsApp.
                                                        </Label>
                                                    </div>
                                                    <div className="flex items-center gap-1 p-1 mt-2 bg-red-100 border rounded-md">
                                                        <BiErrorAlt className="text-red-600" />
                                                        <span
                                                            className="text-sm "
                                                        >
                                                            This box must be
                                                            ticked to submit
                                                            this template.
                                                        </span>
                                                    </div>
                                                </div>
                                            </div>
                                        </button>
                                        <button
                                            className="flex items-start py-1.5 px-3 gap-2 border border-gray-300 focus:outline-none focus:ring-1 focus:ring-blue-500 group rounded focus:bg-blue-50"
                                            onClick={() =>
                                                handleButtonClick("USA")
                                            }
                                        >
                                            <input
                                                className="mt-1"
                                                type="radio"
                                                id="united-state"
                                                name="countries"
                                                value="USA"
                                                checked={
                                                    selectedCountry === "USA"
                                                }
                                                onChange={() =>
                                                    setSelectedCountry("USA")
                                                }
                                            />
                                            <div className="flex flex-col text-start">
                                                <div className="flex items-center gap-2">
                                                    <label>
                                                        One-tap auto-fill
                                                    </label>
                                                    <IoMdInformationCircleOutline className="text-xl text-slate-400 " />
                                                </div>
                                                <span className="mt-1 text-sm text-gray-400">
                                                    The code sends to your app
                                                    when customers tap the
                                                    button. A copy code message
                                                    will be sent if auto-fill
                                                    isn't possible.
                                                </span>
                                            </div>
                                        </button>
                                        <button
                                            className="flex items-start py-1.5 px-3 gap-2 border border-gray-300 focus:outline-none focus:ring-1 focus:ring-blue-500 group rounded focus:bg-blue-50"
                                            onClick={() =>
                                                handleButtonClick("USA")
                                            }
                                        >
                                            <input
                                                className="mt-1"
                                                type="radio"
                                                id="united-state"
                                                name="countries"
                                                value="USA"
                                                checked={
                                                    selectedCountry === "USA"
                                                }
                                                onChange={() =>
                                                    setSelectedCountry("USA")
                                                }
                                            />
                                            <div className="flex flex-col text-start">
                                                <div className="flex items-center gap-2">
                                                    <label>Copy Code</label>
                                                    <IoMdInformationCircleOutline className="text-xl text-slate-400 " />
                                                </div>
                                                <span className="mt-1 text-sm text-gray-400">
                                                    Basic authentication with
                                                    quick setup. Your customers
                                                    copy and paste the code into
                                                    your app.
                                                </span>
                                            </div>
                                        </button>
                                    </div>
                                </div>
                                <div className="flex flex-col gap-3 p-2 bg-white rounded-lg">
                                    <div>
                                        <div className="block mb-2">
                                            <Label htmlFor="email1">
                                                <div>Add setup</div>
                                                <div className="font-normal text-gray-400">
                                                    You can add up to 5 apps.
                                                </div>
                                            </Label>
                                        </div>
                                        <div className="flex items-center gap-3">
                                            <div className="w-full">
                                                <div className="mb-1">
                                                    <label
                                                        htmlFor=""
                                                        className="mb-2 text-sm"
                                                    >
                                                        Package Name{" "}
                                                        <span className="text-red-600 ms-1">
                                                            *
                                                        </span>
                                                    </label>
                                                </div>
                                                <TextInput
                                                    theme={input_custom}
                                                    color="gray"
                                                    id="email1"
                                                    type="email"
                                                    placeholder="E.g. com.example.myapplication"
                                                    required
                                                />
                                            </div>
                                            <div className="w-full">
                                                <div className="mb-1">
                                                    <label
                                                        htmlFor=""
                                                        className="mb-2 text-sm"
                                                    >
                                                        App Signature Hash{" "}
                                                        <span className="text-red-600 ms-1">
                                                            *
                                                        </span>
                                                    </label>
                                                </div>
                                                <TextInput
                                                    theme={input_custom}
                                                    color="gray"
                                                    id="email1"
                                                    type="email"
                                                    placeholder="Enter text"
                                                    required
                                                />
                                            </div>
                                        </div>
                                        <div className="flex items-start gap-3 p-1 my-2 text-red-600 border rounded bg-red-50">
                                            <BiErrorAlt className="mt-1" />
                                            <List className="text-sm">
                                                <List.Item>
                                                    Your signature hash must be
                                                    11 characters long.
                                                </List.Item>
                                                <List.Item>
                                                    Add text in all required
                                                    fields to publish your
                                                    message template.
                                                </List.Item>
                                            </List>
                                        </div>
                                        <Button
                                                    theme={button_custom}
                                                    size="sm"
                                                    // outline
                                                    className="rounded-lg w-fit "
                                                    color="blue"
                                                    id="varName"
                                                >
                                                    <div className="flex items-center gap-1 text-sm">
                                                        <MdOutlineAdd className="text-lg text-white" />
                                                        Add Another App
                                                    </div>
                                                </Button>
                                    </div>
                                </div>
                                <div className="flex flex-col gap-3 p-2 px-3 bg-white border rounded-lg">
                                    <div className="flex flex-col gap-2">
                                        <div className="flex items-center gap-1">
                                        <h4 className="text-base">Content</h4>
                                        <MdInfoOutline className="text-lg text-slate-400"/>
                                        </div>
                                        <div className="flex items-center gap-2">
                                        <Checkbox color="blue" id="promotion" />
                                        <Label className="font-normal" htmlFor="promotion">Add security recommendation</Label>
                                    </div>
                                    <div className="flex items-start gap-2">
                                        <Checkbox color="blue" id="promotion" />
                                        <Label className="font-normal" htmlFor="promotion">Add expiry time for the code<br/>After the code has expired, the auto-fill button will be disabled.</Label>
                                    </div>
                                    <div className="p-1 px-2 pb-2 rounded bg-slate-200">
                                    <div className="max-w-md">
        <div className="block mb-1">
          <Label htmlFor="email1" value="Expires in" />
        </div>
        <TextInput theme={input_custom} id="email1" type="number" placeholder="10 minutes" required />
      </div>
                                    </div>
                                    </div>

                                    {/* Quick Reply */}
                                    <div>
                                        <h1 className="mb-2 text-base">
                                            Quick Reply
                                        </h1>
                                        <div className="flex items-center gap-4 p-2 border rounded bg-blue-50">
                                            <div className="w-fit">
                                                <div className="mb-1">
                                                    <Label>Type</Label>
                                                </div>
                                                <div className="bg-white border py-2.5 px-3 rounded-md">
                                                    <Dropdown
                                                        className="w-fit"
                                                        arrowIcon={true}
                                                        inline
                                                        label={
                                                            <div className="text-sm">
                                                                Custom
                                                            </div>
                                                        }
                                                    >
                                                        <DropdownItem>
                                                            Visit website
                                                        </DropdownItem>
                                                        <DropdownDivider />

                                                        <DropdownItem>
                                                            Call phone number
                                                        </DropdownItem>
                                                        <DropdownDivider />
                                                        <DropdownItem>
                                                            <div className="text-start">
                                                                <div className="text-xs ">
                                                                    Call Phone
                                                                    Number
                                                                    <br />1
                                                                    button
                                                                    maximum
                                                                </div>
                                                            </div>
                                                        </DropdownItem>
                                                        <DropdownDivider />
                                                        <DropdownItem>
                                                            <div className="text-start">
                                                                <div className="text-xs ">
                                                                    Copy Offer
                                                                    Code
                                                                    <br />1
                                                                    button
                                                                    maximum
                                                                </div>
                                                            </div>
                                                        </DropdownItem>
                                                    </Dropdown>
                                                </div>
                                            </div>
                                            <div>
                                                <div className="block mb-1">
                                                    <Label
                                                        htmlFor="email1"
                                                        value="Button Text "
                                                    />
                                                </div>
                                                <div className="flex items-center w-full">
                                                    <TextInput
                                                        className="w-full"
                                                        theme={input_custom}
                                                        id="email1"
                                                        type="email"
                                                        placeholder="Quick Reply"
                                                        required
                                                    />
                                                    <Button
                                                        theme={button_custom}
                                                        color="transparentForTab"
                                                    >
                                                        <MdClose className="text-xl" />
                                                    </Button>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div className="flex flex-col gap-3 p-2 bg-white rounded-lg">
                                    <div>
                                        <div className="block mb-2">
                                            <Label htmlFor="email1">
                                                <div>Buttons</div>
                                                <div className="font-normal text-gray-400">
                                                You can customise the button text for both auto-fill and copy code. Even when zero-tap is turned on, buttons are still needed for the backup code
                                                delivery method.
                                                </div>
                                            </Label>
                                        </div>
                                        <div className="flex items-center gap-3">
                                            <div className="w-full">
                                                <div className="mb-1">
                                                    <label
                                                        htmlFor=""
                                                        className="mb-2 text-sm"
                                                    >
                                                        Auto-fill
                                                    </label>
                                                </div>
                                                <TextInput
                                                    theme={input_custom}
                                                    color="gray"
                                                    id="email1"
                                                    type="email"
                                                    placeholder="Autofill"
                                                    required
                                                />
                                            </div>
                                            <div className="w-full">
                                                <div className="mb-1">
                                                    <label
                                                        htmlFor=""
                                                        className="mb-2 text-sm"
                                                    >
                                                        Copy code
                                                    </label>
                                                </div>
                                                <TextInput
                                                    theme={input_custom}
                                                    color="gray"
                                                    id="email1"
                                                    type="email"
                                                    placeholder="Copy code"
                                                    required
                                                />
                                            </div>
                                        </div>

                                    </div>
                                </div>
                                <div className="flex flex-col gap-3 p-2 bg-white rounded-lg">
                                    <div>
                                        <div className="block mb-2">
                                            <Label htmlFor="email1">
                                                <div className="flex items-center gap-1 ">
                                                    Message validity period<MdOutlineInfo className="text-lg text-slate-400"/></div>
                                                <p className="font-light text-gray-400">
                                                It's recommended to set a custom validity period that your authentication message must be delivered by before it expires. If a message is not delivered within this time frame, you will not be charged and your customer will not see the message.
                                                </p>
                                            </Label>
                                        </div>
                                        <div className="block mb-2">
                                            <Label htmlFor="email1">
                                                <div className="flex items-center justify-between">
                                                    <span>

                                                Set custom validity period for your message
                                                    </span>
                                                    <ToggleSwitch
                                                theme={toggle_custom}
                                                sizing="xs"
                                            />

                                                </div>
                                                <div className="font-light text-gray-400">
                                                If you don't set a custom validity period, the standard 30 days WhatsApp message validity period will be applied.
                                                </div>
                                            </Label>
                                        </div>
                                        <div className="w-full p-2 bg-white border rounded-lg">
                                    <h4 className="text-base">
                                    Validity Period
                                    </h4>
                                    <div className="w-full mt-3">
                                        <select
                                            className="w-full rounded-lg text-sm border-gray-300 p-2.5 hover:bg-gray-50"
                                            name="cars"
                                            id="cars"
                                        >
                                            <option value="volvo">
                                            10 minutes
                                            </option>
                                            <option value="saab">30 Seconds</option>
                                            <option value="opel">
                                                1 Seconds
                                            </option>
                                            <option value="audi">City</option>
                                            <option value="audi">
                                                Address
                                            </option>
                                        </select>
                                    </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div
                                className="col-span-12 mb-2 xl:col-span-3 lg:col-span-4 md:col-span-12 sm:col-span-12 lg:flex"
                                // key={index}
                            >
                                <Card
                                    theme={card_custom}
                                    className="relative w-full bg-[url('/assets/img/bgwtsp.png')]"
                                >
                                    <div className="absolute -left-0 top-6 w-0 h-0 border-t-[1px] border-t-transparent border-b-[12px] border-b-transparent border-r-[18px] border-r-white"></div>
                                    <div>
                                        <div
                                            className="p-2 overflow-auto"
                                            // style={{
                                            //     height: "250px",
                                            // }}
                                        >
                                            <div className="bg-white px-3 rounded-lg mx-2.5 py-0.5">
                                                <>
                                                    <img
                                                        src={showAsset(
                                                            "/storage/"
                                                        )}
                                                        className="w-full my-2 rounded-md "
                                                    />
                                                </>
                                                <div
                                                    className="p-3 text-center text-gray-300"
                                                    style={{
                                                        fontSize: "7rem",
                                                    }}
                                                ></div>
                                                <div className="flex gap-2 p-3 my-2 bg-gray-100 rounded-lg">
                                                    <div className="text-3xl text-gray-400"></div>
                                                    <div className="text-sm text-gray-700">
                                                        Thank you for using your
                                                        credit card at CS
                                                        Mutual. Your receipt is
                                                        attached as a PDF.
                                                    </div>
                                                </div>

                                                <></>
                                                <div className="text-xs text-end text-slate-500">
                                                    09:45
                                                </div>
                                                <div className="flex justify-center py-2 mt-2 border-t">
                                                    <button>
                                                        <div className="flex items-center justify-center gap-2 font-medium text-cyan-700">
                                                            <MdOutlineOpenInNew />
                                                            Shop now
                                                        </div>
                                                    </button>
                                                </div>
                                                <div className="flex justify-center py-2 mt-2 border-t">
                                                    <button>
                                                        <div className="flex items-center justify-center gap-2 font-medium text-cyan-700">
                                                            <MdOutlineContentCopy />
                                                            Copy Code
                                                        </div>
                                                    </button>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </Card>
                            </div>
                        </div>
                    </Typography>
                );
        }
    };

    return (
        <WaMain>
            <div className="flex items-center gap-2 p-2 px-4">
                <HiOutlineTemplate className="text-2xl text-slate-400" />
                <span className="text-lg">Create New Template</span>
            </div>
            <Box sx={{ width: "100%" }}>
                {/* Stepper */}
                <Stepper
                    activeStep={activeStep}
                    className="flex-wrap gap-2 p-2 px-2 font-medium text-nowrap"
                >
                    {steps.map((label) => (
                        <Step key={label}>
                            <StepLabel className="font-bold ">
                                {label}
                            </StepLabel>
                        </Step>
                    ))}
                </Stepper>

                {/* Content */}

                <React.Fragment>
                    {/* Render content dynamically */}
                    <Box sx={{ mt: 0, mb: 0, px: 2 }}>
                        {renderStepContent(activeStep)}
                    </Box>
                    <Box
                        sx={{
                            display: "flex",
                            flexDirection: "row",
                            pt: 2,
                        }}
                    >
                        <Button
                            theme={button_custom}
                            color="withoutBorder"
                            className="text-blue-600"
                            disabled={activeStep === 0}
                            onClick={handleBack}
                            sx={{ mr: 1 }}
                        >
                            <div className="flex items-center">
                                <IoChevronBack />
                                Back
                            </div>
                        </Button>
                        <Box sx={{ flex: "1 1 auto" }} />
                        <div className="flex items-center gap-1">
                            <Button
                                size="sm"
                                className="me-4"
                                color="failure"
                                theme={button_custom}
                                onClick={handleNext}
                            >
                                <div className="flex items-center gap-1">
                                    <IoMdClose className="text-lg" />
                                    Cancel
                                </div>
                            </Button>
                            <Button
                                size="sm"
                                className="me-4"
                                color="blue"
                                theme={button_custom}
                                onClick={handleNext}
                            >
                                <div className="flex items-center gap-1">
                                    {activeStep === steps.length - 1
                                        ? "Finish"
                                        : "Next"}
                                    <GrFormNext className="text-lg" />
                                </div>
                            </Button>
                        </div>
                    </Box>
                </React.Fragment>
            </Box>
        </WaMain>
    );
}
