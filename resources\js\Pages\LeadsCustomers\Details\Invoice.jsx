import TextInput from '@/Components/TextInput';
import { button_custom, input_custom, tabBar_underline, table_custom, textarea_custom, toggle_custom } from '@/Pages/Helpers/DesignHelper'
import EmojiPicker from 'emoji-picker-react';
import { Badge, Button, Label, Table, TableBody, TableCell, TableHead, TableHeadCell, TableRow, Tabs, Textarea, ToggleSwitch, Tooltip } from 'flowbite-react'
import React from 'react'
import { useState } from 'react';
import { AiOutlineMergeCells } from 'react-icons/ai';
import { BiCommentAdd } from 'react-icons/bi';
import { FaBold, FaItalic, FaRegFaceSmileBeam, FaStrikethrough, FaWhatsapp } from 'react-icons/fa6';
import { HiOutlineMailOpen } from 'react-icons/hi';
import { IoMdAdd, IoMdClose } from 'react-icons/io';
import { LiaLongArrowAltRightSolid } from 'react-icons/lia';
import { MdCallSplit, MdOutlineCall, MdOutlineEdit, MdOutlinePlagiarism, MdOutlineTextsms } from 'react-icons/md';
import { PiCaretUpDownBold } from 'react-icons/pi'
import { RiCheckDoubleFill } from 'react-icons/ri';

export default function Invoice() {
  const [switch2, setSwitch2] = useState(false);
  const [openEmoji, setOpenEmoji] = useState(false);

  return (
    <div>
      <div className=" flex flex-col justify-between">
        <div className="bg-white h-[60vh] rounded-lg border">
          {/* documents table */}
          <div className="overflow-x-auto">
            <Table hoverable theme={table_custom}>
              <TableHead className=" bg-slate-100">
                <TableHeadCell>
                  <div className="flex items-center justify-between gap-2">
                    <h3>Date & Time</h3>
                    <PiCaretUpDownBold />
                  </div>
                </TableHeadCell>
                <TableHeadCell>Customer</TableHeadCell>
                <TableHeadCell>
                  INV Number
                </TableHeadCell>
                <TableHeadCell>Payable Amount</TableHeadCell>
                <TableHeadCell>GST</TableHeadCell>
                <TableHeadCell>Currency</TableHeadCell>
              </TableHead>

              <TableBody className="divide-y">
                <TableRow className="bg-white dark:border-gray-700 dark:bg-gray-800">
                  <TableCell >
                    <div className="text-blue-700">1/07/2024</div>
                    <div className="text-xs">02:45 PM</div>
                  </TableCell>

                  <TableCell className="whitespace-nowrap dark:text-white">
                    <div className="flex flex-wrap items-center gap-2 text-nowrap w-fit">
                      <div>
                        <div className="text-blue-500 text-nowrap">
                          Rohan Preet
                          (2568)
                        </div>
                        <div className="text-xs text-nowrap">
                          Sales Executive
                        </div>
                      </div>
                    </div>
                  </TableCell>
                  <TableCell>
                    IN-2223-3266
                  </TableCell>
                  <TableCell>
                    ₹ 12,763
                  </TableCell>
                  <TableCell>
                    No
                  </TableCell>
                  <TableCell className="text-nowrap">
                    INR
                  </TableCell>
                </TableRow>
                <TableRow className="bg-white dark:border-gray-700 dark:bg-gray-800">
                  <TableCell >
                    <div className="text-blue-700">1/07/2024</div>
                    <div className="text-xs">02:45 PM</div>
                  </TableCell>

                  <TableCell className="whitespace-nowrap dark:text-white">
                    <div className="flex flex-wrap items-center gap-2 text-nowrap w-fit">
                      <div>
                        <div className="text-blue-500 text-nowrap">
                          Rohan Preet
                          (2568)
                        </div>
                        <div className="text-xs text-nowrap">
                          Sales Executive
                        </div>
                      </div>
                    </div>
                  </TableCell>
                  <TableCell>
                    IN-2223-3266
                  </TableCell>
                  <TableCell>
                    ₹ 12,763
                  </TableCell>
                  <TableCell>
                    No
                  </TableCell>
                  <TableCell className="text-nowrap">
                    INR
                  </TableCell>
                </TableRow>
                <TableRow className="bg-white dark:border-gray-700 dark:bg-gray-800">
                  <TableCell >
                    <div className="text-blue-700">1/07/2024</div>
                    <div className="text-xs">02:45 PM</div>
                  </TableCell>

                  <TableCell className="whitespace-nowrap dark:text-white">
                    <div className="flex flex-wrap items-center gap-2 text-nowrap w-fit">
                      <div>
                        <div className="text-blue-500 text-nowrap">
                          Rohan Preet
                          (2568)
                        </div>
                        <div className="text-xs text-nowrap">
                          Sales Executive
                        </div>
                      </div>
                    </div>
                  </TableCell>
                  <TableCell>
                    IN-2223-3266
                  </TableCell>
                  <TableCell>
                    ₹ 12,763
                  </TableCell>
                  <TableCell>
                    No
                  </TableCell>
                  <TableCell className="text-nowrap">
                    INR
                  </TableCell>
                </TableRow>
              </TableBody>
            </Table>
          </div>
        </div>

        <div className="float-end border bottom-0 p-3 w-full bg-white rounded-b-lg">
          <div className="flex flex-wrap justify-between lg:gap-0 md:gap-0 gap-2">
            <div className="flex gap-3 items-center">
              <div className="text-gray-400 text-sm ">
                Showing 1 to 25 of 62 entries
              </div>
              <div>
                <select
                  id="countries"
                  className=" bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded focus:ring-blue-500 focus:border-blue-500 block p-1 ps-2 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-blue-500 dark:focus:border-blue-500"
                >
                  <option
                    value={10}
                    defaultValue={10}
                  >
                    10
                  </option>
                  <option value={15}>15</option>
                  <option value={20}>20</option>
                  <option value={25}>25</option>
                  <option value={30}>30</option>
                </select>
              </div>
            </div>
            <div className="flex">
              <Button
                size="xs"
                color="gray"
                className="border-e-0 text-gray-400 px-2 rounded text-xs "
              >
                Previous
              </Button>
              <Button
                size="xs"
                color="blue"
                className="border text-white border-blue-600 bg-blue-600 px-2.5 rounded-none text-sm "
              >
                1
              </Button>
              <Button
                size="xs"
                color="gray"
                className="border text-blue-600 px-2.5 rounded-none text-xs "
              >
                2
              </Button>
              <Button
                size="xs"
                color="gray"
                className="border-s-0 text-blue-600 px-2 rounded text-xs "
              >
                Next
              </Button>
            </div>
          </div>
        </div>

        <Tabs
          className="mt-1"
          theme={tabBar_underline}
          aria-label="Tabs with underline"
          variant="underline"
        >
          <Tabs.Item
            className=""
            active
            title={
              <div className="flex items-center gap-2">
                Call
                <sup>
                  <Badge
                    className="rounded-full bg-fuchsia-600 text-white"
                    size="xs"
                  >
                    05
                  </Badge>
                </sup>
              </div>
            }
            icon={MdOutlineCall}
          >
            <div className="bg-white p-2 rounded-md ">
              <div >
                <select
                  id="countries"
                  className="mt-1 w-full bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block p-2 ps-2 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-blue-500 dark:focus:border-blue-500"
                >
                  <option
                    value={10}
                    defaultValue={10}
                  >
                    Select Number
                  </option>
                  <option value={15}>15</option>
                  <option value={20}>20</option>
                  <option value={25}>25</option>
                  <option value={30}>30</option>
                </select>
              </div>
              <div className="mt-2 flex flex-wrap gap-3">
                <div className="flex items-center gap-2 border border-gray p-1 w-fit rounded">
                  <div className="flex items-center justify-center rounded-full size-10 bg-green-500">
                    <MdOutlineCall className="text-2xl text-white" />
                  </div>
                  <div className="flex flex-col text-sm">
                    <span>
                      Mohit Mathur (8563)
                    </span>
                    <span>
                      +91 8964236589
                    </span>
                  </div>
                </div>
                <div className="flex items-center gap-2 border border-gray p-1 w-fit rounded">
                  <div className="flex items-center justify-center rounded-full size-10 bg-green-500">
                    <MdOutlineCall className="text-2xl text-white" />
                  </div>
                  <div className="flex flex-col text-sm">
                    <span>
                      Mohit Mathur (8563)
                    </span>
                    <span>
                      +91 8964236589
                    </span>
                  </div>
                </div>
                <div className="flex items-center gap-2 border border-gray p-1 w-fit rounded">
                  <div className="flex items-center justify-center rounded-full size-10 bg-green-500">
                    <MdOutlineCall className="text-2xl text-white" />
                  </div>
                  <div className="flex flex-col text-sm">
                    <span>
                      Mohit Mathur (8563)
                    </span>
                    <span>
                      +91 8964236589
                    </span>
                  </div>
                </div>
                <div className="flex items-center gap-2 border border-gray p-1 w-fit rounded">
                  <div className="flex items-center justify-center rounded-full size-10 bg-green-500">
                    <MdOutlineCall className="text-2xl text-white" />
                  </div>
                  <div className="flex flex-col text-sm">
                    <span>
                      Mohit Mathur (8563)
                    </span>
                    <span>
                      +91 8964236589
                    </span>
                  </div>
                </div>
                <div className="flex items-center gap-2 border border-gray p-1 w-fit rounded">
                  <div className="flex items-center justify-center rounded-full size-10 bg-green-500">
                    <MdOutlineCall className="text-2xl text-white" />
                  </div>
                  <div className="flex flex-col text-sm">
                    <span>
                      Mohit Mathur (8563)
                    </span>
                    <span>
                      +91 8964236589
                    </span>
                  </div>
                </div>
                <div className="flex items-center gap-2 border border-gray p-1 w-fit rounded">
                  <div className="flex items-center justify-center rounded-full size-10 bg-green-500">
                    <MdOutlineCall className="text-2xl text-white" />
                  </div>
                  <div className="flex flex-col text-sm">
                    <span>
                      Mohit Mathur (8563)
                    </span>
                    <span>
                      +91 8964236589
                    </span>
                  </div>
                </div>
              </div>
            </div>
          </Tabs.Item>
          <Tabs.Item
            className=""
            active
            title={
              <div className="flex items-center gap-2">
                WhatsApp
                <sup>
                  <Badge
                    className="rounded-full bg-fuchsia-600 text-white"
                    size="xs"
                  >
                    05
                  </Badge>
                </sup>
              </div>
            }
            icon={FaWhatsapp}
          >
            <div className="">
              <div >
                <Label htmlFor="">Reason</Label>
                <select
                  id="countries"
                  className="mt-1 w-full bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block p-2 ps-2 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-blue-500 dark:focus:border-blue-500"
                >
                  <option
                    value={10}
                    defaultValue={10}
                  >
                    10
                  </option>
                  <option value={15}>15</option>
                  <option value={20}>20</option>
                  <option value={25}>25</option>
                  <option value={30}>30</option>
                </select>
              </div>
              <div className="">
                <div className="mt-2">
                  {openEmoji && (
                    <div
                      className="absolute z-50 "
                      style={{
                        top: "25%",
                        bottom: "25%",
                      }}
                    >
                      <EmojiPicker
                        open={openEmoji}
                        lazyLoadEmojis={true}
                      />
                    </div>
                  )}
                  <Label htmlFor="">Reason</Label>

                  <div className="mt-1 p-2 border border-gray-300 rounded-md bg-gray-50">
                    <Textarea
                      theme={textarea_custom}
                      name="message"
                      placeholder="Type Message here..."
                      rows={4}
                      id="myTextarea"
                      className="border-0 focus:ring-white"
                    />

                    <div className="flex flex-col flex-wrap justify-between gap-1 flex-center lg:flex-row">
                      <div className="flex flex-wrap items-center gap-1 space-x-1">
                        <div className="">
                          <Button
                            className="px-0"
                            size="xs"
                            color="gray"
                          >
                            <FaRegFaceSmileBeam />
                          </Button>
                        </div>
                        <div className="">
                          <Button
                            className="px-0"
                            size="xs"
                            color="gray"
                            id="boldButton"
                          >
                            <FaBold />
                          </Button>
                        </div>
                        <div className="">
                          <Button
                            className="px-0"
                            size="xs"
                            color="gray"
                            id="italicButton"
                          >
                            <FaItalic />
                          </Button>
                        </div>
                        <div className="">
                          <Button
                            className="px-0"
                            size="xs"
                            color="gray"
                            id="strike"
                          >
                            <FaStrikethrough />
                          </Button>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
                <div className="flex justify-end gap-5 py-3">
                  <Button
                    theme={button_custom}
                    color="blue"
                    className="rounded"
                    size="xs"
                  >
                    <div className="flex items-center gap-0.5">
                      <AiOutlineMergeCells className="text-xl" />
                      <span className="text-sm">
                        Merge Lead
                      </span>
                    </div>
                  </Button>
                </div>
              </div>
            </div>
          </Tabs.Item>
          <Tabs.Item
            className=""
            active
            title={
              <div className="flex items-center gap-2">
                SMS
                <sup>
                  <Badge
                    className="rounded-full bg-fuchsia-600 text-white"
                    size="xs"
                  >
                    05
                  </Badge>
                </sup>
              </div>
            }
            icon={MdOutlineTextsms}
          >
            <div className="">
              <div >
                <Label htmlFor="">Reason</Label>
                <select
                  id="countries"
                  className="mt-1 w-full bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block p-2 ps-2 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-blue-500 dark:focus:border-blue-500"
                >
                  <option
                    value={10}
                    defaultValue={10}
                  >
                    10
                  </option>
                  <option value={15}>15</option>
                  <option value={20}>20</option>
                  <option value={25}>25</option>
                  <option value={30}>30</option>
                </select>
              </div>
              <div className="">
                <div className="mt-2">
                  {openEmoji && (
                    <div
                      className="absolute z-50 "
                      style={{
                        top: "25%",
                        bottom: "25%",
                      }}
                    >
                      <EmojiPicker
                        open={openEmoji}
                        lazyLoadEmojis={true}
                      />
                    </div>
                  )}
                  <Label htmlFor="">Reason</Label>

                  <div className="mt-1 p-2 border border-gray-300 rounded-md bg-gray-50">
                    <Textarea
                      theme={textarea_custom}
                      name="message"
                      placeholder="Type Message here..."
                      rows={4}
                      id="myTextarea"
                      className="border-0 focus:ring-white"
                    />

                    <div className="flex flex-col flex-wrap justify-between gap-1 flex-center lg:flex-row">
                      <div className="flex flex-wrap items-center gap-1 space-x-1">
                        <div className="">
                          <Button
                            className="px-0"
                            size="xs"
                            color="gray"
                          >
                            <FaRegFaceSmileBeam />
                          </Button>
                        </div>
                        <div className="">
                          <Button
                            className="px-0"
                            size="xs"
                            color="gray"
                            id="boldButton"
                          >
                            <FaBold />
                          </Button>
                        </div>
                        <div className="">
                          <Button
                            className="px-0"
                            size="xs"
                            color="gray"
                            id="italicButton"
                          >
                            <FaItalic />
                          </Button>
                        </div>
                        <div className="">
                          <Button
                            className="px-0"
                            size="xs"
                            color="gray"
                            id="strike"
                          >
                            <FaStrikethrough />
                          </Button>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
                <div className="flex justify-end gap-5 py-3">
                  <Button
                    theme={button_custom}
                    color="blue"
                    className="rounded"
                    size="xs"
                  >
                    <div className="flex items-center gap-0.5">
                      <AiOutlineMergeCells className="text-xl" />
                      <span className="text-sm">
                        Merge Lead
                      </span>
                    </div>
                  </Button>
                </div>
              </div>
            </div>
          </Tabs.Item>
          <Tabs.Item
            className=""
            active
            title={
              <div className="flex items-center gap-2">
                Email
                <sup>
                  <Badge
                    className="rounded-full bg-fuchsia-600 text-white"
                    size="xs"
                  >
                    05
                  </Badge>
                </sup>
              </div>
            }
            icon={HiOutlineMailOpen}
          >
            <div className="">
              <div >
                <Label htmlFor="">Reason</Label>
                <select
                  id="countries"
                  className="mt-1 w-full bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block p-2 ps-2 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-blue-500 dark:focus:border-blue-500"
                >
                  <option
                    value={10}
                    defaultValue={10}
                  >
                    10
                  </option>
                  <option value={15}>15</option>
                  <option value={20}>20</option>
                  <option value={25}>25</option>
                  <option value={30}>30</option>
                </select>
              </div>
              <div className="">
                <div className="mt-2">
                  {openEmoji && (
                    <div
                      className="absolute z-50 "
                      style={{
                        top: "25%",
                        bottom: "25%",
                      }}
                    >
                      <EmojiPicker
                        open={openEmoji}
                        lazyLoadEmojis={true}
                      />
                    </div>
                  )}
                  <Label htmlFor="">Reason</Label>

                  <div className="mt-1 p-2 border border-gray-300 rounded-md bg-gray-50">
                    <Textarea
                      theme={textarea_custom}
                      name="message"
                      placeholder="Type Message here..."
                      rows={4}
                      id="myTextarea"
                      className="border-0 focus:ring-white"
                    />

                    <div className="flex flex-col flex-wrap justify-between gap-1 flex-center lg:flex-row">
                      <div className="flex flex-wrap items-center gap-1 space-x-1">
                        <div className="">
                          <Button
                            className="px-0"
                            size="xs"
                            color="gray"
                          >
                            <FaRegFaceSmileBeam />
                          </Button>
                        </div>
                        <div className="">
                          <Button
                            className="px-0"
                            size="xs"
                            color="gray"
                            id="boldButton"
                          >
                            <FaBold />
                          </Button>
                        </div>
                        <div className="">
                          <Button
                            className="px-0"
                            size="xs"
                            color="gray"
                            id="italicButton"
                          >
                            <FaItalic />
                          </Button>
                        </div>
                        <div className="">
                          <Button
                            className="px-0"
                            size="xs"
                            color="gray"
                            id="strike"
                          >
                            <FaStrikethrough />
                          </Button>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
                <div className="flex justify-end gap-5 py-3">
                  <Button
                    theme={button_custom}
                    color="blue"
                    className="rounded"
                    size="xs"
                  >
                    <div className="flex items-center gap-0.5">
                      <AiOutlineMergeCells className="text-xl" />
                      <span className="text-sm">
                        Merge Lead
                      </span>
                    </div>
                  </Button>
                </div>
              </div>
            </div>
          </Tabs.Item>
        </Tabs>
      </div>
    </div>
  )
}

