/** @type {import('tailwindcss').Config} */
const flowbite = require("flowbite-react/tailwind");

export default {
    content: ["./resources/**/*.{blade.php,php,jsx,js}", flowbite.content()],

    theme: {
        extend: {
            fontFamily: {
                sans: ["Figtree", "ui-sans-serif", "system-ui", "sans-serif"],
            },
            width: {
                // '96': '32rem',
            },
            gridTemplateColumns: {
                // 24 column grid
                16: "repeat(16, minmax(0, 1fr))",
            },
            height: {
                '10vh': '10vh',
                '20vh': '20vh',
                '30vh': '30vh',
                '40vh': '40vh',
                '50vh': '50vh',
                '60vh': '60vh',
                '70vh': '70vh',
                '80vh': '80vh',
                '90vh': '90vh',
                // Add more as needed
            },
            rotate: {
                '270': '270deg',
            },

        },
    },

    plugins: [
        require("@tailwindcss/forms"),
        flowbite.plugin(),
    ],
};
