import useFetch from "@/Global/useFetch";
import { button_custom } from "@/Pages/Helpers/DesignHelper";
import { Link, router } from "@inertiajs/react";
import { <PERSON><PERSON>, But<PERSON>, Modal } from "flowbite-react";
import { useState } from "react";
import { CgSpinner } from "react-icons/cg";
import { MdDeleteOutline, MdOutlineAdd, MdOutlineEdit } from "react-icons/md";
import AddCategory from "./AddCategory";
import EditCategory from "./EditCategory";
import ConfirmBox from "@/Components/HelperComponents/ConfirmBox";


export default function Category({ selectedCategory, setSelectedCategory }) {
    const [reFetch, setReFetch] = useState(0);
    const { data: cateData, loading: cateDataLoading } = useFetch(route('voice.template.category.index'), reFetch);

    const [modalState, setModalState] = useState({ openAdd: false, openEdit: false, selected: null });
    const [selectedCategories, setSelectedCategories] = useState([]);
    const [isMultipleDeleteConfirmOpen, setIsMultipleDeleteConfirmOpen] = useState(false);

    const handleMultipleDelete = (res) => {
        if (res) {
            router.delete(route("voice.template.category.destroy", { category: selectedCategories.toString() }));
            setReFetch(prev => prev + 1);
        }
        setSelectedCategories([]);
        setIsMultipleDeleteConfirmOpen(false);
    };

    return (
        <div className="w-full bg-white rounded-md h-fit">
            <div className="flex items-center justify-between gap-3 px-3 py-2">
                <span>Categories</span>
                <Button theme={button_custom} color="gray" size="xs" onClick={() => setModalState({ ...modalState, openAdd: true })}>
                    <div className="flex items-center">
                        <MdOutlineAdd className="text-slate-500" />
                        <span className="text-xs">Add</span>
                    </div>
                </Button>
            </div>
            <div className={`flex items-center justify-between border-b text-sm px-1.5 ${selectedCategory === 0 ? "bg-blue-400 text-white" : "hover:bg-blue-200 bg-white"}`}>
                <Button className="justify-start w-full p-2 bg-transparent border-0" color="gray"
                    as={Link} href={route('voice.templates.index')}
                >
                    <div className="flex items-center gap-2">All</div>
                </Button>
            </div>
            <div className="flex flex-col h-full overflow-y-auto bg-white lg:flex-col md:flex-col">
                {cateData ? (
                    cateData.collection.categories.map((category, key) => (
                        <div key={key}
                            className={`flex items-center justify-between border-b text-sm px-1.5
                            ${category.id === selectedCategory ? "bg-blue-400 text-white" : "hover:bg-blue-200 bg-white"}`}
                        >
                            <Button className="justify-start w-full p-2 bg-transparent border-0"
                                color="gray" as={Link} href={route('voice.template.category.show', { category: category.id })}
                                theme={{
                                    "inner": {
                                        "base": "flex w-full transition-all duration-200"
                                    }
                                }}
                            >
                                <div className="flex justify-between w-full gap-2">
                                    <div className=""> {category.name} </div>
                                    <Badge color="info"> {category.templates_count} </Badge>
                                </div>
                            </Button>
                            <div className="flex items-center gap-2">
                                <Button theme={button_custom} color="withoutBorder" size="xxs" onClick={() => setModalState({ openEdit: true, selected: category })}>
                                    <MdOutlineEdit className={category.id === selectedCategory ? "text-white" : "text-slate-400"} />
                                </Button>
                                <Button theme={button_custom} color="withoutBorder" size="xxs"
                                    onClick={() => {
                                        setSelectedCategories([category.id]);
                                        setIsMultipleDeleteConfirmOpen(true);
                                    }}>
                                    <MdDeleteOutline className={category.id === selectedCategory ? "text-white" : "text-slate-400"} />
                                </Button>
                            </div>
                        </div>
                    ))
                ) : (
                    <div className="flex justify-center p-2">
                        <CgSpinner className="text-3xl text-blue-400 ease-in-out animate-spin" />
                    </div>
                )}
            </div>
            {
                modalState.openAdd && (
                    <Modal show={modalState.openAdd} size="md" onClose={() => setModalState({ ...modalState, openAdd: false })} popup>
                        <ModalHeader className="p-2">
                            <h4 className="text-lg">Add Category</h4>
                        </ModalHeader>
                        <ModalBody className="px-4 py-3 rounded-b-lg bg-slate-100">
                            <AddCategory onClose={() => setModalState({ ...modalState, openAdd: false })} setReFetch={setReFetch} />
                        </ModalBody>
                    </Modal>
                )
            }
            {
                modalState.openEdit && (
                    <Modal show={modalState.openEdit} size="md" onClose={() => setModalState({ ...modalState, openEdit: false })} popup>
                        <ModalHeader className="p-2">
                            <h4 className="text-lg">Edit Category</h4>
                        </ModalHeader>
                        <ModalBody className="px-4 py-3 rounded-b-lg bg-slate-100">
                            <EditCategory onClose={() => setModalState({ ...modalState, openEdit: false })} category={modalState.selected} setReFetch={setReFetch} />
                        </ModalBody>
                    </Modal>
                )
            }
            {
                isMultipleDeleteConfirmOpen && (
                    <ConfirmBox
                        isOpen={isMultipleDeleteConfirmOpen}
                        onClose={() => setIsMultipleDeleteConfirmOpen(false)}
                        onAction={handleMultipleDelete}
                        title="Delete Category"
                        message="Do you want to delete the selected category?"
                        confirmText="Yes, Delete!"
                        cancelText="No, Keep It"
                        confirmColor="orange"
                        cancelColor="gray"
                    />
                )
            }
        </div >
    );
}

