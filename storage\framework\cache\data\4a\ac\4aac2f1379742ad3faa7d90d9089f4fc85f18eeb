1752749444a:10:{i:0;O:8:"stdClass":6:{s:4:"name";s:9:"Dashboard";s:4:"icon";s:18:"MdOutlineDashboard";s:3:"url";s:31:"http://127.0.0.1:8000/dashboard";s:14:"routeForActive";s:9:"dashboard";s:5:"order";i:1;s:18:"requiredPermission";s:14:"dashboard.view";}i:1;O:8:"stdClass":6:{s:4:"name";s:5:"Tasks";s:4:"icon";s:13:"FaCheckDouble";s:3:"url";s:27:"http://127.0.0.1:8000/tasks";s:14:"routeForActive";s:7:"tasks.*";s:5:"order";i:2;s:18:"requiredPermission";s:10:"tasks.view";}i:2;O:8:"stdClass":6:{s:4:"name";s:17:"Leads & Customers";s:4:"icon";s:14:"HiOutlineUsers";s:3:"url";s:39:"http://127.0.0.1:8000/leadsAndCustomers";s:14:"routeForActive";s:18:"leadsAndCustomers*";s:5:"order";i:3;s:18:"requiredPermission";s:22:"leadsAndCustomers.view";}i:3;O:8:"stdClass":6:{s:4:"name";s:7:"Billing";s:4:"icon";s:16:"IoReceiptOutline";s:3:"url";s:29:"http://127.0.0.1:8000/billing";s:14:"routeForActive";s:8:"billing*";s:5:"order";i:4;s:18:"requiredPermission";s:12:"billing.view";}i:4;O:8:"stdClass":6:{s:4:"name";s:12:"Subscription";s:4:"icon";s:11:"TbCoinRupee";s:3:"url";s:35:"http://127.0.0.1:8000/subscriptions";s:14:"routeForActive";s:14:"subscriptions*";s:5:"order";i:5;s:18:"requiredPermission";s:18:"subscriptions.view";}i:5;O:8:"stdClass":6:{s:4:"name";s:8:"Projects";s:4:"icon";s:19:"MdOutlineFolderCopy";s:3:"url";s:30:"http://127.0.0.1:8000/projects";s:14:"routeForActive";s:9:"projects*";s:5:"order";i:6;s:18:"requiredPermission";s:13:"projects.view";}i:6;O:8:"stdClass":6:{s:4:"name";s:5:"Admin";s:4:"icon";s:18:"FaMoneyCheckDollar";s:3:"url";s:27:"http://127.0.0.1:8000/admin";s:14:"routeForActive";s:6:"admin*";s:5:"order";i:7;s:18:"requiredPermission";s:10:"admin.view";}i:7;O:8:"stdClass":6:{s:4:"name";s:8:"Products";s:4:"icon";s:17:"MdOutlineCategory";s:3:"url";s:30:"http://127.0.0.1:8000/products";s:14:"routeForActive";s:9:"products*";s:5:"order";i:8;s:18:"requiredPermission";s:13:"products.view";}i:8;O:8:"stdClass":6:{s:4:"name";s:11:"Integration";s:4:"icon";s:14:"FaNetworkWired";s:3:"url";s:38:"http://127.0.0.1:8000/integration/call";s:14:"routeForActive";s:12:"integration*";s:5:"order";i:9;s:18:"requiredPermission";s:16:"integration.view";}i:9;O:8:"stdClass":6:{s:4:"name";s:4:"HRMS";s:4:"icon";s:11:"FaUserCheck";s:3:"url";s:26:"http://127.0.0.1:8000/hrms";s:14:"routeForActive";s:6:"hrms.*";s:5:"order";i:10;s:18:"requiredPermission";s:9:"hrms.view";}}