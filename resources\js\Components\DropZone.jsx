import React, { useCallback, useState } from "react";
import { useDropzone } from "react-dropzone";

const DropZone = ({ padding = "p-5", multiple = false, updateFormData, dropzoneParams }) => {
    
    const onDrop = useCallback((acceptedFiles) => {
        updateFormData(acceptedFiles);
    }, []);

    const { getRootProps, getInputProps, isDragActive } = useDropzone({
        onDrop,
        multiple: multiple,
        ...dropzoneParams,
    });



    return (
       
            <div
                {...getRootProps()}
                className={` border-gray-300 ${padding} text-center cursor-pointer`}
            >
                <input {...getInputProps()} />
                {isDragActive ? (
                    <p className="text-gray-600">Drop the file here...</p>
                ) : (
                    <p className="text-gray-600">Drag & drop a file here, or click to select one</p>
                )}
            </div>
       
    );
};

export default DropZone;
