import { <PERSON>, <PERSON>, useForm } from "@inertiajs/react";
import {
    <PERSON><PERSON>, Card,
    Checkbox,
    Drawer,
    Table,
    Tooltip
} from "flowbite-react";
import { useState } from "react";
import { IoMdAdd, IoMdGlobe } from "react-icons/io";
import { TbFolderCog } from "react-icons/tb";
import {
    button_custom,
    custom_global_card,
    customDrawer,

    table_custom
} from "../Helpers/DesignHelper";
import Edit from "./Edit";

import ConfirmBox from "@/Components/HelperComponents/ConfirmBox";
import NoRecord from "@/Components/HelperComponents/NoRecord";
import Paginate from "@/Components/HelperComponents/Paginate";
import PerPageDropdown from "@/Components/HelperComponents/PerPageDropdown";
import Main from "@/Layouts/Main";
import $ from "jquery";
import { FaRegTrashCan } from "react-icons/fa6";
import { HiOutlineUsers } from "react-icons/hi2";
import { <PERSON>ScrollText } from "react-icons/lu";
import { MdDeleteOutline, MdOutlineEdit } from "react-icons/md";
import { RiDeleteBin6Line } from "react-icons/ri";
import Add from "./Add";

export default function Index({ collection }) {
    console.log("collection Contacrt: ", collection);
    const [isCheckAll, setIsCheckAll] = useState(false);
    const [isAddContactList, setIsContactList] = useState(false);
    const [editContactDrawerData, setEditContactDrawerData] = useState({
        id: null,
        name: null,
        countryCallingCode: null,
    });
    const [editContactList, setEditContactList] = useState(false);

    const [checkValue, setCheckValue] = useState([]);
    const [isConfirmOpen, setConfirmOpen] = useState(false);
    // -------- add contact list -------------
    const handleAddContactListClose = () => setIsContactList(false);

    const { setData, delete: destroy } = useForm({
        id: [],
    });

    // ----------------Edit --------------------
    // executes when user click table row checkbox
    function getCheckedIds(e) {
        let previousIds = checkValue;
        if (e.target.checked) {
            if (!previousIds.includes(e.target.id)) {
                previousIds.push(e.target.id);
                setCheckValue(previousIds);
            }
        } else {
            const newIds = previousIds.filter((item) => item !== e.target.id);
            setCheckValue(newIds);
        }
    }

    // executes when user click table header checkbox
    function headerCheckBoxChecked(e) {
        let previousIds = [];
        if (
            e.target.checked &&
            e.target.id == 0 &&
            collection.contactLists.data.length > 0
        ) {
            collection.contactLists.data.map((contactList, key) => {
                if (!previousIds.includes(contactList.id)) {
                    previousIds.push(contactList.id);
                    setCheckValue(previousIds);
                }
            });
            setIsCheckAll(true);
            $(".rowCheckBox").prop("checked", true);
        } else {
            setCheckValue(previousIds);
            setIsCheckAll(false);
            $(".rowCheckBox").prop("checked", false);
        }
    }

    // handle checkBox Check or uncheck
    function checkAddcheckBoxChecked() {
        let allCheckBoxes = $(".rowCheckBox");
        let checkedCheckBoxes = $(".rowCheckBox:checked");

        if (allCheckBoxes.length == checkedCheckBoxes.length) {
            setIsCheckAll(true);
        } else {
            setIsCheckAll(false);
        }
    }

    function handleConfirmBoxResult(result) {

        if (checkValue.length > 0 && result) {
            setData({ id: checkValue });
            setConfirmOpen(false);
            destroy(
                route("contactList.destroy", {
                    contact: checkValue,
                })
            );
            setCheckValue([]);
            setIsCheckAll(false);
        } else {
            setCheckValue([]);
            setIsCheckAll(false);
        }
        setConfirmOpen(false);
    }

    return (
        <Main>
            <Head title="Contacts" />
            {/* <TabBar /> */}
            <Card theme={custom_global_card}>
                <div className="">
                    <div className="flex justify-between p-2">
                        <div>
                            {
                                collection.can_delete &&
                                <Button
                                    className=""
                                    size="xs"
                                    color="gray"
                                    theme={button_custom}
                                    onClick={() =>
                                        setConfirmOpen(true)
                                    }
                                >
                                    <div className="flex items-center gap-1">
                                        <MdDeleteOutline className="text-slate-500" />
                                        <span className="text-xs">Delete</span>
                                    </div>
                                </Button>
                            }
                        </div>
                        {
                            collection.can_add &&
                            <Button
                                className="border pe-1"
                                size="xs"
                                color="gray"
                                theme={button_custom}
                                onClick={() => setIsContactList(true)}
                            >
                                <div className="flex items-center gap-1">
                                    <IoMdAdd className="text-slate-500" />
                                    <span className="text-xs">Add</span>
                                </div>
                            </Button>
                        }
                    </div>
                    <div className="overflow-auto h-ull">
                        {/* documents table */}
                        <div className="overflow-x-auto border rounded-lg">
                            <Table hoverable theme={table_custom} >
                                <Table.Head className="">
                                    <Table.HeadCell>
                                        <Checkbox
                                            color="blue"
                                            checked={isCheckAll}
                                            id={0}
                                            onChange={(e) => {
                                                headerCheckBoxChecked(e);
                                            }}
                                        />
                                    </Table.HeadCell>

                                    <Table.HeadCell>
                                        <Link
                                            href={route("contactList.index", {
                                                column: "name",
                                                sort:
                                                    collection.getData.sort ==
                                                        "asc"
                                                        ? "desc"
                                                        : "asc",
                                            })}
                                        >
                                            <div className="flex items-center justify-between">
                                                <span>Name</span>
                                                <svg
                                                    xmlns="http://www.w3.org/2000/svg"
                                                    height="16px"
                                                    viewBox="0 0 24 24"
                                                    width="16px"
                                                    className="fill-gray-600"
                                                >
                                                    <path
                                                        d="M0 0h24v24H0V0z"
                                                        fill="none"
                                                    />
                                                    <path d="M12 5.83L15.17 9l1.41-1.41L12 3 7.41 7.59 8.83 9 12 5.83zm0 12.34L8.83 15l-1.41 1.41L12 21l4.59-4.59L15.17 15 12 18.17z" />
                                                </svg>
                                            </div>
                                        </Link>
                                    </Table.HeadCell>

                                    <Table.HeadCell>
                                        <Link
                                            href={route("contactList.index", {
                                                column: "user_id",
                                                sort:
                                                    collection.getData.sort ==
                                                        "asc"
                                                        ? "desc"
                                                        : "asc",
                                            })}
                                        >
                                            <div className="flex items-center justify-between">
                                                <span>Created By</span>
                                                <svg
                                                    xmlns="http://www.w3.org/2000/svg"
                                                    height="16px"
                                                    viewBox="0 0 24 24"
                                                    width="16px"
                                                    className="fill-gray-600"
                                                >
                                                    <path
                                                        d="M0 0h24v24H0V0z"
                                                        fill="none"
                                                    />
                                                    <path d="M12 5.83L15.17 9l1.41-1.41L12 3 7.41 7.59 8.83 9 12 5.83zm0 12.34L8.83 15l-1.41 1.41L12 21l4.59-4.59L15.17 15 12 18.17z" />
                                                </svg>
                                            </div>
                                        </Link>
                                    </Table.HeadCell>

                                    <Table.HeadCell>
                                        <Link
                                            href={route("contactList.index", {
                                                column: "totalContacts",
                                                sort:
                                                    collection.getData.sort ==
                                                        "asc"
                                                        ? "desc"
                                                        : "asc",
                                            })}
                                        >
                                            <div className="flex items-center justify-between text-nowrap">
                                                <span>Total Contacts</span>
                                                <svg
                                                    xmlns="http://www.w3.org/2000/svg"
                                                    height="16px"
                                                    viewBox="0 0 24 24"
                                                    width="16px"
                                                    className="fill-gray-600"
                                                >
                                                    <path
                                                        d="M0 0h24v24H0V0z"
                                                        fill="none"
                                                    />
                                                    <path d="M12 5.83L15.17 9l1.41-1.41L12 3 7.41 7.59 8.83 9 12 5.83zm0 12.34L8.83 15l-1.41 1.41L12 21l4.59-4.59L15.17 15 12 18.17z" />
                                                </svg>
                                            </div>
                                        </Link>
                                    </Table.HeadCell>

                                    <Table.HeadCell>Actions</Table.HeadCell>
                                </Table.Head>

                                <Table.Body className="divide-y">
                                    {collection.contactLists.data.length >
                                        0 ? (
                                        collection.contactLists.data.map(
                                            (contact, key) => {
                                                return (
                                                    <Table.Row
                                                        className="bg-white dark:border-gray-700 dark:bg-gray-800"
                                                        key={key}
                                                    >
                                                        <Table.Cell>
                                                            <Checkbox
                                                                color={"blue"}
                                                                className="rowCheckBox"
                                                                id={contact.id}
                                                                onChange={(
                                                                    e
                                                                ) => {
                                                                    getCheckedIds(
                                                                        e
                                                                    );
                                                                    checkAddcheckBoxChecked();
                                                                }}
                                                            />
                                                        </Table.Cell>
                                                        <Table.Cell className="font-medium text-gray-900 whitespace-nowrap dark:text-white">
                                                            {contact.name}
                                                        </Table.Cell>
                                                        <Table.Cell>
                                                            {
                                                                contact.user
                                                                    .username
                                                            }
                                                        </Table.Cell>
                                                        <Table.Cell>
                                                            {
                                                                contact.totalContacts
                                                            }
                                                        </Table.Cell>

                                                        <Table.Cell>
                                                            {/* --------------- Action buttons ---------- */}
                                                            <div className="flex gap-2 py-1">
                                                                {
                                                                    collection.can_edit &&
                                                                    <Button
                                                                        color="success"
                                                                        theme={
                                                                            button_custom
                                                                        }
                                                                        size="xs"
                                                                        className="text-nowrap"
                                                                        onClick={() => {
                                                                            setEditContactList(
                                                                                true
                                                                            );
                                                                            setEditContactDrawerData(
                                                                                {
                                                                                    id: contact.id,
                                                                                    name: contact.name,
                                                                                }
                                                                            );
                                                                        }}
                                                                    >
                                                                        <MdOutlineEdit className="text-sm" />
                                                                        <span className="text-xs ms-1">
                                                                            Edit
                                                                        </span>
                                                                    </Button>
                                                                }
                                                                {
                                                                    collection.can_contact.view &&
                                                                    <div className="relative">
                                                                        <Button
                                                                            theme={
                                                                                button_custom
                                                                            }
                                                                            as={
                                                                                Link
                                                                            }
                                                                            className="text-nowrap"
                                                                            href={route(
                                                                                "contactList.show",
                                                                                {
                                                                                    contact:
                                                                                        contact.id,
                                                                                }
                                                                            )}
                                                                            color="purple"
                                                                            size="xs"
                                                                        >
                                                                            <TbFolderCog className="text-sm" />
                                                                            <span className="text-xs ms-1">
                                                                                Manage
                                                                            </span>
                                                                        </Button>
                                                                    </div>
                                                                }
                                                                {
                                                                    collection.can_delete &&
                                                                    <Tooltip content="Delete">
                                                                        <Button
                                                                            theme={
                                                                                button_custom
                                                                            }
                                                                            color="failure"
                                                                            size="xs"
                                                                            onClick={() => {
                                                                                setCheckValue([contact.id]);
                                                                                setConfirmOpen(true);
                                                                            }}
                                                                        >
                                                                            <RiDeleteBin6Line className="text-sm" />
                                                                            <span className="text-xs ms-1">
                                                                                Delete
                                                                            </span>
                                                                        </Button>
                                                                    </Tooltip>
                                                                }
                                                            </div>
                                                        </Table.Cell>
                                                    </Table.Row>
                                                );
                                            }
                                        )
                                    ) : (
                                        <Table.Row className="bg-white dark:border-gray-700 dark:bg-gray-800">
                                            <Table.Cell
                                                colSpan={11}
                                                className="text-center"
                                            >
                                                <NoRecord />
                                            </Table.Cell>
                                        </Table.Row>
                                    )}
                                </Table.Body>
                            </Table>
                        </div>
                    </div>

                    <div className="bottom-0 w-full p-3 bg-white rounded-lg float-end dark:border-gray-500 dark:rounded-lg dark:bg-gray-700">
                        <div className="flex flex-wrap justify-between gap-2 lg:gap-0 md:gap-0">
                            <div className="flex items-center gap-4">
                                <PerPageDropdown
                                    getDataFields={collection.getData ?? null}
                                    routeName={"contactList.index"}
                                    data={collection.contactLists}
                                />
                            </div>
                            <Paginate tableData={collection.contactLists} />
                        </div>
                    </div>
                </div>
            </Card>
            {/*---------------Edit contact list drawer---------------*/}
            {editContactList === true ? (
                <Drawer
                    open={editContactList}
                    onClose={() => setEditContactList(false)}
                    position="right"
                    theme={customDrawer}
                    className="w-full lg:w-2/5 md:w-3/5"
                >
                    <DrawerHeader
                        title={<span>Edit ContactList({editContactDrawerData.name})</span>}
                        titleIcon={IoMdGlobe}
                    />
                    <DrawerItems className="px-4 py-2 mt-4 bg-white">
                        <Edit
                            onClose={() => setEditContactList(false)}
                            contactListData={editContactDrawerData}
                        />
                    </DrawerItems>
                </Drawer>
            ) : (
                <></>
            )}

            {/*---------------add contact list drawer---------------*/}
            {isAddContactList === true ? (
                <Drawer
                    theme={customDrawer}
                    className="w-full lg:w-2/5 md:w-3/5"
                    open={isAddContactList}
                    onClose={handleAddContactListClose}
                    position="right"
                    id="addContactListDrawer"
                >
                    <DrawerHeader
                        titleIcon={LuScrollText}
                        title={<span>Add Contact List</span>}
                    />
                    <DrawerItems className="py-2">
                        <Add onClose={handleAddContactListClose}></Add>
                    </DrawerItems>
                </Drawer>
            ) : (
                <></>
            )}


            {/* confirm box popup */}
            {
                isConfirmOpen &&
                <ConfirmBox
                    isOpen={isConfirmOpen}
                    onClose={() => setConfirmOpen(false)} // Close the confirm box
                    onAction={handleConfirmBoxResult} // Handle the user's choice
                    title="Are you sure you want to delete this?"
                    message="This action cannot be undone."
                    confirmText="Yes, Delete!"
                    cancelText="No, Keep It"
                    confirmColor="orange"
                    cancelColor="gray"
                    icon={<FaRegTrashCan />}
                />
            }
        </Main>
    );
}
