import {
    button_custom,
    customDrawer,
    input_custom,
    textarea_custom,
} from "@/Pages/Helpers/DesignHelper";
import { fetchJson, fileIcons } from "@/Pages/Helpers/Helper";
import { useForm } from "@inertiajs/react";
import EmojiPicker from "emoji-picker-react";
import {
    Button,
    Checkbox,
    Drawer,
    FileInput,
    Label,
    Textarea,
    TextInput,
} from "flowbite-react";
import { useEffect, useRef, useState } from "react";
import { CgAttachment } from "react-icons/cg";
import {
    FaBold,
    FaItalic,
    FaRegFaceSmileBeam,
    FaRegNoteSticky,
    FaStrikethrough,
} from "react-icons/fa6";
import { HiOutlineTemplate } from "react-icons/hi";
import { VscSend } from "react-icons/vsc";
import Templates from "./Templates";
import { IoMdClose } from "react-icons/io";

function TextBox({ handleChange = null, sendTo = null }) {

    const [isOpenTemplates, setIsOpenTemplates] = useState(false);
    const [openEmoji, setOpenEmoji] = useState(false);
    const textareaRef = useRef(null);
    const [isScheduled, setIsScheduled] = useState(false);
    const [messageDatetime, setMessageDatetime] = useState(new Date());
    const [gateways, setGateways] = useState(null);
    const [files, setFiles] = useState([]);
    useEffect(() => {
        setMessageDatetime(new Date());
    }, [isScheduled]);


    const { data, setData, post, reset, processing, errors } = useForm({
        message: "",
        attachment: {},
        isScheduled: isScheduled,
        ScheduleTime: messageDatetime,
        template_id: "",
        sendTo: sendTo,
        gateway: 0
    });
    useEffect(() => {
        setData('sendTo', sendTo);
    }, [sendTo]);


    const getGateways = () => {
        Promise.all([fetchJson(route('helper.getAllChannels'), {}, true)])
            .then(([content]) => {
                const defaultGate = content.channels.filter((item) => item.isDefault === 1)
                // console.log(defaultGate[0].id);

                setGateways(content.channels);
                setData('gateway', defaultGate ? defaultGate[0]?.id ?? null : null);

            });
    }
    useEffect(() => {

        getGateways();
    }, []);

    const textFormat = (formatAs) => {
        const textarea = textareaRef.current;
        const start = textarea.selectionStart;
        const end = textarea.selectionEnd;

        // Get the selected text
        const selectedText = data.message.substring(start, end);

        // Wrap the selected text in ** to make it bold in markdown
        const boldText = `${formatAs + selectedText + formatAs}`;

        // Update the message state with the bold text
        setData(
            "message",
            data.message.substring(0, start) +
            boldText +
            data.message.substring(end)
        );

        // Set the cursor position after the inserted bold text
        setTimeout(() => {
            textarea.setSelectionRange(
                start + boldText.length,
                start + boldText.length
            );
            textarea.focus();
        }, 0);
    };

    const sendMessage = (e) => {
        e.preventDefault();
        post(route("whatsapp.chats.store"), {
            onSuccess: (res) => {
                setData({
                    ...data,
                    message: "",
                    attachment: [],
                    isScheduled: isScheduled,
                    template_id: "",
                });
                handleChange();
            },
            onError: (res) => {
            },
        });
        setFiles([])
    };
    const fileInputRef = useRef(null);
    const handleClick = () => {
        fileInputRef.current.click();
    };

    const handleFileChange = (event) => {
        const selectedFiles = Array.from(event.target.files);
        const filePreviews = selectedFiles.map((file) => {
            const extension = file.name.split(".").pop().toLowerCase();
            return {
                name: file.name,
                url: validImageExtensions.includes(extension)
                    ? URL.createObjectURL(file)
                    : null,
                extension,
            };
        });
        setFiles(filePreviews);
        setData("attachment", event.target.files);
    };

    const handleRemove = (index) => {
        const TempStore = { ...data.attachment };
        delete TempStore[index];

        const updatedFiles = [...files];
        if (updatedFiles[index].url) {
            URL.revokeObjectURL(updatedFiles[index].url); // Revoke URL for images
        }
        updatedFiles.splice(index, 1);
        setFiles(updatedFiles);
        setData("files", TempStore);
    };

    const validImageExtensions = ["jpeg", "jpg", "png", "webp", "gif"];
    function checkImage(fileName) {
        const extension = fileName.split(".").pop();
        return validImageExtensions.includes(extension);
    }

    return (
        <form onSubmit={sendMessage}>
            <div className="bg-[#F0EEED] p-2">
                {/*-------- Message -----------*/}
                <div className="">
                    {openEmoji && (
                        <div className="absolute z-50" style={{ top: "25%", bottom: "25%" }}>
                            <EmojiPicker
                                open={openEmoji}
                                lazyLoadEmojis={true}
                                onEmojiClick={(emojiObject) =>
                                    setData("message", data.message + emojiObject.emoji)
                                }
                            />
                        </div>
                    )}

                    <div className="p-2 border border-gray-300 rounded-md bg-gray-50">
                        <div className="grid grid-cols-3 md:grid-cols-5 lg:grid-cols-5 xl:grid-cols-7">
                            {files.map((file, index) => (
                                <div
                                    className="relative p-2 border rounded"
                                    key={index}
                                >
                                    <div className="flex ">
                                        <div className="text-ellipsis overflow-hidden ... m-auto">
                                            {file.url ? (
                                                <div className="flex justify-center ">
                                                    <div className="m-auto">
                                                        <img
                                                            className="object-contain"
                                                            src={file.url}
                                                            alt={file.name}
                                                        />
                                                    </div>
                                                </div>
                                            ) : (
                                                <div className="p-2 text-4xl text-gray-400 bg-gray-200 rounded-lg ">
                                                    <div className="flex justify-center ">
                                                        <div className="m-auto">
                                                            {fileIcons[file.extension]}
                                                        </div>
                                                    </div>
                                                </div>
                                            )}

                                            <p className="text-sm truncate">{file.name}</p>
                                        </div>
                                        <div className="absolute top-0 end-0">
                                            <Button
                                                size="xs"
                                                color="gray"
                                                pill
                                                onClick={() => handleRemove(index)}
                                            >
                                                <IoMdClose />
                                            </Button>
                                        </div>
                                    </div>
                                </div>
                            ))}
                        </div>

                        <Textarea
                            theme={textarea_custom}
                            name="message"
                            placeholder="Type Message here..."
                            rows={4}
                            ref={textareaRef}
                            value={data.message}
                            onFocus={() => setOpenEmoji(false)}
                            color={errors.message ? "failure" : "gray"}
                            onChange={(e) => setData("message", e.target.value)}
                            className="border-0 focus:ring-white"
                            helperText={errors.message && errors.message}
                        />

                        <div className="flex flex-col flex-wrap justify-between gap-1 lg:flex-row">
                            <div className="flex flex-wrap items-center gap-1">
                                <div className="">
                                    <label htmlFor="File-Input">
                                        <div className="flex items-center p-1 text-sm bg-white border rounded-md">
                                            <div className="">
                                                <CgAttachment className="text-xs text-slate-700" />
                                            </div>
                                            <div className="">Add File</div>
                                        </div>
                                        {/* </Button> */}
                                    </label>
                                    <div className="hidden">
                                        <FileInput
                                            size={"xs"}
                                            id="File-Input"
                                            onChange={handleFileChange}
                                        />
                                    </div>
                                </div>
                                <Button
                                    className="px-0"
                                    size="xs"
                                    color="gray"
                                    onClick={() => setOpenEmoji(!openEmoji)}
                                >
                                    <FaRegFaceSmileBeam />
                                </Button>
                                <Button
                                    className="px-0"
                                    size="xs"
                                    color="gray"
                                    onClick={() => textFormat("*")}
                                >
                                    <FaBold />
                                </Button>
                                <Button
                                    className="px-0"
                                    size="xs"
                                    color="gray"
                                    onClick={() => textFormat("_")}
                                >
                                    <FaItalic />
                                </Button>
                                <Button
                                    className="px-0"
                                    size="xs"
                                    color="gray"
                                    onClick={() => textFormat("~")}
                                >
                                    <FaStrikethrough />
                                </Button>
                                <Button
                                    theme={button_custom}
                                    size="xs"
                                    className="rounded-lg bg-gray-50"
                                    color="gray"
                                    onClick={() => setIsOpenTemplates(true)}
                                    disabled={!data.gateway}
                                >
                                    <div className="flex items-center gap-1 text-sm">
                                        <FaRegNoteSticky className="text-lg text-sla" />
                                        Select Template
                                    </div>
                                </Button>
                            </div>
                            <div className="flex flex-wrap gap-2">
                                <select
                                    className="p-1 px-3 text-sm border-gray-300 rounded-md hover:bg-gray-50"
                                    onChange={(e) => setData('gateway', e.target.value)}
                                >
                                    <option value="">Gateway <span className="text-red-600">*</span></option>
                                    {gateways && gateways.map((gate) => (
                                        <option key={gate.id} value={gate.id} selected={gate.isDefault}>
                                            {gate.name}
                                        </option>
                                    ))}
                                </select>
                                <div className="flex items-center gap-2">
                                    <Checkbox
                                        color="blue"
                                        id="accept"
                                        defaultChecked={isScheduled}
                                        onChange={() => {
                                            setIsScheduled(!isScheduled);
                                            setData("isScheduled", !isScheduled);
                                        }}
                                    />
                                    <Label htmlFor="accept" className="flex">
                                        Schedule&nbsp;
                                    </Label>
                                </div>
                                {isScheduled && (
                                    <TextInput
                                        className={"w-fit"}
                                        sizing="xs"
                                        theme={input_custom}
                                        id="startTime"
                                        name="startTime"
                                        type="datetime-local"
                                        defaultValue={new Date().toLocaleString("sv-SE").slice(0, 16)}
                                        required
                                        color={errors.startTime ? "failure" : "gray"}
                                        onChange={(e) => setData("ScheduleTime", e.target.value)}
                                        helperText={errors.startTime && errors.startTime}
                                    />
                                )}
                                <Button
                                    type="submit"
                                    size="xs"
                                    color="blue"
                                    onClick={sendMessage}
                                    disabled={!data.gateway}
                                >
                                    <div className="flex items-center gap-1 text-sm">
                                        <VscSend className="text-lg" />
                                        {isScheduled ? "Schedule" : "Send Now"}
                                    </div>
                                </Button>
                            </div>
                        </div>
                    </div>
                </div>
                <span className="text-sm text-red-600 ms-4">
                    Maximum upload file size: 10 MB.
                </span>

                {isOpenTemplates && (
                    <Drawer
                        theme={customDrawer}
                        open={isOpenTemplates}
                        onClose={() => setIsOpenTemplates(false)}
                        position="right"
                        className="w-full xl:w-10/12 lg:w-full md:w-full"
                    >
                        <DrawerHeader
                            titleIcon={HiOutlineTemplate}
                            title="Template library"
                        />
                        <DrawerItems>
                            <Templates sendTo={sendTo} gateway={data.gateway} setClose={setIsOpenTemplates} refetch={handleChange} />
                        </DrawerItems>
                    </Drawer>
                )}
            </div>
        </form>
    );
}

export default TextBox;



