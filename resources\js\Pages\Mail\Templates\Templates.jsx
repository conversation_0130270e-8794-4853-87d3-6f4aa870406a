import {
    button_custom,
    card_custom,
    customDrawer,
} from "@/Pages/Helpers/DesignHelper";
import Search from "@/Pages/Users/<USER>";
import { Link } from "@inertiajs/react";
import { <PERSON><PERSON>, Card, Drawer, DrawerHeader, DrawerItems } from "flowbite-react";
import { useState } from "react";
import { CgAttachment } from "react-icons/cg";
import { HiOutlineTemplate } from "react-icons/hi";
import { IoMdAdd } from "react-icons/io";
import {
    MdDeleteOutline,
    MdOutlineEdit,
} from "react-icons/md";
import { RiFileSearchLine } from "react-icons/ri";
import Add from "./Add";
import Category from "./Category";
import Edit from "./Edit";
import View from "./View";

export default function Templates({ categories, templates, category_id }) {
    // Add Template
    const [isOpen, setIsOpen] = useState(false);
    const [isEdit, setIsEdit] = useState(false);
    const [isView, setIsView] = useState(false);

    const [editId, setEditId] = useState(null);
    const [selectedCategory, setSelectedCategory] = useState("0");
    const handleClose = () => setIsOpen(false);

    return (
        <>
            <div className="relative p-2 overflow-hidden">
                <div className="grid grid-flow-row-dense grid-cols-12 gap-2 p-0 border-none md:grid-cols-12 sm:grid-cols-1 bg-slate-100">
                    <div className="hidden bg-white border rounded-lg xl:col-span-2 lg:col-span-3 md:col-span-2 sm:col-span-3 lg:flex">
                        <Category
                            setSelectedCategory={setSelectedCategory}
                            selectedCategory={selectedCategory}
                        />
                    </div>
                    <div className="relative p-2 pt-0 bg-white border rounded-lg dark:text-gray-400 lg:p-0 xl:col-span-10 lg:col-span-9 md:col-span-12 col-span-full">
                        <div>
                            {/* Welcome message  */}
                            <div className="flex items-center justify-between p-2 ">
                                <div className="flex items-center gap-2">
                                    <div className="max-w-md">
                                        <Search
                                            className=""
                                            inputPlaceholder="Search a Template..."
                                        ></Search>
                                    </div>
                                    <select
                                        className="py-1 mb-1 text-sm border-gray-300 rounded hover:bg-gray-50"
                                        name="cars"
                                        id="cars"
                                    >
                                        <option value="volvo">
                                            Filter by Gateway
                                        </option>
                                        <option value="saab">
                                            Name
                                        </option>
                                        <option value="opel">
                                            Mobile Number
                                        </option>
                                        <option value="audi">
                                            City
                                        </option>
                                        <option value="audi">
                                            Address
                                        </option>
                                    </select>
                                </div>
                                <div>
                                    <Button
                                        as={Link}
                                        size="xs"
                                        color="gray"
                                        theme={button_custom}
                                        href={route(
                                            "whatsappB.templates.create"
                                        )}
                                    >
                                        <div className="flex items-center gap-1 text-xs">
                                            <IoMdAdd className="text-sm text-slate-500" />
                                            <span>Add</span>
                                        </div>
                                    </Button>
                                </div>
                            </div>
                            <div className="h-full px-2 pb-2">
                                <div className="grid grid-flow-row-dense grid-cols-1 gap-4 2xl:grid:cols-5 xl:grid-cols-4 lg:grid-cols-3 md:grid-cols-3 sm:grid-cols-2">
                                    {/* Templates card */}

                                    <div
                                        className=""
                                    // key={index}
                                    >
                                        <Card
                                            theme={card_custom}
                                            className="relative w-full bg-slate-300"
                                        >
                                            <div className="absolute -left-0 top-6 w-0 h-0 border-t-[1px] border-t-transparent border-b-[12px] border-b-transparent border-r-[18px] border-r-white"></div>
                                            <div>
                                                <div
                                                    className="p-2 overflow-auto"
                                                    style={{
                                                        height: "250px",
                                                    }}
                                                >
                                                    <div className="bg-white rounded-lg mx-2.5 py-0.5">
                                                        <>
                                                            <div>
                                                                {/* <img
                                                                                                                           src={showAsset(
                                                                                                                               "/storage/"
                                                                                                                           )}
                                                                                                                           className="w-full my-2 rounded-md max-h-36 min-w-fit"
                                                                                                                       /> */}
                                                            </div>
                                                        </>
                                                        <div
                                                            className="text-center text-gray-300 "
                                                            style={{
                                                                fontSize:
                                                                    "7rem",
                                                            }}
                                                        ></div>
                                                        <div className="flex gap-2 p-2 rounded-lg">
                                                            <div className="text-3xl text-gray-400"></div>
                                                            <div className="text-sm text-gray-700">
                                                                Body: Hi
                                                                [Customer’s
                                                                First
                                                                Name],
                                                                Welcome
                                                                to
                                                                RapBooster—we’re
                                                                thrilled
                                                                to have
                                                                you with
                                                                us!
                                                                We’re
                                                                here to
                                                                help you
                                                                WhatsApp
                                                                Marketing,
                                                                SMS
                                                                Marketing,
                                                                and
                                                                boosting
                                                                your
                                                                overall
                                                                productivity.
                                                            </div>
                                                        </div>

                                                        <></>
                                                        <div className="flex items-center gap-1 px-2">

                                                            <span className="text-sm text-blue-600">
                                                                <CgAttachment />
                                                            </span>
                                                            <span className="text-sm text-blue-600">
                                                                2
                                                            </span>
                                                        </div>
                                                    </div>
                                                </div>
                                                <div className="p-1 bg-slate-100">
                                                    <div className="flex items-start justify-between gap-2 rounded-b-lg">
                                                        {/* <div>
                                                                <div className="text-sm">
                                                                    {
                                                                        template.name
                                                                    }
                                                                </div>
                                                                <div className="text-xs">
                                                                    Gateway 1
                                                                </div>
                                                            </div> */}
                                                        <div className="truncate">
                                                            <div className="text-sm">
                                                                Welcome Message
                                                            </div>
                                                            <div className="text-xs text-slate-500">
                                                                Gateway
                                                                1
                                                            </div>
                                                        </div>

                                                        <div className="flex items-center">
                                                            <Button
                                                                theme={
                                                                    button_custom
                                                                }
                                                                color="withoutBorder"
                                                                size="xs"
                                                                onClick={() => setIsView(true)}
                                                            >
                                                                <RiFileSearchLine className="text-slate-400" />
                                                            </Button>

                                                            <Button
                                                                theme={
                                                                    button_custom
                                                                }
                                                                color="withoutBorder"
                                                                size="xs"
                                                            // onClick={() => {
                                                            //     setIsEdit(
                                                            //         true
                                                            //     );
                                                            //     setEditId(
                                                            //         template.id
                                                            //     );
                                                            // }}
                                                            >
                                                                <MdOutlineEdit className="text-slate-400" />
                                                            </Button>
                                                            <Button
                                                                theme={
                                                                    button_custom
                                                                }
                                                                color="withoutBorder"
                                                                size="xs"
                                                            // onClick={() =>
                                                            //     window.confirm(
                                                            //         "Do you really want to delete Template ?"
                                                            //     ) &&
                                                            //     removeObjectAndDeleteRecord(
                                                            //         "wa_template",
                                                            //         template.id
                                                            //     )
                                                            // }
                                                            >
                                                                <MdDeleteOutline className="text-slate-400" />
                                                            </Button>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </Card>
                                    </div>

                                </div>
                            </div>
                            <div className="bottom-0 w-full mt-1.5 p-3 bg-white rounded-lg float-end dark:border-gray-500 dark:rounded-lg dark:bg-gray-700">
                                <div className="flex flex-wrap justify-between gap-2 lg:justify-between lg:gap-0 md:gap-0">
                                    <div className="flex items-center gap-4">
                                        {/* <PerPageDropdown
                                                    // getDataFields={
                                                    //     getData ?? null
                                                    // }
                                                    routeName={
                                                        "whatsapp.templates.index"
                                                    }
                                                    data={templates}
                                                    customPerPage={8}
                                                /> */}
                                    </div>

                                    {/* <Paginate tableData={templates} /> */}
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            {isOpen && (
                <Drawer
                    theme={customDrawer}
                    open={isOpen}
                    onClose={handleClose}
                    position="right"
                    className="w-full lg:w-5/6 md:w-4/5"
                >
                    <DrawerHeader
                        titleIcon={HiOutlineTemplate}
                        title="Add Template"
                    />
                    <DrawerItems>
                        <Add
                            categories={categories}
                            onClose={() => setIsOpen(false)}
                            setTemplate={setTemplateData}
                            selectedCategory={selectedCategory}
                            setSelectedCategory={setSelectedCategory}
                        />
                    </DrawerItems>
                </Drawer>
            )}
            {isEdit && (
                <Drawer
                    theme={customDrawer}
                    open={isEdit}
                    onClose={() => setIsEdit(false)}
                    position="right"
                    className="w-full lg:w-5/6 md:w-4/5"
                >
                    <DrawerHeader
                        titleIcon={HiOutlineTemplate}
                        title="Edit Template"
                    />
                    <DrawerItems>
                        <Edit
                            id={editId}
                            onClose={() => setIsEdit(false)}
                            setTemplate={setTemplateData}
                            setSelectedCategory={setSelectedCategory}
                        />
                    </DrawerItems>
                </Drawer>
            )}
            <Drawer
                theme={customDrawer}
                open={isView}
                onClose={() => setIsView(false)}
                position="right"
                className="w-full lg:w-2/5 md:w-2/5"
            >
                <DrawerHeader
                    titleIcon={RiFileSearchLine}
                    title="View Template (Welcome Message)"
                />
                <DrawerItems>
                    <View />
                </DrawerItems>
            </Drawer>
        </>
    );
}



