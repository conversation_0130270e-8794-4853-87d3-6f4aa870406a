import {
    table_custom
} from "@/Pages/Helpers/DesignHelper";
import {
    Table
} from "flowbite-react";
import $ from "jquery";
import { useEffect, useState } from "react";
import Main from "./Main";
import PerPageDropdown from "@/Components/HelperComponents/PerPageDropdown";
import Paginate from "@/Components/HelperComponents/Paginate";
import NoRecord from "@/Components/HelperComponents/NoRecord";

function Autoreply({ collection }) {

    const [isCheckAll, setIsCheckAll] = useState(false);

    useEffect(() => {
        $(".rowCheckBox").prop("checked", isCheckAll);
    }, [isCheckAll]);

    return (
        <Main>
            <div className="overflow-x-auto">
                <Table hoverable theme={table_custom}>
                    <TableHead className=" bg-slate-100">

                        <TableHeadCell>
                            <div className="flex items-center justify-between gap-2">
                                <span>Id</span>
                            </div>
                        </TableHeadCell>
                        <TableHeadCell>
                            <div className="flex items-center justify-between gap-2">
                                <span>user_id</span>
                            </div>
                        </TableHeadCell>
                        <TableHeadCell>
                            <div className="flex items-center justify-between gap-2">
                                <span>wa_gateway_id</span>
                            </div>
                        </TableHeadCell>
                        <TableHeadCell>
                            <div className="flex items-center justify-between gap-2">
                                <span>mobile</span>
                            </div>
                        </TableHeadCell>
                        <TableHeadCell>
                            <div className="flex items-center justify-between gap-2">
                                <span>fileType</span>
                            </div>
                        </TableHeadCell>
                        <TableHeadCell>
                            <div className="flex items-center justify-between gap-2">
                                <span>fileName</span>
                            </div>
                        </TableHeadCell>
                        <TableHeadCell>
                            <div className="flex items-center justify-between gap-2">
                                <span>file</span>
                            </div>
                        </TableHeadCell>
                        <TableHeadCell>
                            <div className="flex items-center justify-between gap-2">
                                <span>mimeType</span>
                            </div>
                        </TableHeadCell>
                        <TableHeadCell>
                            <div className="flex items-center justify-between gap-2">
                                <span>caption</span>
                            </div>
                        </TableHeadCell>
                        <TableHeadCell>
                            <div className="flex items-center justify-between gap-2">
                                <span>msg</span>
                            </div>
                        </TableHeadCell>
                        <TableHeadCell>
                            <div className="flex items-center justify-between gap-2">
                                <span>status</span>
                            </div>
                        </TableHeadCell>
                        <TableHeadCell>
                            <div className="flex items-center justify-between gap-2">
                                <span>Response</span>
                            </div>
                        </TableHeadCell>
                        <TableHeadCell>
                            <div className="flex items-center justify-between gap-2">
                                <span>datetime</span>
                            </div>
                        </TableHeadCell>
                    </TableHead>
                    <TableBody className="divide-y">
                        {
                            (collection && collection.botData.data.length > 0) ?
                                collection.botData.data.map((bot, key) => {
                                    return (
                                        <TableRow
                                            className="bg-white dark:border-gray-700 dark:bg-gray-800 text-nowrap"
                                            key={key}
                                        >
                                            <TableCell>
                                                {bot.id}
                                            </TableCell>
                                            <TableCell>
                                                {bot.user_id}
                                            </TableCell>
                                            <TableCell>
                                                {bot.wa_gateway_id}
                                            </TableCell>
                                            <TableCell>
                                                {bot.mobile}
                                            </TableCell>
                                            <TableCell>
                                                {bot.fileType}
                                            </TableCell>
                                            <TableCell>
                                                {bot.fileName}
                                            </TableCell>
                                            <TableCell>
                                                {bot.file}
                                            </TableCell>
                                            <TableCell>
                                                {bot.mimeType}
                                            </TableCell>
                                            <TableCell>
                                                {bot.caption}
                                            </TableCell>
                                            <TableCell>
                                                {bot.msg}
                                            </TableCell>
                                            <TableCell>
                                                {bot.status}
                                            </TableCell>
                                            <TableCell>
                                                {bot.wapiResponse}
                                            </TableCell>
                                            <TableCell>
                                                {bot.datetime}
                                            </TableCell>
                                        </TableRow>
                                    );
                                })
                                :
                                <TableRow className="bg-white dark:border-gray-700 dark:bg-gray-800">
                                    <TableCell
                                        colSpan={13}
                                        className="text-center"
                                    >
                                        <NoRecord />
                                    </TableCell>
                                </TableRow>
                        }
                    </TableBody>
                </Table>
            </div>
            <div className="bottom-0 w-full p-3 mt-1.5 bg-white rounded-lg float-end dark:border-gray-500 dark:rounded-lg dark:bg-gray-700">
                <div className="flex flex-wrap justify-between gap-2 lg:justify-between lg:gap-0 md:gap-0">
                    <div className="flex items-center gap-4">

                        <PerPageDropdown
                            // getDataFields={
                            //     autoReplyData.getData ?? null
                            // }
                            routeName={"whatsappB.log.autoreply"}
                            data={collection.botData}
                        />
                    </div>
                    <Paginate tableData={collection.botData} />
                </div>
            </div>
        </Main>
    )
}

export default Autoreply

