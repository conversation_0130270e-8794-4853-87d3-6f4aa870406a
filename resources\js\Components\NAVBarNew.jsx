import React, { memo } from "react";
import { usePage } from "@inertiajs/react";
import { Navbar } from "flowbite-react";

const NAVBar = memo(function NAVBar({ items, sidebar_items }) {
    const { auth, logo } = usePage().props;

    return (
        <Navbar className="lg:h-14 md:h-14" rounded>
            <Navbar.Brand href={route("dashboard")}>
                <img src={logo} className="h-6" alt="Logo" />
                <span className="ml-2 text-lg font-semibold">Test Navbar - Step 1</span>
            </Navbar.Brand>
        </Navbar>
    );
});

export default NAVBar;
