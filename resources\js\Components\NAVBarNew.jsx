import React, { memo } from "react";
import { usePage } from "@inertiajs/react";
import { Navbar, NavbarBrand } from "flowbite-react";

const NAVBar = memo(function NAVBar({ items, sidebar_items }) {
    const { auth, logo } = usePage().props;

    return (
        <div className="bg-white border-b border-gray-200 h-14 flex items-center px-4">
            <div className="flex items-center">
                <img src={logo} className="h-6" alt="Logo" />
                <span className="ml-2 text-lg font-semibold">Test Navbar - Step 3 (No Flowbite)</span>
            </div>
        </div>
    );
});

export default NAVBar;
