import Main from "@/Layouts/Main";
import React, { useState } from "react";
import TabBar from "../TabBar";
import {
    Badge,
    Button,
    ButtonGroup,
    Checkbox,
    Drawer,
    FileInput,
    Label,
    Modal,
    ModalBody,
    ModalHeader,
    Progress,
    Table,
    TableBody,
    TableCell,
    TableHead,
    TableHeadCell,
    TableRow,
} from "flowbite-react";
import {
    button_custom,
    customModal,
    table_custom,
} from "../../Helpers/DesignHelper";
import { TbColumns3 } from "react-icons/tb";
import { IoMdAdd } from "react-icons/io";
import {
    MdDeleteOutline,
    MdFilterList,
    MdOutlineCampaign,
    MdOutlineIndeterminateCheckBox,
} from "react-icons/md";
import { Link } from "@inertiajs/react";
import { PiCaretUpDownBold } from "react-icons/pi";
import { IoCloseSharp, IoMicOutline, IoPauseOutline } from "react-icons/io5";
import { FiPlay } from "react-icons/fi";
import { showAsset } from "@/Pages/Helpers/Helper";

export default function Campaign() {
    const [openModal, setOpenModal] = useState(true);
    // ----------------Edit --------------------

    return (
        <Main>
            <div className="p-2 overflow-hidden">
                <TabBar />
                <div className="pb-2 rounded-lg">
                    <div className="h-fit bg-white rounded border overflow-auto w-full mt-2.5">
                        <div className="flex items-center justify-between ps-2">
                            <ButtonGroup className="h-fit">
                                <Button
                                    className=""
                                    size="xs"
                                    color="gray"
                                    theme={button_custom}
                                // onClick={deleteRecords}
                                >
                                    <div className="flex items-center gap-1">
                                        <MdDeleteOutline className="text-slate-500" />
                                        <span className="text-xs">Delete</span>
                                    </div>
                                </Button>
                                <Button
                                    theme={button_custom}
                                    size="xs"
                                    color="gray"
                                    id="dropdownInformationButton"
                                    data-dropdown-toggle="dropdownNotification"
                                    type="button"
                                >
                                    <div className="flex items-center gap-1 text-xs">
                                        <TbColumns3 className="text-slate-500 ms-1" />
                                        <span className="text-xs">Columns</span>
                                    </div>
                                </Button>
                            </ButtonGroup>
                            <div className="flex items-center gap-2 p-2">
                                <Button
                                    // className="pe-1"
                                    as={Link}
                                    size="xs"
                                    color="gray"
                                    theme={button_custom}

                                    href={route("call.campaign.addcampaign")}
                                // onClick={() => setIsAddOpen(true)}
                                >
                                    <div className="flex items-center gap-1 text-xs">
                                        <IoMicOutline className="text-sm text-slate-500" />
                                        <span>Record Audio</span>
                                    </div>
                                </Button>
                                <Button
                                    // className="pe-1"
                                    as={Link}
                                    size="xs"
                                    color="gray"
                                    theme={button_custom}

                                    href={route("call.campaign.addcampaign")}
                                // onClick={() => setIsAddOpen(true)}
                                >
                                    <div className="flex items-center gap-1 text-xs">
                                        <IoMdAdd className="text-sm text-slate-500" />
                                        <span>Add Audio File</span>
                                    </div>
                                </Button>
                            </div>
                        </div>
                        <div className="h-full">
                            {/* documents table */}
                            <div className="overflow-x-auto bg-white border rounded-lg text-nowrap">
                                <Table hoverable theme={table_custom}>
                                    <TableHead className="bg-slate-100">
                                        <TableHeadCell>
                                            <Checkbox color={"blue"} />
                                        </TableHeadCell>

                                        <TableHeadCell>
                                            <Link
                                            // href={route(
                                            //     "whatsapp.campaign.index",
                                            //     {
                                            //         column: "startTime",
                                            //         sort:
                                            //             getData.sort ==
                                            //             "asc"
                                            //                 ? "desc"
                                            //                 : "asc",
                                            //         perPage:
                                            //             getData.perPage,
                                            //     }
                                            // )}
                                            >
                                                <div className="flex items-center justify-between gap-2">
                                                    <h3>Date & Time</h3>
                                                    <PiCaretUpDownBold
                                                    // className={
                                                    //     (getData.column ==
                                                    //     "startTime"
                                                    //         ? "  text-gray-900 dark:text-gray-200 "
                                                    //         : "text-gray-600 dark:text-gray-400 ") +
                                                    //     "  cursor-pointer "
                                                    // }
                                                    />
                                                </div>
                                            </Link>
                                        </TableHeadCell>

                                        <TableHeadCell>
                                            <Link
                                            // href={route(
                                            //     "whatsapp.campaign.index",
                                            //     {
                                            //         column: "name",
                                            //         sort:
                                            //             getData.sort ==
                                            //             "asc"
                                            //                 ? "desc"
                                            //                 : "asc",
                                            //         perPage:
                                            //             getData.perPage,
                                            //     }
                                            // )}
                                            >
                                                <div className="flex items-center justify-between gap-2">
                                                    <h3>Audio Name</h3>
                                                    <PiCaretUpDownBold
                                                    // className={
                                                    //     (getData.column ==
                                                    //     "name"
                                                    //         ? "  text-gray-900 dark:text-gray-200 "
                                                    //         : "text-gray-600 dark:text-gray-400 ") +
                                                    //     "  cursor-pointer "
                                                    // }
                                                    />
                                                </div>
                                            </Link>
                                        </TableHeadCell>
                                        <TableHeadCell>
                                            Duration
                                        </TableHeadCell>
                                        <TableHeadCell>
                                            <Link

                                            >
                                                <div className="flex items-center justify-between gap-2">
                                                    <h3>Recording</h3>
                                                    <PiCaretUpDownBold
                                                    />
                                                </div>
                                            </Link>
                                        </TableHeadCell>

                                        <TableHeadCell>
                                            <h3>Actions</h3>
                                        </TableHeadCell>
                                    </TableHead>
                                    <TableBody className="divide-y">
                                        <TableRow
                                            className="items-center bg-white dark:border-gray-700 dark:bg-gray-800"
                                        // key={"campaign" + k}
                                        >
                                            <TableCell>
                                                <Checkbox color={"blue"} />
                                            </TableCell>
                                            <TableCell className="text-blue-600 ">
                                                <div className="flex flex-col">
                                                    <span className="font-medium">
                                                        11/1/2024
                                                    </span>
                                                    <span className="text-xs text-slate-500">
                                                        5:21
                                                    </span>
                                                    {/* {convertDateFormat(
                                                    campaign.startTime
                                                )} */}
                                                </div>
                                            </TableCell>

                                            <TableCell
                                                onClick={() => {
                                                    setIsOpenDetail(true);
                                                    setCampaignID({
                                                        id: "73",
                                                        name: "Dana Gates",
                                                        status: "Terminated",
                                                    });
                                                }}
                                                className="cursor-pointer "
                                            >
                                                Dana Gates
                                                {/* {campaign.name} */}
                                            </TableCell>
                                            <TableCell
                                                onClick={() => {
                                                    setIsOpenDetail(true);
                                                    setCampaignID({
                                                        id: "73",
                                                        name: "Dana Gates",
                                                        status: "Terminated",
                                                    });
                                                }}
                                                className="cursor-pointer "
                                            >
                                                Dana Gates
                                                {/* {campaign.name} */}
                                            </TableCell>
                                            <TableCell>
                                                <audio className="h-12" autoplay="autoplay" controls="controls" id="player">
                                                    <source src="http://www.issilissinew.com/zindorg/1/mp3/2014/Mukunda/128/02%20-%20Daredumdadum%20%5bwww.AtoZmp3.in%5d.mp3.ogg" />
                                                    <source src="http://www.issilissinew.com/zindorg/1/mp3/2014/Mukunda/128/02%20-%20Daredumdadum%20%5bwww.AtoZmp3.in%5d.mp3" />
                                                    <p> Your browser doesn't support the audio tag </p>
                                                </audio>
                                                {/* <div>
                                                                                                <button onclick="document.getElementById('player').play()">Play</button>
                                                                                                <button onclick="document.getElementById('player').pause()">Pause</button>
                                                                                                <button onclick="document.getElementById('player').volume+ = 0.5">Vol+ </button>
                                                                                                <button onclick="document.getElementById('player').volume- = 0.5">Vol- </button>    
                                                                                            </div>   */}
                                            </TableCell>
                                            <TableCell>
                                                <Button
                                                    theme={button_custom}
                                                    color="failure"
                                                    size="xs"
                                                >
                                                    <IoPauseOutline className="text-sm" />
                                                    <span className="text-xs ms-1">
                                                        Delete
                                                    </span>
                                                </Button>
                                            </TableCell>
                                        </TableRow>
                                    </TableBody>
                                </Table>
                            </div>
                        </div>

                        <div className="bottom-0 w-full p-3 bg-white border rounded-b-lg float-end">
                            <div className="flex flex-wrap justify-between gap-2 lg:gap-0 md:gap-0">
                                <div className="flex items-center gap-3">
                                    <div className="text-sm text-gray-400 ">
                                        Showing 1 to 25 of 62 entries
                                    </div>
                                    <div>
                                        <select
                                            id="countries"
                                            className="block p-1 text-xs text-gray-900 border border-gray-300 rounded bg-gray-50 focus:ring-blue-500 focus:border-blue-500 ps-2 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-blue-500 dark:focus:border-blue-500"
                                        >
                                            <option
                                                value={10}
                                                defaultValue={10}
                                            >
                                                10
                                            </option>
                                            <option value={15}>15</option>
                                            <option value={20}>20</option>
                                            <option value={25}>25</option>
                                            <option value={30}>30</option>
                                        </select>
                                    </div>
                                </div>

                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <Modal theme={customModal} show={openModal} onClose={() => setOpenModal(false)}>
                <ModalHeader>
                    <div className="flex items-center gap-2">
                        <IoMdAdd />
                        <h4>
                            Import Audio File
                        </h4>
                    </div>
                </ModalHeader>
                <ModalBody>
                    <div className="flex w-full items-center justify-center">
                        <Label
                            htmlFor="dropzone-file"
                            className="flex h-64 w-full cursor-pointer flex-col items-center justify-center rounded-lg border-2 border-dashed border-gray-300 bg-gray-50 hover:bg-gray-100 dark:border-gray-600 dark:bg-gray-700 dark:hover:border-gray-500 dark:hover:bg-gray-600"
                        >
                            <div className="flex flex-col items-center justify-center pb-6 pt-5">
                                <svg
                                    className="mb-4 h-8 w-8 text-gray-500 dark:text-gray-400"
                                    aria-hidden="true"
                                    xmlns="http://www.w3.org/2000/svg"
                                    fill="none"
                                    viewBox="0 0 20 16"
                                >
                                    <path
                                        stroke="currentColor"
                                        strokeLinecap="round"
                                        strokeLinejoin="round"
                                        strokeWidth="2"
                                        d="M13 13h3a3 3 0 0 0 0-6h-.025A5.56 5.56 0 0 0 16 6.5 5.5 5.5 0 0 0 5.207 5.021C5.137 5.017 5.071 5 5 5a4 4 0 0 0 0 8h2.167M10 15V6m0 0L8 8m2-2 2 2"
                                    />
                                </svg>
                                <p className="mb-2 text-sm text-gray-500 dark:text-gray-400">
                                    <span className="font-semibold">Click to upload</span> or drag and drop
                                </p>
                                <p className="text-xs text-gray-500 dark:text-gray-400">SVG, PNG, JPG or GIF (MAX. 800x400px)</p>
                            </div>
                            <FileInput id="dropzone-file" className="hidden" />
                        </Label>
                    </div>
                    <div className="flex justify-end gap-5  pt-3">
                        <Button
                            theme={button_custom}
                            color="failure"
                            className="rounded"
                            size="xs"
                        >
                            <div className="flex items-center gap-0.5">
                                <IoCloseSharp className="text-xl" />
                                <h4 className="text-sm">Cancel</h4>
                            </div>
                        </Button>
                        <Button
                            theme={button_custom}
                            color="blue"
                            className="rounded"
                            size="xs"
                        >
                            <div className="flex items-center gap-0.5">
                                <IoMdAdd className="text-xl" />
                                <h4 className="text-sm">Add File</h4>
                            </div>
                        </Button>
                    </div>
                </ModalBody>
            </Modal>
        </Main>
    );
}

