import {
    But<PERSON>,
    Drawer,
    Dropdown
} from "flowbite-react";
import { useEffect, useRef, useState } from "react";

import {
    customDrawer,
    dropdownChats_custom
} from "@/Pages/Helpers/DesignHelper";
import { convertDateFormat, fetchJson, showAsset } from "@/Pages/Helpers/Helper";
import { Head } from "@inertiajs/react";
import { BiErrorAlt } from "react-icons/bi";
import {
    FaCheck
} from "react-icons/fa6";
import { ImSpinner8 } from "react-icons/im";
import { IoClose } from "react-icons/io5";
import { RiAccountBoxLine } from "react-icons/ri";
// import AndroMain from "./Main";;
import PeopleList from "./PeopleList";
import Profile from "./Profile";
import TextBox from "./TextBox";
import AndroMain from "../AndroMain";

function Show({ data = null, id = null }) {

    const [isOpenProfile, setIsOpenProfile] = useState(false);
    const handleCloseProfile = () => setIsOpenProfile(false);
    const [messageData, setMessageData] = useState([]);
    const [isLoading, setIsLoading] = useState(true);
    const divRef = useRef(null);
    // const [isAtTop, setIsAtTop] = useState(false);
    const [nextPageUrl, setNextPageUrl] = useState('');
    const [userId, setUserId] = useState();
    const fetchChat = () => {
        Promise.all([
            fetchJson(nextPageUrl, {}, true),
            fetchJson(route('whatsappB.chats.show', { chat: id }), {}, true),

        ]).then(([chatData]) => {
            setMessageData(chatData.data.data)
            setNextPageUrl(chatData.data.next_page_url);
            setIsLoading(false)
            divRef.current.scrollTop = divRef.current.scrollHeight;
            let divElement = document.getElementById('chatsBox');
            divElement.scrollTop = divElement.scrollHeight;
        });




    }
    useEffect(() => {
        fetchChat();


    }, []);

    // useEffect(() => {

    //     console.log(userId);

    // }, [userId]);

    const handleScrollToTop = () => {
        Promise.all([
            fetchJson(nextPageUrl, {}, true),
        ]).then(([chatData]) => {
            setMessageData(chatData.data.data)
            setNextPageUrl(chatData.data.next_page_url);
            setIsLoading(false)
            divRef.current.scrollTop = divRef.current.scrollHeight;
            let divElement = document.getElementById('chatsBox');
            divElement.scrollTop = divElement.scrollHeight;
        });

    };

    const renderChange = () => {
        fetchChat();
        // console.log('data');
    }

    // const userCatch = (userId) => {
    //     console.log(userId);
    // }

    return (
        <AndroMain>
            <Head title="Inbox" />
            {/* <TabBar /> */}
            <div className="relative p-2 overflow-hidden">
                <div className="grid grid-cols-12 gap-2 p-0 border-none grid-2 flow-row-dense md:grid-cols-12 sm:grid-cols-1 bg-slate-100">
                    <div className="hidden xl:col-span-3 lg:col-span-4 md:col-span-5 sm:col-span-3 lg:flex ">
                        <PeopleList chatFor={userCatch} />
                    </div>
                    <div className="relative pt-0 dark:text-gray-400 lg:p-0 xl:col-span-9 lg:col-span-8 md:col-span-12 col-span-full">
                        <div className="border rounded-lg border-s-0">
                            <div
                                className="bg-[#EEEEEE] rounded-t-lg w-full"
                            // onClick={() => setIsOpenProfile(true)}
                            >
                                <div className="flex items-start">
                                    <img
                                        className="p-2 h-14 w-14"
                                        src={showAsset(
                                            "/assets/img/dfprofile.png", ''
                                        )}
                                        alt=""
                                    />
                                    <div className="flex flex-col mt-1 text-start">
                                        <h5 className="font-medium text-blue-600">
                                            Simran Malhotra 91 8560123450
                                        </h5>
                                        <span className="text-sm text-gray-500">
                                            Online
                                        </span>
                                    </div>
                                </div>
                            </div>
                            <div className="bg-[url('/assets/img/bgwtsp.png')]">
                                <div className="p-4 space-y-4 h-[50vh] overflow-auto scroll-smooth" id="chatsBox" ref={divRef}>
                                    {/* Date Label */}
                                    <div className="text-sm text-center text-gray-700 ">
                                        <span className="px-2 py-1 rounded-md w-fit bg-sky-200">
                                            27/04/2024
                                        </span>
                                    </div>
                                    <div className="flex justify-center">
                                        {nextPageUrl ?
                                            <Button size="xs" className={``} onClick={handleScrollToTop}>Load More</Button>
                                            : 'End of chat'}
                                    </div>
                                    {messageData.length != 0 ?

                                        messageData.map((msg, k) => {
                                            return (
                                                <div className={`${(msg.fromMe == true) ? " justify-end " : " justify-start "}  flex `} key={"chatItem-" + k}>
                                                    <div className={`${(msg.fromMe == true) ? " justify-end " : " justify-start "} flex w-3/4 text-sm lg:w-2/4 md:w-3/4   `}>
                                                        <div className={`${(msg.fromMe == true) ? " bg-[#DCF8C6]  " : " bg-white "} p-2 bg-[#DCF8C6] rounded-lg shadow-md`}>
                                                            <p className="text-gray-800">
                                                                {msg.msg}
                                                            </p>
                                                            <span className="mt-1 text-xs text-gray-500">
                                                                <div className="flex items-center justify-end gap-1">

                                                                    {convertDateFormat(msg.messageDateTime)}
                                                                    {(msg.fromMe == true) ?
                                                                        <Dropdown
                                                                            theme={
                                                                                dropdownChats_custom
                                                                            }
                                                                            className=""
                                                                            arrowIcon={false}
                                                                            label={
                                                                                <BiErrorAlt className="text-sm text-red-600" />
                                                                            }
                                                                            // color={"white"}
                                                                            inline
                                                                        >
                                                                            <DropdownItem>
                                                                                <div className="flex items-center gap-1">
                                                                                    <FaCheck />
                                                                                    Approve
                                                                                </div>
                                                                            </DropdownItem>
                                                                            <DropdownItem>
                                                                                <div className="flex items-center gap-1.5">
                                                                                    <IoClose />
                                                                                    Reject
                                                                                </div>
                                                                            </DropdownItem>
                                                                        </Dropdown>
                                                                        : <></>}
                                                                </div>
                                                            </span>
                                                        </div>
                                                    </div>
                                                </div>
                                            )

                                        })

                                        : <>
                                            {
                                                isLoading
                                                    ?
                                                    <div className="flex justify-center">
                                                        <div className="text-center"                                        >

                                                            <ImSpinner8 className="w-12 h-12 m-auto text-blue-400 animate-spin" />
                                                            <span className="text-lg text-gray-600">loading...</span>
                                                        </div>
                                                    </div>
                                                    : <>
                                                        <div className="flex justify-center">
                                                            <div className="text-center"                                        >
                                                                <span className="text-xl text-gray-600">
                                                                    Start Communication
                                                                </span>
                                                            </div>
                                                        </div>
                                                    </>
                                            }
                                        </>
                                    }

                                </div>
                            </div>

                            <TextBox handleChange={renderChange} />
                        </div>
                    </div>
                </div>
            </div>
            {/* profile details */}
            {
                isOpenProfile &&
                <Drawer
                    theme={customDrawer}
                    open={isOpenProfile}
                    onClose={handleCloseProfile}
                    position="right"
                    className="w-full lg:w-5/6 md:w-4/5"
                >
                    <Drawer.Header
                        titleIcon={RiAccountBoxLine}
                        title="Profile Details"
                    />
                    <Drawer.Items>
                        <Profile></Profile>
                    </Drawer.Items>
                </Drawer>
            }

        </AndroMain>
    );
}

export default Show;
