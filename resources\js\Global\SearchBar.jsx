import React, { useState, useEffect, useRef } from 'react';
import { HiSearch, HiX } from 'react-icons/hi';

const SearchBar = ({
    url,
    setData,
    debounceDelay = 500,
    placeholder = "Search...",
    className = "",
    autoFocus = false,
    initialValue = ""
}) => {
    if (!url || !setData) return(<div className='text-red-600 text-sm'>url and setData are required</div>);
    const [searchTerm, setSearchTerm] = useState(initialValue);
    const [loading, setLoading] = useState(false);
    const inputRef = useRef(null);

    useEffect(() => {
        if (!url || !setData) return(<div>url and setData are required</div>);

        const handler = setTimeout(() => {
            fetchData(searchTerm.trim());
        }, debounceDelay);

        return () => clearTimeout(handler);
    }, [searchTerm]);

    const fetchData = async (query) => {
        try {
            setLoading(true);
            const fullUrl = url.includes('?')
                ? `${url}&search=${encodeURIComponent(query)}`
                : `${url}?search=${encodeURIComponent(query)}`;

            const res = await fetch(fullUrl, {
                headers: {
                    'Content-Type': 'application/json',
                    'Accept': 'application/json'
                }
            });
            const data = await res.json();
            setData(data);
        } catch (err) {
            console.error("SearchBar fetch error:", err);
        } finally {
            setLoading(false);
        }
    };

    const handleClear = () => {
        setSearchTerm("");
        fetchData("");
        inputRef.current?.focus();
    };

    return (
        <div className={`w-full ${className}`}>
            <div className="relative">
                {/* Search Icon */}
                <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                    <HiSearch className="text-gray-400 w-5 h-5" />
                </div>

                {/* Input */}
                <input
                    ref={inputRef}
                    type="text"
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    placeholder={placeholder}
                    autoFocus={autoFocus}
                    className="block w-full pl-10 pr-10 py-2 text-sm text-gray-900 bg-white border border-gray-300 rounded-lg focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-800 dark:text-white dark:border-gray-600 dark:placeholder-gray-400 dark:focus:ring-blue-500 dark:focus:border-blue-500"
                />

                {/* Clear Button */}
                {searchTerm && !loading && (
                    <button
                        type="button"
                        onClick={handleClear}
                        className="absolute inset-y-0 right-0 flex items-center pr-3 text-gray-400 hover:text-gray-600"
                    >
                        <HiX className="w-5 h-5" />
                    </button>
                )}

                {/* Inline Spinner */}
                {loading && (
                    <div className="absolute inset-y-0 right-0 flex items-center pr-3">
                        <div className="w-4 h-4 border-2 border-blue-500 border-t-transparent rounded-full animate-spin"></div>
                    </div>
                )}
            </div>

            {/* Bottom Loader Text */}
            {/* {loading && (
        <div className="mt-2 text-sm text-gray-500 flex items-center gap-2">
          <div className="w-4 h-4 border-2 border-blue-500 border-t-transparent rounded-full animate-spin"></div>
          Searching...
        </div>
      )} */}
        </div>
    );
};

export default SearchBar;
