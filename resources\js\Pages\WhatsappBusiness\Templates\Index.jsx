import Paginate from "@/Components/HelperComponents/Paginate";
import PerPageDropdown from "@/Components/HelperComponents/PerPageDropdown";
import TemplateDisplay from "@/Components/WhatsappBusinessHelpers/TemplateDisplay";
import useFetch from "@/Global/useFetch";
import {
    button_custom,
    customDrawer,
} from "@/Pages/Helpers/DesignHelper";
import { Head, Link, router } from "@inertiajs/react";
import { Button, Drawer, Dropdown, DropdownDivider, DropdownItem, Label, Modal, Select, Spinner, TextInput, Tooltip } from "flowbite-react";
import { useEffect, useState } from "react";
import { CgSpinner } from "react-icons/cg";
import { FaArrowsRotate } from "react-icons/fa6";
import { HiOutlineTemplate } from "react-icons/hi";
import { IoMdAdd } from "react-icons/io";
import WaMain from "../WaMain";
import Add from "./Add";
import Category from "./Category";
import Edit from "./Edit";
import { fetchJson } from "@/Pages/Helpers/Helper";

export default function Index({ collection, selectedCategoryName }) {

    const { categories, templates, can_template } = collection;
    const currentPageRoute = "whatsappB.templates.index";

    const [isOpenSyncModal, setIsOpenSyncModal] = useState({ open: false, loading: false });
    const [isOpen, setIsOpen] = useState(false);
    const [selectedGateway, setSelectedGateway] = useState(null);
    const [isEdit, setIsEdit] = useState(false);
    const [editId, setEditId] = useState();
    const [templateData, setTemplateData] = useState(templates.data);
    const [selectedCategory, setSelectedCategory] = useState(selectedCategoryName);
    const [warnings, setWarnings] = useState({ gateway: "" });
    const handleClose = () => setIsOpen(false);
    const [searchQuery, setSearchQuery] = useState();
    const [gatewaysData, setGatewaysData] = useState([]);
    const [gatewayLoading, setGatewayLoading] = useState();


    // template search
    const { data: searchData, loading: searchLoading } = useFetch(route(currentPageRoute, { search: searchQuery }), 5000);
    useEffect(() => {
        const updatedRoles = searchQuery && searchData?.collection?.templates?.data
            ? searchData.collection.templates.data
            : collection.templates.data;
        setTemplateData(updatedRoles);
    }, [searchQuery, searchData?.collection.templates.data, collection.templates]);


    const [nextPageUrlGateway, setNextPageUrlGateway] = useState('');
    const fetchGateway = (nextPage = null) => {
        setGatewayLoading(true);
        Promise.all([
            fetchJson(nextPage != null ? nextPage : "whatsappB.gateway.index", { perPage: 10 }, nextPage != null ? true : false),
        ]).then(([result]) => {
            setGatewaysData((prevRecords) => [...prevRecords, ...result.collection.gateways.data]);
            setNextPageUrlGateway(result.collection.gateways.next_page_url);
            setGatewayLoading(false)
        });
    }


    useEffect(() => {
        fetchGateway();
    }, []);

    // function to fetch all templates form meta 
    const fetchMetaTemplates = async () => {
        if (selectedGateway) {
            try {
                const response = await fetch(
                    route("whatsappB.templates.meta.all", { gateway: selectedGateway })
                );
                const result = await response.json();
                if (result.status) {
                    setIsOpenSyncModal({ open: false, loading: false });
                    router.get(route("whatsappB.templates.index"));
                } else {
                    console.error("Error fetching templates: ", result.message);
                }
            } catch (error) {
                console.error("Error fetching templates:", error);
            }
        } else {
            setWarnings({ gateway: "Please select a gateway" });
            setIsOpenSyncModal({ open: true, loading: false });
        }

    };


    // template approval status array
    const statuses = [
        { label: "All", value: undefined },
        { label: "Approved", value: "approved" },
        { label: "Pending", value: "pending" },
        { label: "Rejected", value: "rejected" }
    ];



    return (
        <WaMain>
            <Head title="Templates" />
            <>
                <div className="relative p-2 mt-1 overflow-hidden">
                    <div className="grid grid-flow-row-dense grid-cols-12 gap-2 p-0 border-none md:grid-cols-12 sm:grid-cols-1 bg-slate-100">
                        <div className="relative p-2 pt-0 dark:text-gray-400 lg:p-0 xl:col-span-2 lg:col-span-3 md:col-span-full col-span-full">
                            <div className="bg-white h-fit border rounded-lg mb-2 ">
                                <Dropdown label="Select Gateway" dismissOnClick={false} size="sm" color="gray" theme={{
                                    floating: {
                                        base: "z-10 w-full divide-y divide-gray-100 rounded shadow focus:outline-none",
                                        target: "w-full"
                                    }
                                }}>
                                    <div className="max-h-96 overflow-y-auto">
                                        {
                                            gatewaysData && gatewaysData.map((item) => {
                                                return <DropdownItem key={item.id} as={Link} href={route("whatsappB.templates.index", { gateway: item.id })} >{item.name}</DropdownItem>
                                            })
                                        }

                                        <DropdownDivider />


                                        {nextPageUrlGateway != null ?
                                            <div className="flex items-center justify-center my-4">
                                                <Button isProcessing={gatewayLoading} color='blue' size='sm' onClick={() => fetchGateway(nextPageUrlGateway)}>
                                                    Load more
                                                </Button>
                                            </div>
                                            : <p className='mt-3 text-xl text-center text-gray-500'>No more records.</p>
                                        }

                                    </div>
                                </Dropdown>
                            </div>
                            <div className="hidden bg-white h-fit border rounded-lg xl:col-span-2 lg:col-span-3 md:col-span-2 sm:col-span-3 lg:flex">
                                <Category
                                    selectedCategory={selectedCategory}
                                />
                            </div>
                        </div>
                        <div className="relative pt-0 dark:text-gray-400 lg:p-0 xl:col-span-10 lg:col-span-9 md:col-span-12 col-span-full">
                            <div className="flex flex-col gap-2">
                                <div className="flex items-center justify-between p-2 bg-white rounded-lg">
                                    <div className="flex items-center gap-2 ">
                                        <div className="flex items-center max-w-md gap-3">
                                            <TextInput sizing="sm" disabled={true} placeholder="Search..." onChange={(e) => setSearchQuery(e.target.value)} />
                                            {/* {searchLoading && <CgSpinner className="text-2xl text-blue-400 ease-in-out animate-spin" />} */}
                                        </div>

                                    </div>
                                    <div className="flex gap-2">
                                        <div>
                                            <Tooltip content="Sync">
                                                {/* <Button color="blue" size={"xs"} onClick={fetchMetaTemplates} ><FaArrowsRotate className="mt-0.5 me-1" /> <span>Sync with Meta</span> </Button> */}
                                                <Button color="blue" size={"xs"} onClick={() => setIsOpenSyncModal({ open: true, loading: false })} ><FaArrowsRotate className="mt-0.5 me-1" /> <span>Sync with Meta</span> </Button>
                                            </Tooltip>
                                        </div>
                                        {
                                            can_template && can_template.add &&
                                            <Button
                                                as={Link}
                                                size="xs"
                                                color="gray"
                                                theme={button_custom}
                                                href={route("whatsappB.templates.create")}
                                            >
                                                <div className="flex items-center gap-1 text-xs">
                                                    <IoMdAdd className="text-sm text-slate-500" />
                                                    <span>Add</span>
                                                </div>
                                            </Button>
                                        }
                                    </div>
                                </div>
                                <div className="flex px-2 pt-2 bg-white rounded-lg">
                                    {
                                        statuses.map(({ label, value }) => (
                                            <Link
                                                key={value ?? "all"}
                                                href={route(route().current(), { ...route().routeParams, ...(value ? { status: value } : {}) })}
                                                className={`text-dark px-4 py-1 ${route().params.status === value ? 'border-b-2 border-blue-500' : ''}`}
                                            >
                                                {label}
                                            </Link>
                                        ))
                                    }
                                </div>
                                {templateData &&
                                    <div className="h-full p-2 bg-white rounded-lg">
                                        <div className="grid grid-flow-row-dense grid-cols-1 gap-4 2xl:grid-cols-5 xl:grid-cols-4 lg:grid-cols-3 md:grid-cols-3 sm:grid-cols-2">
                                            <TemplateDisplay templateObject={templateData} can_template={can_template} />
                                        </div>
                                    </div>
                                }
                                {templateData?.length > 0 &&
                                    <div className="bottom-0 w-full p-3 bg-white rounded-lg float-end dark:border-gray-500 dark:rounded-lg dark:bg-gray-700">
                                        <div className="flex flex-wrap justify-between gap-2 lg:justify-between lg:gap-0 md:gap-0">
                                            <div className="flex items-center gap-4">
                                                <PerPageDropdown
                                                    routeName="whatsappB.templates.index"
                                                    data={collection}
                                                    customPerPage={10}
                                                />
                                            </div>
                                            <Paginate tableData={collection.templates} />
                                        </div>
                                    </div>
                                }

                            </div>
                        </div>
                    </div>
                </div>
                {isOpen && (
                    <Drawer
                        theme={customDrawer}
                        open={isOpen}
                        onClose={handleClose}
                        position="right"
                        className="w-full lg:w-5/6 md:w-4/5"
                    >
                        <Drawer.Header titleIcon={HiOutlineTemplate} title="Add Template" />
                        <Drawer.Items>
                            <Add
                                categories={categories}
                                onClose={() => setIsOpen(false)}
                                setTemplate={setTemplateData}
                                selectedCategory={selectedCategory}
                                setSelectedCategory={setSelectedCategory}
                            />
                        </Drawer.Items>
                    </Drawer>
                )
                }
                {
                    isEdit && (
                        <Drawer
                            theme={customDrawer}
                            open={isEdit}
                            onClose={() => setIsEdit(false)}
                            position="right"
                            className="w-full lg:w-5/6 md:w-4/5"
                        >
                            <Drawer.Header titleIcon={HiOutlineTemplate} title="Edit Template" />
                            <Drawer.Items>
                                <Edit
                                    id={editId}
                                    onClose={() => setIsEdit(false)}
                                    setTemplate={setTemplateData}
                                    setSelectedCategory={setSelectedCategory}
                                />
                            </Drawer.Items>
                        </Drawer>
                    )
                }

                {/* sync with meta modal */}
                {
                    isOpenSyncModal.open &&
                    <Modal show={isOpenSyncModal.open} size="sm" onClose={() => { setIsOpenSyncModal({ open: false, loading: false }); setSelectedGateway(null); setWarnings({ gateway: "" }) }}>
                        <Modal.Header className="p-3" >Sync with Meta</Modal.Header>
                        <Modal.Body className="p-3">
                            <div className="max-w-md">
                                <div className="mb-2">
                                    <Label htmlFor="countries" value="Select your Gateway to Sync" />
                                </div>

                                <Select id="countries" required onChange={(e) => setSelectedGateway(e.target.value)} color="gray">
                                    <option value="" >Select Gateway</option>
                                    {
                                        gatewaysData && gatewaysData.map((item, key) => {
                                            return <option key={key} value={item.id}>{item.name}</option>
                                        })
                                    }
                                </Select>
                                {warnings.gateway && <span className="text-red-500">{warnings.gateway}</span>}
                            </div>
                        </Modal.Body>
                        <Modal.Footer className="p-2 flex justify-end gap-2">
                            <Button color="gray" onClick={() => { setIsOpenSyncModal({ open: false, loading: false }); setSelectedGateway(null); setWarnings({ gateway: "" }) }} size="xs" >
                                Close
                            </Button>
                            <Button onClick={() => { setIsOpenSyncModal({ open: true, loading: true }); fetchMetaTemplates(); }} color="blue" size="xs" >
                                <div className="flex gap-2">
                                    {isOpenSyncModal.loading && <Spinner size="sm" className="animate-spin" />}
                                    Start Sync
                                </div>
                            </Button>
                        </Modal.Footer>
                    </Modal>
                }
            </>
        </WaMain>
    );
}


