import NoRecord from "@/Components/HelperComponents/NoRecord";
import { button_custom, table_custom } from "@/Pages/Helpers/DesignHelper";
import {
    changeFlag,
    convertDateFormat,
    deleteSingle,
    fileIcons,
    isDateGreaterThanToday,
    showAsset,
} from "@/Pages/Helpers/Helper";
import { Badge, Button, Modal, Table, Tooltip } from "flowbite-react";
import { useCallback, useEffect, useState } from "react";
import { Chart } from "react-google-charts";
import { FaWhatsapp } from "react-icons/fa6";
import { IoMdAdd, IoMdClose, IoMdQrScanner } from "react-icons/io";
import { IoPauseOutline } from "react-icons/io5";
import {
    MdDeleteOutline,
    MdOutlineEdit,
    MdOutlineIndeterminateCheckBox,
    MdOutlineRocketLaunch,
} from "react-icons/md";
import { RiFileSearchLine, RiFileUserLine } from "react-icons/ri";
import { TbFileTypeCsv, TbReport } from "react-icons/tb";
import AddChannel from "./AddChannel";
import QRCode from "./QrCode";
import { router } from "@inertiajs/react";

function Details({ campaign }) {
    const [campaignData, setCampaignData] = useState(null);
    const [isLoading, setIsLoading] = useState(true);
    const [showConfirmBox, setShowConfirmBox] = useState();
    useEffect(() => {
        campaignShowData();
    }, []);

    function campaignShowData() {
        fetch(route("whatsappB.campaign.show", { id: campaign }))
            .then((res) => {
                return res.json();
            })
            .then((data) => {
                setCampaignData(data.data);
                setIsLoading(false);
            });
    }

    const removeObjectAndDeleteRecord = (table, id, objKey) => {
        deleteSingle(table, id);
        let elementToRemove = document.querySelector("#" + objKey);
        elementToRemove.classList.add("hidden");
        campaignShowData();
    };
    const HandleChangeFlag = (Column, id, flag) => {
        let table = "wa_campaign";
        changeFlag(table, Column, id, flag);
        campaignShowData();
    };
    const selectInput_custom = {
        root: {
            base: "relative",
        },
        popup: {
            root: {
                base: "absolute top-10 z-50 block pt-2",
                inline: "relative top-0 z-auto",
                inner: "inline-block rounded-lg bg-white p-4 shadow-lg dark:bg-gray-700",
            },
            header: {
                base: "",
                title: "px-2 py-3 text-center font-semibold text-gray-900 dark:text-white",
                selectors: {
                    base: "mb-2 flex justify-between",
                    button: {
                        base: "rounded-lg bg-white px-5 py-2.5 text-sm font-semibold text-gray-900 hover:bg-gray-100 focus:outline-none focus:ring-2 focus:ring-gray-200 dark:bg-gray-700 dark:text-white dark:hover:bg-gray-600",
                        prev: "",
                        next: "",
                        view: "",
                    },
                },
            },
            view: {
                base: "p-1",
            },
            footer: {
                base: "mt-2 flex space-x-2",
                button: {
                    base: "w-full rounded-lg px-5 py-2 text-center text-sm font-medium focus:ring-4 focus:ring-cyan-300",
                    today: "bg-cyan-700 text-white hover:bg-cyan-800 dark:bg-cyan-600 dark:hover:bg-cyan-700",
                    clear: "border border-gray-300 bg-white text-gray-900 hover:bg-gray-100 dark:border-gray-600 dark:bg-gray-700 dark:text-white dark:hover:bg-gray-600",
                },
            },
        },
        views: {
            days: {
                header: {
                    base: "mb-1 grid grid-cols-7",
                    title: "h-6 text-center text-sm font-medium leading-6 text-gray-500 dark:text-gray-400",
                },
                items: {
                    base: "grid w-64 grid-cols-7",
                    item: {
                        base: "block flex-1 cursor-pointer rounded-lg border-0 text-center text-sm font-semibold leading-9 text-gray-900 hover:bg-gray-100 dark:text-white dark:hover:bg-gray-600",
                        selected: "bg-cyan-700 text-white hover:bg-cyan-600",
                        disabled: "text-gray-500",
                    },
                },
            },
            months: {
                items: {
                    base: "grid w-64 grid-cols-4",
                    item: {
                        base: "block flex-1 cursor-pointer rounded-lg border-0 text-center text-sm font-semibold leading-9 text-gray-900 hover:bg-gray-100 dark:text-white dark:hover:bg-gray-600",
                        selected: "bg-cyan-700 text-white hover:bg-cyan-600",
                        disabled: "text-gray-500",
                    },
                },
            },
            years: {
                items: {
                    base: "grid w-64 grid-cols-4",
                    item: {
                        base: "block flex-1 cursor-pointer rounded-lg border-0 text-center text-sm font-semibold leading-9 text-gray-900 hover:bg-gray-100 dark:text-white dark:hover:bg-gray-600",
                        selected: "bg-cyan-700 text-white hover:bg-cyan-600",
                        disabled: "text-gray-500",
                    },
                },
            },
            decades: {
                items: {
                    base: "grid w-64 grid-cols-4",
                    item: {
                        base: "block flex-1 cursor-pointer rounded-lg border-0 text-center text-sm font-semibold leading-9 text-gray-900 hover:bg-gray-100 dark:text-white dark:hover:bg-gray-600",
                        selected: "bg-cyan-700 text-white hover:bg-cyan-600",
                        disabled: "text-gray-500",
                    },
                },
            },
        },
    };
    const statusCodes = {
        0: { bg: "purple", text: "Draft" },
        1: { bg: "pink", text: "Started" },
        2: { bg: "info", text: "Paused" },
        3: { bg: "success", text: "Completed" },
        4: { bg: "failure", text: "Terminated" },
        6: { bg: "indigo", text: "Scheduled" },
    };
    const validImageExtensions = ["jpeg", "jpg", "webp", "gif"];
    function checkImage(fileName) {
        const extension = fileName.split(".").pop();
        return validImageExtensions.includes(extension);
    }
    const [chartData, setChartData] = useState();
    const [chartOptions, setChartOptions] = useState();
    const [openModal, setOpenModal] = useState(false);
    const [selectedGateway, setSelectedGateway] = useState();

    const updateChartData = () => {
        var pending =
            campaignData.campaign.totalContacts -
            campaignData.campaign.completedContacts;
        var complete = campaignData.campaign.completedContacts;
        var failed = campaignData.campaign.failed_count;
        var nonWapp = campaignData.campaign.non_whatsapp_count;

        setChartData([
            ["Status", "Complete", "Pending", "Failed", "Non Whatsapp"],
            ["data", complete, pending, failed, nonWapp],
        ]);
        setChartOptions({
            title:
                campaignData.campaign.completedContacts +
                " Out of " +
                campaignData.campaign.totalContacts +
                " Completed",
            chartArea: { width: "70%", height: "100px" },
            isStacked: true,
            hAxis: {
                title: "Total Contacts",
                minValue: 0,
            },
        });
    };
    useEffect(() => {
        if (campaignData && campaignData.hasOwnProperty("campaign")) {
            updateChartData();
        }
    }, [campaignData]);

    const addChannelToCampaign = () => {
        if (selectedGateway) {
            router.post(route("whatsappB.campaign.update.add.channel"), {
                data: { campaign: campaign, gateway: selectedGateway },
                onSuccess: (data) => {
                    campaignShowData(); //refresh data
                },
            });
            campaignShowData(); //refresh data
        } else {
            alert("Select Gateway.");
            return false;
        }
    };

    return (
        <>
            <div className="grid grid-flow-row-dense grid-cols-12 p-0 pt-2 border-none md:grid-cols-12 sm:grid-cols-1 lg:gap-2">
                <div className="relative flex flex-col gap-2 pt-0 dark:text-gray-400 lg:p-0 xl:col-span-8 lg:col-span-8 md:col-span-12 col-span-full">
                    {/* Status, Buttons */}
                    <div className="flex items-center justify-between gap-1">
                        <div className="flex items-center gap-1">
                            {campaignData &&
                                campaignData.hasOwnProperty("campaign") ? (
                                isDateGreaterThanToday(
                                    campaignData.campaign.startTime
                                ) ? (
                                    [0].includes(
                                        campaignData.campaign.status
                                    ) ? (
                                        <Badge
                                            className="cursor-default"
                                            color={statusCodes[6].bg}
                                        >
                                            {statusCodes[6].text}
                                        </Badge>
                                    ) : (
                                        <Badge
                                            className="cursor-default"
                                            color={
                                                statusCodes[
                                                    campaignData.campaign.status
                                                ].bg
                                            }
                                        >
                                            {
                                                statusCodes[
                                                    campaignData.campaign.status
                                                ].text
                                            }
                                        </Badge>
                                    )
                                ) : (
                                    <Badge
                                        className="cursor-default"
                                        color={
                                            statusCodes[
                                                campaignData.campaign.status
                                            ].bg
                                        }
                                    >
                                        {
                                            statusCodes[
                                                campaignData.campaign.status
                                            ].text
                                        }
                                    </Badge>
                                )
                            ) : (
                                <NoRecord loading={true} />
                            )}
                        </div>
                        {campaignData &&
                            campaignData.hasOwnProperty("campaign") ? (
                            <div className="relative flex items-center justify-between gap-3">
                                <div className="flex items-center gap-3 ">
                                    {campaignData.campaign.isReportReady ==
                                        1 ? (
                                        <Tooltip content="Download Report">
                                            <Button
                                                theme={button_custom}
                                                color="blue"
                                                size="xs"
                                            >
                                                <TbFileTypeCsv />
                                            </Button>
                                        </Tooltip>
                                    ) : (
                                        <></>
                                    )}
                                    {/* {campaignData.campaign.isReportReady} __* *__  {campaignData.campaign.status} */}
                                    <Tooltip content="Request Report">
                                        <Button
                                            onClick={() =>
                                                HandleChangeFlag(
                                                    "reportRequest",
                                                    campaignData.campaign.id,
                                                    1
                                                )
                                            }
                                            theme={button_custom}
                                            color="blue"
                                            size="xs"
                                            disabled={
                                                campaignData.campaign
                                                    .reportRequest == 1 ||
                                                campaignData.campaign.status !=
                                                3
                                            }
                                        >
                                            <TbReport className="text-sm" />
                                            <span className="text-xs ms-1">
                                                Generate Report
                                            </span>
                                        </Button>
                                    </Tooltip>

                                    <Button
                                        onClick={() =>
                                            HandleChangeFlag(
                                                "status",
                                                campaignData.campaign.id,
                                                2
                                            )
                                        }
                                        theme={button_custom}
                                        color="orange"
                                        size="xs"
                                        disabled={
                                            ![1].includes(
                                                campaignData.campaign.status
                                            )
                                        }
                                    >
                                        <IoPauseOutline className="text-sm" />
                                        <span className="text-xs ms-1">
                                            Pause
                                        </span>
                                    </Button>

                                    <Button
                                        onClick={() =>
                                            HandleChangeFlag(
                                                "status",
                                                campaignData.campaign.id,
                                                1
                                            )
                                        }
                                        theme={button_custom}
                                        color="success"
                                        size="xs"
                                        disabled={
                                            ![0, 2].includes(
                                                campaignData.campaign.status
                                            )
                                        }
                                    >
                                        {/* <IoPlayOutline className="text-sm" /> */}
                                        <div className="flex items-center gap-1">
                                            <MdOutlineRocketLaunch className="" />
                                            <span className="text-xs">
                                                Start
                                            </span>
                                        </div>
                                    </Button>

                                    <Button
                                        onClick={() =>
                                            HandleChangeFlag(
                                                "status",
                                                campaignData.campaign.id,
                                                4
                                            )
                                        }
                                        theme={button_custom}
                                        color="failure"
                                        size="xs"
                                        disabled={
                                            ![1, 2].includes(
                                                campaignData.campaign.status
                                            )
                                        }
                                    >
                                        <MdOutlineIndeterminateCheckBox className="text-sm" />
                                        <span className="text-xs ms-1">
                                            Stop
                                        </span>
                                    </Button>
                                </div>
                            </div>
                        ) : (
                            <NoRecord loading={true} />
                        )}
                    </div>

                    {/* Template Name, Category */}
                    <div className="bg-white rounded-lg p-3 flex flex-col gap-1.5">
                        {/* <Progress color="blue" progress={45} /> */}
                        {/* <div id="example3.1" style={{ height: "200px" }}></div> */}
                        {campaignData &&
                            campaignData.hasOwnProperty("campaign") ? (
                            <div>
                                <Chart
                                    chartType="BarChart"
                                    width="100%"
                                    height="150px"
                                    data={chartData}
                                    options={chartOptions}
                                />
                            </div>
                        ) : (
                            <NoRecord loading={true} />
                        )}

                        <div className="mt-1 overflow-x-auto border rounded-lg">
                            {campaignData &&
                                campaignData.hasOwnProperty("campaign") ? (
                                <Table hoverable theme={table_custom}>
                                    <TableBody className="divide-y">
                                        <TableRow className="bg-white dark:border-gray-700 dark:bg-gray-800 ">
                                            <TableCell className="font-medium text-gray-900 whitespace-nowrap dark:text-white">
                                                Add Date Time in Msg Footer
                                            </TableCell>
                                            <TableCell className="text-end">
                                                {campaignData.campaign
                                                    .msgFooterDateTime
                                                    ? "Yes"
                                                    : "No"}
                                            </TableCell>
                                        </TableRow>
                                        <TableRow className="bg-white dark:border-gray-700 dark:bg-gray-800 ">
                                            <TableCell className="font-medium text-gray-900 whitespace-nowrap dark:text-white">
                                                View once
                                            </TableCell>
                                            <TableCell className="text-end">
                                                {campaignData.campaign.viewOnce
                                                    ? "Yes"
                                                    : "No"}
                                            </TableCell>
                                        </TableRow>
                                        <TableRow className="bg-white dark:border-gray-700 dark:bg-gray-800">
                                            <TableCell className="font-medium text-gray-900 whitespace-nowrap dark:text-white">
                                                Sleep After
                                            </TableCell>
                                            <TableCell className="text-end">
                                                {
                                                    campaignData.campaign
                                                        .sleepAfterMsgs
                                                }
                                            </TableCell>
                                        </TableRow>
                                        <TableRow className="bg-white dark:border-gray-700 dark:bg-gray-800">
                                            <TableCell className="font-medium text-gray-900 whitespace-nowrap dark:text-white">
                                                Sleep For
                                            </TableCell>
                                            <TableCell className="text-end">
                                                {
                                                    campaignData.campaign
                                                        .sleepForSeconds
                                                }
                                            </TableCell>
                                        </TableRow>
                                        <TableRow className="bg-white dark:border-gray-700 dark:bg-gray-800">
                                            <TableCell className="font-medium text-gray-900 whitespace-nowrap dark:text-white">
                                                Created By
                                            </TableCell>
                                            <TableCell className="text-end">
                                                {
                                                    campaignData.campaign.user
                                                        .username
                                                }
                                            </TableCell>
                                        </TableRow>
                                        <TableRow className="bg-white dark:border-gray-700 dark:bg-gray-800">
                                            <TableCell className="font-medium text-gray-900 whitespace-nowrap dark:text-white">
                                                Start Time
                                            </TableCell>
                                            <TableCell className="text-end">
                                                {convertDateFormat(
                                                    campaignData.campaign
                                                        .startTime
                                                )}
                                            </TableCell>
                                        </TableRow>
                                    </TableBody>
                                </Table>
                            ) : (
                                <NoRecord loading={true} />
                            )}
                        </div>
                    </div>
                    <div className="p-3 px-4 mt-2 text-sm bg-white rounded-lg">
                        <div className="flex items-center w-full gap-2">
                            <div className="w-full p-2 border rounded-md">
                                <div className="flex items-center justify-between w-full">
                                    <span>
                                        Add Date & Time in Message Footer
                                    </span>
                                    <span className="text-gray-500">Yes</span>
                                </div>
                            </div>
                            <div className="w-full p-2 border rounded-md">
                                <div className="flex items-center justify-between w-full">
                                    <span>Message View Only Once</span>
                                    <span className="text-gray-500">No</span>
                                </div>
                            </div>
                        </div>
                        <div className="flex items-center gap-2 mt-2">
                            <div className="w-full p-2 border rounded-md">
                                <div className="flex items-center justify-between w-full">
                                    <span>Start Time</span>
                                    <span className="text-gray-500">
                                        24/10/24 | 02:45
                                    </span>
                                </div>
                            </div>
                            <div className="w-full p-2 border rounded-md">
                                <div className="flex items-center justify-between w-full">
                                    <span>Stop Time</span>
                                    <span className="text-gray-500">
                                        27/10/24 | 02:45
                                    </span>
                                </div>
                            </div>
                        </div>
                        <div className="flex items-center gap-1.5 w-full border p-2 mt-2">
                            <span>Sleep After </span>
                            <span className="text-gray-500">20</span>
                            <span>Messages for </span>
                            <span className="text-gray-500">30</span>
                            <span>Seconds. </span>
                        </div>
                    </div>
                    {/* Gateway */}
                    <div className="bg-white rounded-lg p-2 px-3 flex flex-col gap-1.5">
                        <div className="flex items-center justify-between gap-2">
                            <div className="flex items-center gap-2">
                                <FaWhatsapp className="text-slate-400" />
                                <span>Gateway</span>
                            </div>
                        </div>
                        <div className="overflow-x-auto border rounded-lg">
                            <Table hoverable theme={table_custom}>
                                <TableBody className="border-b divide-y">
                                    {campaignData &&
                                        campaignData.hasOwnProperty("campaign") ? (
                                        campaignData.campaign.channels.map(
                                            (channel, k) => {
                                                return (
                                                    <TableRow
                                                        className="bg-white dark:border-gray-700 dark:bg-gray-800"
                                                        id={
                                                            "campaign-channel-2" +
                                                            k
                                                        }
                                                        key={k}
                                                    >
                                                        <TableCell className="font-medium text-gray-900 whitespace-nowrap dark:text-white">
                                                            {
                                                                channel.channel
                                                                    .name
                                                            }
                                                        </TableCell>
                                                        {campaignData.campaign
                                                            .status != 4 ? (
                                                            <TableCell>
                                                                <div className="flex items-center justify-end gap-2">
                                                                    <Button
                                                                        theme={
                                                                            button_custom
                                                                        }
                                                                        color="gray"
                                                                        size="xs"
                                                                        onClick={() =>
                                                                            setOpenModal(
                                                                                true
                                                                            )
                                                                        }
                                                                    >
                                                                        <IoMdQrScanner className="text-sm" />
                                                                        <span className="text-xs ms-1">
                                                                            Login
                                                                        </span>
                                                                    </Button>

                                                                    <div className="flex">
                                                                        {showConfirmBox ===
                                                                            "campaign-channel-confirmBox" +
                                                                            k ? (
                                                                            <div className="flex gap-3">
                                                                                <Button
                                                                                    theme={
                                                                                        button_custom
                                                                                    }
                                                                                    color="gray"
                                                                                    size="xs"
                                                                                    onClick={() =>
                                                                                        removeObjectAndDeleteRecord(
                                                                                            "wa_campaign_channel",
                                                                                            channel.id,
                                                                                            "campaign-channel-2" +
                                                                                            k
                                                                                        )
                                                                                    }
                                                                                >
                                                                                    <span className="text-xs text-red-600 ms-1">
                                                                                        Confirm
                                                                                    </span>
                                                                                </Button>
                                                                                <Button
                                                                                    theme={
                                                                                        button_custom
                                                                                    }
                                                                                    color="gray"
                                                                                    size="xs"
                                                                                    onClick={() =>
                                                                                        setShowConfirmBox(
                                                                                            ""
                                                                                        )
                                                                                    }
                                                                                >
                                                                                    <span className="text-xs text-blue-600 ms-1">
                                                                                        Cancel
                                                                                    </span>
                                                                                </Button>
                                                                            </div>
                                                                        ) : (
                                                                            <Button
                                                                                theme={
                                                                                    button_custom
                                                                                }
                                                                                color="gray"
                                                                                size="xs"
                                                                                onClick={() =>
                                                                                    setShowConfirmBox(
                                                                                        "campaign-channel-confirmBox" +
                                                                                        k
                                                                                    )
                                                                                }
                                                                            >
                                                                                <IoMdClose className="text-sm" />
                                                                                <span className="text-xs ms-1">
                                                                                    Remove
                                                                                </span>
                                                                            </Button>
                                                                        )}
                                                                    </div>
                                                                </div>
                                                            </TableCell>
                                                        ) : (
                                                            <></>
                                                        )}
                                                    </TableRow>
                                                );
                                            }
                                        )
                                    ) : (
                                        <TableRow className="bg-white dark:border-gray-700 dark:bg-gray-800">
                                            <TableCell colSpan={2}>
                                                <NoRecord loading={isLoading} />
                                            </TableCell>
                                        </TableRow>
                                    )}
                                </TableBody>
                            </Table>
                            {campaignData &&
                                campaignData.hasOwnProperty("remainingGateways") ? (
                                campaignData.remainingGateways.length > 0 &&
                                    ![4, 3].includes(
                                        campaignData.campaign.status
                                    ) ? (
                                    <div className="flex items-center gap-2 p-2">
                                        <div className="max-w-md">
                                            <select
                                                theme={selectInput_custom}
                                                className="focus:ring-blue-600 focus:border-blue-600 rounded-md text-sm p-1.5 border-gray-300"
                                                id="countries"
                                                required
                                                onChange={(e) =>
                                                    setSelectedGateway(
                                                        e.target.value
                                                    )
                                                }
                                            >
                                                <option value="">
                                                    Select Gateway
                                                </option>
                                                {campaignData.remainingGateways
                                                    .length != 0 ? (
                                                    campaignData.remainingGateways.map(
                                                        (ch, k) => (
                                                            <option
                                                                value={ch.id}
                                                            >
                                                                {ch.name}
                                                            </option>
                                                        )
                                                    )
                                                ) : (
                                                    <option value="">
                                                        No record
                                                    </option>
                                                )}
                                            </select>
                                        </div>
                                        <Button
                                            theme={button_custom}
                                            color="blue"
                                            size="xs"
                                            onClick={() =>
                                                addChannelToCampaign()
                                            }
                                        >
                                            <IoMdAdd />
                                        </Button>
                                    </div>
                                ) : (
                                    <></>
                                )
                            ) : (
                                <NoRecord loading={isLoading} />
                            )}
                        </div>
                    </div>

                    {/* Contact List */}
                    <div className="bg-white rounded-lg p-2 px-3 flex flex-col gap-1.5 mb-2">
                        <div className="flex items-center gap-2">
                            <RiFileUserLine className="text-slate-400" />
                            <span>Contact List</span>
                        </div>
                        <div className="overflow-x-auto border rounded-lg">
                            <Table hoverable theme={table_custom}>
                                <TableBody className="divide-y">
                                    {campaignData &&
                                        campaignData.hasOwnProperty("campaign") ? (
                                        campaignData.campaign.contact_lists.map(
                                            (cList, k) => {
                                                return (
                                                    <TableRow className="bg-white dark:border-gray-700 dark:bg-gray-800">
                                                        <TableCell className="font-medium text-gray-900 whitespace-nowrap dark:text-white">
                                                            {
                                                                cList
                                                                    .contact_list
                                                                    .name
                                                            }
                                                        </TableCell>
                                                    </TableRow>
                                                );
                                            }
                                        )
                                    ) : (
                                        <TableRow>
                                            <TableCell>
                                                <NoRecord loading={isLoading} />
                                            </TableCell>
                                        </TableRow>
                                    )}
                                </TableBody>
                            </Table>
                        </div>
                    </div>

                    {/* Gateway Add List */}
                    {/* <div className="bg-red-100 rounded-lg p-2 px-3 flex flex-col gap-1.5">
                        <div className="flex items-center gap-2">
                            <FaWhatsapp className="text-slate-400" />
                            <span>Gateway</span>
                            <span className="flex items-center gap-1 text-sm text-red-600">
                                <BiErrorAlt /> Add gateway for gateway details.
                            </span>
                        </div>
                        <div className="flex items-center gap-2">
                            <Button
                                className="m-1 pe-1"
                                size="xs"
                                // color="success"
                                theme={button_custom}
                            >
                                <div className="flex items-center gap-1">
                                    <IoMdAdd className="" />
                                    <span className="text-sm">Add Gateway</span>
                                </div>
                            </Button>
                        </div>
                    </div> */}
                </div>
                <div className="col-span-12 mb-2 xl:col-span-4 lg:col-span-4 md:col-span-12 sm:col-span-12 lg:flex">
                    <div className="w-full p-5 bg-white rounded-lg">
                        <div>
                            <div className=" rounded-b-md max-h-[55vh] overflow-auto">
                                <div className="flex flex-col items-end gap-3 pb-2 rounded-b-lg">
                                    {campaignData &&
                                        campaignData.hasOwnProperty("campaign") ? (
                                        <div className="flex flex-col gap-1 p-2 m-2 bg-white rounded-lg">
                                            {campaignData &&
                                                campaignData.hasOwnProperty(
                                                    "attachments"
                                                ) ? (
                                                campaignData.attachments.map(
                                                    (att, k) => {
                                                        return (
                                                            <div
                                                                key={
                                                                    "campaign-image" +
                                                                    k
                                                                }
                                                            >
                                                                {/* {checkImage(
                                                                    att.file
                                                                ) ? (
                                                                    <div className="">
                                                                        <a
                                                                            href={showAsset(
                                                                                "/storage/" +
                                                                                    att.file
                                                                            )}
                                                                            download={
                                                                                att.name
                                                                            }
                                                                        >
                                                                            <img
                                                                                className="rounded-lg"
                                                                                src={showAsset(
                                                                                    "/storage/" +
                                                                                        att.file
                                                                                )}
                                                                                alt=""
                                                                            />
                                                                        </a>
                                                                    </div>
                                                                ) : (
                                                                    <a
                                                                        href={showAsset(
                                                                            "/storage/" +
                                                                                att.file
                                                                        )}
                                                                        download={
                                                                            att.name
                                                                        }
                                                                    >
                                                                        <div className="flex w-full gap-2 p-2 text-center text-gray-300 bg-gray-100 rounded-lg">
                                                                            <div className="text-3xl text-gray-400">
                                                                                {att.file
                                                                                    .split(
                                                                                        "."
                                                                                    )
                                                                                    .pop() in
                                                                                fileIcons
                                                                                    ? fileIcons[
                                                                                          att.file
                                                                                              .split(
                                                                                                  "."
                                                                                              )
                                                                                              .pop()
                                                                                      ]
                                                                                    : att.file
                                                                                          .split(
                                                                                              "."
                                                                                          )
                                                                                          .pop()}
                                                                            </div>
                                                                            <div className="text-gray-700">
                                                                                {
                                                                                    att.name
                                                                                }

                                                                                .
                                                                                {att.file
                                                                                    .split(
                                                                                        "."
                                                                                    )
                                                                                    .pop()}
                                                                            </div>
                                                                        </div>
                                                                    </a>
                                                                )} */}

                                                                <Card
                                                                    theme={card_custom}
                                                                    className="relative w-full bg-slate-300"
                                                                >
                                                                    <div className="absolute -left-0 top-6 w-0 h-0 border-t-[1px] border-t-transparent border-b-[12px] border-b-transparent border-r-[18px] border-r-white"></div>
                                                                    <div>
                                                                        <div
                                                                            className="p-2 overflow-auto"
                                                                            style={{
                                                                                height: "250px",
                                                                            }}
                                                                        >
                                                                            <div className="bg-white rounded-lg mx-2.5 py-0.5">
                                                                                <>
                                                                                    <div>
                                                                                        {/* <img
                                                                                                                                                                                           src={showAsset(
                                                                                                                                                                                               "/storage/"
                                                                                                                                                                                           )}
                                                                                                                                                                                           className="w-full my-2 rounded-md max-h-36 min-w-fit"
                                                                                                                                                                                       /> */}
                                                                                    </div>
                                                                                </>
                                                                                <div
                                                                                    className=" text-center text-gray-300"
                                                                                    style={{
                                                                                        fontSize:
                                                                                            "7rem",
                                                                                    }}
                                                                                ></div>
                                                                                <div className="flex gap-2 p-2 rounded-lg">
                                                                                    <div className="text-3xl text-gray-400"></div>
                                                                                    <div className="text-gray-700 text-sm">
                                                                                        Body: Hi
                                                                                        [Customer’s
                                                                                        First
                                                                                        Name],
                                                                                        Welcome
                                                                                        to
                                                                                        RapBooster—we’re
                                                                                        thrilled
                                                                                        to have
                                                                                        you with
                                                                                        us!
                                                                                        We’re
                                                                                        here to
                                                                                        help you
                                                                                        WhatsApp
                                                                                        Marketing,
                                                                                        SMS
                                                                                        Marketing,
                                                                                        and
                                                                                        boosting
                                                                                        your
                                                                                        overall
                                                                                        productivity.
                                                                                    </div>
                                                                                </div>

                                                                                <></>
                                                                                <div className="flex items-center gap-1 px-2">

                                                                                    <span className="text-sm text-blue-600">
                                                                                        <CgAttachment />
                                                                                    </span>
                                                                                    <span className="text-sm text-blue-600">
                                                                                        2
                                                                                    </span>
                                                                                </div>
                                                                            </div>
                                                                        </div>
                                                                        <div className="bg-slate-100 p-1">
                                                                            <div className="flex items-start justify-between gap-2 rounded-b-lg">
                                                                                {/* <div>
                                                                                                                                <div className="text-sm">
                                                                                                                                    {
                                                                                                                                        template.name
                                                                                                                                    }
                                                                                                                                </div>
                                                                                                                                <div className="text-xs">
                                                                                                                                    Gateway 1
                                                                                                                                </div>
                                                                                                                            </div> */}
                                                                                <div className="truncate">
                                                                                    <div className="text-sm">
                                                                                        Welcome Message
                                                                                    </div>
                                                                                    <div className="text-xs text-slate-500">
                                                                                        Gateway
                                                                                        1
                                                                                    </div>
                                                                                </div>

                                                                                <div className="flex items-center">
                                                                                    <Button
                                                                                        theme={
                                                                                            button_custom
                                                                                        }
                                                                                        color="withoutBorder"
                                                                                        size="xs"
                                                                                        onClick={() => setIsView(true)}
                                                                                    >
                                                                                        <RiFileSearchLine className="text-slate-400" />
                                                                                    </Button>

                                                                                    <Button
                                                                                        theme={
                                                                                            button_custom
                                                                                        }
                                                                                        color="withoutBorder"
                                                                                        size="xs"
                                                                                    // onClick={() => {
                                                                                    //     setIsEdit(
                                                                                    //         true
                                                                                    //     );
                                                                                    //     setEditId(
                                                                                    //         template.id
                                                                                    //     );
                                                                                    // }}
                                                                                    >
                                                                                        <MdOutlineEdit className="text-slate-400" />
                                                                                    </Button>
                                                                                    <Button
                                                                                        theme={
                                                                                            button_custom
                                                                                        }
                                                                                        color="withoutBorder"
                                                                                        size="xs"
                                                                                    // onClick={() =>
                                                                                    //     window.confirm(
                                                                                    //         "Do you really want to delete Template ?"
                                                                                    //     ) &&
                                                                                    //     removeObjectAndDeleteRecord(
                                                                                    //         "wa_template",
                                                                                    //         template.id
                                                                                    //     )
                                                                                    // }
                                                                                    >
                                                                                        <MdDeleteOutline className="text-slate-400" />
                                                                                    </Button>
                                                                                </div>
                                                                            </div>
                                                                        </div>
                                                                    </div>
                                                                </Card>
                                                            </div>
                                                        );
                                                    }
                                                )
                                            ) : (
                                                <></>
                                            )}
                                            {campaignData.campaign.msg ? (
                                                <div className="w-full p-1">
                                                    <div className="">
                                                        <p>
                                                            {
                                                                campaignData
                                                                    .campaign
                                                                    .msg
                                                            }
                                                        </p>
                                                    </div>
                                                    <div className="self-end mt-2 text-xs text-end text-slate-500">
                                                        {convertDateFormat(
                                                            new Date(),
                                                            "time"
                                                        )}
                                                    </div>
                                                </div>
                                            ) : (
                                                <></>
                                            )}
                                        </div>
                                    ) : (
                                        <NoRecord loading={true} />
                                    )}
                                </div>
                            </div>

                            {/* footer button */}
                            {/* <div>
                                <div className="mt-2">
                                    <Button
                                        className="w-full"
                                        // outline
                                        // theme={button_custom}
                                        color="blue"
                                        size="xs"
                                    >
                                        <div className="flex items-center gap-1">
                                            <RiCheckDoubleFill className="text-lg" />
                                            Save
                                        </div>
                                    </Button>
                                </div>
                            </div> */}
                        </div>
                    </div>
                </div>
            </div>
            {openModal ? (
                <Modal show={openModal} onClose={() => setOpenModal(false)}>
                    <ModalHeader>Login</ModalHeader>
                    <ModalBody>
                        <QRCode />
                    </ModalBody>
                </Modal>
            ) : (
                ""
            )}
        </>
    );
}

export default Details;

