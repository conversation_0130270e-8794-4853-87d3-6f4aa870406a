import React from "react";
import { GrCurrency } from "react-icons/gr";
import { <PERSON><PERSON>, Checkbox, Drawer, Table } from "flowbite-react";
import { CiExport } from "react-icons/ci";
import { useState } from "react";
import { Tooltip } from "flowbite-react";
import { CgMail } from "react-icons/cg";
import { IoArrowDownOutline } from "react-icons/io5";
import { FiEye } from "react-icons/fi";

export default function SalarySlip() {
    const [isCheckAll, setIsCheckAll] = useState(false);
    // ----------------Edit --------------------
    const [isOpen, setIsOpen] = useState(false);
    const handleClose = () => setIsOpen(false);
    const table_custom = {
        root: {
            base: "w-full text-left text-sm text-gray-500 dark:text-gray-400 shadow-lg",
            shadow: "absolute left-0 top-0 -z-10 h-full w-full rounded-lg bg-white drop-shadow-md dark:bg-black",
            wrapper: "relative",
        },
        head: {
            base: "group/head text-gray-700 dark:text-gray-400 ",
            cell: {
                base: "text-slate-900 dark:text-slate-300 font-medium border dark:border-slate-500 bg-slate-50 text-nowrap px-3 py-2 group-first/head:first:rounded-tl-lg group-first/head:last:rounded-tr-lg dark:bg-gray-700",
            },
        },
        body: {
            base: "group/body",
            cell: {
                base: "border-b px-3 py-2.5 text-gray-800 group-first/body:group-first/row:first:rounded-tl-lg group-first/body:group-first/row:last:rounded-tr-lg group-last/body:group-last/row:first:rounded-bl-lg group-last/body:group-last/row:last:rounded-br-lg",
            },
        },
    };

    const button_custom = {
        color: {
            orange: "border border-transparent bg-orange-500 text-white focus:ring-4 focus:ring-orange-300 enabled:hover:bg-orange-500 dark:bg-orange-500 dark:hover:bg-orange-500 dark:focus:ring-orange-300",
        },
        pill: {
            off: "rounded",
            on: "rounded-full",
        },
        size: {
            xs: "p-0.5 text-sm",
            sm: "px-3 py-1.5 text-sm",
            md: "px-4 py-2 text-sm",
            lg: "px-5 py-2.5 text-base",
            xl: "px-6 py-3 text-base",
        },
    };

    const customTheme = {
        root: {
            position: {
                right: {
                    on: "right-0 top-0 h-screen w-[70vw] transform-none bg-slate-100",
                    off: "right-0 top-0 h-screen w-80 translate-x-full",
                },
            },
        },
        header: {
            inner: {
                closeButton:
                    "absolute end-2.5 top-2.5 flex h-8 w-8 items-center justify-center rounded-lg bg-transparent text-sm text-gray-400 hover:bg-gray-200 hover:text-gray-900 dark:hover:bg-gray-600 dark:hover:text-white",
                closeIcon: "h-4 w-4",
                titleIcon: "me-2.5 h-6 w-6 text-gray-400",
                titleText:
                    "mb-4 inline-flex items-center text-lg font-semibold text-sky-950 dark:text-gray-400",
            },
        },
    };
    return (
        <>
            <div className="px-4 pb-2 rounded-lg">
                <div className="flex items-center gap-2 p-2 text-xl">
                    <GrCurrency className="text-slate-400" />
                    <span className="font-medium ">Salary Slip</span>
                </div>
                <div className="w-full py-1 overflow-auto bg-white border rounded h-fit">
                    <Button
                        theme={button_custom}
                        size="xs"
                        color="gray"
                        className="mt-1 mb-2 ms-3"
                    >
                        <div className="flex items-center gap-1">
                            <CiExport className="text-slate-500" />
                            <span className="text-xs">Export</span>
                        </div>
                    </Button>
                    <div className="bg-white h-[73vh] overflow-auto">
                        {/* documents table */}
                        <div className="overflow-x-auto bg-white border rounded-lg">
                            <Table hoverable theme={table_custom}>
                                <TableHead className=" bg-slate-100">
                                    <TableHeadCell>
                                        <Checkbox
                                            checked={isCheckAll}
                                            onChange={() =>
                                                setIsCheckAll(!isCheckAll)
                                            }
                                        />
                                    </TableHeadCell>

                                    <TableHeadCell>
                                        <h3>Month</h3>
                                    </TableHeadCell>

                                    <TableHeadCell>
                                        <div className="flex items-center justify-between">
                                            <h3>Year</h3>
                                            <svg
                                                xmlns="http://www.w3.org/2000/svg"
                                                height="16px"
                                                viewBox="0 0 24 24"
                                                width="16px"
                                                className="fill-gray-600"
                                            >
                                                <path
                                                    d="M0 0h24v24H0V0z"
                                                    fill="none"
                                                />
                                                <path d="M12 5.83L15.17 9l1.41-1.41L12 3 7.41 7.59 8.83 9 12 5.83zm0 12.34L8.83 15l-1.41 1.41L12 21l4.59-4.59L15.17 15 12 18.17z" />
                                            </svg>
                                        </div>
                                    </TableHeadCell>

                                    <TableHeadCell>
                                        <h3>Working Days</h3>
                                    </TableHeadCell>

                                    <TableHeadCell>
                                        <h3>Leave Taken</h3>
                                    </TableHeadCell>

                                    <TableHeadCell></TableHeadCell>
                                </TableHead>

                                <TableBody className="divide-y">
                                    <TableRow className="bg-white dark:border-gray-700 dark:bg-gray-800">
                                        <TableCell>
                                            <Checkbox className="rowCheckBox" />
                                        </TableCell>

                                        <TableCell className="font-medium text-gray-900 whitespace-nowrap dark:text-white">
                                            August
                                        </TableCell>
                                        <TableCell>2024</TableCell>
                                        <TableCell>24</TableCell>
                                        <TableCell>2</TableCell>
                                        <TableCell>
                                            <div className="flex justify-between gap-2">
                                                <div className="flex gap-2">
                                                    <Tooltip
                                                        content="Documents"
                                                        className="p-1 px-2 bg-slate-700"
                                                    >
                                                        <Button
                                                            theme={
                                                                button_custom
                                                            }
                                                            color="failure"
                                                            size="xs"
                                                        >
                                                            <CgMail />
                                                        </Button>
                                                    </Tooltip>
                                                    <Tooltip
                                                        content="Edit"
                                                        className="p-1 px-2 bg-slate-700"
                                                    >
                                                        <Button
                                                            theme={
                                                                button_custom
                                                            }
                                                            color="orange"
                                                            size="xs"
                                                        >
                                                            <FiEye />
                                                        </Button>
                                                    </Tooltip>
                                                    <Tooltip
                                                        content="Delete"
                                                        className="p-1 px-2 bg-slate-700"
                                                    >
                                                        <Button
                                                            theme={
                                                                button_custom
                                                            }
                                                            color="blue"
                                                            size="xs"
                                                        >
                                                            <IoArrowDownOutline />
                                                        </Button>
                                                    </Tooltip>
                                                </div>
                                            </div>
                                        </TableCell>
                                    </TableRow>
                                    <TableRow className="bg-white dark:border-gray-700 dark:bg-gray-800">
                                        <TableCell>
                                            <Checkbox className="rowCheckBox" />
                                        </TableCell>

                                        <TableCell className="font-medium text-gray-900 whitespace-nowrap dark:text-white">
                                            July
                                        </TableCell>
                                        <TableCell>2024</TableCell>
                                        <TableCell>26</TableCell>
                                        <TableCell>0</TableCell>
                                        <TableCell>
                                            <div className="flex justify-between gap-2">
                                                <div className="flex gap-2">
                                                    <Tooltip
                                                        content="Documents"
                                                        className="p-1 px-2 bg-slate-700"
                                                    >
                                                        <Button
                                                            theme={
                                                                button_custom
                                                            }
                                                            color="failure"
                                                            size="xs"
                                                        >
                                                            <CgMail />
                                                        </Button>
                                                    </Tooltip>
                                                    <Tooltip
                                                        content="Edit"
                                                        className="p-1 px-2 bg-slate-700"
                                                    >
                                                        <Button
                                                            theme={
                                                                button_custom
                                                            }
                                                            color="orange"
                                                            size="xs"
                                                        >
                                                            <FiEye />
                                                        </Button>
                                                    </Tooltip>
                                                    <Tooltip
                                                        content="Delete"
                                                        className="p-1 px-2 bg-slate-700"
                                                    >
                                                        <Button
                                                            theme={
                                                                button_custom
                                                            }
                                                            color="blue"
                                                            size="xs"
                                                        >
                                                            <IoArrowDownOutline />
                                                        </Button>
                                                    </Tooltip>
                                                </div>
                                            </div>
                                        </TableCell>
                                    </TableRow>
                                    <TableRow className="bg-white dark:border-gray-700 dark:bg-gray-800">
                                        <TableCell>
                                            <Checkbox className="rowCheckBox" />
                                        </TableCell>

                                        <TableCell className="font-medium text-gray-900 whitespace-nowrap dark:text-white">
                                            July
                                        </TableCell>
                                        <TableCell>2024</TableCell>
                                        <TableCell>26</TableCell>
                                        <TableCell>0</TableCell>
                                        <TableCell>
                                            <div className="flex justify-between gap-2">
                                                <div className="flex gap-2">
                                                    <Tooltip
                                                        content="Documents"
                                                        className="p-1 px-2 bg-slate-700"
                                                    >
                                                        <Button
                                                            className="pb-1 text-white bg-red-600"
                                                            theme={
                                                                button_custom
                                                            }
                                                            color="failure"
                                                            size="xs"
                                                        >
                                                            <CgMail />
                                                        </Button>
                                                    </Tooltip>
                                                    <Tooltip
                                                        content="Edit"
                                                        className="p-1 px-2 bg-slate-700"
                                                    >
                                                        <Button
                                                            theme={
                                                                button_custom
                                                            }
                                                            color="orange"
                                                            size="xs"
                                                            onClick={() =>
                                                                setIsOpen(true)
                                                            }
                                                        >
                                                            <FiEye />
                                                        </Button>
                                                    </Tooltip>
                                                    <Tooltip
                                                        content="Delete"
                                                        className="p-1 px-2 bg-slate-700"
                                                    >
                                                        <Button
                                                            theme={
                                                                button_custom
                                                            }
                                                            color="blue"
                                                            size="xs"
                                                        >
                                                            <IoArrowDownOutline />
                                                        </Button>
                                                    </Tooltip>
                                                </div>
                                            </div>
                                        </TableCell>
                                    </TableRow>
                                </TableBody>
                            </Table>
                        </div>
                    </div>

                    <div className="w-full p-3 bg-white border-t ">
                        <div className="flex flex-wrap justify-between gap-2 lg:gap-0 md:gap-0">
                            <div className="flex items-center gap-3">
                                <div className="text-sm text-gray-400 ">
                                    Showing 1 to 25 of 62 entries
                                </div>
                                <div>
                                    <select
                                        id="countries"
                                        className="block p-1 text-xs text-gray-900 border border-gray-300 rounded bg-gray-50 focus:ring-blue-500 focus:border-blue-500 ps-2 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-blue-500 dark:focus:border-blue-500"
                                    >
                                        <option value={10} defaultValue={10}>
                                            10
                                        </option>
                                        <option value={15}>15</option>
                                        <option value={20}>20</option>
                                        <option value={25}>25</option>
                                        <option value={30}>30</option>
                                    </select>
                                </div>
                            </div>
                            <div className="flex">
                                <Button
                                    size="xs"
                                    color="gray"
                                    className="px-2 text-xs text-gray-400 rounded border-e-0 "
                                >
                                    Previous
                                </Button>
                                <Button
                                    size="xs"
                                    color="blue"
                                    className="border text-white border-blue-600 bg-blue-600 px-2.5 rounded-none text-sm "
                                >
                                    1
                                </Button>
                                <Button
                                    size="xs"
                                    color="gray"
                                    className="border text-blue-600 px-2.5 rounded-none text-xs "
                                >
                                    2
                                </Button>
                                <Button
                                    size="xs"
                                    color="gray"
                                    className="px-2 text-xs text-blue-600 rounded border-s-0 "
                                >
                                    Next
                                </Button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <Drawer
                theme={customTheme}
                open={isOpen}
                onClose={handleClose}
                position="right"
                className="w-full xl:w-3/5 lg:w-4/5 md:w-4/5"
            >
                <DrawerHeader title="Salary Slip" titleIcon={GrCurrency} />
                <DrawerItems>
                    <div className="bg-white rounded">
                        <div className="flex items-start justify-between p-2 px-4">
                            <div className="h-36 w-36">
                                <img src="assets/img/df.png" alt="" />
                            </div>
                            <div className="text-sm font-medium text-right">
                                <h6>Opp. Bajaj RE Motors, Road No. 5,</h6>
                                <h6>Bikaner-334001</h6>
                                <h6>+91-9680440011</h6>
                                <h6><EMAIL></h6>
                                <h6>www.dunesfactory.com</h6>
                            </div>
                        </div>
                        {/* Table */}
                        <div className="overflow-x-auto">
                            <Table hoverable>
                                <TableHead className="text-sm text-white bg-blue-500 dark:bg-gray-700 dark:text-gray-400">
                                    <TableHeadCell>Salary Slip</TableHeadCell>
                                    <TableHeadCell></TableHeadCell>
                                    <TableHeadCell></TableHeadCell>
                                    <TableHeadCell>Month</TableHeadCell>
                                    <TableHeadCell>August-2024</TableHeadCell>
                                </TableHead>
                                <TableBody className="divide-y">
                                    <TableRow className="bg-white dark:border-gray-700 dark:bg-gray-800">
                                        <TableCell className="font-medium text-gray-900 whitespace-nowrap dark:text-white">
                                            Employee Name
                                        </TableCell>
                                        <TableCell>Gaurav Kachhawa</TableCell>
                                        <TableCell className="font-medium text-gray-900">
                                            Total Working Days
                                        </TableCell>
                                        <TableCell>26</TableCell>
                                        <TableCell></TableCell>
                                    </TableRow>
                                    {/* Add more TableRow components for other rows */}
                                </TableBody>
                            </Table>
                            <Table hoverable>
                                <TableHead className="text-sm text-white bg-blue-500 dark:bg-gray-700 dark:text-gray-400">
                                    <TableHeadCell></TableHeadCell>
                                    <TableHeadCell></TableHeadCell>
                                    <TableHeadCell></TableHeadCell>
                                    <TableHeadCell>Deductions</TableHeadCell>
                                    <TableHeadCell></TableHeadCell>
                                </TableHead>
                                <TableHead className="text-sm text-white bg-slate-400 dark:bg-gray-700 dark:text-gray-400">
                                    <TableHeadCell>Particulars</TableHeadCell>
                                    <TableHeadCell></TableHeadCell>
                                    <TableHeadCell>Particulars</TableHeadCell>
                                    <TableHeadCell>Amount</TableHeadCell>
                                    <TableHeadCell></TableHeadCell>
                                </TableHead>
                                <TableBody className="divide-y">
                                    <TableRow className="bg-white dark:border-gray-700 dark:bg-gray-800">
                                        <TableCell className="font-medium text-gray-900 whitespace-nowrap dark:text-white">
                                            Basic Salary
                                        </TableCell>
                                        <TableCell>₹ 15,000/</TableCell>
                                        <TableCell className="font-medium text-gray-900">
                                            Leave
                                        </TableCell>
                                        <TableCell>₹ 0/</TableCell>
                                        <TableCell></TableCell>
                                    </TableRow>
                                    {/* Add more TableRow components for other rows */}
                                </TableBody>
                            </Table>
                            <div className="p-3 text-sm text-center">
                                This is a computer generated document, no need
                                for a signature.
                            </div>
                        </div>
                        <div className="p-2 py-4 float-end">
                            <Button
                                size="xs"
                                theme={button_custom}
                                color="blue"
                            >
                                <div className="flex items-center gap-1">
                                    <IoArrowDownOutline />
                                    Download
                                </div>
                            </Button>
                        </div>
                    </div>
                </DrawerItems>
            </Drawer>
        </>
    );
}



