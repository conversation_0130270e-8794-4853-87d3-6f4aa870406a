import { Badge, Button, Checkbox, Drawer, Dropdown, DropdownItem, Select, Table, ToggleSwitch } from "flowbite-react";
import { useState } from "react";

import ConfirmBox from "@/Components/HelperComponents/ConfirmBox";
import Paginate from "@/Components/HelperComponents/Paginate";
import PerPageDropdown from "@/Components/HelperComponents/PerPageDropdown";
import SortLink from "@/Components/SortLink";
import { button_custom, customDrawerEdit, table_custom } from "@/Pages/Helpers/DesignHelper";
import { changeFlag } from "@/Pages/Helpers/Helper";
import { Link, router } from "@inertiajs/react";
import { IoMdAdd } from "react-icons/io";
import { MdDeleteOutline, MdOutlineEdit } from "react-icons/md";
import { RiDeleteBin6Line } from "react-icons/ri";
import MailMain from "../MailMain";
import Add from "./Add";
import Edit from "./Edit";
import { IoFilter } from "react-icons/io5";


export default function Index({ collection }) {
    const { gateways, getData } = collection;
    // console.log(getData);

    // ----------------Edit --------------------
    const currentPageRoute = "voice.gateways.index";
    const [isAddOpen, setIsAddOpen] = useState(false);
    const [isEditOpen, setIsEditOpen] = useState(false);
    const [selected, setSelected] = useState();
    const [selectedGateways, setSelectedGateways] = useState([]);
    const [isMultipleDeleteConfirmOpen, SetIsMultipleDeleteConfirmOpen] = useState(false);

    const status = {
        0: { text: 'pending', color: 'warning', value: 0 },
        1: { text: 'Active', value: 1, color: 'success' },
        2: { text: 'close', color: 'failure', value: 2 }
    }
    const handleCheckboxChange = (id) => {
        setSelectedGateways((prevSelected) =>
            prevSelected.includes(id)
                ? prevSelected.filter((gwId) => gwId !== id) // Deselect if already selected
                : [...prevSelected, id] // Add if not selected
        );
    };
    // Handle "Select All" Checkbox
    const handleSelectAll = (e) => {
        if (e.target.checked) {
            if (selectedGateways.length === gateways.length) {
                setSelectedGateways([]);
            } else {
                setSelectedGateways(gateways?.data?.map((gateway) => gateway.id));
            }
        } else {
            setSelectedGateways([]);
        }
    };

    function deleteRecords() {
        if (selectedGateways.length > 0) {
            router.delete(route("voice.gateways.destroy", { gateway: selectedGateways.toLocaleString() }));
        }
    }
    const handleMultipleDelete = (res) => {
        if (res) {
            deleteRecords(); // Your function to delete records (ensure it handles the selection).
        }
        // Reset checkboxes and state
        setSelectedGateways([]); // Clear selected checkboxes
        // setIsCheckAll(false); // Reset the "Select All" checkbox
        SetIsMultipleDeleteConfirmOpen(false); // Close the confirmation dialog
    };

    return (
        <MailMain>
            <>
                <div className="pb-2 rounded-lg">
                    <div className="h-fit bg-white rounded  overflow-auto w-full my-2.5">
                        <div className="flex justify-between p-2">
                            {/* <Button.Group className=""> */}
                            <Button
                                className=""
                                size="xs"
                                color="gray"
                                theme={button_custom}
                                onClick={() => SetIsMultipleDeleteConfirmOpen(!isMultipleDeleteConfirmOpen)}
                            >
                                <div className="flex items-center gap-1">
                                    <MdDeleteOutline className="text-slate-500" />
                                    <span className="text-xs">Delete</span>
                                </div>
                            </Button>


                            {/* </Button.Group>  */}
                            <div className="flex justify-between gap-2 ">
                              
                                <Dropdown dismissOnClick={false} size="xs" arrowIcon={true} color="gray"
                                    label={"Status"}
                                >
                                    <DropdownItem as={Link} href={route(currentPageRoute)}>
                                        All
                                    </DropdownItem>
                                    {Object.values(status).map((item) => (
                                        <DropdownItem key={item.value} as={Link} href={route(currentPageRoute, { status: item.value })}>
                                            <Badge className="capitalize" color={item.color}>
                                                {item.text}
                                            </Badge>
                                        </DropdownItem>
                                    ))}
                                </Dropdown>

                                <Button
                                    className="pe-1"
                                    size="xs"
                                    color="gray"
                                    theme={button_custom}
                                    onClick={() => setIsAddOpen(true)}
                                >
                                    <div className="flex items-center gap-1">
                                        <IoMdAdd className="text-xs text-slate-500" />
                                        <span className="text-xs">Add</span>
                                    </div>
                                </Button>

                            </div>
                        </div>
                        <div className="h-full">
                            <div className="overflow-x-auto bg-white border rounded-lg text-nowrap">
                                <Table hoverable theme={table_custom}>
                                    <Table.Head>
                                        <Table.HeadCell>
                                            <Checkbox
                                                color="blue"
                                                onChange={handleSelectAll}
                                                checked={selectedGateways.length == gateways?.data?.length}
                                            />
                                        </Table.HeadCell>
                                        <Table.HeadCell>
                                            <SortLink showName={'Name'} routeName={currentPageRoute} column={'name'} params={getData} sortType={getData?.sort == "asc" ? "desc" : "asc"} />
                                        </Table.HeadCell>
                                        <Table.HeadCell>

                                            <SortLink showName={'SMTP Host'} routeName={currentPageRoute} column={'host'} params={getData} sortType={getData?.sort == "asc" ? "desc" : "asc"} />
                                        </Table.HeadCell>
                                        <Table.HeadCell>

                                            <SortLink showName={'SMTP Username'} routeName={currentPageRoute} column={'username'} params={getData} sortType={getData?.sort == "asc" ? "desc" : "asc"} />


                                        </Table.HeadCell>
                                        <Table.HeadCell>
                                            <SortLink showName={'Email From'} routeName={currentPageRoute} column={'email_from'} params={getData} sortType={getData?.sort == "asc" ? "desc" : "asc"} />

                                        </Table.HeadCell>
                                        <Table.HeadCell>
                                            <SortLink showName={'Port'} routeName={currentPageRoute} column={'port'} params={getData} sortType={getData?.sort == "asc" ? "desc" : "asc"} />


                                        </Table.HeadCell>
                                        <Table.HeadCell>
                                            <SortLink showName={'Authentication'} routeName={currentPageRoute} column={'auth_type'} params={getData} sortType={getData?.sort == "asc" ? "desc" : "asc"} />
                                        </Table.HeadCell>
                                        <Table.HeadCell>
                                            <SortLink showName={'Limit'} routeName={currentPageRoute} column={'limit_per_minute'} params={getData} sortType={getData?.sort == "asc" ? "desc" : "asc"} />
                                        </Table.HeadCell>
                                        <Table.HeadCell>

                                            Status


                                        </Table.HeadCell>
                                        <Table.HeadCell>
                                            <SortLink showName={'Active'} routeName={currentPageRoute} column={'isActive'} params={getData} sortType={getData?.sort == "asc" ? "desc" : "asc"} />
                                        </Table.HeadCell>
                                        <Table.HeadCell>Action</Table.HeadCell>
                                    </Table.Head>
                                    <Table.Body className="divide-y">
                                        {gateways?.data?.map((gateway) =>
                                            <Table.Row key={'gateway-' + gateway.id}>
                                                <Table.Cell>
                                                    <Checkbox
                                                        color={"blue"}
                                                        className="rowCheckBox"
                                                        checked={selectedGateways.includes(gateway.id)}
                                                        onChange={() => handleCheckboxChange(gateway.id)}
                                                    />
                                                </Table.Cell>
                                                <Table.Cell className="font-medium text-gray-900 whitespace-nowrap dark:text-white">
                                                    {gateway.name ?? '-'}
                                                </Table.Cell>
                                                <Table.Cell className="font-medium text-gray-900 whitespace-nowrap dark:text-white">
                                                    {gateway.host ?? '-'}
                                                </Table.Cell>
                                                <Table.Cell>{gateway.username ?? '-'}</Table.Cell>
                                                <Table.Cell>
                                                    {gateway.email_from ?? '-'}

                                                </Table.Cell>
                                                <Table.Cell>
                                                    {gateway.port ?? '-'}
                                                </Table.Cell>
                                                <Table.Cell>
                                                    {gateway.auth_type ?? '-'}
                                                </Table.Cell>
                                                <Table.Cell> {gateway.limit_per_minute ?? '-'} / Minute</Table.Cell>
                                                <Table.Cell> <div className="w-fit">
                                                    <Badge
                                                        color={status[gateway.status]?.color ?? 'warning'}
                                                        className="capitalize"
                                                    >
                                                        {status[gateway.status]?.text ?? 'warning'}
                                                    </Badge>
                                                </div>
                                                </Table.Cell>
                                                <Table.Cell>
                                                    <ToggleSwitch sizing="sm" color="cyan" checked={gateway.isActive} onChange={() => changeFlag('mail_gateway', 'isActive', gateway.id, !gateway.isActive)} />
                                                </Table.Cell>

                                                <Table.Cell>
                                                    <div className="flex gap-2">
                                                        <Button
                                                            theme={button_custom}
                                                            color="success"
                                                            size="xs"
                                                            onClick={() => { setIsEditOpen(true); setSelected(gateway) }}
                                                        >
                                                            <MdOutlineEdit className="text-sm" />
                                                            <span className="text-xs ms-1">
                                                                Edit
                                                            </span>
                                                        </Button>
                                                        <Button
                                                            theme={button_custom}
                                                            color="failure"
                                                            size="xs"
                                                            onClick={() => {
                                                                SetIsMultipleDeleteConfirmOpen(!isMultipleDeleteConfirmOpen);
                                                                setSelectedGateways([gateway.id]);
                                                            }}
                                                        >
                                                            <RiDeleteBin6Line className="text-sm" />
                                                            <span className="text-xs ms-1">
                                                                Delete
                                                            </span>
                                                        </Button>
                                                    </div>
                                                </Table.Cell>
                                            </Table.Row>
                                        )}
                                    </Table.Body>
                                </Table>
                            </div>
                        </div>
                    </div>
                    <div className="bottom-0 w-full p-3 bg-white rounded-lg float-end dark:border-gray-500 dark:rounded-lg dark:bg-gray-700">
                        <div className="flex flex-wrap justify-between gap-2 lg:justify-between lg:gap-0 md:gap-0">
                            <div className="flex items-center gap-4">
                                <PerPageDropdown
                                    getDataFields={getData ?? null}
                                    routeName={"voice.gateways.index"}
                                    data={gateways}
                                    idName={"name"}
                                // id={target}
                                />
                            </div>

                            <Paginate tableData={gateways} />
                        </div>
                    </div>
                </div>

                {isAddOpen &&
                    <Drawer open={isAddOpen} onClose={() => setIsAddOpen(!isAddOpen)} position="right" className="w-1/2" theme={customDrawerEdit}>
                        <Drawer.Header title="Add Email Server Configuration" />
                        <Drawer.Items>
                            <div className="flex items-center justify-between px-4 py-2 mb-3 bg-white rounded-lg">
                                <Add onClose={() => setIsAddOpen(!isAddOpen)} />
                            </div>
                        </Drawer.Items>
                    </Drawer>
                }
                {isEditOpen &&
                    <Drawer open={isEditOpen} onClose={() => setIsEditOpen(!isEditOpen)} position="right" className="w-1/2" theme={customDrawerEdit}>
                        <Drawer.Header title="Edit Email Server Configuration" />
                        <Drawer.Items>
                            <div className="flex items-center justify-between px-4 py-2 mb-3 bg-white rounded-lg">
                                <Edit onClose={() => setIsEditOpen(!isEditOpen)} gateway={selected} />
                            </div>
                        </Drawer.Items>
                    </Drawer>
                }
                {isMultipleDeleteConfirmOpen &&
                    <ConfirmBox
                        isOpen={isMultipleDeleteConfirmOpen}
                        onClose={() => SetIsMultipleDeleteConfirmOpen(false)} // Close the confirm box
                        onAction={handleMultipleDelete} // Handle the user's choice
                        title="Delete Gateway "
                        message="Do you want to Delete gateway."
                        confirmText="Yes, Delete!"
                        cancelText="No, Keep It"
                        confirmColor="orange"
                        cancelColor="gray"

                    />
                }
            </>
        </MailMain >
    );
}


