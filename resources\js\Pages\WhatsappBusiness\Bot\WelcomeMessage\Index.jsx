import { FilePreview } from "@/Components/FilePreview";
import ConfirmBox from "@/Components/HelperComponents/ConfirmBox";
import NoRecord from "@/Components/HelperComponents/NoRecord";
import Paginate from "@/Components/HelperComponents/Paginate";
import PerPageDropdown from "@/Components/HelperComponents/PerPageDropdown";
import { Head, Link, useForm } from "@inertiajs/react";
import {
    Badge,
    Button,
    Checkbox,
    Drawer,
    DrawerHeader,
    DrawerItems,
    Table,
    TableBody,
    TableCell,
    TableHead,
    TableHeadCell,
    TableRow,
    Tooltip
} from "flowbite-react";
import $ from "jquery";
import { useState } from "react";
import { FaRegTrashCan } from "react-icons/fa6";
import { IoIosArrowBack, IoMdAdd, IoMdRepeat } from "react-icons/io";
import {
    MdDeleteOutline,
    MdOutlineModeEdit
} from "react-icons/md";
import { RiDeleteBin6Line } from "react-icons/ri";
import {
    button_custom,
    customDrawerEdit,
    table_custom
} from "../../../Helpers/DesignHelper";
import WaMain from "../../WaMain";
import AddWelcome from "./AddWelcome";
import EditWelcome from "./EditWelcome";

export default function Index({ collection }) {

    const { messages, getData, bot } = collection;
    const [isCheckAll, setIsCheckAll] = useState(false);
    const [checkValue, setCheckValue] = useState([]);

    const [enableEditWelcomeMessageDrawer, setEnableWelcomeMessageDrawer] = useState(false);
    const [editWelcomeMsgData, setEditWelcomeMsgData] = useState(null);
    const [enableAddWelcome, setEnableAddWelcome] = useState(false);

    const {
        data,
        setData,
        delete: destroy,
        processing,
        errors,
    } = useForm({
        id: [],
    });

    const [isConfirmOpen, setConfirmOpen] = useState(false);

    function getCheckedIds(e) {
        let previousIds = checkValue;
        if (e.target.checked) {
            if (!previousIds.includes(e.target.id)) {
                previousIds.push(e.target.id);
                setCheckValue(previousIds);
            }
        } else {
            const newIds = previousIds.filter((item) => item !== e.target.id);
            setCheckValue(newIds);
        }
    }

    // executes when user click table header checkbox
    function headerCheckBoxChecked(e) {
        let previousIds = [];
        if (
            e.target.checked &&
            e.target.id == 0 &&
            collection.messages.data.length > 0
        ) {
            collection.messages.data.map((message, key) => {
                if (!previousIds.includes(message.id)) {
                    previousIds.push(message.id);
                    setCheckValue(previousIds);
                }
            });
            setIsCheckAll(true);
            $(".rowCheckBox").prop("checked", true);
        } else {
            setCheckValue(previousIds);
            setIsCheckAll(false);
            $(".rowCheckBox").prop("checked", false);
        }
    }

    // handle checkBox Check or uncheck
    function checkAddcheckBoxChecked() {
        let allCheckBoxes = $(".rowCheckBox");
        let checkedCheckBoxes = $(".rowCheckBox:checked");

        if (allCheckBoxes.length == checkedCheckBoxes.length) {
            setIsCheckAll(true);
        } else {
            setIsCheckAll(false);
        }
    }



    function handleConfirmBoxResult(result) {
        if (checkValue.length > 0 && result) {
            setData({ id: checkValue });
            setConfirmOpen(false);
            destroy(route("whatsappB.bot.welcome.destroy", { bot: bot.id, welcome: checkValue }));
            setCheckValue([]);
            setIsCheckAll(false);
        } else {
            setCheckValue([]);
            setIsCheckAll(false);
        }
        setConfirmOpen(false);
    }

    return (
        <WaMain>
            <Head title="Bots" />
            {/* <TabBar /> */}
            <div className="px-2 pb-2 rounded-lg">
                <div className="h-fit bg-white rounded border overflow-auto w-full mt-2.5">
                    <div className="flex justify-between p-2">
                        <div className="flex items-center gap-2">
                            <Button
                                className="pe-1.5"
                                size="xs"
                                color="pink"
                                theme={button_custom}
                                onClick={() => window.history.back()}
                            >
                                <div className="flex items-center gap-1">
                                    <IoIosArrowBack />
                                    <span className="text-xs">Back</span>
                                </div>
                            </Button>
                            <div>
                                {
                                    collection.can_delete &&
                                    <Button
                                        className=""
                                        size="xs"
                                        color="gray"
                                        theme={button_custom}
                                        onClick={() =>
                                            setConfirmOpen(true)
                                        }
                                        isProcessing={processing}
                                    >
                                        <div className="flex items-center gap-1">
                                            <MdDeleteOutline className="text-slate-500" />
                                            <span className="text-xs">Delete</span>
                                        </div>
                                    </Button>
                                }
                            </div>
                            {/* <Button
                                className=""
                                theme={button_custom}
                                size="xs"
                                color="gray"
                                id="dropdownInformationButton"
                                data-dropdown-toggle="dropdownNotification"
                                type="button"
                            >
                                <div className="flex items-center gap-1">
                                    <TbColumns3 className="text-xs text-slate-500" />
                                    <span className="text-xs">Columns</span>
                                </div>
                            </Button> */}
                            <div className="">
                                Bot Name :
                            </div>
                            <Badge className="capitalize">
                                {bot.name}
                            </Badge>
                        </div>
                        <div className="flex">
                            {
                                collection.can_add &&
                                <Button
                                    className="border pe-1"
                                    size="xs"
                                    color="gray"
                                    theme={button_custom}
                                    onClick={() => setEnableAddWelcome(true)}
                                >
                                    <div className="flex items-center gap-1">
                                        <IoMdAdd className="text-slate-500" />
                                        <span className="text-xs">Add</span>
                                    </div>
                                </Button>
                            }


                        </div>
                    </div>
                    <div className="overflow-auto bg-white h-ull">
                        {/* documents table */}
                        <div className="overflow-x-auto border rounded-lg">
                            <Table hoverable theme={table_custom}>
                                <TableHead className=" bg-slate-100">
                                    <TableHeadCell className="w-10">
                                        <Checkbox
                                            color="blue"
                                            checked={isCheckAll}
                                            id={0}
                                            onChange={(e) =>
                                                headerCheckBoxChecked(e)
                                            }
                                        />
                                    </TableHeadCell>

                                    <TableHeadCell>
                                        <Link
                                            href={route(
                                                "whatsappB.bot.welcome.index",
                                                {
                                                    column: "name",
                                                    sort:
                                                        collection.getData
                                                            .sort == "asc"
                                                            ? "desc"
                                                            : "asc",
                                                    bot: bot.id
                                                }
                                            )}
                                        >
                                            <div className="flex items-center justify-between">
                                                <span>Message</span>
                                                <svg
                                                    xmlns="http://www.w3.org/2000/svg"
                                                    height="16px"
                                                    viewBox="0 0 24 24"
                                                    width="16px"
                                                    className="fill-gray-600"
                                                >
                                                    <path
                                                        d="M0 0h24v24H0V0z"
                                                        fill="none"
                                                    />
                                                    <path d="M12 5.83L15.17 9l1.41-1.41L12 3 7.41 7.59 8.83 9 12 5.83zm0 12.34L8.83 15l-1.41 1.41L12 21l4.59-4.59L15.17 15 12 18.17z" />
                                                </svg>
                                            </div>
                                        </Link>
                                    </TableHeadCell>

                                    <TableHeadCell>
                                        <Link
                                            href={route(
                                                "whatsappB.bot.welcome.index",
                                                {
                                                    column: "user_id",
                                                    sort:
                                                        collection.getData
                                                            .sort == "asc"
                                                            ? "desc"
                                                            : "asc",
                                                    bot: bot.id
                                                }
                                            )}
                                        >
                                            <div className="flex items-center justify-between gap-1">
                                                <span>File</span>
                                                <svg
                                                    xmlns="http://www.w3.org/2000/svg"
                                                    height="16px"
                                                    viewBox="0 0 24 24"
                                                    width="16px"
                                                    className="fill-gray-600"
                                                >
                                                    <path
                                                        d="M0 0h24v24H0V0z"
                                                        fill="none"
                                                    />
                                                    <path d="M12 5.83L15.17 9l1.41-1.41L12 3 7.41 7.59 8.83 9 12 5.83zm0 12.34L8.83 15l-1.41 1.41L12 21l4.59-4.59L15.17 15 12 18.17z" />
                                                </svg>
                                            </div>
                                        </Link>
                                    </TableHeadCell>
                                    <TableHeadCell>Actions</TableHeadCell>
                                </TableHead>

                                <TableBody className="divide-y">
                                    {collection.messages.data.length > 0
                                        ?
                                        (
                                            collection.messages.data.map(
                                                (message, key) => {
                                                    return (
                                                        <TableRow
                                                            className="bg-white dark:border-gray-700 dark:bg-gray-800"
                                                            key={key}
                                                        >
                                                            <TableCell>
                                                                <Checkbox
                                                                    color={"blue"}
                                                                    className="rowCheckBox"
                                                                    id={message.id}
                                                                    onChange={(
                                                                        e
                                                                    ) => {
                                                                        getCheckedIds(
                                                                            e
                                                                        );
                                                                        checkAddcheckBoxChecked();
                                                                    }}
                                                                />
                                                            </TableCell>

                                                            <TableCell
                                                                className="text-center cursor-default "
                                                            >
                                                                {message.body}
                                                            </TableCell>

                                                            <TableCell>
                                                                {message.file &&
                                                                    <FilePreview object={message.file} imageSize="lg" width=" w-fit" />
                                                                }
                                                            </TableCell>
                                                            <TableCell>
                                                                <div className="flex items-center gap-3 pt-1">
                                                                    {
                                                                        collection.can_edit &&
                                                                        <Tooltip content="Edit">
                                                                            <Button
                                                                                theme={
                                                                                    button_custom
                                                                                }
                                                                                color="success"
                                                                                size="xs"

                                                                                onClick={() => {
                                                                                    setEnableWelcomeMessageDrawer(
                                                                                        true
                                                                                    );
                                                                                    setEditWelcomeMsgData(message);
                                                                                }}
                                                                            >
                                                                                <MdOutlineModeEdit className="text-sm" />
                                                                                <span className="text-xs ms-1">
                                                                                    Edit
                                                                                </span>
                                                                            </Button>
                                                                        </Tooltip>
                                                                    }
                                                                    {
                                                                        collection.can_delete &&
                                                                        <Tooltip content="Delete">
                                                                            <Button
                                                                                theme={
                                                                                    button_custom
                                                                                }
                                                                                color="failure"
                                                                                size="xs"
                                                                                onClick={() => {
                                                                                    setCheckValue([message.id]);
                                                                                    setConfirmOpen(true);
                                                                                }}
                                                                            >
                                                                                <RiDeleteBin6Line className="text-sm" />
                                                                                <span className="text-xs ms-1">
                                                                                    Delete
                                                                                </span>
                                                                            </Button>
                                                                        </Tooltip>
                                                                    }
                                                                </div>
                                                            </TableCell>
                                                        </TableRow>
                                                    );
                                                }
                                            )
                                        ) : (
                                            <TableRow className="bg-white dark:border-gray-700 dark:bg-gray-800">
                                                <TableCell
                                                    colSpan={11}
                                                    className="text-center"
                                                >
                                                    <NoRecord />
                                                </TableCell>
                                            </TableRow>
                                        )}
                                </TableBody>
                            </Table>
                        </div>
                    </div>

                    <div className="bottom-0 w-full p-3 bg-white rounded-lg float-end dark:border-gray-500 dark:rounded-lg dark:bg-gray-700">
                        <div className="flex flex-wrap justify-between gap-2 lg:justify-between lg:gap-0 md:gap-0">
                            <div className="flex items-center gap-4">
                                <PerPageDropdown
                                    getDataFields={
                                        collection.getData ?? null
                                    }
                                    routeName={"whatsappB.bot.index"}
                                    data={collection.messages}
                                />
                            </div>

                            <Paginate tableData={collection.messages} />
                        </div>
                    </div>
                </div>
            </div>


            {/*-------- edit bot welcome message  ---------*/}
            {
                enableEditWelcomeMessageDrawer === true ? (
                    <Drawer
                        theme={customDrawerEdit}
                        className="w-full lg:w-4/5"
                        open={enableEditWelcomeMessageDrawer}
                        onClose={() => setEnableWelcomeMessageDrawer(false)}
                        position="right"
                    >
                        <DrawerHeader
                            titleIcon={IoMdRepeat}
                            title={"Edit Bot Welcome Message"}
                        />
                        <DrawerItems className="pb-2">
                            <EditWelcome
                                message={editWelcomeMsgData}
                                onClose={() => setEnableWelcomeMessageDrawer(false)}
                            ></EditWelcome>
                        </DrawerItems>
                    </Drawer>
                ) : (
                    <></>
                )
            }

            {/* add welcome */}

            {enableAddWelcome &&
                <Drawer
                    theme={customDrawerEdit}
                    className="w-full lg:w-5/6 md:w-6/6 xl:w-3/4"
                    open={enableAddWelcome}
                    onClose={() => setEnableAddWelcome(false)}
                    position="right"
                >
                    <DrawerHeader titleIcon={IoMdRepeat} title="Add Welcome Message" />
                    <DrawerItems className="rounded-lg">
                        <AddWelcome onClose={() => setEnableAddWelcome(false)} bot={bot} />
                    </DrawerItems>
                </Drawer>
            }

            {/* confirm box popup */}
            {
                isConfirmOpen &&
                <ConfirmBox
                    isOpen={isConfirmOpen}
                    onClose={() => setConfirmOpen(false)} // Close the confirm box
                    onAction={handleConfirmBoxResult} // Handle the user's choice
                    title="Are you sure you want to delete this?"
                    message="This action cannot be undone."
                    confirmText="Yes, Delete!"
                    cancelText="No, Keep It"
                    confirmColor="orange"
                    cancelColor="gray"
                    icon={<FaRegTrashCan />}
                />
            }
        </WaMain >
    );
}



