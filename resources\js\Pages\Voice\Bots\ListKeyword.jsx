import { TruncatedCell } from "@/Components/ExpandMsg";
import NoRecord from "@/Components/HelperComponents/NoRecord";
import Paginate from "@/Components/HelperComponents/Paginate";
import PerPageDropdown from "@/Components/HelperComponents/PerPageDropdown";
import Main from "@/Layouts/Main";
import { Head, Link, useForm } from "@inertiajs/react";
import { Button, ButtonGroup, Checkbox, Drawer, Table } from "flowbite-react";
import $ from "jquery";
import { useEffect, useState } from "react";
import { IoIosArrowBack, IoMdAdd, IoMdRepeat } from "react-icons/io";
import { MdDeleteOutline, MdOutlineModeEdit } from "react-icons/md";
import { TbColumns3 } from "react-icons/tb";
import {
    button_custom,
    buttongroup_custom,
    customDrawer,
    table_custom,   
} from "../../Helpers/DesignHelper";
import TabBar from "../TabBar";
import { default as Add, default as AddKeyword } from "./AddKeyword";
import EditListKeyword from "./EditListKeyword";
import ViewListKeyword from "./ViewListKeyword";

export default function ListKeyword({ keywordList }) {
    const { autoreply, getData, keywords } = keywordList;
    // -------- add campaign -------------
    const [isAddOpen, setIsAddOpen] = useState(false);
    const [isEditOpen, setIsEditOpen] = useState(false);
    const [isViewOpen, setIsViewOpen] = useState(false);
    const [isAddAutoReplyRuleOpen, setIsAddAutoReplyRule] = useState(false);
    const [isCheckAll, setIsCheckAll] = useState(false);
    const [autoReplyData, setautoReplyData] = useState({});
    const [checkValue, setCheckValue] = useState([]);
    const { data, setData, post, processing, errors } = useForm({
        id: [],
    });

    // useEffect(() => {
    //     let idsAry = [];
    //     if (isCheckAll) {
    //         $(".rowCheckBox").prop("checked", true);
    //         let ids = $(".rowCheckBox");
    //         ids.each((index, element) => {
    //             idsAry.push(element.id);
    //         });
    //     } else {
    //         $(".rowCheckBox").prop("checked", false);
    //         idsAry = [];
    //     }
    //     setCheckValue(idsAry);
    // }, [isCheckAll]);

    // executes when user click table row checkbox
    function getCheckedIds(e) {
        let previousIds = checkValue;
        if (e.target.checked) {
            if (!previousIds.includes(e.target.id)) {
                previousIds.push(e.target.id);
                setCheckValue(previousIds);
            }
        } else {
            const newIds = previousIds.filter((item) => item !== e.target.id);
            setCheckValue(newIds);
        }
    }


    // executes when user click table header checkbox
    function headerCheckBoxChecked(e) {
        let previousIds = [];
        if (e.target.checked && e.target.id == 0 && keywords.data.length > 0) {
            keywords.data.map((keyWords, key) => {
                if (!previousIds.includes(keyWords.id)) {
                    previousIds.push(keyWords.id);
                    setCheckValue(previousIds);
                }
            });
            setIsCheckAll(true);
            $(".rowCheckBox").prop("checked", true);
        } else {
            setCheckValue(previousIds);
            setIsCheckAll(false);
            $(".rowCheckBox").prop("checked", false);
        }
    }


    // handle checkBox Check or uncheck
    function checkAddcheckBoxChecked() {
        let allCheckBoxes = $(".rowCheckBox");
        let checkedCheckBoxes = $(".rowCheckBox:checked");

        if (allCheckBoxes.length == checkedCheckBoxes.length) {
            setIsCheckAll(true);
        } else {
            setIsCheckAll(false);
        }
    }
    function deleteRecords() {
        if (checkValue.length > 0) {
            setData({ id: checkValue });
            post(
                route("whatsapp.bot.deleteKeywords", {
                    keyword_ids: checkValue,
                })
            );
            setCheckValue([]);
            setIsCheckAll(false);
        }
    }

    const customDrawerEdit = {
        root: {
            base: "fixed z-50 overflow-y-auto bg-white p-4 transition-transform dark:bg-gray-800 bg-slate-100 ",
            backdrop: "fixed inset-0 z-40 bg-gray-900/50 dark:bg-gray-900/80",
            edge: "bottom-16",
            position: {
                top: {
                    on: "left-0 right-0 top-0 w-full transform-none",
                    off: "left-0 right-0 top-0 w-full -translate-y-full",
                },
                right: {
                    on: "right-0 top-0 h-screen w-80 transform-none",
                    off: "right-0 top-0 h-screen w-80 translate-x-full",
                },
                bottom: {
                    on: "bottom-0 left-0 right-0 w-full transform-none",
                    off: "bottom-0 left-0 right-0 w-full translate-y-full",
                },
                left: {
                    on: "left-0 top-0 h-screen w-80 transform-none",
                    off: "left-0 top-0 h-screen w-80 -translate-x-full",
                },
            },
        },
        header: {
            inner: {
                closeButton:
                    "absolute end-2.5 top-2.5 flex h-8 w-8 items-center justify-center rounded-lg bg-transparent text-sm text-gray-400 hover:bg-gray-200 hover:text-gray-900 dark:hover:bg-gray-600 dark:hover:text-white",
                closeIcon: "h-6 w-6",
                titleIcon: "me-2.5 h-5 w-5 text-slate-400",
                titleText:
                    "mb-4 inline-flex items-center text-xl font-semibold text-slate-800 dark:text-gray-400",
            },
            collapsed: {
                on: "hidden",
                off: "block",
            },
        },
        items: {
            base: "rounded-lg bg-white dark:!bg-gray-700",
        },
    };

    return (
        <Main>
            <TabBar />
            <Head title="Bots" />
            <div className="mt-2.5 bg-white rounded-lg dark:bg-gray-800 mx-2">
                {/* <BackButton /> */}
                <div className="flex justify-between p-2">
                    <div className="flex items-center gap-2">
                        <ButtonGroup theme={buttongroup_custom} outline>
                            <Button
                                className="pe-1.5"
                                size="xs"
                                color="pink"
                                theme={button_custom}
                                onClick={() => window.history.back()}
                            >
                                <div className="flex items-center gap-1">
                                    <IoIosArrowBack className="text-slate-500" />
                                    <span className="text-xs">Back</span>
                                </div>
                            </Button>
                            <Button
                                theme={button_custom}
                                size="xs"
                                color="gray"
                                id="dropdownInformationButton"
                                data-dropdown-toggle="dropdownNotification"
                                type="button"
                            >
                                <div className="flex items-center gap-1 ps-1">
                                    <TbColumns3 className="text-slate-500 text-sm" />
                                    <span className="text-xs">Columns</span>
                                </div>
                            </Button>

                        </ButtonGroup>
                        <div>({autoreply.name})</div>
                    </div>
                    <div className="flex items-center">
                        <ButtonGroup theme={buttongroup_custom} outline>
                            <Button
                                className="border rounded-s-none rounded-e-none pe-1"
                                size="xs"
                                color="gray"
                                theme={button_custom}
                                onClick={() => {
                                    setIsAddOpen(true);
                                }}
                            >
                                <div className="flex items-center gap-1">
                                    <IoMdAdd className="text-slate-500" />
                                    <span className="text-xs">Add</span>
                                </div>
                            </Button>
                        </ButtonGroup>
                        {/* Dropdown menu */}
                        <div
                            id="dropdownNotification"
                            className="z-20 border shadow-lg hidden w-full max-w-sm bg-white divide-y divide-gray-100 rounded-lg  dark:bg-gray-800 dark:divide-gray-700"
                            aria-labelledby="dropdownNotificationButton"
                        >
                            <div className="block px-4 py-2 font-medium text-center text-gray-700 rounded-t-lg bg-gray-50 dark:bg-gray-800 dark:text-white">
                                <div className="flex justify-between">
                                    <div className="">
                                        <div className="group  text-gray-400 p-1 flex items-center gap-1">
                                            <svg
                                                xmlns="http://www.w3.org/2000/svg"
                                                className=" fill-slate-400  "
                                                height="24px"
                                                viewBox="0 0 24 24"
                                                width="24px"
                                            >
                                                <rect
                                                    fill="none"
                                                    height="24"
                                                    width="24"
                                                />
                                                <path d="M20,4H4C2.9,4,2,4.9,2,6v12c0,1.1,0.9,2,2,2h16c1.1,0,2-0.9,2-2V6C22,4.9,21.1,4,20,4z M8,18H4V6h4V18z M14,18h-4V6h4V18z M20,18h-4V6h4V18z" />
                                            </svg>
                                            Column
                                        </div>
                                    </div>
                                    <div className="">
                                        <button className="p-1 flex text-blue-600">
                                            <svg
                                                xmlns="http://www.w3.org/2000/svg"
                                                height="24px"
                                                viewBox="0 0 24 24"
                                                width="24px"
                                                className="fill-blue-600"
                                            >
                                                <g>
                                                    <path
                                                        d="M0,0h24v24H0V0z"
                                                        fill="none"
                                                    />
                                                </g>
                                                <g>
                                                    <g>
                                                        <path d="M6,13c0-1.65,0.67-3.15,1.76-4.24L6.34,7.34C4.9,8.79,4,10.79,4,13c0,4.08,3.05,7.44,7,7.93v-2.02 C8.17,18.43,6,15.97,6,13z M20,13c0-4.42-3.58-8-8-8c-0.06,0-0.12,0.01-0.18,0.01l1.09-1.09L11.5,2.5L8,6l3.5,3.5l1.41-1.41 l-1.08-1.08C11.89,7.01,11.95,7,12,7c3.31,0,6,2.69,6,6c0,2.97-2.17,5.43-5,5.91v2.02C16.95,20.44,20,17.08,20,13z" />
                                                    </g>
                                                </g>
                                            </svg>
                                            Reset
                                        </button>
                                    </div>
                                </div>
                            </div>
                            <div className="divide-y divide-gray-100 dark:divide-gray-700">
                                <div className="flex flex-col">
                                    <div className="relative overflow-x-auto shadow-md sm:rounded-lg ">
                                        <table className=" w-full text-sm text-left rtl:text-right text-gray-500 dark:text-gray-400">
                                            <thead className="text-xs text-gray-700 uppercase dark:text-gray-400">
                                                <tr>
                                                    <th className="p-2 bg-gray-50 dark:bg-gray-800 w-14 text-center ">
                                                        List
                                                    </th>
                                                    <th className="p-2  bg-gray-50 dark:bg-gray-800 w-14 text-center">
                                                        Details
                                                    </th>
                                                    <th className="p-2"></th>
                                                </tr>
                                            </thead>
                                            <tbody className=" overflow-auto">
                                                <tr className="border-b border-gray-200 dark:border-gray-700">
                                                    <td className="text-center max-w-max p-2 font-medium text-gray-900 whitespace-nowrap bg-gray-50 dark:text-white dark:bg-gray-800">
                                                        <Checkbox></Checkbox>
                                                    </td>
                                                    <td className="text-center p-2 font-medium text-gray-900 whitespace-nowrap bg-gray-50 dark:text-white dark:bg-gray-800">
                                                        <Checkbox></Checkbox>
                                                    </td>
                                                    <td className=" max-w-max px-6 py-4">
                                                        Name
                                                    </td>
                                                </tr>
                                                <tr className="border-b border-gray-200 dark:border-gray-700">
                                                    <td className="text-center  max-w-max p-2 font-medium text-gray-900 whitespace-nowrap bg-gray-50 dark:text-white dark:bg-gray-800">
                                                        <Checkbox></Checkbox>
                                                    </td>
                                                    <td className="text-center p-2 font-medium text-gray-900 whitespace-nowrap bg-gray-50 dark:text-white dark:bg-gray-800">
                                                        <Checkbox></Checkbox>
                                                    </td>
                                                    <td className=" max-w-max px-6 py-4">
                                                        Name
                                                    </td>
                                                </tr>
                                                <tr className="border-b border-gray-200 dark:border-gray-700">
                                                    <td className="text-center  max-w-max p-2 font-medium text-gray-900 whitespace-nowrap bg-gray-50 dark:text-white dark:bg-gray-800">
                                                        <Checkbox></Checkbox>
                                                    </td>
                                                    <td className="text-center p-2 font-medium text-gray-900 whitespace-nowrap bg-gray-50 dark:text-white dark:bg-gray-800">
                                                        <Checkbox></Checkbox>
                                                    </td>
                                                    <td className="max-w-max px-6 py-4">
                                                        Name
                                                    </td>
                                                </tr>
                                                <tr className="border-b border-gray-200 dark:border-gray-700">
                                                    <td className="text-center max-w-max p-2 font-medium text-gray-900 whitespace-nowrap bg-gray-50 dark:text-white dark:bg-gray-800">
                                                        <Checkbox></Checkbox>
                                                    </td>
                                                    <td className="text-center p-2 font-medium text-gray-900 whitespace-nowrap bg-gray-50 dark:text-white dark:bg-gray-800">
                                                        <Checkbox></Checkbox>
                                                    </td>
                                                    <td className="max-w-max px-6 py-4">
                                                        Name
                                                    </td>
                                                </tr>
                                            </tbody>
                                        </table>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div className="flex flex-col justify-between ">
                    <div className="overflow-x-auto rounded-lg border">
                        <Table hoverable theme={table_custom}>
                            <TableHead>
                                <TableHeadCell className="w-10 text-center">
                                    <Checkbox
                                        checked={isCheckAll}
                                        color="blue"
                                        id={0}
                                        onChange={(e) => {
                                            headerCheckBoxChecked(e);

                                        }}
                                    />
                                </TableHeadCell>
                                <TableHeadCell>
                                    <Link
                                        href={route(
                                            "whatsapp.bot.listKeyword",
                                            {
                                                column: "id",
                                                sort:
                                                    getData.sort == "asc"
                                                        ? "desc"
                                                        : "asc",
                                                id: autoreply.id,
                                            }
                                        )}
                                    >
                                        <div className="flex items-center justify-between gap-2">
                                            <span>Id</span>
                                            <svg
                                                xmlns="http://www.w3.org/2000/svg"
                                                height="16px"
                                                viewBox="0 -960 960 960"
                                                width="20px"
                                                fill="#5f6368"
                                            >
                                                <path d="M480-120 300-300l58-58 122 122 122-122 58 58-180 180ZM358-598l-58-58 180-180 180 180-58 58-122-122-122 122Z" />
                                            </svg>
                                        </div>
                                    </Link>
                                </TableHeadCell>
                                <TableHeadCell>
                                    <Link
                                        href={route(
                                            "whatsapp.bot.listKeyword",
                                            {
                                                column: "user_id",
                                                sort:
                                                    getData.sort == "asc"
                                                        ? "desc"
                                                        : "asc",
                                                id: autoreply.id,
                                            }
                                        )}
                                    >
                                        <div className="flex items-center justify-between gap-2">
                                            <span>User</span>
                                            <svg
                                                xmlns="http://www.w3.org/2000/svg"
                                                height="16px"
                                                viewBox="0 -960 960 960"
                                                width="20px"
                                                fill="#5f6368"
                                            >
                                                <path d="M480-120 300-300l58-58 122 122 122-122 58 58-180 180ZM358-598l-58-58 180-180 180 180-58 58-122-122-122 122Z" />
                                            </svg>
                                        </div>
                                    </Link>
                                </TableHeadCell>
                                <TableHeadCell>
                                    <Link
                                        href={route(
                                            "whatsapp.bot.listKeyword",
                                            {
                                                column: "keyword",
                                                sort:
                                                    getData.sort == "asc"
                                                        ? "desc"
                                                        : "asc",
                                                id: autoreply.id,
                                            }
                                        )}
                                    >
                                        <div className="flex items-center justify-between gap-2">
                                            <span>Keyword</span>
                                            <svg
                                                xmlns="http://www.w3.org/2000/svg"
                                                height="16px"
                                                viewBox="0 -960 960 960"
                                                width="20px"
                                                fill="#5f6368"
                                            >
                                                <path d="M480-120 300-300l58-58 122 122 122-122 58 58-180 180ZM358-598l-58-58 180-180 180 180-58 58-122-122-122 122Z" />
                                            </svg>
                                        </div>
                                    </Link>
                                </TableHeadCell>
                                <TableHeadCell>
                                    <span>Message</span>
                                </TableHeadCell>

                                <TableHeadCell>
                                    <h3>Actions</h3>
                                </TableHeadCell>
                            </TableHead>
                            <TableBody className="divide-y">
                                {keywords.data.length > 0 ? (
                                    keywords.data.map((list, key) => {
                                        return (
                                            <TableRow
                                                className="bg-white dark:border-gray-700 dark:bg-gray-800"
                                                key={key}
                                            >
                                                <TableCell className="text-center">
                                                    <Checkbox
                                                        className="rowCheckBox"
                                                        id={list.id}
                                                        color="blue"
                                                        onChange={(e) => {
                                                            getCheckedIds(e);
                                                            checkAddcheckBoxChecked();
                                                        }}
                                                    />
                                                </TableCell>

                                                <TableCell className="font-medium text-gray-900 whitespace-nowrap dark:text-white">
                                                    {list.id}
                                                </TableCell>
                                                <TableCell>
                                                    {list.user.username}
                                                </TableCell>
                                                <TableCell>
                                                    {list.keyword}
                                                </TableCell>
                                                <TableCell>
                                                    <TruncatedCell
                                                        content={
                                                            list.msg ?? "-"
                                                        }
                                                    />
                                                </TableCell>

                                                <TableCell>
                                                    <div className="flex items-center gap-3">
                                                        <Button
                                                            theme={
                                                                button_custom
                                                            }
                                                            color="success"
                                                            size="xs"
                                                            onClick={() => {
                                                                setIsEditOpen(
                                                                    true
                                                                );
                                                                setautoReplyData(
                                                                    {
                                                                        id: list.id,
                                                                        autoReplyName:
                                                                            autoreply.name,
                                                                        autoReplyId:
                                                                            autoreply.id,
                                                                        msg: list.msg,
                                                                        media: list.wa_media,
                                                                        keyword:
                                                                            list.keyword,
                                                                    }
                                                                );
                                                            }}
                                                        >
                                                            <MdOutlineModeEdit className="text-sm" />
                                                            <span className="text-xs ms-1">
                                                                Edit
                                                            </span>
                                                        </Button>
                                                    </div>
                                                </TableCell>
                                            </TableRow>
                                        );
                                    })
                                ) : (
                                    <TableRow className="bg-white dark:border-gray-700 dark:bg-gray-800">
                                        <TableCell
                                            colSpan={11}
                                            className="text-center"
                                        >
                                            <NoRecord />
                                        </TableCell>
                                    </TableRow>
                                )}
                            </TableBody>
                        </Table>
                    </div>
                    <div className="bottom-0 w-full p-3 bg-white rounded-lg float-end dark:border-gray-500 dark:rounded-lg dark:bg-gray-700">
                        <div className="flex flex-wrap justify-center gap-2 lg:justify-between lg:gap-0 md:gap-0">
                            <div className="flex items-center gap-3">
                                <div className="flex gap-3">
                                    <Button
                                        className="border rounded-md"
                                        size="xs"
                                        color="gray"
                                        theme={button_custom}
                                        onClick={() =>
                                            window.confirm(
                                                "Do you really want to delete AutoReply ?"
                                            ) && deleteRecords()
                                        }
                                    >
                                        <div className="flex items-center gap-1">
                                            <MdDeleteOutline className="text-slate-500" />
                                            <span className="text-xs">
                                                Delete
                                            </span>
                                        </div>
                                    </Button>
                                    <PerPageDropdown
                                        getDataFields={getData ?? null}
                                        routeName={
                                            "whatsapp.bot.listKeyword"
                                        }
                                        id={autoreply.id}
                                        data={keywords}
                                    />
                                </div>
                            </div>
                            <Paginate tableData={keywords} />
                        </div>
                    </div>
                </div>
            </div>

            {/*--------Add Keyword---------*/}
            {isAddOpen === true ? (
                <Drawer
                    theme={customDrawerEdit}
                    className="w-full lg:w-2/5 md:w-3/5"
                    open={isAddOpen}
                    onClose={() => setIsAddOpen(false)}
                    position="right"
                >
                    <DrawerHeader
                        titleIcon={IoMdRepeat}
                        title={`Add List Keyword (${autoreply.name})`}
                    />
                    <DrawerItems className="px-4 py-2">
                        <Add
                            autoReplyData={autoreply}
                            onClose={() => setIsAddOpen(false)}
                        />
                    </DrawerItems>
                </Drawer>
            ) : (
                <></>
            )}

            {/*--------edit Keyword---------*/}
            {isEditOpen === true ? (
                <Drawer
                    theme={customDrawerEdit}
                    className="w-full xl:w-1/2 lg:w-2/3 md:w-4/5"
                    open={isEditOpen}
                    onClose={() => setIsEditOpen(false)}
                    position="right"
                >
                    <DrawerHeader
                        titleIcon={IoMdRepeat}
                        title={`Edit List Keyword (${autoReplyData.autoReplyName})`}
                    />
                    <DrawerItems className="px-4 py-2">
                        <EditListKeyword
                            autoReplyData={autoReplyData}
                            onClose={() => setIsEditOpen(false)}
                        ></EditListKeyword>
                    </DrawerItems>
                </Drawer>
            ) : (
                <></>
            )}

            {/*--------view list keyword---------*/}
            {isViewOpen === true ? (
                <Drawer
                    theme={customDrawer}
                    className="w-full lg:w-2/5 md:w-3/5"
                    open={isViewOpen}
                    onClose={() => setIsViewOpen(false)}
                    position="right"
                >
                    <DrawerHeader
                        titleIcon={IoMdRepeat}
                        title="View Auto-Reply"
                    />
                    <DrawerItems className="px-4 pt-2 pb-4  bg-white rounded-lg mt-4">
                        <ViewListKeyword
                            autoReplyData={autoReplyData}
                        ></ViewListKeyword>
                    </DrawerItems>
                </Drawer>
            ) : (
                <></>
            )}
            {/*--------Add Auto Reply Rule---------*/}
            {isAddAutoReplyRuleOpen === true ? (
                <Drawer
                    theme={customDrawer}
                    className="w-full lg:w-2/5 md:w-3/5"
                    open={isAddAutoReplyRuleOpen}
                    onClose={() => setIsAddAutoReplyRule(false)}
                    position="right"
                >
                    <DrawerHeader
                        titleIcon={IoMdRepeat}
                        title={`Add Auto Reply Rule (${autoReplyData.autoReplyName})`}
                    />
                    <DrawerItems className="px-4 py-2">
                        <AddKeyword
                            autoReplyData={autoReplyData}
                            onClose={() => setIsAddAutoReplyRule(false)}
                        ></AddKeyword>
                    </DrawerItems>
                </Drawer>
            ) : (
                <></>
            )}
        </Main>
    );
}



