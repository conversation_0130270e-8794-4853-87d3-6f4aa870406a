import { button_custom } from "@/Pages/Helpers/DesignHelper";
import Search from "@/Pages/Users/<USER>";
import { Link } from "@inertiajs/react";
import { Badge, Button, ButtonGroup, Dropdown, DropdownItem } from "flowbite-react";
import { FiPhoneCall } from "react-icons/fi";
import { IoCallOutline } from "react-icons/io5";
import { Lu<PERSON>ail<PERSON>pen, LuPhoneOutgoing } from "react-icons/lu";
import { MdBlockFlipped, MdOutlineCall, MdOutlineMarkEmailRead, MdOutlineMoveToInbox, MdOutlinePhoneMissed } from "react-icons/md";
import { PiPhoneList, } from "react-icons/pi";
import { RiSendPlane2Line } from "react-icons/ri";
export function SideMenu() {
    const menuDropdown = {
        arrowIcon: "ml-2 h-4 w-4",
        content: "py-1 focus:outline-none ",
        floating: {
            animation: "transition-opacity",
            arrow: {
                base: "absolute z-10 h-2 w-2 rotate-45",
                style: {
                    dark: "bg-gray-900 dark:bg-gray-700",
                    light: "bg-white",
                    auto: "bg-white dark:bg-gray-700",
                },
                placement: "-4px",
            },
            base: "z-10  divide-y divide-gray-100 rounded shadow focus:outline-none w-full",
            content: "py-1 text-sm text-gray-700 dark:text-gray-200",
            divider: "my-1 h-px bg-gray-100 dark:bg-gray-600",
            header: "block px-4 py-2 text-sm text-gray-700 dark:text-gray-200",
            hidden: "invisible opacity-0",
            item: {
                container: "w-full",
                base: "flex w-full cursor-pointer items-center justify-start px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 hover:bg-gray-100 focus:outline-none dark:text-gray-200 dark:hover:bg-gray-600 dark:hover:text-white dark:focus:bg-gray-600 dark:focus:text-white",
                icon: "mr-2 h-4 w-4",
            },
            style: {
                dark: "bg-gray-900 text-white dark:bg-gray-700",
                light: "border border-gray-200 bg-white text-gray-900",
                auto: "border border-gray-200 bg-white text-gray-900 dark:border-none dark:bg-gray-700 dark:text-white",
            },
            target: "w-full ",
        },
        inlineWrapper: "flex items-center",
    };
    const items = [
        {
            name: <div><span className="text-sm">Prachi (5682)</span>
            <div className="text-sm flex items-center gap-1 ">
                <LuPhoneOutgoing className="text-blue-700"/>
                <span className="text-xs">19 sec ago</span>
            </div>
            </div>,
            url: route("call.calllogs.index"),
            active: route().current("mail.mails.inbox"),
            icon: <div className="bg-blue-500 text-white p-1 text-sm size-8 flex items-center justify-center rounded-full">
                P
            </div>,
            classes: {
                groupHover: " group-hover:text-blue-500 ",
                group: " hover:bg-blue-50 ",
                active: " bg-blue-50 ",
            },
        },
        {
            name: <div><span className="text-sm">Prachi (5682)</span>
            <div className="text-sm flex items-center gap-1 ">
                <LuPhoneOutgoing className="text-blue-700"/>
                <span className="text-xs">19 sec ago</span>
            </div>
            </div>,
            url: route("call.calllogs.index"),
            active: route().current("mail.mails.inbox"),
            icon: <div className="bg-blue-500 text-white p-1 text-sm size-8 flex items-center justify-center rounded-full">
                P
            </div>,
            classes: {
                groupHover: " group-hover:text-blue-500 ",
                group: " hover:bg-blue-50 ",
                active: " bg-blue-50 ",
            },
        },
        {
            name: <div><span className="text-sm">Prachi (5682)</span>
            <div className="text-sm flex items-center gap-1 ">
                <LuPhoneOutgoing className="text-blue-700"/>
                <span className="text-xs">19 sec ago</span>
            </div>
            </div>,
            url: route("call.calllogs.index"),
            active: route().current("mail.mails.inbox"),
            icon: <div className="bg-blue-500 text-white p-1 text-sm size-8 flex items-center justify-center rounded-full">
                P
            </div>,
            classes: {
                groupHover: " group-hover:text-blue-500 ",
                group: " hover:bg-blue-50 ",
                active: " bg-blue-50 ",
            },
        },
    ];
    return (
        <div className="w-full bg-white rounded ">
            <div className="p-1 pb-0.5">
                <Search inputPlaceholder="Search by name, id or phone number"></Search>
            </div>
            <div className="flex items-center px-1">
                <ButtonGroup className="h-fit">
                    <Button
                        className=""
                        size="xs"
                        color="gray"
                        theme={button_custom}
                    // onClick={deleteRecords}
                    >
                        <div className="flex items-center gap-1">
                            <PiPhoneList className="text-slate-500" />
                            <span className="text-xs">All</span>
                        </div>
                    </Button>
                    <Button
                        className=""
                        size="xs"
                        color="gray"
                        theme={button_custom}
                    // onClick={deleteRecords}
                    >
                        <div className="flex items-center gap-1">
                            <MdOutlinePhoneMissed className="text-red-600 text-sm ms-1" />
                            <span className="text-xs">Missed</span>
                        </div>
                    </Button>
                    <Button
                        className=""
                        size="xs"
                        color="gray"
                        theme={button_custom}
                    // onClick={deleteRecords}
                    >
                        <div className="flex items-center gap-1">
                            <FiPhoneCall className="text-green-500 text-sm ms-1" />
                            <span className="text-xs">All</span>
                        </div>
                    </Button>
                    <Button
                        className=""
                        size="xs"
                        color="gray"
                        theme={button_custom}
                    // onClick={deleteRecords}
                    >
                        <div className="flex items-center gap-1">
                            <LuPhoneOutgoing className="text-blue-500 text-sm ms-1" />
                            <span className="text-xs">Outgoing</span>
                        </div>
                    </Button>
                </ButtonGroup>
            </div>
            <div className="lg:hidden md:hidden">
                <Dropdown
                    label="Menu"
                    size="sm"
                    className="w-full"
                    theme={menuDropdown}
                    color="light"
                >
                    {items.map((item, k) => {
                        return (
                            <DropdownItem
                                key={"navItems-" + k}
                                as={Link}
                                href={item.url}
                            >
                                {item.name}
                            </DropdownItem>
                        );
                    })}
                </Dropdown>
            </div>
            <div className="hidden w-full lg:block md:block">
                {items.map((item, k) => {
                    return (
                        <Link href={item.url}>
                            <div
                                className={
                                    "border-b hover:border-transparent p-1.5 px-2 flex items-center justify-between gap-3 bg-white rounded rounded-b-none hover:rounded-b  group" +
                                    item.classes.group +
                                    (item.active ? item.classes.active : "")
                                }
                            >
                                <div className="flex items-center gap-3">
                                    {item.icon}
                                    <span className={item.classes.groupHover}>
                                        {item.name}
                                    </span>
                                </div>
                                <Button theme={button_custom} size="xs" color="Fuchsia_custom">   
                                    <MdOutlineCall />
                                </Button>
                            </div>
                        </Link>
                    );
                })}
            </div>
        </div>
    );
}

export default SideMenu;
