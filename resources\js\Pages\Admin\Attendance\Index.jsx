import AttendanceTable from "@/Components/AttendanceTable";
import Main from "@/Layouts/Main";
import { button_custom } from "@/Pages/Helpers/DesignHelper";
import { Head } from "@inertiajs/react";
import { <PERSON><PERSON>, <PERSON>dal, ModalBody, ModalHeader } from "flowbite-react";
import { useState } from "react";
import { FaCircle } from "react-icons/fa6";
import { MdOutlineCelebration } from "react-icons/md";
import SideMenu from "../SideMenu";
import AddEvent from "./AddEvent";
import Filter from "./Filter";

function Index() {
    // Define months and initialize state
    const months = [
        "January",
        "February",
        "March",
        "April",
        "May",
        "June",
        "July",
        "August",
        "September",
        "October",
        "November",
        "December",
    ];

    const [selectedMonth, setSelectedMonth] = useState("August"); // Default to "August"
    const [selectedYear, setSelectedYear] = useState(2024); // Default to 2024
    const [isDropdownOpen, setIsDropdownOpen] = useState(false); // Toggle state

    // Handlers for previous and next year
    const goToPreviousYear = () => {
        setSelectedYear(selectedYear - 1);
    };

    const goToNextYear = () => {
        setSelectedYear(selectedYear + 1);
    };

    const handleMonthSelect = (month) => {
        setSelectedMonth(month);
        setIsDropdownOpen(false); // Close dropdown after selecting a month
    };

    // Toggle dropdown visibility
    const toggleDropdown = () => {
        setIsDropdownOpen(!isDropdownOpen);
    };
    //------------- Missed Punch Request Model-------------
    const [openAddEventModal, setAddEventOpenModal] = useState(false);
    return (
        <Main>
            <Head title="User" />

            <div className="flex ">
                <div className="relative flex flex-col justify-between w-full dark:text-gray-400 lg:p-0">
                    <div>
                        <div className="relative p-2 overflow-hidden">
                            <div className="grid grid-flow-row-dense grid-cols-12 gap-2 p-0 border-none md:grid-cols-12 sm:grid-cols-1 bg-slate-100">
                                <div className="hidden bg-white border rounded-lg shadow-sm xl:col-span-2 lg:col-span-3 md:col-span-3 sm:col-span-3 lg:flex">
                                    <div className="w-full overflow-auto dark:bg-gray-900">
                                        {/* <Filter /> */}
                                        <SideMenu></SideMenu>
                                    </div>
                                </div>
                                <div className="relative pt-0 dark:text-gray-400 lg:p-0 xl:col-span-10 lg:col-span-9 md:col-span-12 col-span-full">
                                    <div className="grid grid-flow-row-dense grid-cols-12 gap-2 p-0 border-none md:grid-cols-12 sm:grid-cols-1 bg-slate-100">
                                        <div className="hidden bg-white border rounded-lg shadow-sm xl:col-span-2 lg:col-span-3 md:col-span-3 sm:col-span-3 lg:flex">
                                            <Filter></Filter>
                                        </div>
                                        <div className="relative pt-0 dark:text-gray-400 lg:p-0 xl:col-span-10 lg:col-span-9 md:col-span-12 col-span-full">
                                            <div className="flex justify-between p-2 bg-white rounded-md">
                                                <div className="flex items-center gap-3 ps-2">
                                                    <div className="flex items-center gap-1 text-[#3f9f31]">
                                                        <FaCircle className="text-xs " />
                                                        Event
                                                    </div>
                                                    <div className="flex items-center gap-1 text-blue-600">
                                                        <FaCircle className="text-xs " />
                                                        Holiday
                                                    </div>
                                                </div>
                                                <Button
                                                    size="xs"
                                                    color="gray"
                                                    theme={button_custom}
                                                    onClick={() =>
                                                        setAddEventOpenModal(
                                                            true
                                                        )
                                                    }
                                                >
                                                    <div className="flex items-center gap-1">
                                                        <MdOutlineCelebration className="text-slate-500" />
                                                        <span className="text-xs">
                                                            Add Event
                                                        </span>
                                                    </div>
                                                </Button>
                                            </div>
                                            <div className="overflow-auto bg-white border rounded-lg h-fit">
                                                <AttendanceTable></AttendanceTable>
                                            </div>
                                        </div>
                                    </div>

                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            {/*--------------- Missed Punch Modal--------------- */}
            <Modal
                show={openAddEventModal}
                onClose={() => setAddEventOpenModal(false)}
            >
                <ModalHeader className="p-2">
                    <div className="flex items-center gap-2">
                        <MdOutlineCelebration className="text-slate-400" />
                        <h4 className="text-lg">Add Event</h4>
                    </div>
                </ModalHeader>
                <ModalBody className="px-4 py-3 rounded-b-lg bg-slate-100">
                    <AddEvent></AddEvent>
                </ModalBody>
            </Modal>
        </Main>
    );
}

export default Index;

