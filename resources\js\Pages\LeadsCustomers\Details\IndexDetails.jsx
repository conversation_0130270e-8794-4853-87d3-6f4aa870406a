import {
    <PERSON><PERSON>,
    <PERSON><PERSON>,
    Ta<PERSON>
} from "flowbite-react";
import $ from "jquery";
import { useEffect, useState } from "react";
import { AiOutlineMergeCells } from "react-icons/ai";
import { HiOutlineClock } from 'react-icons/hi';
import { MdCallSplit, MdOutlineBackHand, MdOutlineInventory, MdPointOfSale } from "react-icons/md";

import { tabBar_underline } from "@/Pages/Helpers/DesignHelper";
import LeadSplit from "../LeadSplit";
import Grab from "./Grab";
import Invoice from "./Invoice";
import Merge from "./Merge";
import Split from "./Split";
import Subscription from "./Subscription";
import Transaction from "./Transaction";

export default function IndexDetails() {
    const [openEmoji, setOpenEmoji] = useState(false);
    const [openMissedPunchModal, setMissedPunchOpenModal] = useState(false);
    const [switch2, setSwitch2] = useState(false);

    const textinput_custom = {
        colors: {
            gray: "border-gray-300 bg-gray-50 text-gray-900 focus:border-blue-500 focus:ring-blue-500 dark:border-gray-600 dark:bg-gray-700 dark:text-white dark:placeholder-gray-400 dark:focus:border-blue-500 dark:focus:ring-blue-500",
        },
    };
    const custom_datepicker = {
        root: {
            base: "relative",
        },
        field: {
            base: "relative w-full",
            icon: {
                base: "pointer-events-none absolute inset-y-0 end-3.5 flex items-center pl-3",
                svg: "h-5 w-5 text-slate-400 dark:text-gray-400",
            },
            rightIcon: {
                base: "pointer-events-none absolute border-s inset-y-0 right-0 flex items-center ps-2 pr-3  text-slate-300",
                svg: "h-5 w-5 text-slate-400 dark:text-gray-400 ",
            },
        },
        input: {
            base: "block w-full border disabled:cursor-not-allowed disabled:opacity-50",
            sizes: {
                sm: "p-2 sm:text-xs",
                md: "p-2.5 text-sm",
                lg: "p-4 sm:text-base",
            },
            gray: "border-blue-300 bg-blue-50 text-blue-900 placeholder-blue-700 focus:border-blue-500 focus:ring-blue-500 dark:border-blue-400 dark:bg-blue-100 dark:focus:border-blue-500 dark:focus:ring-blue-500",
        },

        popup: {
            root: {
                base: "absolute bottom-20 z-50 block pt-2",
                inline: "relative top-0 z-auto",
                inner: "inline-block rounded-lg bg-white p-4 shadow-lg dark:bg-gray-700",
            },
            header: {
                base: "",
                title: "px-2 py-3 text-center font-semibold text-gray-900 dark:text-white",
                selectors: {
                    base: "mb-2 flex justify-between",
                    button: {
                        base: "rounded-lg bg-white px-5 py-2.5 text-sm font-semibold text-gray-900 hover:bg-gray-100 focus:outline-none focus:ring-2 focus:ring-gray-200 dark:bg-gray-700 dark:text-white dark:hover:bg-gray-600",
                        prev: "",
                        next: "",
                        view: "",
                    },
                },
            },
            view: {
                base: "p-1",
            },
            footer: {
                base: "mt-2 flex space-x-2",
                button: {
                    base: "w-full rounded-lg px-5 py-2 text-center text-sm font-medium focus:ring-4 focus:ring-blue-300",
                    today: "bg-blue-700 text-white hover:bg-blue-800 dark:bg-blue-600 dark:hover:bg-blue-700",
                    clear: "border border-gray-300 bg-white text-gray-900 hover:bg-gray-100 dark:border-gray-600 dark:bg-gray-700 dark:text-white dark:hover:bg-gray-600",
                },
            },
        },
        views: {
            days: {
                header: {
                    base: "mb-1 grid grid-cols-7",
                    title: "h-6 text-center text-sm font-medium leading-6 text-gray-500 dark:text-gray-400",
                },
                items: {
                    base: "grid w-64 grid-cols-7",
                    item: {
                        base: "block flex-1 cursor-pointer rounded-lg border-0 text-center text-sm font-semibold leading-9 text-gray-900 hover:bg-gray-100 dark:text-white dark:hover:bg-gray-600",
                        selected: "bg-blue-700 text-white hover:bg-blue-600",
                        disabled: "text-gray-500",
                    },
                },
            },
            months: {
                items: {
                    base: "grid w-64 grid-cols-4",
                    item: {
                        base: "block flex-1 cursor-pointer rounded-lg border-0 text-center text-sm font-semibold leading-9 text-gray-900 hover:bg-gray-100 dark:text-white dark:hover:bg-gray-600",
                        selected: "bg-cyan-700 text-white hover:bg-cyan-600",
                        disabled: "text-gray-500",
                    },
                },
            },
            years: {
                items: {
                    base: "grid w-64 grid-cols-4",
                    item: {
                        base: "block flex-1 cursor-pointer rounded-lg border-0 text-center text-sm font-semibold leading-9 text-gray-900 hover:bg-gray-100 dark:text-white dark:hover:bg-gray-600",
                        selected: "bg-cyan-700 text-white hover:bg-cyan-600",
                        disabled: "text-gray-500",
                    },
                },
            },
            decades: {
                items: {
                    base: "grid w-64 grid-cols-4",
                    item: {
                        base: "block flex-1 cursor-pointer rounded-lg border-0 text-center text-sm font-semibold leading-9 text-gray-900 hover:bg-gray-100 dark:text-white dark:hover:bg-gray-600",
                        selected: "bg-cyan-700 text-white hover:bg-cyan-600",
                        disabled: "text-gray-500",
                    },
                },
            },
        },
    };

    const [isCheckAll, setIsCheckAll] = useState(false);

    useEffect(() => {
        if (isCheckAll) {
            $(".rowCheckBox").prop("checked", true);
        } else {
            $(".rowCheckBox").prop("checked", false);
        }
    }, [isCheckAll]);

    return (
        <>
            <div className="m-2">
                <Tabs
                    theme={tabBar_underline}
                    aria-label="Tabs with underline"
                    variant="underline"
                >
                    {/* Contacts */}
                    <Tabs.Item
                        className=""
                        active
                        title={
                            <div className="flex items-center gap-2">
                                Grab
                                <sup>
                                    <Badge
                                        className="text-white rounded-full bg-fuchsia-600"
                                        size="xs"
                                    >
                                        05
                                    </Badge>
                                </sup>
                            </div>
                        }
                        icon={MdOutlineBackHand}
                    >
                        <Grab></Grab>
                    </Tabs.Item>
                    {/* Call */}
                    <Tabs.Item
                        title={
                            <div className="flex items-center gap-2">
                                Split
                                <sup>
                                    <Badge
                                        className="text-white rounded-full bg-sky-600"
                                        size="xs"
                                    >
                                        05
                                    </Badge>
                                </sup>
                            </div>
                        }
                        icon={MdCallSplit}
                    >
                        <Split />
                    </Tabs.Item>
                    {/* WhatsApp */}
                    <Tabs.Item
                        className=""
                        active
                        title={
                            <div className="flex items-center gap-2">
                                Merge
                                <sup>
                                    <Badge
                                        className="text-white bg-green-600 rounded-full"
                                        size="xs"
                                    >
                                        05
                                    </Badge>
                                </sup>
                            </div>
                        }
                        icon={AiOutlineMergeCells}>
                        <Merge />
                    </Tabs.Item>
                    {/* SMS */}
                    <Tabs.Item
                        className=""
                        active
                        title={
                            <div className="flex items-center gap-2">
                                Subscription
                                <sup>
                                    <Badge
                                        className="text-black bg-yellow-300 rounded-full"
                                        size="xs"
                                    >
                                        05
                                    </Badge>
                                </sup>
                            </div>
                        }
                        icon={HiOutlineClock}>
                        <Subscription>
                        </Subscription>
                    </Tabs.Item>
                    {/* Email */}
                    <Tabs.Item
                        className=""
                        active
                        title={
                            <div className="flex items-center gap-2">
                                Invoice
                                <sup>
                                    <Badge
                                        className="text-white bg-red-600 rounded-full"
                                        size="xs"
                                    >
                                        05
                                    </Badge>
                                </sup>
                            </div>
                        }
                        icon={MdOutlineInventory}>
                        <Invoice />

                    </Tabs.Item>
                    <Tabs.Item
                        className=""
                        active
                        title={
                            <div className="flex items-center gap-2">
                                Transaction
                                <sup>
                                    <Badge
                                        className="text-white bg-red-600 rounded-full"
                                        size="xs"
                                    >
                                        05
                                    </Badge>
                                </sup>
                            </div>
                        }
                        icon={MdPointOfSale}>
                        <div>
                            <Transaction />

                        </div>
                    </Tabs.Item>
                </Tabs>
                <Modal
                    show={openMissedPunchModal}
                    onClose={() => setMissedPunchOpenModal(false)}
                >
                    <ModalHeader className="p-2">
                        <div className="flex items-center gap-2">
                            <MdCallSplit className="text-slate-400" />
                            <h4 className="text-lg">Lead Split</h4>
                        </div>
                    </ModalHeader>
                    <ModalBody className="px-4 py-3 rounded-b-lg bg-slate-100">
                        <LeadSplit></LeadSplit>
                    </ModalBody>
                </Modal>
            </div>
        </>
    );
}

