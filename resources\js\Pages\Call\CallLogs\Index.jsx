import React from 'react'
import SideMenu from './SideMenu'
import { <PERSON><PERSON><PERSON>Forwarded, LuPhoneOutgoing } from 'react-icons/lu'
import { Table, TableBody, TableCell, TableHead, TableHeadCell, TableRow } from 'flowbite-react';
import { table_custom } from '@/Pages/Helpers/DesignHelper';
import { Link } from '@inertiajs/react';
import { PiCaretUpDownBold } from 'react-icons/pi';
import { MdOutlinePhoneMissed } from 'react-icons/md';
import { FiPhoneCall } from 'react-icons/fi';
export default function Index() {
    return (
        <>
            <div className="p-2 overflow-hidden">
                <div
                    className="grid grid-cols-12 gap-2 p-0 border-none grid-2 flow-row-dense md:grid-cols-12 sm:grid-cols-1 
                bg-slate-100 mt-2"
                >
                    <div className="hidden xl:col-span-3 lg:col-span-3 md:col-span-3 sm:col-span-3 lg:flex ">
                        <SideMenu></SideMenu>
                    </div>
                    <div className="relative pt-0 dark:text-gray-400 lg:p-0 xl:col-span-9 lg:col-span-9 md:col-span-12 col-span-full">
                        <div className='bg-white rounded-lg rounded-b-none p-1.5 border'>
                            <div className='flex items-center gap-2'>
                                <div className="bg-orange-500 text-white p-1 text-base size-9 flex items-center justify-center rounded-full">
                                    K
                                </div>
                                <div>
                                    <span className="text-sm">Prachi (5682)</span>
                                    <div className="text-sm flex items-center gap-1 ">
                                        <LuPhoneOutgoing className="text-blue-700" />
                                        <span className="text-xs">19 sec ago</span>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div className="h-full mt-2">
                            {/* documents table */}
                            <div className="overflow-x-auto bg-white border rounded-lg text-nowrap">
                                <Table hoverable theme={table_custom}>
                                    <TableHead className="bg-slate-100">
                                        <TableHeadCell>
                                        </TableHeadCell>

                                        <TableHeadCell>
                                            <Link
                                            // href={route(
                                            //     "whatsapp.campaign.index",
                                            //     {
                                            //         column: "startTime",
                                            //         sort:
                                            //             getData.sort ==
                                            //             "asc"
                                            //                 ? "desc"
                                            //                 : "asc",
                                            //         perPage:
                                            //             getData.perPage,
                                            //     }
                                            // )}
                                            >
                                                <div className="flex items-center justify-between gap-2">
                                                    <h3>Date & Time</h3>
                                                    <PiCaretUpDownBold
                                                    // className={
                                                    //     (getData.column ==
                                                    //     "startTime"
                                                    //         ? "  text-gray-900 dark:text-gray-200 "
                                                    //         : "text-gray-600 dark:text-gray-400 ") +
                                                    //     "  cursor-pointer "
                                                    // }
                                                    />
                                                </div>
                                            </Link>
                                        </TableHeadCell>

                                        <TableHeadCell>
                                            <Link>
                                                <div className="flex items-center justify-between gap-2">
                                                    <h3>Duration</h3>
                                                </div>
                                            </Link>
                                        </TableHeadCell>
                                        <TableHeadCell>
                                        </TableHeadCell>
                                    </TableHead>
                                    <TableBody className="divide-y">
                                        <TableRow
                                            className="items-center bg-white dark:border-gray-700 dark:bg-gray-800"
                                        // key={"campaign" + k}
                                        >
                                            <TableCell>
                                                <MdOutlinePhoneMissed className='text-red-600' />
                                            </TableCell>
                                            <TableCell className="text-blue-600 ">
                                                <div className="flex flex-col">
                                                    <span className="font-medium">
                                                        11/1/2024
                                                    </span>
                                                    <span className="text-xs text-slate-500">
                                                        5:21
                                                    </span>
                                                    {/* {convertDateFormat(
                                                    campaign.startTime
                                                )} */}
                                                </div>
                                            </TableCell>

                                            <TableCell
                                                // onClick={() => {
                                                //     setIsOpenDetail(true);
                                                //     setCampaignID({
                                                //         id: "73",
                                                //         name: "Dana Gates",
                                                //         status: "Terminated",
                                                //     });
                                                // }}
                                                className="cursor-pointer "
                                            >
                                                Dana Gates
                                                {/* {campaign.name} */}
                                            </TableCell>

                                            <TableCell>
                                                <audio className="h-12" autoplay="autoplay" controls="controls" id="player">
                                                    <source src="http://www.issilissinew.com/zindorg/1/mp3/2014/Mukunda/128/02%20-%20Daredumdadum%20%5bwww.AtoZmp3.in%5d.mp3.ogg" />
                                                    <source src="http://www.issilissinew.com/zindorg/1/mp3/2014/Mukunda/128/02%20-%20Daredumdadum%20%5bwww.AtoZmp3.in%5d.mp3" />
                                                    <p> Your browser doesn't support the audio tag </p>
                                                </audio>
                                                {/* <div>
                                                                                                <button onclick="document.getElementById('player').play()">Play</button>
                                                                                                <button onclick="document.getElementById('player').pause()">Pause</button>
                                                                                                <button onclick="document.getElementById('player').volume+ = 0.5">Vol+ </button>
                                                                                                <button onclick="document.getElementById('player').volume- = 0.5">Vol- </button>    
                                                                                            </div>   */}
                                            </TableCell>
                                        </TableRow>
                                        <TableRow
                                            className="items-center bg-white dark:border-gray-700 dark:bg-gray-800"
                                        // key={"campaign" + k}
                                        >
                                            <TableCell>
                                                <FiPhoneCall className='text-green-600' />
                                            </TableCell>
                                            <TableCell className="text-blue-600 ">
                                                <div className="flex flex-col">
                                                    <span className="font-medium">
                                                        11/1/2024
                                                    </span>
                                                    <span className="text-xs text-slate-500">
                                                        5:21
                                                    </span>
                                                    {/* {convertDateFormat(
                                                    campaign.startTime
                                                )} */}
                                                </div>
                                            </TableCell>

                                            <TableCell
                                                // onClick={() => {
                                                //     setIsOpenDetail(true);
                                                //     setCampaignID({
                                                //         id: "73",
                                                //         name: "Dana Gates",
                                                //         status: "Terminated",
                                                //     });
                                                // }}
                                                className="cursor-pointer "
                                            >
                                                Dana Gates
                                                {/* {campaign.name} */}
                                            </TableCell>

                                            <TableCell>
                                                <audio className="h-12" autoplay="autoplay" controls="controls" id="player">
                                                    <source src="http://www.issilissinew.com/zindorg/1/mp3/2014/Mukunda/128/02%20-%20Daredumdadum%20%5bwww.AtoZmp3.in%5d.mp3.ogg" />
                                                    <source src="http://www.issilissinew.com/zindorg/1/mp3/2014/Mukunda/128/02%20-%20Daredumdadum%20%5bwww.AtoZmp3.in%5d.mp3" />
                                                    <p> Your browser doesn't support the audio tag </p>
                                                </audio>
                                                {/* <div>
                                                                                                <button onclick="document.getElementById('player').play()">Play</button>
                                                                                                <button onclick="document.getElementById('player').pause()">Pause</button>
                                                                                                <button onclick="document.getElementById('player').volume+ = 0.5">Vol+ </button>
                                                                                                <button onclick="document.getElementById('player').volume- = 0.5">Vol- </button>    
                                                                                            </div>   */}
                                            </TableCell>
                                        </TableRow>
                                        <TableRow
                                            className="items-center bg-white dark:border-gray-700 dark:bg-gray-800"
                                        // key={"campaign" + k}
                                        >
                                            <TableCell>
                                                <LuPhoneForwarded className='text-blue-600' />
                                            </TableCell>
                                            <TableCell className="text-blue-600 ">
                                                <div className="flex flex-col">
                                                    <span className="font-medium">
                                                        11/1/2024
                                                    </span>
                                                    <span className="text-xs text-slate-500">
                                                        5:21
                                                    </span>
                                                    {/* {convertDateFormat(
                                                    campaign.startTime
                                                )} */}
                                                </div>
                                            </TableCell>

                                            <TableCell
                                                // onClick={() => {
                                                //     setIsOpenDetail(true);
                                                //     setCampaignID({
                                                //         id: "73",
                                                //         name: "Dana Gates",
                                                //         status: "Terminated",
                                                //     });
                                                // }}
                                                className="cursor-pointer "
                                            >
                                                Dana Gates
                                                {/* {campaign.name} */}
                                            </TableCell>

                                            <TableCell>
                                                <audio className="h-12" autoplay="autoplay" controls="controls" id="player">
                                                    <source src="http://www.issilissinew.com/zindorg/1/mp3/2014/Mukunda/128/02%20-%20Daredumdadum%20%5bwww.AtoZmp3.in%5d.mp3.ogg" />
                                                    <source src="http://www.issilissinew.com/zindorg/1/mp3/2014/Mukunda/128/02%20-%20Daredumdadum%20%5bwww.AtoZmp3.in%5d.mp3" />
                                                    <p> Your browser doesn't support the audio tag </p>
                                                </audio>
                                                {/* <div>
                                                                                                <button onclick="document.getElementById('player').play()">Play</button>
                                                                                                <button onclick="document.getElementById('player').pause()">Pause</button>
                                                                                                <button onclick="document.getElementById('player').volume+ = 0.5">Vol+ </button>
                                                                                                <button onclick="document.getElementById('player').volume- = 0.5">Vol- </button>    
                                                                                            </div>   */}
                                            </TableCell>
                                        </TableRow>
                                    </TableBody>
                                </Table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </>
    )
}

