import {
    button_custom,
    customDrawer,
    input_custom,
    textarea_custom,
} from "@/Pages/Helpers/DesignHelper";
import { useForm } from "@inertiajs/react";
import EmojiPicker from "emoji-picker-react";
import {
    Button,
    Checkbox,
    Drawer,
    FileInput,
    Label,
    Textarea,
    TextInput,
} from "flowbite-react";
import { useEffect, useRef, useState } from "react";
import {
    FaBold,
    FaItalic,
    FaRegFaceSmileBeam,
    FaRegNoteSticky,
    FaStrikethrough,
} from "react-icons/fa6";
import { HiOutlineTemplate } from "react-icons/hi";
import { MdOutlineFingerprint } from "react-icons/md";
import { VscSend } from "react-icons/vsc";
import Templates from "./Templates";
import { CgAttachment } from "react-icons/cg";

function TextBox({ handleChange = null, sendTo = null }) {
    // console.log(sendTo);
    // // useEffect(() => {
    // // }, []);
    const [isOpenTemplates, setIsOpenTemplates] = useState(false);
    const [openEmoji, setOpenEmoji] = useState(false);
    const textareaRef = useRef(null);
    const [isScheduled, setIsScheduled] = useState(false);
    const [messageDatetime, setMessageDatetime] = useState(new Date());

    useEffect(() => {
        setMessageDatetime(new Date());
    }, [isScheduled]);

    const { data, setData, post, reset, processing, errors } = useForm({
        message: "",
        attachment: [],
        isScheduled: isScheduled,
        ScheduleTime: messageDatetime,
        template_id: "",
        sendTo: sendTo,
    });
    // function handleMsgSend(e) {
    //     e.preventDefault();
    //     console.log(data);

    //     post(route("whatsappB.chats.store"), {
    //         onSuccess: () => {
    //             reset();
    //         },
    //     });
    // }
    const textFormat = (formatAs) => {
        const textarea = textareaRef.current;
        const start = textarea.selectionStart;
        const end = textarea.selectionEnd;

        // Get the selected text
        const selectedText = data.message.substring(start, end);

        // Wrap the selected text in ** to make it bold in markdown
        const boldText = `${formatAs + selectedText + formatAs}`;

        // Update the message state with the bold text
        setData(
            "message",
            data.message.substring(0, start) +
                boldText +
                data.message.substring(end)
        );

        // Set the cursor position after the inserted bold text
        setTimeout(() => {
            textarea.setSelectionRange(
                start + boldText.length,
                start + boldText.length
            );
            textarea.focus();
        }, 0);
    };

    const sendMessage = () => {
        // console.log(data);
        post(route("whatsappB.chats.store"), {
            onSuccess: (res) => {
                reset();
                handleChange();
            },
            onError: (res) => {
            },
        });
    };
    const fileInputRef = useRef(null);
    const handleClick = () => {
        fileInputRef.current.click();
    };
    // const [fileExtension, setFileExtension] = useState();
    // const [images, setImages] = useState([]);

    const handleFileChange = (event) => {
        const selectedFiles = Array.from(event.target.files);

        // Filter images and create preview URLs
        const imageFiles = selectedFiles
            // .filter((file) => file.type.startsWith("image/"))
            .map((file) => {
                return {
                    file,
                    previewUrl: URL.createObjectURL(file),
                };
            });
        setData("attachment", imageFiles);
    };

    const removeImage = (index) => {
        const result = Object.values(data.attachment);
        delete result[index];
        let filteredArray = result.filter(
            (item) => item !== null && item !== undefined
        );
        setData("attachment", filteredArray);
    };
    const validImageExtensions = ["jpeg", "jpg", "webp", "gif"];
    function checkImage(fileName) {
        const extension = fileName.split(".").pop();
        return validImageExtensions.includes(extension);
    }

    return (
        <div className="bg-[#F0EEED] p-2">
            {/*-------- Message -----------*/}
            <div className="">
                <div className="">
                    {openEmoji && (
                        <div
                            className="absolute z-50 "
                            style={{ top: "25%", bottom: "25%" }}
                        >
                            <EmojiPicker
                                open={openEmoji}
                                lazyLoadEmojis={true}
                                onEmojiClick={(emojiObject) =>
                                    setData(
                                        "message",
                                        (data.message += emojiObject.emoji)
                                    )
                                }
                            />
                        </div>
                    )}

                    <div className="p-2 border border-gray-300 rounded-md bg-gray-50">
                        <div className="grid grid-cols-3 md:grid-cols-5 lg:grid-cols-5 xl:grid-cols-7">
                            {data.attachment.map((item, k) => {
                                return (
                                    <div
                                        className="flex justify-center"
                                        key={"images-item" + k}
                                    >
                                        <div className="relative w-full p-1">
                                            {checkImage(item.file.name) ? (
                                                <img
                                                    src={window.URL.createObjectURL(
                                                        item.file
                                                    )}
                                                    className="w-full my-2 rounded-md "
                                                />
                                            ) : (
                                                <div className="p-3 text-3xl text-center text-gray-300">{`.${item.file.name
                                                    .split(".")
                                                    .pop()}`}</div>
                                            )}
                                            <div className="absolute top-0 end-0">
                                                <Button
                                                    size="xs"
                                                    color="failure"
                                                    className="rounded-full opacity-80"
                                                    onClick={() =>
                                                        removeImage(k)
                                                    }
                                                    // gradientDuoTone="purpleToBlue"
                                                >
                                                    x
                                                </Button>
                                            </div>
                                        </div>
                                    </div>
                                );
                            })}
                        </div>

                        <Textarea
                            theme={textarea_custom}
                            name="message"
                            placeholder="Type Message here..."
                            rows={4}
                            id="myTextarea"
                            ref={textareaRef}
                            value={data.message}
                            onFocus={() => setOpenEmoji(false)}
                            color={errors.message ? "failure" : "gray"}
                            onChange={(e) => setData("message", e.target.value)}
                            className="border-0 focus:ring-white"
                            helperText={errors.message && errors.message}
                        />

                        <div className="flex flex-col flex-wrap justify-between gap-1 flex-center lg:flex-row">
                            <div className="flex flex-wrap items-center gap-1 space-x-1">
                                <div className="">
                                    <label htmlFor="message-file-input">
                                        <Button
                                            className="px-0"
                                            size="xs"
                                            color="gray"
                                            theme={button_custom}
                                            onClick={() => handleClick()}
                                        >
                                            <div className="flex items-center gap-1">
                                                <CgAttachment className="text-slate-500" />
                                                <span className="text-sm">
                                                    Add File
                                                </span>
                                            </div>
                                        </Button>
                                    </label>
                                    <FileInput
                                        id="message-file-input"
                                        ref={fileInputRef}
                                        className="hidden"
                                        onChange={(e) => handleFileChange(e)}
                                    />
                                </div>
                                <div className="">
                                    <Button
                                        className="px-0"
                                        size="xs"
                                        color="gray"
                                        onClick={() => setOpenEmoji(!openEmoji)}
                                    >
                                        <FaRegFaceSmileBeam />
                                    </Button>
                                </div>
                                <div className="">
                                    <Button
                                        className="px-0"
                                        size="xs"
                                        color="gray"
                                        id="boldButton"
                                        onClick={() => textFormat("*")}
                                    >
                                        <FaBold />
                                    </Button>
                                </div>
                                <div className="">
                                    <Button
                                        className="px-0"
                                        size="xs"
                                        color="gray"
                                        id="italicButton"
                                        onClick={() => textFormat("_")}
                                    >
                                        <FaItalic />
                                    </Button>
                                </div>
                                <div className="">
                                    <Button
                                        className="px-0"
                                        size="xs"
                                        color="gray"
                                        id="strike"
                                        onClick={() => textFormat("~")}
                                    >
                                        <FaStrikethrough />
                                    </Button>
                                </div>
                                <div className=""></div>
                            </div>

                            <div className="flex flex-wrap gap-2">
                                <div className="">
                                    <select
                                        className="rounded-md text-sm border-gray-300 p-1 px-3 hover:bg-gray-50"
                                        name="cars"
                                        id="cars"
                                    >
                                        <option value="volvo">Gateway</option>
                                        <option value="saab">Saab</option>
                                        <option value="opel">Opel</option>
                                        <option value="audi">
                                            dfdfdfsfsfdfs
                                        </option>
                                    </select>
                                </div>
                                <Button
                                    theme={button_custom}
                                    size="xs"
                                    // outline
                                    className="rounded-lg bg-gray-50"
                                    color="gray"
                                    id="varName"
                                    onClick={() => setIsOpenTemplates(true)}
                                >
                                    <div className="flex items-center gap-1 text-sm">
                                        <FaRegNoteSticky className="text-lg text-sla" />
                                        Select Template
                                    </div>
                                </Button>

                                <Button
                                    type="submit"
                                    size="xs"
                                    color="blue"
                                    id="var1"
                                    onClick={sendMessage}
                                >
                                    <div className="flex items-center gap-1 text-sm">
                                        <VscSend className="text-lg" />
                                        {isScheduled ? "Schedule" : "Send Now"}
                                    </div>
                                </Button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <span className="text-sm text-red-600 ms-4">
                Maximum upload file size: 10 MB.
            </span>

            {isOpenTemplates && (
                <Drawer
                    theme={customDrawer}
                    open={isOpenTemplates}
                    onClose={() => setIsOpenTemplates(false)}
                    position="right"
                    className="w-full xl:w-10/12 lg:w-full md:w-full"
                >
                    <DrawerHeader
                        titleIcon={HiOutlineTemplate}
                        title="Template library"
                    />
                    <DrawerItems>
                        <Templates />
                    </DrawerItems>
                </Drawer>
            )}
        </div>
    );
}

export default TextBox;



