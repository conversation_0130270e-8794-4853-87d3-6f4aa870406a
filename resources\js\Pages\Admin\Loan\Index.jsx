import { Head } from "@inertiajs/react";
import { But<PERSON>, Checkbox, DrawerItems, Table, Tooltip } from "flowbite-react";
import $ from "jquery";
import { useState } from "react";
import { BiCheckDouble } from "react-icons/bi";
import { CiExport } from "react-icons/ci";
import { FaEye } from "react-icons/fa";
import { IoClose, IoCloseSharp } from "react-icons/io5";
import { MdDeleteOutline } from "react-icons/md";
import { TbColumns3 } from "react-icons/tb";
import AddLoan from "./AddLoan";
import Main from "@/Layouts/Main";
import { button_custom, customDrawer, table_custom } from "../../Helpers/DesignHelper";
import SideMenu from "../SideMenu";
import { IoMdAdd } from "react-icons/io";

function Index() {
    const [isCheckAll, setIsCheckAll] = useState(false);
    // ---------------- View --------------------
    const [isLoanOpen, setIsLoanOpen] = useState(false);
    const handleLoanClose = () => setIsLoanOpen(false);

    return (
        <Main>
            <Head title="User" />
            <div className="flex ">
                <div className="relative flex flex-col justify-between w-full dark:text-gray-400 lg:p-0">
                    <div>
                        <div className="relative p-2 overflow-hidden">
                            <div className="grid grid-flow-row-dense grid-cols-12 gap-2 p-0 border-none md:grid-cols-12 sm:grid-cols-1 bg-slate-100">
                                <div className="hidden lg:col-span-2 md:col-span-3 sm:col-span-3 lg:flex">
                                    <div className="bg-white shadow-sm rounded-lg border dark:bg-gray-900 w-full overflow-auto min-h-[90vh] max-h-[90vh]">
                                        <SideMenu />
                                    </div>
                                </div>
                                <div className="relative pt-0 dark:text-gray-400 lg:p-0 lg:col-span-10 md:col-span-12 col-span-full">
                                    <div className="mb-2 col-span-full">
                                        {/* <UserRoleTabBar />   */}
                                    </div>
                                    <div className="min-h-[80vh] max-h-[80vh] bg-white border rounded-lg">
                                        <div className="flex justify-between p-2">
                                            <div className="flex gap-2">
                                                <Button.Group>
                                                    <Button
                                                        // className="border rounded-s-none"
                                                        size="xs"
                                                        color="gray"
                                                        theme={button_custom}
                                                    >
                                                        <div className="flex items-center gap-1">
                                                            <MdDeleteOutline className="text-slate-500" />
                                                            <span className="text-xs">
                                                                Delete
                                                            </span>
                                                        </div>
                                                    </Button>
                                                    <Button
                                                        theme={button_custom}
                                                        size="xs"
                                                        color="gray"
                                                        id="dropdownInformationButton"
                                                        data-dropdown-toggle="dropdownNotification"
                                                        type="button"
                                                    >
                                                        <div className="flex items-center gap-1">
                                                            <TbColumns3 className="text-slate-500" />
                                                            <span className="text-xs">
                                                                Column
                                                            </span>
                                                        </div>
                                                    </Button>
                                                </Button.Group>
                                                <Button
                                                    theme={button_custom}
                                                    size="xs"
                                                    color="gray"
                                                // className="border-e-0 rounded-e-none"
                                                >
                                                    <div className="flex items-center gap-1">
                                                        <CiExport className="text-slate-500" />
                                                        <span className="text-xs">
                                                            Export
                                                        </span>
                                                    </div>
                                                </Button>

                                            </div>
                                            <div className="">
                                                <Button
                                                    theme={button_custom}
                                                    size="xs"
                                                    color="gray"

                                                    onClick={() =>
                                                        setIsLoanOpen(
                                                            true
                                                        )
                                                    }
                                                >
                                                    <div className="flex items-center gap-1">
                                                        <IoMdAdd className="text-slate-500" />
                                                        <span className="text-xs">
                                                            Add Loan
                                                        </span>
                                                    </div>
                                                </Button>
                                                {/* Dropdown menu */}
                                                <div
                                                    id="dropdownNotification"
                                                    className="z-20 border shadow-lg hidden w-full max-w-sm bg-white divide-y divide-gray-100 rounded-lg  dark:bg-gray-800 dark:divide-gray-700"
                                                    aria-labelledby="dropdownNotificationButton"
                                                >
                                                    <div className="block px-4 py-2 font-medium text-center text-gray-700 rounded-t-lg bg-gray-50 dark:bg-gray-800 dark:text-white">
                                                        <div className="flex justify-between">
                                                            <div className="">
                                                                <div className="group  text-gray-400 p-1 flex items-center gap-1">
                                                                    <svg
                                                                        xmlns="http://www.w3.org/2000/svg"
                                                                        className=" fill-slate-400  "
                                                                        height="24px"
                                                                        viewBox="0 0 24 24"
                                                                        width="24px"
                                                                    >
                                                                        <rect
                                                                            fill="none"
                                                                            height="24"
                                                                            width="24"
                                                                        />
                                                                        <path d="M20,4H4C2.9,4,2,4.9,2,6v12c0,1.1,0.9,2,2,2h16c1.1,0,2-0.9,2-2V6C22,4.9,21.1,4,20,4z M8,18H4V6h4V18z M14,18h-4V6h4V18z M20,18h-4V6h4V18z" />
                                                                    </svg>
                                                                    Column
                                                                </div>
                                                            </div>
                                                            <div className="">
                                                                <button className="p-1 flex text-blue-600">
                                                                    <svg
                                                                        xmlns="http://www.w3.org/2000/svg"
                                                                        height="24px"
                                                                        viewBox="0 0 24 24"
                                                                        width="24px"
                                                                        className="fill-blue-600"
                                                                    >
                                                                        <g>
                                                                            <path
                                                                                d="M0,0h24v24H0V0z"
                                                                                fill="none"
                                                                            />
                                                                        </g>
                                                                        <g>
                                                                            <g>
                                                                                <path d="M6,13c0-1.65,0.67-3.15,1.76-4.24L6.34,7.34C4.9,8.79,4,10.79,4,13c0,4.08,3.05,7.44,7,7.93v-2.02 C8.17,18.43,6,15.97,6,13z M20,13c0-4.42-3.58-8-8-8c-0.06,0-0.12,0.01-0.18,0.01l1.09-1.09L11.5,2.5L8,6l3.5,3.5l1.41-1.41 l-1.08-1.08C11.89,7.01,11.95,7,12,7c3.31,0,6,2.69,6,6c0,2.97-2.17,5.43-5,5.91v2.02C16.95,20.44,20,17.08,20,13z" />
                                                                            </g>
                                                                        </g>
                                                                    </svg>
                                                                    Reset
                                                                </button>
                                                            </div>
                                                        </div>
                                                    </div>
                                                    <div className="divide-y divide-gray-100 dark:divide-gray-700">
                                                        <div className="flex flex-col">
                                                            <div className="relative overflow-x-auto shadow-md sm:rounded-lg ">
                                                                <table className=" w-full text-sm text-left rtl:text-right text-gray-500 dark:text-gray-400">
                                                                    <thead className="text-xs text-gray-700 uppercase dark:text-gray-400">
                                                                        <tr>
                                                                            <th className="p-2 bg-gray-50 dark:bg-gray-800 w-14 text-center ">
                                                                                List
                                                                            </th>
                                                                            <th className="p-2  bg-gray-50 dark:bg-gray-800 w-14 text-center">
                                                                                Details
                                                                            </th>
                                                                            <th className="p-2"></th>
                                                                        </tr>
                                                                    </thead>
                                                                    <tbody className=" overflow-auto">
                                                                        <tr className="border-b border-gray-200 dark:border-gray-700">
                                                                            <td className="text-center max-w-max p-2 font-medium text-gray-900 whitespace-nowrap bg-gray-50 dark:text-white dark:bg-gray-800">
                                                                                <Checkbox></Checkbox>
                                                                            </td>
                                                                            <td className="text-center p-2 font-medium text-gray-900 whitespace-nowrap bg-gray-50 dark:text-white dark:bg-gray-800">
                                                                                <Checkbox></Checkbox>
                                                                            </td>
                                                                            <td className=" max-w-max px-6 py-4">
                                                                                Name
                                                                            </td>
                                                                        </tr>
                                                                        <tr className="border-b border-gray-200 dark:border-gray-700">
                                                                            <td className="text-center  max-w-max p-2 font-medium text-gray-900 whitespace-nowrap bg-gray-50 dark:text-white dark:bg-gray-800">
                                                                                <Checkbox></Checkbox>
                                                                            </td>
                                                                            <td className="text-center p-2 font-medium text-gray-900 whitespace-nowrap bg-gray-50 dark:text-white dark:bg-gray-800">
                                                                                <Checkbox></Checkbox>
                                                                            </td>
                                                                            <td className=" max-w-max px-6 py-4">
                                                                                Name
                                                                            </td>
                                                                        </tr>
                                                                        <tr className="border-b border-gray-200 dark:border-gray-700">
                                                                            <td className="text-center  max-w-max p-2 font-medium text-gray-900 whitespace-nowrap bg-gray-50 dark:text-white dark:bg-gray-800">
                                                                                <Checkbox></Checkbox>
                                                                            </td>
                                                                            <td className="text-center p-2 font-medium text-gray-900 whitespace-nowrap bg-gray-50 dark:text-white dark:bg-gray-800">
                                                                                <Checkbox></Checkbox>
                                                                            </td>
                                                                            <td className="max-w-max px-6 py-4">
                                                                                Name
                                                                            </td>
                                                                        </tr>
                                                                        <tr className="border-b border-gray-200 dark:border-gray-700">
                                                                            <td className="text-center max-w-max p-2 font-medium text-gray-900 whitespace-nowrap bg-gray-50 dark:text-white dark:bg-gray-800">
                                                                                <Checkbox></Checkbox>
                                                                            </td>
                                                                            <td className="text-center p-2 font-medium text-gray-900 whitespace-nowrap bg-gray-50 dark:text-white dark:bg-gray-800">
                                                                                <Checkbox></Checkbox>
                                                                            </td>
                                                                            <td className="max-w-max px-6 py-4">
                                                                                Name
                                                                            </td>
                                                                        </tr>
                                                                    </tbody>
                                                                </table>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>

                                        <div className="overflow-x-auto rounded-lg border">
                                            <Table
                                                hoverable
                                                theme={table_custom}
                                            >
                                                <Table.Head className=" bg-slate-100">
                                                    <Table.HeadCell>
                                                        <Checkbox
                                                            color={"blue"}
                                                            checked={isCheckAll}
                                                            onChange={() =>
                                                                setIsCheckAll(
                                                                    !isCheckAll
                                                                )
                                                            }
                                                        />
                                                    </Table.HeadCell>

                                                    <Table.HeadCell>
                                                        <div className="flex items-center justify-between">
                                                            <h3>Date & Time</h3>
                                                            <svg
                                                                xmlns="http://www.w3.org/2000/svg"
                                                                height="16px"
                                                                viewBox="0 0 24 24"
                                                                width="16px"
                                                                className="fill-gray-600"
                                                            >
                                                                <path
                                                                    d="M0 0h24v24H0V0z"
                                                                    fill="none"
                                                                />
                                                                <path d="M12 5.83L15.17 9l1.41-1.41L12 3 7.41 7.59 8.83 9 12 5.83zm0 12.34L8.83 15l-1.41 1.41L12 21l4.59-4.59L15.17 15 12 18.17z" />
                                                            </svg>
                                                        </div>
                                                    </Table.HeadCell>

                                                    <Table.HeadCell>
                                                        <div className="flex items-center justify-between">
                                                            <h3>Employee</h3>
                                                            <svg
                                                                xmlns="http://www.w3.org/2000/svg"
                                                                height="16px"
                                                                viewBox="0 0 24 24"
                                                                width="16px"
                                                                className="fill-gray-600"
                                                            >
                                                                <path
                                                                    d="M0 0h24v24H0V0z"
                                                                    fill="none"
                                                                />
                                                                <path d="M12 5.83L15.17 9l1.41-1.41L12 3 7.41 7.59 8.83 9 12 5.83zm0 12.34L8.83 15l-1.41 1.41L12 21l4.59-4.59L15.17 15 12 18.17z" />
                                                            </svg>
                                                        </div>
                                                    </Table.HeadCell>

                                                    <Table.HeadCell>
                                                        <h3>
                                                            Principal Amount
                                                        </h3>
                                                    </Table.HeadCell>

                                                    <Table.HeadCell>
                                                        <h3>Interest Rate</h3>
                                                    </Table.HeadCell>

                                                    <Table.HeadCell className="w-28">
                                                        <h3>Purpose of loan</h3>
                                                    </Table.HeadCell>

                                                    <Table.HeadCell>
                                                        <h3>Total loan</h3>
                                                    </Table.HeadCell>
                                                    <Table.HeadCell>
                                                        <div className="flex items-center justify-between">
                                                            <h3>Status</h3>
                                                            <svg
                                                                xmlns="http://www.w3.org/2000/svg"
                                                                height="16px"
                                                                viewBox="0 0 24 24"
                                                                width="16px"
                                                                className="fill-gray-600"
                                                            >
                                                                <path
                                                                    d="M0 0h24v24H0V0z"
                                                                    fill="none"
                                                                />
                                                                <path d="M12 5.83L15.17 9l1.41-1.41L12 3 7.41 7.59 8.83 9 12 5.83zm0 12.34L8.83 15l-1.41 1.41L12 21l4.59-4.59L15.17 15 12 18.17z" />
                                                            </svg>
                                                        </div>
                                                    </Table.HeadCell>
                                                    <Table.HeadCell>
                                                        <h3>Actions</h3>
                                                    </Table.HeadCell>
                                                </Table.Head>

                                                <Table.Body className="">
                                                    <Table.Row className="bg-white dark:border-gray-700 dark:bg-gray-800">
                                                        <Table.Cell>
                                                            <Checkbox
                                                                color={"blue"}
                                                                className="rowCheckBox"
                                                            />
                                                        </Table.Cell>

                                                        <Table.Cell className="font-medium text-gray-900 dark:text-white">
                                                            <div className="flex justify-between ">
                                                                <div className="flex flex-col w-20 truncate">
                                                                    <div className="text-sm text-blue-500 ">
                                                                        12/08/24
                                                                    </div>
                                                                    <div className="text-[12px]">
                                                                        2:24 PM
                                                                    </div>
                                                                </div>
                                                            </div>
                                                        </Table.Cell>
                                                        <Table.Cell>
                                                            <div className="flex flex-wrap items-center gap-2 text-nowrap w-fit">
                                                                <img
                                                                    src="https://img.freepik.com/premium-photo/profile-icon-white-background_941097-160810.jpg?w=50"
                                                                    alt=""
                                                                    className="rounded-full"
                                                                />
                                                                <div>
                                                                    <div className="text-sm text-blue-500 text-nowrap">
                                                                        Rohan
                                                                        Preet
                                                                        (2568)
                                                                    </div>
                                                                    <div className="text-[12px] text-nowrap">
                                                                        Sales
                                                                        Executive
                                                                    </div>
                                                                </div>
                                                            </div>
                                                        </Table.Cell>
                                                        <Table.Cell>
                                                            ₹ 100,000
                                                        </Table.Cell>
                                                        <Table.Cell>
                                                            12%
                                                        </Table.Cell>
                                                        <Table.Cell className="text-nowrap">
                                                            My sister's
                                                            wedding...
                                                        </Table.Cell>
                                                        <Table.Cell>
                                                            3
                                                        </Table.Cell>

                                                        <Table.Cell className="text-blue-500">
                                                            Running
                                                        </Table.Cell>

                                                        <Table.Cell>
                                                            <div className="flex justify-between gap-2">
                                                                <div className="flex gap-2">
                                                                    <Tooltip
                                                                        content="Reject"
                                                                        className="p-1 px-2 bg-slate-700"
                                                                    >
                                                                        <Button
                                                                            theme={
                                                                                button_custom
                                                                            }
                                                                            color="failure"
                                                                            size="xs"
                                                                        >
                                                                            {" "}
                                                                            <IoCloseSharp />
                                                                        </Button>
                                                                    </Tooltip>

                                                                    <Tooltip
                                                                        content="Approve"
                                                                        className="p-1 px-2 bg-slate-700"
                                                                    >
                                                                        <Button
                                                                            theme={
                                                                                button_custom
                                                                            }
                                                                            size="xs"
                                                                        >
                                                                            {" "}
                                                                            <BiCheckDouble />
                                                                        </Button>
                                                                    </Tooltip>

                                                                    <Tooltip
                                                                        content="View"
                                                                        className="p-1 px-2 bg-slate-700"
                                                                    >
                                                                        <Button
                                                                            color="blue"
                                                                            theme={
                                                                                button_custom
                                                                            }
                                                                            size="xs"
                                                                            onClick={() =>
                                                                                setIsLoanOpen(
                                                                                    true
                                                                                )
                                                                            }
                                                                        >
                                                                            {" "}
                                                                            <FaEye />
                                                                        </Button>
                                                                    </Tooltip>
                                                                    <Drawer
                                                                        theme={
                                                                            customDrawer
                                                                        }
                                                                        open={
                                                                            isLoanOpen
                                                                        }
                                                                        onClose={
                                                                            handleLoanClose
                                                                        }
                                                                        position="right"
                                                                        className="w-full p-0 lg:w-4/5"
                                                                    >
                                                                        <div className="flex items-center justify-between px-4 py-2 mb-2 bg-white">
                                                                            <div className="flex items-center gap-2">
                                                                                <img
                                                                                    className="w-12 h-12 rounded-full"
                                                                                    src="https://img.freepik.com/free-vector/businessman-character-avatar-isolated_24877-60111.jpg?t=st=1720614745~exp=1720618345~hmac=38ff343a9443982af738325ad4d54d1d39ba540be4c28013b03cf063347d255e&w=200"
                                                                                    alt=""
                                                                                />
                                                                                <div>
                                                                                    <h4 className="text-[16px] font-medium text-blue-600">
                                                                                        Abhishek
                                                                                        Chatterjee(DF25)
                                                                                    </h4>
                                                                                    <h6>
                                                                                        Sales
                                                                                        Manager
                                                                                    </h6>
                                                                                </div>
                                                                            </div>
                                                                            <button
                                                                                className="bg-transparent"
                                                                                onClick={
                                                                                    handleLoanClose
                                                                                }
                                                                            >
                                                                                <IoClose
                                                                                    className="text-[24px] text-slate-400
                                                                    "
                                                                                />
                                                                            </button>
                                                                        </div>
                                                                        <DrawerItems>
                                                                            <AddLoan></AddLoan>
                                                                        </DrawerItems>
                                                                    </Drawer>
                                                                </div>
                                                            </div>
                                                        </Table.Cell>
                                                    </Table.Row>
                                                </Table.Body>
                                            </Table>
                                        </div>
                                    </div>
                                    <div className="absolute bottom-0 w-full p-3 bg-white border rounded-b-lg float-end">
                                        <div className="flex flex-wrap justify-between gap-2 lg:gap-0 md:gap-0">
                                            <div className="flex items-center gap-3">
                                                <div className="text-sm text-gray-400 ">
                                                    Showing 1 to 25 of 62
                                                    entries
                                                </div>
                                                <div>
                                                    <select
                                                        id="countries"
                                                        className="block p-1 text-xs text-gray-900 border border-gray-300 rounded bg-gray-50 focus:ring-blue-500 focus:border-blue-500 ps-2 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-blue-500 dark:focus:border-blue-500"
                                                    >
                                                        <option
                                                            value={10}
                                                            defaultValue={10}
                                                        >
                                                            10
                                                        </option>
                                                        <option value={15}>
                                                            15
                                                        </option>
                                                        <option value={20}>
                                                            20
                                                        </option>
                                                        <option value={25}>
                                                            25
                                                        </option>
                                                        <option value={30}>
                                                            30
                                                        </option>
                                                    </select>
                                                </div>
                                            </div>
                                            <div className="flex">
                                                <Button
                                                    size="xs"
                                                    color="gray"
                                                    className="border-e-0 text-gray-400 px-2 rounded text-[12px] "
                                                >
                                                    Previous
                                                </Button>
                                                <Button
                                                    size="xs"
                                                    color="blue"
                                                    className="border text-white border-blue-600 bg-blue-600 px-2.5 rounded-none text-sm "
                                                >
                                                    1
                                                </Button>
                                                <Button
                                                    size="xs"
                                                    color="gray"
                                                    className="border text-blue-600 px-2.5 rounded-none text-[12px] "
                                                >
                                                    2
                                                </Button>
                                                <Button
                                                    size="xs"
                                                    color="gray"
                                                    className="border-s-0 text-blue-600 px-2 rounded text-[12px] "
                                                >
                                                    Next
                                                </Button>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </Main>
    );
}

export default Index;
