import {
    table_custom
} from "@/Pages/Helpers/DesignHelper";
import {
    Table,
    TableBody,
    TableCell,
    TableHead,
    TableHeadCell,
    TableRow
} from "flowbite-react";
import $ from "jquery";
import { useEffect, useState } from "react";
import Main from "./Main";
import NoRecord from "@/Components/HelperComponents/NoRecord";
import PerPageDropdown from "@/Components/HelperComponents/PerPageDropdown";
import Paginate from "@/Components/HelperComponents/Paginate";
import { convertDateFormat } from "@/Pages/Helpers/Helper";


function Message({ collection }) {
    const [isCheckAll, setIsCheckAll] = useState(false);
    useEffect(() => {
        $(".rowCheckBox").prop("checked", isCheckAll);
    }, [isCheckAll]);

    return (
        <Main>
            <div className="overflow-x-auto">
                <Table hoverable theme={table_custom}>
                    <TableHead className=" bg-slate-100">
                        <TableHeadCell>
                            <div className="flex items-center justify-between gap-2">
                                <span>Id</span>
                            </div>
                        </TableHeadCell>
                        <TableHeadCell >Channel</TableHeadCell>

                        <TableHeadCell>
                            <div className="flex items-center justify-between gap-2">
                                <span>msgId</span>
                            </div>
                        </TableHeadCell>

                        <TableHeadCell>
                            <div className="flex items-center justify-between gap-2">
                                <span>remoteJid</span>
                            </div>
                        </TableHeadCell>
                        <TableHeadCell>
                            <div className="flex items-center justify-between gap-2">
                                <span>fromMe</span>
                            </div>
                        </TableHeadCell>
                        <TableHeadCell>
                            <div className="flex items-center justify-between gap-2">
                                <span>messageDateTime</span>
                            </div>
                        </TableHeadCell>
                        <TableHeadCell>
                            <div className="flex items-center justify-between gap-2">
                                <span>msg</span>
                            </div>
                        </TableHeadCell>
                        <TableHeadCell>
                            <div className="flex items-center justify-between gap-2">
                                <span>user_id</span>
                            </div>
                        </TableHeadCell>
                        <TableHeadCell>
                            <div className="flex items-center justify-between gap-2">
                                <span>status</span>
                            </div>
                        </TableHeadCell>
                    </TableHead>
                    <TableBody className="divide-y">
                        {
                            (collection && collection.messageData.data.length > 0) ?
                            collection.messageData.data.map((message, key) => {
                                    return (
                                        <TableRow
                                            className="bg-white dark:border-gray-700 dark:bg-gray-800 text-nowrap"
                                            key={key}
                                        >
                                            <TableCell>
                                                {message.id}
                                            </TableCell>
                                            <TableCell>
                                                {message.channel.name}
                                            </TableCell>
                                            <TableCell>
                                                {message.msgId}
                                            </TableCell>
                                            <TableCell>
                                                {message.remoteJid}
                                            </TableCell>
                                            <TableCell>
                                                {message.fromMe}
                                            </TableCell>
                                            <TableCell>
                                                {convertDateFormat(message.messageDateTime)}
                                            </TableCell>
                                            <TableCell>
                                                {message.msg}
                                            </TableCell>
                                            <TableCell>
                                                {message.user_id}
                                            </TableCell>
                                            <TableCell>
                                                {message.status}
                                            </TableCell>
                                        </TableRow>
                                    );
                                })
                                :
                                <TableRow className="bg-white dark:border-gray-700 dark:bg-gray-800">
                                    <TableCell
                                        colSpan={13}
                                        className="text-center"
                                    >
                                        <NoRecord />
                                    </TableCell>
                                </TableRow>
                        }
                    </TableBody>
                </Table>
            </div>
            <div className="bottom-0 w-full p-3 bg-white  mt-1.5 rounded-lg float-end dark:border-gray-500 dark:rounded-lg dark:bg-gray-700">
                <div className="flex flex-wrap justify-between gap-2 lg:justify-between lg:gap-0 md:gap-0">
                    <div className="flex items-center gap-4">
                        <PerPageDropdown
                            // getDataFields={
                            //     autoReplyData.getData ?? null
                            // }
                            routeName={"whatsappB.log.incomingMessages"}
                            data={collection.messageData}
                        />
                    </div>
                    <Paginate tableData={collection.messageData} />
                </div>
            </div>
        </Main >
    )
}

export default Message

