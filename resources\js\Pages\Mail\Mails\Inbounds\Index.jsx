import ConfirmBox from "@/Components/HelperComponents/ConfirmBox";
import Paginate from "@/Components/HelperComponents/Paginate";
import PerPageDropdown from "@/Components/HelperComponents/PerPageDropdown";
import SortLink from "@/Components/SortLink";
import { button_custom, table_custom } from "@/Pages/Helpers/DesignHelper";
import { changeFlag } from "@/Pages/Helpers/Helper";
import { Link, router } from "@inertiajs/react";
import { Button, ButtonGroup, Checkbox, Table } from "flowbite-react";
import { lazy, useState } from 'react';
import { BiHide } from "react-icons/bi";
import { BsPin, BsPinAngle } from "react-icons/bs";
import { IoCheckmarkDone } from "react-icons/io5";
import { MdAutorenew, MdBlock, MdOutlineMoveToInbox, MdOutlinePushPin, MdOutlineReport } from "react-icons/md";
import { RiDeleteBin6Line, RiInboxArchiveLine, RiInboxUnarchiveLine } from "react-icons/ri";
import { TiStarOutline } from "react-icons/ti";

const InboxMain = lazy(() => import('../InboxMain'));
const currentPageRoute = route().current();



const tableHeadItems = [
    { name: 'Date & Time', column: 'datetime', isLink: true },
    { name: 'From', column: 'from', isLink: true },
    { name: 'Gateway', column: 'mail_gateway_id', isLink: true },
    { name: 'From Email', column: 'fromEmail', isLink: false },
    { name: 'Subject', column: 'subject', isLink: false },
    { name: 'CC', column: 'cc', isLink: false },
    { name: 'BCC', column: 'bcc', isLink: false },
    // { name: 'Pinned', column: 'isPinned', isLink: false },
];

export default function Index({ collection }) {
    const [isMultipleDeleteConfirmOpen, SetIsMultipleDeleteConfirmOpen] = useState(false);
    const currentTable = 'mail_incoming';

    const actionButtonsConfig = [
        {
            key: 'markUnread',
            title: 'Mark Unread',
            icon: <BiHide className="text-slate-400" />,
            ariaLabel: 'Mark as unread',
            type: 1,
        },
        {
            key: 'markRead',
            title: 'Mark Read',
            icon: <IoCheckmarkDone className="text-slate-400" />,
            ariaLabel: 'Mark read',
            type: 0,
        },
        {
            key: 'archive',
            title: 'Archive',
            icon: <RiInboxArchiveLine className="text-slate-400" />,
            ariaLabel: 'Archive',
            type: 1,
        },
        {
            key: 'unArchive',
            title: 'Un-Archive',
            icon: <RiInboxUnarchiveLine className="text-slate-400" />,
            ariaLabel: 'Un-Archive',
            type: 0,
        },
        {
            key: 'pinned',
            title: 'Pin',
            icon: <BsPin className="text-slate-400" />,
            ariaLabel: 'Pin',
            type: 1,
        },
        {
            key: 'unpinned',
            title: 'Unpin',
            icon: <BsPinAngle className="text-slate-400" />,
            ariaLabel: 'Unpin',
            type: 0,
        },
        {
            key: 'spam',
            title: 'Mark Spam',
            icon: <MdBlock className="text-slate-400" />,
            ariaLabel: 'Report spam',
            type: 1,
        },
        {
            key: 'unSpam',
            title: 'Not Spam',
            icon: <MdOutlineReport className="text-slate-400" />,
            ariaLabel: 'Not spam',
            type: 0,
        },
    ];


    const [selectedCheckBox, setSelectedCheckBox] = useState([]);

    const handleCheckboxChange = (id) => {
        setSelectedCheckBox((prevSelected) =>
            prevSelected.includes(id)
                ? prevSelected.filter((gwId) => gwId !== id) // Deselect if already selected
                : [...prevSelected, id] // Add if not selected
        );
    };
    // Handle "Select All" Checkbox
    const handleSelectAll = (e) => {
        if (e.target.checked) {
            if (selectedCheckBox.length === messages.length) {
                setSelectedCheckBox([]);
            } else {
                setSelectedCheckBox(messages?.data?.map((ch) => ch.id));
            }
        } else {
            setSelectedCheckBox([]);
        }
    };
    const { messages, getData } = collection;

    const updatedSelected = (type, markAs) => {
        if (selectedCheckBox.length) {
            router.post(route('mail.inbound.update.mails'), { type: type, mark: markAs, ids: selectedCheckBox.toString() }, {
                onSuccess: page => {
                    setSelectedCheckBox([]);
                }
            },)
        }

    }


    function deleteRecords() {
        if (selectedCheckBox.length > 0) {
            router.delete(route("mail.inbound.destroy", { inbound: selectedCheckBox.toLocaleString() }));
        }
    }
    const handleMultipleDelete = (res) => {
        if (res) {
            deleteRecords(); // Your function to delete records (ensure it handles the selection).
        }
        // Reset checkboxes and state
        setSelectedCheckBox([]); // Clear selected checkboxes
        SetIsMultipleDeleteConfirmOpen(false); // Close the confirmation dialog
    };

    const ActionButton = () => (
        <ButtonGroup>
            {actionButtonsConfig.map((button) => (
                <Button
                    key={button.key}
                    className="ps-1"
                    size="xs"
                    color="gray"
                    theme={button_custom}
                    onClick={() => updatedSelected(button.key, button.type ? '1' : '0')}
                    title={button.title}
                    aria-label={button.ariaLabel}
                >
                    <div className="flex items-center gap-1">
                        {button.icon}
                        <span className="text-xs text-nowrap">{button.title}</span>
                    </div>
                </Button>
            ))}
        </ButtonGroup>
    );

    return (
        <InboxMain>
            <div className="relative h-full rounded-md">
                <div className="mb-3 bg-white rounded-lg">
                    <div className="flex justify-between p-2">
                        <div className="flex w-full gap-2 overflow-auto ms-8 lg:ms-1">
                            <Button className="ps-1" size="xs" color="gray" theme={button_custom}>
                                <MdAutorenew className="text-slate-400" />
                            </Button>
                            <Button
                                className="ps-1"
                                size="xs"
                                color="gray"
                                theme={button_custom}
                                title={'Delete'}
                                aria-label={'Delete'}
                                onClick={() => SetIsMultipleDeleteConfirmOpen(!isMultipleDeleteConfirmOpen)}
                            >
                                <div className="flex items-center gap-1">
                                    <RiDeleteBin6Line className="text-slate-400" />
                                    <span className="text-xs">{'Delete'}</span>
                                </div>
                            </Button>
                            <ActionButton />
                        </div>
                        <div className=""></div>
                    </div>
                    <div className="overflow-auto bg-white border rounded-lg ">
                        <Table hoverable theme={table_custom} className="">
                            <Table.Head className="bg-slate-100 text-nowrap">
                                <Table.HeadCell className="w-8">
                                    <Checkbox
                                        color="blue" onChange={handleSelectAll}
                                        checked={selectedCheckBox.length == messages?.data?.length}
                                    />
                                </Table.HeadCell>
                                {tableHeadItems.map((item, index) => (
                                    <Table.HeadCell key={index}>
                                        {item.isLink ?
                                            <SortLink showName={item.name} routeName={currentPageRoute} column={item.column} params={getData} sortType={getData?.sort == "asc" ? "desc" : "asc"} />
                                            : item.name}
                                    </Table.HeadCell>
                                ))}
                            </Table.Head>
                            <Table.Body className="divide-y">
                                {messages?.data.map((m, k) =>
                                    <Table.Row key={k} as={Link} href={route("userprofile.index")} className={` ${m.isUnread ? " bg-gray-100 " : " bg-white "}  dark:border-gray-700 dark:bg-gray-800 `}>
                                        <Table.Cell>
                                            <div className="flex items-center">
                                                <Checkbox
                                                    color={"blue"} className="rowCheckBox"
                                                    checked={selectedCheckBox.includes(m.id)}
                                                    onChange={() => handleCheckboxChange(m.id)}
                                                />
                                                <Button className="ps-1" size="xs" color="withoutBorder" theme={button_custom}
                                                    onClick={() =>
                                                        changeFlag(currentTable, 'isPinned', m.id, m.isPinned ? 0 : 1)
                                                    }
                                                >
                                                    <TiStarOutline className={`${m.isPinned ? 'text-yellow-400' : 'text-slate-400'}`} />
                                                </Button>
                                            </div>
                                        </Table.Cell>
                                        <Table.Cell className="font-medium text-gray-900 whitespace-nowrap dark:text-white">
                                            <div className="flex flex-col w-20">
                                                <div className="text-sm text-blue-500">{new Date(m.datetime).toLocaleDateString()}</div>
                                                <div className="text-xs">{new Date(m.datetime).toLocaleTimeString()}</div>
                                            </div>
                                        </Table.Cell>
                                        <Table.Cell>
                                            <div className="text-sm font-medium">{m.from}</div>
                                        </Table.Cell>
                                        <Table.Cell className="text-nowrap">{m.gateway.name ?? '-'}</Table.Cell>
                                        <Table.Cell>{m.fromEmail ?? '-'}</Table.Cell>
                                        <Table.Cell className="text-nowrap">{m.subject ?? '-'}</Table.Cell>
                                        <Table.Cell>{m.cc ?? '-'}</Table.Cell>
                                        <Table.Cell>{m.bcc ?? '-'}</Table.Cell>

                                    </Table.Row>
                                )}
                            </Table.Body>
                        </Table>
                    </div>
                </div>
                <div className="w-full p-3 bg-white rounded-lg float-end dark:border-gray-500 dark:rounded-lg dark:bg-gray-700 h-fit">
                    <div className="flex flex-wrap justify-between gap-2 lg:justify-between lg:gap-0 md:gap-0">
                        <div className="flex items-center gap-4">
                            <PerPageDropdown
                                getDataFields={getData ?? null}
                                routeName={currentPageRoute}
                                data={messages}
                                routeParams={{ getData, ...route().params }}
                                idName={"name"}
                            // id={target}
                            />
                        </div>

                        <Paginate tableData={messages} />
                    </div>
                </div>
            </div>

            {isMultipleDeleteConfirmOpen &&
                <ConfirmBox isOpen={isMultipleDeleteConfirmOpen}
                    onClose={() => SetIsMultipleDeleteConfirmOpen(false)} // Close the confirm box
                    onAction={handleMultipleDelete} // Handle the user's choice
                    title="Delete Gateway "
                    message="Do you want to Delete gateway."
                    confirmText="Yes, Delete!"
                    cancelText="No, Keep It"
                    confirmColor="orange"
                    cancelColor="gray"

                />
            }
        </InboxMain>
    );
}
