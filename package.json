{"private": true, "type": "module", "scripts": {"dev": "vite", "build": "vite build"}, "devDependencies": {"@headlessui/react": "^2.2.0", "@inertiajs/react": "^2.0.3", "@tailwindcss/forms": "^0.5.10", "@vitejs/plugin-react": "^4.3.4", "autoprefixer": "^10.4.20", "axios": "^1.7.9", "flowbite": "^2.5.2", "laravel-vite-plugin": "^1.0.6", "postcss": "^8.5.1", "react": "^19.0.0", "react-dom": "^19.0.0", "tailwindcss": "^4.1.11", "vite": "^5.4.11"}, "dependencies": {"@emotion/react": "^11.14.0", "@emotion/styled": "^11.14.0", "@mui/material": "^5.16.14", "emoji-picker-react": "^4.12.0", "flowbite-react": "^0.10.2", "jquery": "^3.7.1", "react-dropzone": "^14.3.8", "react-facebook-sdk": "^1.1.1", "react-google-charts": "^5.2.1", "react-icons": "^5.4.0", "react-international-phone": "^4.5.0"}}