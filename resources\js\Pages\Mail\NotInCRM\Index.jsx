import Main from "@/Layouts/Main";
import React, { useState } from "react";
import {
    <PERSON>ton,
    Checkbox,
    Drawer,
    Modal,
    Table,
    Label,
    Textarea,
    TableHead,
    TableHeadCell,
    TableBody,
    TableRow,
    TableCell,
    ModalHeader,
    ModalBody,
    DrawerHeader,
    DrawerItems,
} from "flowbite-react";
import {
    button_custom,
    customTheme_drawer,
    table_custom,
    textarea_custom,
} from "@/Pages/Helpers/DesignHelper";
import { CiEdit } from "react-icons/ci";
import {
    MdDeleteOutline,
    MdOutlineGroupAdd,
    MdOutlineRampLeft,
} from "react-icons/md";
import { RiDeleteBinLine } from "react-icons/ri";
import TabBar from "../TabBar";
import Compose from "../Mails/Compose";
import { IoMdAdd } from "react-icons/io";
import { AiOutlineDoubleRight } from "react-icons/ai";
import Add from "@/Pages/LeadsCustomers/Add";


export default function NotInCRM() {
    const [openModal, setOpenModal] = useState(false);
    const [openModalMerge, setOpenModalMerge] = useState(false);
    const [isAddOpen, setIsAddOpen] = useState(false);
    const handleAddClose = () => setIsAddOpen(false);
    return (
        <Main>
            <div className="p-2 overflow-hidden ">
                <TabBar />

                <div className=" rounded-md  h-full mt-2">
                    <div className="h-full bg-white  rounded-lg">
                        <div className="p-2">
                            <Button
                                className="border rounded"
                                size="xs"
                                color="gray"
                                theme={button_custom}
                                // onClick={deleteRecords}
                            >
                                <div className="flex items-center gap-1">
                                    <MdDeleteOutline className="text-slate-500" />
                                    <span className="text-xs">Delete</span>
                                </div>
                            </Button>
                        </div>
                        <div className="overflow-x-auto bg-white border rounded-lg">
                            <Table hoverable theme={table_custom}>
                                <TableHead className="bg-slate-100">
                                    <TableHeadCell className="w-8">
                                        <Checkbox
                                            color="blue"
                                            // checked={isCheckAll}
                                            // onChange={() =>
                                            //     setIsCheckAll(
                                            //         !isCheckAll
                                            //     )
                                            // }
                                        />
                                    </TableHeadCell>
                                    <TableHeadCell>
                                        <div className="flex items-center justify-between gap-2">
                                            Mails Id
                                            <svg
                                                xmlns="http://www.w3.org/2000/svg"
                                                height="16px"
                                                viewBox="0 0 24 24"
                                                width="16px"
                                                className="fill-gray-600"
                                            >
                                                <path
                                                    d="M0 0h24v24H0V0z"
                                                    fill="none"
                                                />
                                                <path d="M12 5.83L15.17 9l1.41-1.41L12 3 7.41 7.59 8.83 9 12 5.83zm0 12.34L8.83 15l-1.41 1.41L12 21l4.59-4.59L15.17 15 12 18.17z" />
                                            </svg>
                                        </div>
                                    </TableHeadCell>
                                    <TableHeadCell>
                                        Add into Existing Lead
                                    </TableHeadCell>

                                    <TableHeadCell>Actions</TableHeadCell>
                                </TableHead>
                                <TableBody className="divide-y">
                                    <TableRow className="bg-white dark:border-gray-700 dark:bg-gray-800">
                                        <TableCell>
                                            <Checkbox
                                                color="blue"
                                                className="rowCheckBox"
                                            />
                                        </TableCell>
                                        <TableCell>
                                            <div className="flex justify-between ">
                                                <div className="">
                                                    <div className=" text-sm font-medium">
                                                        <EMAIL>
                                                    </div>
                                                    {/* <div className="text-xs">
                                                        <EMAIL>
                                                    </div> */}
                                                </div>
                                            </div>
                                        </TableCell>
                                        <TableCell>
                                            <div className="flex items-center gap-2">
                                                <Button
                                                    onClick={() =>
                                                        setOpenModalMerge(true)
                                                    }
                                                    // as={Link}
                                                    // href={route(
                                                    //     "mail.mails.openmail"
                                                    // )}
                                                    theme={button_custom}
                                                    size="xs"
                                                    color="Fuchsia_custom"
                                                    type="button"
                                                >
                                                    <div className="flex items-center gap-1">
                                                        <IoMdAdd className="" />
                                                        <span className="text-xs">
                                                            Add
                                                        </span>
                                                    </div>
                                                </Button>

                                                <Button
                                                    theme={button_custom}
                                                    size="xs"
                                                    color="failure"
                                                    id="dropdownInformationButton"
                                                    data-dropdown-toggle="dropdownNotification"
                                                    type="button"
                                                >
                                                    <div className="flex items-center gap-1">
                                                        <AiOutlineDoubleRight className="" />
                                                        <span className="text-xs">
                                                            Skip
                                                        </span>
                                                    </div>
                                                </Button>
                                            </div>
                                        </TableCell>
                                        <TableCell>
                                            <div className="flex items-center gap-2">
                                                <Button
                                                    // as={Link}
                                                    // href={route(
                                                    //     "mail.mails.openmail"
                                                    // )}
                                                    onClick={() =>
                                                        setIsAddOpen(true)
                                                    }
                                                    theme={button_custom}
                                                    size="xs"
                                                    color="green"
                                                    type="button"
                                                >
                                                    <div className="flex items-center gap-1">
                                                        <IoMdAdd className="" />
                                                        <span className="text-xs">
                                                            Add New Lead
                                                        </span>
                                                    </div>
                                                </Button>

                                                <Button
                                                    theme={button_custom}
                                                    size="xs"
                                                    color="failure"
                                                    id="dropdownInformationButton"
                                                    data-dropdown-toggle="dropdownNotification"
                                                    type="button"
                                                >
                                                    <div className="flex items-center gap-1">
                                                        <RiDeleteBinLine className="" />
                                                        <span className="text-xs">
                                                            Delete
                                                        </span>
                                                    </div>
                                                </Button>
                                            </div>
                                        </TableCell>
                                    </TableRow>
                                </TableBody>
                            </Table>
                        </div>
                        {/* <div className="flex flex-wrap justify-between gap-2 lg:gap-0 md:gap-0 p-2">
                            <div className="flex items-center gap-4"> */}
                        {/* <PerPageDropdown
                                        getDataFields={getData ?? null}
                                        routeName={"whatsapp.campaign.index"}
                                        data={campaigns}
                                    /> */}
                        {/* </div> */}
                        {/* <Paginate tableData={campaigns} /> */}
                        {/* </div>  */}
                    </div>
                </div>
            </div>

            <Modal
                size="5xl"
                show={openModal}
                onClose={() => setOpenModal(false)}
            >
                <ModalHeader className="bg-slate-100 p-2">
                    <div className="flex items-center gap-2">
                        <CiEdit />
                        <h4 className="text-lg">Compose</h4>
                    </div>
                </ModalHeader>
                <ModalBody className="px-4 py-3 rounded-b-lg bg-slate-100">
                    <Compose></Compose>
                </ModalBody>
            </Modal>

            <Modal
                // size="5xl"
                show={openModalMerge}
                onClose={() => setOpenModalMerge(false)}
            >
                <ModalHeader className="bg-white p-2">
                    <div className="flex items-start gap-2">
                        <MdOutlineRampLeft className="text-2xl  text-slate-400" />
                        <h4 className="text-lg">
                            Merge Leads
                            <br />
                            <span className="text-gray-500 text-sm">
                                <EMAIL>
                            </span>
                        </h4>
                    </div>
                </ModalHeader>
                <ModalBody className="px-4 py-3 rounded-b-lg bg-slate-100">
                    <div className="w-full">
                        <div className="mb-1">
                            <Label>
                                Merge With Name or Id
                                <span className="text-red-600 ms-1">*</span>
                            </Label>
                        </div>
                        <select
                            className="w-full rounded-lg text-sm border-gray-300 p-2.5 hover:bg-gray-50"
                            name="cars"
                            id="cars"
                        >
                            <option value="volvo">
                                Ex: Rohan Kapoor (5225)
                            </option>
                            <option value="saab">Name</option>
                            <option value="opel">Mobile Number</option>
                            <option value="audi">City</option>
                            <option value="audi">Address</option>
                        </select>
                        <div className="mt-2">
                            <div className="mb-1 block">
                                <Label>
                                    Reason
                                    <span className="text-red-600 ms-1">*</span>
                                </Label>
                            </div>
                            <Textarea
                                theme={textarea_custom}
                                id="comment"
                                placeholder="Write the reason for merging this lead here..."
                                required
                                rows={4}
                            />
                        </div>
                    </div>
                </ModalBody>
            </Modal>

            <Drawer
                theme={customTheme_drawer}
                open={isAddOpen}
                onClose={handleAddClose}
                position="right"
                className="w-full xl:w-3/5 lg:w-4/5 md:w-4/5"
            >
                <DrawerHeader
                    title="Add New Lead"
                    titleIcon={MdOutlineGroupAdd}
                />
                <DrawerItems>
                    <Add></Add>
                </DrawerItems>
            </Drawer>
        </Main>
    );
}



