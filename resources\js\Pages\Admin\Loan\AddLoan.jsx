import {
    <PERSON><PERSON>,
    <PERSON><PERSON>ip,
    Checkbox,
    Table,
    ToggleSwitch,
    Label,
    TextInput,
    Select,
    FileInput,
    Modal,
    Textarea,
    Carousel,
    Tabs,
} from "flowbite-react";
import {
    MdOutlineEdit,
    MdDeleteOutline,
    MdArrowDownward,
    MdOutlineBrokenImage,
    MdContentCopy,MdOutlinePayment
} from "react-icons/md";
import { useState, useEffect } from "react";
import { IoClose } from "react-icons/io5";
import $ from "jquery";
import { CiExport } from "react-icons/ci";
import { TbColumns3 } from "react-icons/tb";
import { BiCheckDouble } from "react-icons/bi";
import { IoCloseSharp } from "react-icons/io5";
import { GoDownload } from "react-icons/go";
import { HiAdjustments, HiClipboardList, HiUserCircle } from "react-icons/hi";
import { MdDashboard } from "react-icons/md";
import { PiListDashesFill } from "react-icons/pi";
import { TbReceiptDollar } from "react-icons/tb";
import { IoMdAdd } from "react-icons/io";
import { button_custom, custom_tabs, table_custom } from "../../Helpers/DesignHelper";

export default function AddLoan() {
    const [zoom, setZoom] = useState(false);
    const [transformOrigin, setTransformOrigin] = useState({
        x: "50%",
        y: "50%",
    });

    const handleMouseMove = (event) => {
        const { offsetX, offsetY, target } = event.nativeEvent;
        const { offsetWidth, offsetHeight } = target;

        const deltaX = (offsetX / offsetWidth) * 100;
        const deltaY = (offsetY / offsetHeight) * 100;

        setTransformOrigin({ x: `${deltaX}%`, y: `${deltaY}%` });
    };
    const [switch2, setSwitch2] = useState(false);

    const slider_custom = {
        root: {
            base: "relative h-full w-full",
            leftControl:
                "absolute left-0 top-0 flex h-full items-center justify-center focus:outline-none ",
            rightControl:
                "absolute right-0 top-0 flex h-full items-center justify-center focus:outline-none ",
        },
        indicators: {
            active: {
                off: "bg-black/50 hover:bg-black dark:bg-gray-800/50 dark:hover:bg-gray-800",
                on: "bg-black dark:bg-gray-800",
            },
            base: "h-3 w-3 rounded-full",
            wrapper:
                "absolute bottom-5 left-1/2 flex -translate-x-1/2 space-x-3",
        },
        item: {
            base: "absolute left-1/2 top-1/2 block w-full -translate-x-1/2 -translate-y-1/2 m-0",
            wrapper: {
                off: "w-full flex-shrink-0 transform cursor-default snap-center",
                on: "w-full flex-shrink-0 transform cursor-grab snap-center",
            },
        },
        control: {
            base: "inline-flex h-8 w-8 items-center justify-center rounded-full bg-black/30 group-hover:bg-black/50 group-focus:outline-none group-focus:ring-4 group-focus:ring-white dark:bg-gray-800/30 dark:group-hover:bg-gray-800/60 dark:group-focus:ring-gray-800/70 sm:h-10 sm:w-10",

            icon: "h-5 w-5 text-white dark:text-gray-800 sm:h-6 sm:w-6 ",
        },
        scrollContainer: {
            base: "flex h-full snap-mandatory overflow-y-hidden overflow-x-scroll scroll-smooth rounded-lg ",
            snap: "snap-x",
        },
    };

    // ----------open edit modal -------------
    const [openEditModal, setOpenEditModal] = useState(false);

    // ----------open View modal -------------
    const [openViewModal, setOpenViewModal] = useState(false);

    const [isCheckAll, setIsCheckAll] = useState(false);

    useEffect(() => {
        if (isCheckAll) {
            $(".rowCheckBox").prop("checked", true);
        } else {
            $(".rowCheckBox").prop("checked", false);
        }
    }, [isCheckAll]);

    return (
        <>
            <div className="px-2">
                <Tabs
                    theme={custom_tabs}
                    aria-label="Tabs with underline"
                >
                    <Tabs.Item
                        className=""
                        active
                        title="Payment History"
                        icon={PiListDashesFill}
                    >
                        <div className=" flex flex-col justify-between">
                            <div className="bg-white h-[50vh] overflow-auto">
                                {/* documents table */}
                                <div className="overflow-x-auto bg-white border rounded-lg">
                                    <Table hoverable theme={table_custom}>
                                        <TableHead className=" bg-slate-100">
                                            <TableHeadCell>
                                                Payment Date
                                            </TableHeadCell>

                                            <TableHeadCell>
                                                <h3>Payment Amount</h3>
                                            </TableHeadCell>

                                            <TableHeadCell>
                                                <h3>Principal Paid</h3>
                                            </TableHeadCell>

                                            <TableHeadCell>
                                                <h3>Interest Paid</h3>
                                            </TableHeadCell>

                                            <TableHeadCell>
                                                <h3>Outstanding Principal</h3>
                                            </TableHeadCell>

                                            <TableHeadCell className="w-28">
                                                <h3>Payment Method</h3>
                                            </TableHeadCell>

                                            <TableHeadCell>
                                                <h3>Late Fees</h3>
                                            </TableHeadCell>
                                            <TableHeadCell>
                                                <h3>Remarks</h3>
                                            </TableHeadCell>
                                        </TableHead>

                                        <TableBody className="divide-y">
                                            <TableRow className="bg-white dark:border-gray-700 dark:bg-gray-800">
                                                <TableCell className="text-blue-500">
                                                    1/07/2024
                                                </TableCell>

                                                <TableCell className="font-medium text-gray-900 whitespace-nowrap dark:text-white">
                                                    ₹ 1,500
                                                </TableCell>
                                                <TableCell>₹ 1,000</TableCell>
                                                <TableCell>₹ 100</TableCell>
                                                <TableCell>
                                                    ₹ 94,000
                                                </TableCell>
                                                <TableCell>
                                                    Payroll Deduction
                                                </TableCell>
                                                <TableCell>₹ 0</TableCell>

                                                <TableCell>-</TableCell>
                                            </TableRow>
                                            <TableRow className="bg-white dark:border-gray-700 dark:bg-gray-800">
                                                <TableCell className="text-blue-500">
                                                    1/07/2024
                                                </TableCell>

                                                <TableCell className="font-medium text-gray-900 whitespace-nowrap dark:text-white">
                                                    ₹ 1,500
                                                </TableCell>
                                                <TableCell>₹ 1,000</TableCell>
                                                <TableCell>₹ 100</TableCell>
                                                <TableCell>
                                                    ₹ 94,000
                                                </TableCell>
                                                <TableCell>
                                                    Payroll Deduction
                                                </TableCell>
                                                <TableCell>₹ 0</TableCell>

                                                <TableCell>-</TableCell>
                                            </TableRow>
                                            <TableRow className="bg-white dark:border-gray-700 dark:bg-gray-800">
                                                <TableCell className="text-blue-500">
                                                    1/07/2024
                                                </TableCell>

                                                <TableCell className="font-medium text-gray-900 whitespace-nowrap dark:text-white">
                                                    ₹ 1,500
                                                </TableCell>
                                                <TableCell>₹ 1,000</TableCell>
                                                <TableCell>₹ 100</TableCell>
                                                <TableCell>
                                                    ₹ 94,000
                                                </TableCell>
                                                <TableCell>
                                                    Payroll Deduction
                                                </TableCell>
                                                <TableCell>₹ 0</TableCell>

                                                <TableCell>-</TableCell>
                                            </TableRow>
                                        </TableBody>
                                    </Table>
                                </div>
                            </div>

                            <div className="float-end border bottom-0 p-3 w-full bg-white ">
                                <div className="flex flex-wrap justify-between lg:gap-0 md:gap-0 gap-2">
                                    <div className="flex gap-3 items-center">
                                        <div className="text-gray-400 text-sm ">
                                            Showing 1 to 25 of 62 entries
                                        </div>
                                        <div>
                                            <select
                                                id="countries"
                                                className="text-xs bg-gray-50 border border-gray-300 text-gray-900 rounded focus:ring-blue-500 focus:border-blue-500 block p-1 ps-2 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-blue-500 dark:focus:border-blue-500"
                                            >
                                                <option
                                                    value={10}
                                                    defaultValue={10}
                                                >
                                                    10
                                                </option>
                                                <option value={15}>15</option>
                                                <option value={20}>20</option>
                                                <option value={25}>25</option>
                                                <option value={30}>30</option>
                                            </select>
                                        </div>
                                    </div>
                                    <div className="flex">
                                        <Button
                                            size="xs"
                                            color="gray"
                                            className="border-e-0 text-gray-400 px-2 rounded text-xs "
                                        >
                                            Previous
                                        </Button>
                                        <Button
                                            size="xs"
                                            color="blue"
                                            className="border text-white border-blue-600 bg-blue-600 px-2.5 rounded-none text-sm "
                                        >
                                            1
                                        </Button>
                                        <Button
                                            size="xs"
                                            color="gray"
                                            className="border text-blue-600 px-2.5 rounded-none text-xs "
                                        >
                                            2
                                        </Button>
                                        <Button
                                            size="xs"
                                            color="gray"
                                            className="border-s-0 text-blue-600 px-2 rounded text-xs "
                                        >
                                            Next
                                        </Button>
                                    </div>
                                </div>
                            </div>

                            <div className="">
                                <div className="flex items-center gap-1 bg-[#F8F7FC] p-2 rounded-s rounded-e mt-2">
                                    <MdOutlinePayment className="text-slate-400 text-lg" />
                                    <h4 className="text-base">
                                    Add payment
                                    </h4>
                                </div>
                                <div className="bg-white pt-2 px-4">
                                    <div className="">
                                        <form className="flex items-end gap-5 flex-wrap">
                                            <div>
                                                <div className="mb-2 block">
                                                    <Label
                                                        htmlFor="PrincipalPaid"
                                                        value="Principal Paid"
                                                    />
                                                </div>
                                                <TextInput
                                                    id="PrincipalPaid"
                                                    type="email"
                                                    placeholder="Enter Amount"
                                                    required
                                                />
                                            </div>
                                            <div>
                                                <div className="mb-2 block">
                                                    <Label
                                                        htmlFor="PrincipalPaid"
                                                        value="Interest"
                                                    />
                                                </div>
                                                <TextInput
                                                    id="PrincipalPaid"
                                                    type="email"
                                                    placeholder="Enter Interest Rate"
                                                    required
                                                />
                                            </div>
                                            <div>
                                                <div className="mb-2 block">
                                                    <Label
                                                        htmlFor="paymentMethod"
                                                        value="Payment Method"
                                                    />
                                                </div>
                                                <TextInput
                                                    id="paymentMethod"
                                                    type="email"
                                                    placeholder="Ex: Bank Transfer"
                                                    required
                                                />
                                            </div>
                                        </form>
                                        <div className="pt-2">
                                            <div className="mb-2 block">
                                                <Label
                                                    htmlFor="document"
                                                    value="Remark"
                                                />
                                            </div>
                                            <TextInput
                                                id="document"
                                                type="email"
                                                placeholder="Write Remark"
                                                required
                                            />
                                        </div>

                                        <div className="flex justify-end gap-5 py-3">
                                            <Button theme={button_custom}
                                                color="failure"
                                                className="rounded"
                                                size="xs"
                                            >
                                                <div className="flex items-center gap-0.5">
                                                    <IoCloseSharp className="text-xl" />
                                                    <span className="text-sm">
                                                        Close
                                                    </span>
                                                </div>
                                            </Button>
                                            <Button
                                                color="blue"
                                                className="rounded"
                                                size="xs"
                                            >
                                                <div className="flex items-center gap-0.5">
                                                    <IoMdAdd  className="text-xl" />
                                                    <span className="text-sm">
                                                        Add
                                                    </span>
                                                </div>
                                            </Button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </Tabs.Item>
                    <Tabs.Item
                        title="Repayment Schedule"
                        icon={TbReceiptDollar}
                    >
                        <div className="bg-white h-[60vh] overflow-auto flex flex-col justify-between ">
                            <div>
                                {/* documents table */}
                                <div className="overflow-x-auto bg-white border rounded-lg">
                                    <Table hoverable theme={table_custom}>
                                        <TableHead className=" bg-slate-100">
                                            <TableHeadCell>
                                                Instalment Number
                                            </TableHeadCell>

                                            <TableHeadCell>
                                                <h3>Payment Date</h3>
                                            </TableHeadCell>

                                            <TableHeadCell>
                                                <h3>Payment Amount</h3>
                                            </TableHeadCell>

                                            <TableHeadCell>
                                                <h3>Principal Component</h3>
                                            </TableHeadCell>

                                            <TableHeadCell>
                                                <h3>Interest Component</h3>
                                            </TableHeadCell>

                                            <TableHeadCell className="w-28">
                                                <h3></h3>
                                            </TableHeadCell>
                                        </TableHead>

                                        <TableBody className="divide-y">
                                            <TableRow className="bg-white dark:border-gray-700 dark:bg-gray-800">
                                                <TableCell>1</TableCell>

                                                <TableCell className="text-blue-500">
                                                    1/07/2024
                                                </TableCell>
                                                <TableCell>₹ 1,500</TableCell>
                                                <TableCell>₹ 1,000</TableCell>
                                                <TableCell>₹ 100</TableCell>
                                                <TableCell>
                                                    ₹ 94,000
                                                </TableCell>
                                            </TableRow>
                                            <TableRow className="bg-white dark:border-gray-700 dark:bg-gray-800">
                                                <TableCell>1</TableCell>

                                                <TableCell className="text-blue-500">
                                                    1/07/2024
                                                </TableCell>
                                                <TableCell>₹ 1,500</TableCell>
                                                <TableCell>₹ 1,000</TableCell>
                                                <TableCell>₹ 100</TableCell>
                                                <TableCell>
                                                    ₹ 94,000
                                                </TableCell>
                                            </TableRow>
                                        </TableBody>
                                    </Table>
                                </div>
                            </div>

                            <div className="float-end border bottom-0 p-3 w-full bg-white ">
                                <div className="flex flex-wrap justify-between lg:gap-0 md:gap-0 gap-2">
                                    <div className="flex gap-3 items-center">
                                        <div className="text-gray-400 text-sm ">
                                            Showing 1 to 25 of 62 entries
                                        </div>
                                        <div>
                                            <select
                                                id="countries"
                                                className="text-xs bg-gray-50 border border-gray-300 text-gray-900 rounded focus:ring-blue-500 focus:border-blue-500 block p-1 ps-2 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-blue-500 dark:focus:border-blue-500"
                                            >
                                                <option
                                                    value={10}
                                                    defaultValue={10}
                                                >
                                                    10
                                                </option>
                                                <option value={15}>15</option>
                                                <option value={20}>20</option>
                                                <option value={25}>25</option>
                                                <option value={30}>30</option>
                                            </select>
                                        </div>
                                    </div>
                                    <div className="flex">
                                        <Button
                                            size="xs"
                                            color="gray"
                                            className="border-e-0 text-gray-400 px-2 rounded text-xs "
                                        >
                                            Previous
                                        </Button>
                                        <Button
                                            size="xs"
                                            color="blue"
                                            className="border text-white border-blue-600 bg-blue-600 px-2.5 rounded-none text-sm "
                                        >
                                            1
                                        </Button>
                                        <Button
                                            size="xs"
                                            color="gray"
                                            className="border text-blue-600 px-2.5 rounded-none text-xs "
                                        >
                                            2
                                        </Button>
                                        <Button
                                            size="xs"
                                            color="gray"
                                            className="border-s-0 text-blue-600 px-2 rounded text-xs "
                                        >
                                            Next
                                        </Button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </Tabs.Item>
                </Tabs>
            </div>

            {/*---------- Edit Document Modal---------- */}
            <Modal show={openEditModal} onClose={() => setOpenEditModal(false)}>
                <ModalHeader>
                    <div className="flex items-center gap-2">
                        <MdContentCopy className="text-slate-400" />
                        Edit Document
                    </div>
                </ModalHeader>
                <ModalBody className="bg-slate-100 py-3 px-4 rounded-b-lg">
                    <div className="space-y-6">
                        <div>
                            <label
                                className="block mb-2 text-sm font-medium text-slate-700 dark:text-white"
                                htmlFor="document"
                            >
                                <span className="text-red-600">*</span> Document
                            </label>
                            <TextInput
                                id="document"
                                type="email"
                                placeholder="Document name"
                                required
                                defaultValue="PAN Card"
                            />
                        </div>
                        <div>
                            <label
                                className="block mb-2 text-sm font-medium text-slate-700 dark:text-white"
                                htmlFor="document"
                            >
                                <span className="text-red-600">*</span> File
                                Type
                            </label>
                            <TextInput
                                id="document"
                                type="email"
                                placeholder="Document name"
                                required
                                defaultValue="JPEG"
                            />
                        </div>
                        <div>
                            <label
                                className="block mb-2 text-sm font-medium text-gray-900 dark:text-white"
                                htmlFor="uploadFile"
                            >
                                Upload file
                            </label>
                            <input
                                className="block w-full text-sm text-gray-900 border border-gray-300 rounded-lg cursor-pointer bg-gray-50 dark:text-gray-400 focus:outline-none dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400"
                                aria-describedby="uploadFile_help"
                                id="uploadFile"
                                type="file"
                            />
                        </div>
                        <div>
                            <label
                                className="block mb-2 text-sm font-medium text-slate-700 dark:text-white"
                                htmlFor="remark"
                            >
                                Remark
                            </label>
                            <Textarea
                                id="remark"
                                placeholder="Remark"
                                required
                                rows={2}
                            />
                        </div>
                    </div>
                    <div className="flex items-center gap-3 justify-end py-2 pt-5">
                        <Button
                            size="xs"
                            className="rounded"
                            color="failure"
                            onClick={() => setOpenModal(false)}
                        >
                            <div className="flex items-center gap-2">
                                <IoClose className="text-xl" />
                                <span className="text-base">Cancel</span>
                            </div>
                        </Button>
                        <Button
                            size="xs"
                            className="rounded"
                            color="blue"
                            onClick={() => setOpenModal(false)}
                        >
                            <div className="flex items-center gap-2">
                                <MdOutlineEdit className="text-xl" />
                                <span className="text-base">Edit</span>
                            </div>
                        </Button>
                    </div>
                </ModalBody>
            </Modal>

            {/*---------- View Document Modal---------- */}
            <Modal show={openViewModal} onClose={() => setOpenViewModal(false)}>
                <ModalBody className="bg-[#474747] lg:py-3 pb-1 lg:0 pt-2 lg:px-4 px-1 rounded-b-lg">
                    <div className="flex items-center justify-end">
                        <Button size="xs" className="rounded" color="blue">
                            <div className="flex items-center gap-2">
                                <GoDownload className="text-[15px]" />
                                <span className="text-[13px]">Download</span>
                            </div>
                        </Button>

                        <button onClick={() => setOpenViewModal(false)}>
                            <IoClose className="text-white text-[35px]" />
                        </button>
                    </div>
                    <div className="h-full sm:h-full xl:h-full 2xl:h-full m-4">
                        <Carousel
                            theme={slider_custom}
                            slide={false}
                            className=""
                        >
                            <div
                                className={`relative w-full h-full overflow-hidden ${
                                    zoom ? "cursor-zoom-out" : "cursor-zoom-in"
                                }`}
                                onClick={() => setZoom(!zoom)}
                                onMouseMove={handleMouseMove}
                            ></div>
                            <img
                                src="https://flowbite.com/docs/images/carousel/carousel-2.svg"
                                alt="Another image"
                            />
                        </Carousel>
                    </div>
                </ModalBody>
            </Modal>
        </>
    );
}

