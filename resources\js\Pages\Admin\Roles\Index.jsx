import ConfirmBox from "@/Components/HelperComponents/ConfirmBox";
import NoRecord from "@/Components/HelperComponents/NoRecord";
import Paginate from "@/Components/HelperComponents/Paginate";
import PerPageDropdown from "@/Components/HelperComponents/PerPageDropdown";
import SortLink from "@/Components/SortLink";
import useFetch from "@/Global/useFetch";
import { button_custom, table_custom } from "@/Pages/Helpers/DesignHelper";
import { Head, Link, router, usePage } from "@inertiajs/react";
import {
    Button,
    Checkbox, Modal,
    Table,
    TextInput,
    Tooltip
} from "flowbite-react";
import $ from "jquery";
import { useEffect, useState } from "react";
import { CgSpinner } from "react-icons/cg";
import { FaRegTrashCan } from "react-icons/fa6";
import { IoMdAdd } from "react-icons/io";
import {
    MdDeleteOutline,
    MdOutlineEdit
} from "react-icons/md";
import AdminMain from "../AdminMain";
import AddRole from "./AddRole";


function Index({ collection }) {
    const { roles, getData } = collection;
    const { props } = usePage();
    const currentUser = props.auth.user;

    const currentPageRoute = "admin.roles.index";
    const [rolesData, setRolesData] = useState([]);
    const [isConfirmOpen, setConfirmOpen] = useState(false);
    const [checkValue, setCheckValue] = useState([]);
    // Add Role
    const [openAddModal, setAddOpenModal] = useState(false);
    // checkbox checked
    const [isCheckAll, setIsCheckAll] = useState(false);
    const [searchQuery, setSearchQuery] = useState();
    // search logic

    const { data: searchData, loading: searchLoading, error } = useFetch(route(currentPageRoute, { search: searchQuery }));
    useEffect(() => {
        const updatedRoles = searchQuery && searchData?.collection?.roles
            ? searchData.collection.roles
            : roles;
        setRolesData(updatedRoles);
    }, [searchQuery, searchData?.collection?.roles, roles]);


    function headerCheckBoxChecked(e) {
        let previousIds = [];
        if (
            e.target.checked &&
            e.target.id == 0 &&
            roles.data.length > 0
        ) {
            roles.data.map((auto, key) => {
                if (!previousIds.includes(auto.id)) {
                    previousIds.push(auto.id);
                    setCheckValue(previousIds);
                }
            });
            setIsCheckAll(true);
            $(".rowCheckBox").prop("checked", true);
        } else {
            setCheckValue(previousIds);
            setIsCheckAll(false);
            $(".rowCheckBox").prop("checked", false);
        }
    }
    function getCheckedIds(e) {
        let previousIds = checkValue;
        if (e.target.checked) {
            if (!previousIds.includes(e.target.id)) {
                previousIds.push(e.target.id);
                setCheckValue(previousIds);
            }
        } else {
            const newIds = previousIds.filter((item) => item !== e.target.id);
            setCheckValue(newIds);
        }
    }
    function checkAddCheckBoxChecked() {
        let allCheckBoxes = $(".rowCheckBox");
        let checkedCheckBoxes = $(".rowCheckBox:checked");

        if (allCheckBoxes.length == checkedCheckBoxes.length) {
            setIsCheckAll(true);
        } else {
            setIsCheckAll(false);
        }
    }

    function handleConfirmBoxResult(result) {
        if (checkValue.length > 0 && result) {

            setConfirmOpen(false);
            router.delete(route("admin.roles.destroy", { role: checkValue }), {
                // destroy(route("admin.roles.destroy", { role: checkValue }), {
                onSuccess: () => {
                    $('.rowCheckBox').prop('checked', false);
                },
            });
            setCheckValue([]);
            setIsCheckAll(false);
        } else {
            setCheckValue([]);
            setIsCheckAll(false);
            $('.rowCheckBox').prop('checked', false);
        }
        setConfirmOpen(false);
    }

    return (
        <AdminMain>
            <Head title="Role" />
            <div className="min-h-[80vh] max-h-[80vh] bg-white border rounded-lg">
                <div className="flex justify-between p-2">
                    <div className="flex gap-2">
                        <div>
                            {
                                collection.can_delete &&
                                <Button
                                    size="xs"
                                    color="gray"
                                    theme={button_custom}
                                    onClick={() =>
                                        setConfirmOpen(true)
                                    }
                                >
                                    <div className="flex items-center gap-1">
                                        <MdDeleteOutline className="text-slate-500" />
                                        <span className="text-xs">
                                            Delete
                                        </span>
                                    </div>
                                </Button>
                            }
                        </div>

                        <div className="flex items-center gap-3 ">
                            <TextInput sizing="sm" placeholder="Search..." onChange={(e) => setSearchQuery(e.target.value)} />
                            {searchLoading && <CgSpinner className="text-2xl text-blue-400 ease-in-out animate-spin" />}
                            {/* <CgSpinner className="text-2xl text-blue-400 animate-spin" /> */}
                        </div>

                    </div>
                    <div className="">
                        {
                            collection.can_add &&
                            <Button
                                theme={button_custom}
                                size="xs"
                                color="gray"

                                onClick={() =>
                                    setAddOpenModal(
                                        true
                                    )
                                }
                            >
                                <div className="flex items-center gap-1">
                                    <IoMdAdd className="text-slate-500" />
                                    <span className="text-xs">
                                        Add
                                    </span>
                                </div>
                            </Button>
                        }
                    </div>
                </div>

                <div className="overflow-x-auto bg-white border rounded-lg">
                    <Table
                        hoverable
                        theme={table_custom}
                    >
                        <TableHead className="bg-slate-100">
                            <TableHeadCell className="w-8">
                                <Checkbox
                                    checked={
                                        isCheckAll
                                    }
                                    className="text-blue-700"
                                    id={0}
                                    onChange={(e) =>
                                        headerCheckBoxChecked(e)
                                    }
                                />
                            </TableHeadCell>
                            <TableHeadCell>

                                <SortLink showName={'Role'} routeName={currentPageRoute} column={'name'} sortType={getData?.sortBy == "asc" ? "desc" : "asc"} />
                            </TableHeadCell>
                            <TableHeadCell>
                                Actions
                            </TableHeadCell>
                        </TableHead>
                        <TableBody className="divide-y">
                            {
                                rolesData.data ?
                                    rolesData.data.map((role, index) => {
                                        return (
                                            <TableRow className="bg-white dark:border-gray-700 dark:bg-gray-800" key={index} >
                                                <TableCell>

                                                    <Checkbox
                                                        className={`${(role.id !== 1) && "rowCheckBox"}`}
                                                        color={"blue"}
                                                        id={role.id}
                                                        onChange={(e) => {
                                                            getCheckedIds(e);
                                                            checkAddCheckBoxChecked();
                                                        }}
                                                        disabled={role.id == 1}
                                                    />

                                                </TableCell>
                                                <TableCell>
                                                    {role.name}
                                                </TableCell>

                                                <TableCell>

                                                    <div className="flex items-center gap-2">
                                                        {(role.id != 1) && (currentUser.id != 1) &&
                                                            collection.can_edit &&
                                                            <Tooltip
                                                                content="Edit"
                                                                className="p-1 px-2 bg-slate-700"
                                                            >
                                                                <Button
                                                                    theme={
                                                                        button_custom
                                                                    }
                                                                    as={Link}
                                                                    href={route("admin.roles.edit", { id: role.id })}

                                                                    color="success"
                                                                    size="xs"


                                                                >
                                                                    <MdOutlineEdit />
                                                                </Button>
                                                            </Tooltip>
                                                        }
                                                        {(currentUser.id == 1) &&
                                                            <Tooltip
                                                                content="Edit"
                                                                className="p-1 px-2 bg-slate-700"
                                                            >
                                                                <Button
                                                                    theme={
                                                                        button_custom
                                                                    }
                                                                    as={Link}
                                                                    href={route("admin.roles.edit", { id: role.id })}

                                                                    color="success"
                                                                    size="xs"


                                                                >
                                                                    <MdOutlineEdit />
                                                                </Button>
                                                            </Tooltip>
                                                        }

                                                        {
                                                            collection.can_delete &&
                                                            <Tooltip
                                                                content="Delete"
                                                                className="p-1 px-2 bg-slate-700"
                                                            >
                                                                <Button
                                                                    theme={
                                                                        button_custom
                                                                    }
                                                                    color="failure"
                                                                    size="xs"
                                                                    onClick={() => {
                                                                        setCheckValue([role.id]);
                                                                        setConfirmOpen(true);
                                                                    }}
                                                                    disabled={role.id == 1}
                                                                >
                                                                    <MdDeleteOutline />
                                                                </Button>
                                                            </Tooltip>
                                                        }
                                                    </div>

                                                </TableCell>
                                            </TableRow>
                                        )

                                    })
                                    :
                                    <TableRow className="bg-white dark:border-gray-700 dark:bg-gray-800">
                                        <TableCell
                                            colSpan={11}
                                            className="text-center"
                                        >
                                            <NoRecord />
                                        </TableCell>
                                    </TableRow>
                            }

                        </TableBody>
                    </Table>
                </div>
            </div>
            <div className="bottom-0 w-full p-3 mt-3 bg-white rounded-lg float-end dark:border-gray-500 dark:rounded-lg dark:bg-gray-700">
                <div className="flex flex-wrap justify-between gap-2 lg:justify-between lg:gap-0 md:gap-0">
                    <div className="flex items-center gap-4">
                        <PerPageDropdown
                            getDataFields={
                                getData ?? null
                            }
                            routeName={currentPageRoute}
                            data={rolesData}
                        />
                    </div>
                    {rolesData.length &&
                        <Paginate tableData={rolesData} />
                    }
                </div>
            </div>


            {/* add role modal */}
            {openAddModal &&
                <Modal
                    show={
                        openAddModal
                    }
                    onClose={() =>
                        setAddOpenModal(
                            false
                        )
                    }
                >
                    <ModalHeader className="p-2">
                        <div className="flex items-center gap-2">
                            <MdOutlineEdit />
                            <h4 className="text-lg">
                                Add
                                Role
                            </h4>
                        </div>
                    </ModalHeader>
                    <ModalBody className="px-4 py-3 rounded-b-lg bg-slate-100">
                        <AddRole onClose={() => setAddOpenModal(false)}></AddRole>
                    </ModalBody>
                </Modal>
            }
            {/* roles perission drawer */}

            {/* confirm box popup */}
            {isConfirmOpen && (
                <ConfirmBox
                    isOpen={isConfirmOpen}
                    onClose={() => setConfirmOpen(false)} // Close the confirm box
                    onAction={handleConfirmBoxResult} // Handle the user's choice
                    title="Are you sure you want to delete this?"
                    message="This action cannot be undone."
                    confirmText="Yes, Delete!"
                    cancelText="No, Keep It"
                    confirmColor="orange"
                    cancelColor="gray"
                    icon={<FaRegTrashCan />}
                />
            )}
        </AdminMain>
    );
}

export default Index;

