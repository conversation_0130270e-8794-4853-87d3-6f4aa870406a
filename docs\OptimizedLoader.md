# OptimizedLoader Component Documentation

## 📋 Table of Contents
- [Overview](#overview)
- [Quick Start](#quick-start)
- [Components](#components)
- [Utilities](#utilities)
- [Use Cases & Examples](#use-cases--examples)
- [Best Practices](#best-practices)
- [Performance Tips](#performance-tips)
- [Troubleshooting](#troubleshooting)

## 🎯 Overview

The **OptimizedLoader** is a comprehensive React component library designed to handle intelligent component loading, lazy loading, and performance optimization. It provides a complete solution for managing loading states, reducing bundle sizes, and improving user experience through smart caching and progressive loading strategies.

### Key Features
- ✅ **Smart Caching**: Automatic component caching to prevent duplicate loads
- ✅ **Progressive Loading**: Delayed fallbacks to prevent UI flickering
- ✅ **Viewport-based Loading**: Load components only when they become visible
- ✅ **Error Handling**: Graceful error recovery with meaningful fallbacks
- ✅ **Multiple Skeleton Types**: Pre-built loading skeletons for different UI patterns
- ✅ **Preloading Strategies**: Manual and automatic component preloading
- ✅ **TypeScript Ready**: Full TypeScript support (if using TypeScript)

### When to Use OptimizedLoader
- Large applications with many components
- Components that take time to load (charts, heavy forms, etc.)
- Below-the-fold content that doesn't need immediate loading
- Dynamic imports that need caching
- Applications requiring better perceived performance

---

## 🚀 Quick Start

### Installation & Import
```jsx
// Import the components you need
import SmartLoader, { 
  LoadingSkeleton, 
  ProgressiveLoader, 
  LazyLoader,
  preloadComponent,
  useComponentPreloader 
} from '@/Components/OptimizedLoader';
```

### Basic Usage Example
```jsx
// Simple dynamic loading with caching
function MyPage() {
  return (
    <div>
      <h1>My Dashboard</h1>
      
      {/* Load a heavy chart component */}
      <SmartLoader
        importFunction={() => import('@/Components/HeavyChart')}
        componentName="HeavyChart"
        fallbackType="chart"
        className="w-full h-64"
      />
      
      {/* Load content only when visible */}
      <LazyLoader fallback={<LoadingSkeleton type="card" />}>
        <ExpensiveComponent />
      </LazyLoader>
    </div>
  );
}
```

---

## 🧩 Components

### 1. LoadingSkeleton

**Purpose**: Provides pre-designed loading placeholders to improve perceived performance.

#### Props
| Prop | Type | Default | Description |
|------|------|---------|-------------|
| `type` | `string` | `'default'` | Type of skeleton to display |
| `className` | `string` | `''` | Additional CSS classes |

#### Available Skeleton Types
| Type | Description | Use Case |
|------|-------------|----------|
| `default` | Basic text lines | General content loading |
| `button` | Button-shaped placeholder | Loading buttons/actions |
| `card` | Card layout with header/content | Loading cards, posts |
| `table` | Table rows with headers | Loading data tables |
| `sidebar` | Vertical sidebar shape | Loading navigation |
| `navbar` | Horizontal navigation bar | Loading top navigation |
| `chart` | Chart placeholder with label | Loading graphs/charts |

#### Examples
```jsx
{/* Basic text skeleton */}
<LoadingSkeleton type="default" className="mb-4" />

{/* Card skeleton for loading posts */}
<LoadingSkeleton type="card" className="max-w-md" />

{/* Chart skeleton for dashboards */}
<LoadingSkeleton type="chart" className="w-full h-64" />
```

### 2. ProgressiveLoader

**Purpose**: Adds intelligent delays before showing fallbacks to prevent UI flickering and handles loading timeouts.

#### Props
| Prop | Type | Default | Description |
|------|------|---------|-------------|
| `children` | `ReactNode` | - | Component to load |
| `fallback` | `ReactNode` | `null` | Fallback UI shown after delay |
| `delay` | `number` | `200` | Delay before showing fallback (ms) |
| `timeout` | `number` | `10000` | Maximum wait time before timeout (ms) |
| `onTimeout` | `function` | `null` | Callback when timeout occurs |
| `className` | `string` | `''` | CSS classes for wrapper |

#### Examples
```jsx
{/* Basic progressive loading */}
<ProgressiveLoader
  fallback={<LoadingSkeleton type="default" />}
  delay={300}
>
  <SlowComponent />
</ProgressiveLoader>

{/* With timeout handling */}
<ProgressiveLoader
  fallback={<LoadingSkeleton type="chart" />}
  delay={500}
  timeout={15000}
  onTimeout={() => console.log('Component took too long to load')}
>
  <HeavyChartComponent />
</ProgressiveLoader>
```

### 3. SmartLoader (Default Export)

**Purpose**: The main component for dynamic imports with intelligent caching, error handling, and customizable loading states.

#### Props
| Prop | Type | Required | Description |
|------|------|----------|-------------|
| `importFunction` | `function` | ✅ | Function that returns a dynamic import |
| `componentName` | `string` | ✅ | Name for error logging and cache key |
| `fallbackType` | `string` | ❌ | Type of skeleton fallback |
| `cacheKey` | `string` | ❌ | Custom cache key (defaults to componentName) |
| `className` | `string` | ❌ | CSS classes for wrapper |
| `...props` | `any` | ❌ | Props forwarded to loaded component |

#### Examples
```jsx
{/* Basic smart loading */}
<SmartLoader
  importFunction={() => import('@/Components/UserProfile')}
  componentName="UserProfile"
  fallbackType="card"
  userId={123}
  onUserUpdate={handleUpdate}
/>

{/* With custom cache key */}
<SmartLoader
  importFunction={() => import('@/Components/Chart')}
  componentName="SalesChart"
  cacheKey="sales-chart-2024"
  fallbackType="chart"
  data={salesData}
/>
```

### 4. LazyLoader

**Purpose**: Loads components only when they become visible in the viewport using Intersection Observer API.

#### Props
| Prop | Type | Default | Description |
|------|------|---------|-------------|
| `children` | `ReactNode` | - | Component to lazy load |
| `fallback` | `ReactNode` | `null` | Placeholder before becoming visible |
| `rootMargin` | `string` | `'50px'` | Margin around viewport for early loading |
| `threshold` | `number` | `0.1` | Percentage of element visible to trigger load |
| `className` | `string` | `''` | CSS classes for container |

#### Examples
```jsx
{/* Basic viewport-based loading */}
<LazyLoader fallback={<LoadingSkeleton type="card" />}>
  <HeavyComponent />
</LazyLoader>

{/* Load earlier with larger margin */}
<LazyLoader 
  fallback={<div>Loading...</div>}
  rootMargin="200px"
  threshold={0.25}
>
  <ExpensiveChart />
</LazyLoader>
```

---

## 🛠 Utilities

### preloadComponent

**Purpose**: Manually preload a single component into cache.

```jsx
// Preload a component (usually in useEffect or route handlers)
preloadComponent(
  () => import('@/Components/Dashboard'), 
  'Dashboard'
);
```

### preloadComponents

**Purpose**: Preload multiple components in batch.

```jsx
// Preload multiple components at once
preloadComponents([
  { 
    importFunction: () => import('@/Components/UserProfile'), 
    cacheKey: 'UserProfile' 
  },
  { 
    importFunction: () => import('@/Components/Settings'), 
    cacheKey: 'Settings' 
  },
  { 
    importFunction: () => import('@/Components/Analytics'), 
    cacheKey: 'Analytics' 
  }
]);
```

### useComponentPreloader Hook

**Purpose**: React hook for automatic component preloading after a delay.

#### Parameters
| Parameter | Type | Default | Description |
|-----------|------|---------|-------------|
| `components` | `Array` | - | Array of {importFunction, cacheKey} objects |
| `delay` | `number` | `1000` | Delay before starting preload (ms) |

```jsx
function Dashboard() {
  // Preload components that user might navigate to
  useComponentPreloader([
    {
      importFunction: () => import('@/Components/Reports'),
      cacheKey: 'Reports'
    },
    {
      importFunction: () => import('@/Components/Settings'),
      cacheKey: 'Settings'
    }
  ], 2000); // Wait 2 seconds after component mounts

  return <div>Dashboard Content</div>;
}
```

---

## 📚 Use Cases & Examples

### 1. Dashboard with Multiple Charts

```jsx
function AnalyticsDashboard() {
  // Preload likely-to-be-used components
  useComponentPreloader([
    { importFunction: () => import('@/Components/SalesChart'), cacheKey: 'SalesChart' },
    { importFunction: () => import('@/Components/UserChart'), cacheKey: 'UserChart' }
  ]);

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
      {/* Load immediately visible charts */}
      <SmartLoader
        importFunction={() => import('@/Components/RevenueChart')}
        componentName="RevenueChart"
        fallbackType="chart"
        className="col-span-full"
        data={revenueData}
      />

      {/* Load charts below the fold when visible */}
      <LazyLoader fallback={<LoadingSkeleton type="chart" />}>
        <SmartLoader
          importFunction={() => import('@/Components/ConversionChart')}
          componentName="ConversionChart"
          fallbackType="chart"
          data={conversionData}
        />
      </LazyLoader>
    </div>
  );
}
```

### 2. Progressive Form Loading

```jsx
function UserRegistration() {
  const [step, setStep] = useState(1);

  return (
    <div>
      {step === 1 && (
        <ProgressiveLoader
          fallback={<LoadingSkeleton type="card" />}
          delay={100}
        >
          <BasicInfoForm onNext={() => setStep(2)} />
        </ProgressiveLoader>
      )}

      {step === 2 && (
        <SmartLoader
          importFunction={() => import('@/Components/AddressForm')}
          componentName="AddressForm"
          fallbackType="card"
          onNext={() => setStep(3)}
          onBack={() => setStep(1)}
        />
      )}

      {step === 3 && (
        <SmartLoader
          importFunction={() => import('@/Components/PaymentForm')}
          componentName="PaymentForm"
          fallbackType="card"
          onSubmit={handleSubmit}
          onBack={() => setStep(2)}
        />
      )}
    </div>
  );
}
```

### 3. Content Feed with Lazy Loading

```jsx
function ContentFeed({ posts }) {
  return (
    <div className="space-y-6">
      {posts.map((post, index) => (
        <LazyLoader
          key={post.id}
          fallback={<LoadingSkeleton type="card" className="h-48" />}
          rootMargin={index < 3 ? "0px" : "100px"} // Load first 3 immediately
        >
          <SmartLoader
            importFunction={() => import('@/Components/PostCard')}
            componentName="PostCard"
            cacheKey="PostCard" // Shared cache for all posts
            fallbackType="card"
            post={post}
          />
        </LazyLoader>
      ))}
    </div>
  );
}
```

### 4. Modal with Heavy Content

```jsx
function DataModal({ isOpen, onClose }) {
  return (
    <Modal isOpen={isOpen} onClose={onClose}>
      {isOpen && (
        <ProgressiveLoader
          fallback={<LoadingSkeleton type="table" />}
          delay={200}
          timeout={8000}
          onTimeout={() => console.warn('Modal content loading slowly')}
        >
          <SmartLoader
            importFunction={() => import('@/Components/DataTable')}
            componentName="DataTable"
            fallbackType="table"
            data={largeDataset}
            onRowClick={handleRowClick}
          />
        </ProgressiveLoader>
      )}
    </Modal>
  );
}
```

### 5. Route-based Preloading

```jsx
// In your route component or layout
function AppLayout() {
  const location = useLocation();

  useEffect(() => {
    // Preload components based on current route
    if (location.pathname === '/dashboard') {
      preloadComponents([
        { importFunction: () => import('@/Pages/Reports'), cacheKey: 'Reports' },
        { importFunction: () => import('@/Pages/Settings'), cacheKey: 'Settings' }
      ]);
    } else if (location.pathname === '/profile') {
      preloadComponent(
        () => import('@/Components/ProfileEditor'),
        'ProfileEditor'
      );
    }
  }, [location.pathname]);

  return (
    <div>
      <Navigation />
      <main>
        <Outlet />
      </main>
    </div>
  );
}
```

---

## 💡 Best Practices

### ✅ Do's

1. **Use SmartLoader for most dynamic imports**
   ```jsx
   // Good: Cached, error-handled, with appropriate fallback
   <SmartLoader
     importFunction={() => import('@/Components/Chart')}
     componentName="Chart"
     fallbackType="chart"
   />
   ```

2. **Choose appropriate skeleton types**
   ```jsx
   // Good: Skeleton matches expected content
   <SmartLoader fallbackType="table" /> // For data tables
   <SmartLoader fallbackType="chart" /> // For charts
   <SmartLoader fallbackType="card" />  // For card layouts
   ```

3. **Preload high-probability components**
   ```jsx
   // Good: Preload components user is likely to need
   useComponentPreloader([
     { importFunction: () => import('@/Components/UserMenu'), cacheKey: 'UserMenu' }
   ], 1000);
   ```

4. **Use LazyLoader for below-the-fold content**
   ```jsx
   // Good: Don't load until visible
   <LazyLoader fallback={<LoadingSkeleton type="card" />}>
     <HeavyFooterComponent />
   </LazyLoader>
   ```

5. **Provide meaningful cache keys for shared components**
   ```jsx
   // Good: Explicit cache key for reused component
   <SmartLoader
     cacheKey="UserCard"
     importFunction={() => import('@/Components/UserCard')}
     componentName="UserCard"
   />
   ```

### ❌ Don'ts

1. **Don't preload rarely-used or large components unnecessarily**
   ```jsx
   // Bad: Preloading heavy, rarely-used component
   useComponentPreloader([
     { importFunction: () => import('@/Components/HeavyAdminPanel'), cacheKey: 'AdminPanel' }
   ]);
   ```

2. **Don't forget error boundaries for critical components**
   ```jsx
   // Bad: No error handling
   const CriticalComponent = lazy(() => import('@/Components/Critical'));

   // Good: With error boundary
   <ErrorBoundary fallback={<ErrorFallback />}>
     <SmartLoader
       importFunction={() => import('@/Components/Critical')}
       componentName="Critical"
     />
   </ErrorBoundary>
   ```

3. **Don't use generic skeletons for specific content**
   ```jsx
   // Bad: Generic skeleton for chart
   <SmartLoader fallbackType="default" /> // For a chart component

   // Good: Specific skeleton
   <SmartLoader fallbackType="chart" />
   ```

---

## ⚡ Performance Tips

### 1. Optimize Bundle Splitting

```jsx
// Group related components for better caching
const ChartComponents = {
  LineChart: () => import('@/Components/Charts/LineChart'),
  BarChart: () => import('@/Components/Charts/BarChart'),
  PieChart: () => import('@/Components/Charts/PieChart')
};

// Use consistent cache keys
<SmartLoader
  importFunction={ChartComponents.LineChart}
  componentName="LineChart"
  cacheKey="Charts.LineChart"
/>
```

### 2. Strategic Preloading

```jsx
// Preload based on user behavior patterns
function useSmartPreloading() {
  const [userRole] = useUserRole();

  useEffect(() => {
    const componentsToPreload = [];

    if (userRole === 'admin') {
      componentsToPreload.push(
        { importFunction: () => import('@/Components/AdminPanel'), cacheKey: 'AdminPanel' }
      );
    }

    if (userRole === 'manager') {
      componentsToPreload.push(
        { importFunction: () => import('@/Components/Reports'), cacheKey: 'Reports' }
      );
    }

    preloadComponents(componentsToPreload);
  }, [userRole]);
}
```

### 3. Memory Management

```jsx
// Clear cache when needed (e.g., user logout)
import { componentCache } from '@/Components/OptimizedLoader';

function clearComponentCache() {
  componentCache.clear();
}

// Or clear specific components
function clearSpecificCache(cacheKey) {
  componentCache.delete(cacheKey);
}
```

### 4. Loading State Optimization

```jsx
// Use progressive delays to prevent flickering
<ProgressiveLoader
  delay={100}  // Very fast components
  fallback={<LoadingSkeleton type="button" />}
>
  <QuickButton />
</ProgressiveLoader>

<ProgressiveLoader
  delay={500}  // Slower components
  fallback={<LoadingSkeleton type="chart" />}
>
  <ComplexChart />
</ProgressiveLoader>
```

---

## 🔧 Troubleshooting

### Common Issues and Solutions

#### 1. Component Not Loading

**Problem**: Component shows loading skeleton indefinitely.

**Solutions**:
```jsx
// Check import path
<SmartLoader
  importFunction={() => import('@/Components/MyComponent')} // Verify path
  componentName="MyComponent"
  fallbackType="default"
/>

// Add error handling
<SmartLoader
  importFunction={() => import('@/Components/MyComponent')}
  componentName="MyComponent"
  fallbackType="default"
  onError={(error) => console.error('Failed to load:', error)}
/>
```

#### 2. Cache Not Working

**Problem**: Components reload every time instead of using cache.

**Solutions**:
```jsx
// Ensure consistent cache keys
<SmartLoader
  cacheKey="UserProfile" // Same key across uses
  importFunction={() => import('@/Components/UserProfile')}
  componentName="UserProfile"
/>

// Check for dynamic import functions
// Bad: Creates new function each render
<SmartLoader
  importFunction={() => import(`@/Components/${dynamicName}`)}
/>

// Good: Stable function reference
const importFunction = useCallback(
  () => import(`@/Components/${dynamicName}`),
  [dynamicName]
);
```

#### 3. Performance Issues

**Problem**: Too many components loading at once.

**Solutions**:
```jsx
// Use LazyLoader for below-fold content
<LazyLoader rootMargin="100px">
  <SmartLoader ... />
</LazyLoader>

// Stagger preloading
useComponentPreloader(criticalComponents, 500);
useComponentPreloader(secondaryComponents, 2000);
useComponentPreloader(optionalComponents, 5000);
```

#### 4. TypeScript Errors

**Problem**: TypeScript complains about dynamic imports.

**Solutions**:
```tsx
// Type the import function
type ImportFunction<T = any> = () => Promise<{ default: React.ComponentType<T> }>;

const importFunction: ImportFunction<MyComponentProps> = () =>
  import('@/Components/MyComponent');

<SmartLoader<MyComponentProps>
  importFunction={importFunction}
  componentName="MyComponent"
  // ... other props
/>
```

### Debug Mode

```jsx
// Enable debug logging in development
if (process.env.NODE_ENV === 'development') {
  // Log cache hits/misses
  const originalGet = componentCache.get;
  componentCache.get = function(key) {
    const result = originalGet.call(this, key);
    console.log(`Cache ${result ? 'HIT' : 'MISS'} for ${key}`);
    return result;
  };
}
```

---

## 📂 File Structure

```
OptimizedLoader.jsx
│
├─ LoadingSkeleton → Pre-built loading placeholders
├─ ProgressiveLoader → Delayed fallback with timeout handling
├─ SmartLoader → Main dynamic loader with caching
├─ LazyLoader → Viewport-based loading
├─ preloadComponent → Manual single component preload
├─ preloadComponents → Batch component preload
├─ useComponentPreloader → Hook for automatic preloading
└─ componentCache → Internal cache Map
```

---

## 🎯 Summary

The OptimizedLoader provides a complete solution for:

- **Reducing initial bundle size** through smart lazy loading
- **Improving perceived performance** with appropriate loading skeletons
- **Preventing unnecessary re-loads** through intelligent caching
- **Optimizing user experience** with progressive loading strategies
- **Handling errors gracefully** with meaningful fallbacks

Use it as your go-to solution for component loading optimization across your React application. Start with `SmartLoader` for most use cases, add `LazyLoader` for below-the-fold content, and use preloading strategies for components users are likely to need.

For questions or issues, refer to the troubleshooting section or check the component implementation in `@/Components/OptimizedLoader.jsx`.
