import { Head } from "@inertiajs/react";
import $ from "jquery";
import { <PERSON>ltip, Button, ButtonGroup, Drawer, Table, Checkbox, Badge } from "flowbite-react";
import { CiExport } from "react-icons/ci";
import { TbColumns3, Tb<PERSON>eportSearch } from "react-icons/tb";
import { useState } from "react";
import { IoClose } from "react-icons/io5";
import {
    MdOutlineFileDownload,
    MdOutlinePlagiarism,
    MdOutlinePointOfSale,
    MdOutlineReceiptLong,
    MdOutlineSell,
    MdOutlineVisibility,
} from "react-icons/md";
import Main from "@/Layouts/Main";
import { button_custom, customDrawer, table_custom } from "@/Pages/Helpers/DesignHelper";
import { IoIosAddCircle, IoIosArrowDown, IoMdAdd } from "react-icons/io";
import { RiDeleteBin6Line, RiHistoryFill } from "react-icons/ri";
import { Pi<PERSON>aretUpDownBold, PiExportBold } from "react-icons/pi";
import Filter from "./Filter";
import TabBar from "../TabBar";
import Add from "./Add";
import { showAsset } from "@/Pages/Helpers/Helper";
import View from "./View";

function Index() {
    const [isCheckAll, setIsCheckAll] = useState(false);
    // Lead Contact
    const [isOpen, setIsOpen] = useState(false);
    const handleClose = () => setIsOpen(false);
    // Edit Lead
    const [isOpenView, setIsOpenView] = useState(false);
    // 
    const [isOpenAdd, setIsOpenAdd] = useState(false);

    // Toggle Row
    const ToggleChild = (parent, child) => {
        // console.log(parent);
        var parentEle = $("#" + parent);
        var ele = $("#" + child);
        if (ele.hasClass("hidden")) {
            parentEle.addClass("rotate-180");
            ele.removeClass("hidden");
        } else {
            parentEle.removeClass("rotate-180");
            ele.addClass("hidden");
        }
    };

    // const ThTdPaddings = "px-2 py-1 text-nowrap";
    return (
        <Main>
            <Head title="User" />
            <div className="relative overflow-hidden p-2">
                <div className="grid grid-cols-12 md:grid-cols-12 sm:grid-cols-1 gap-2 grid-flow-row-dense bg-slate-100 p-0 border-none">
                    <div className="lg:col-span-2 md:col-span-3 sm:col-span-3 lg:flex hidden">
                        <div className="bg-white shadow-sm rounded-lg border dark:bg-gray-900 w-full overflow-auto min-h-[90vh] max-h-[90vh]">
                            <Filter />
                        </div>
                    </div>

                    <div className="dark:text-gray-400 lg:p-0 pt-0 lg:col-span-10 md:col-span-12 col-span-full relative">
                        <div className="col-span-full mb-2">
                            <TabBar />
                        </div>
                        <div className="min-h-[80vh] max-h-[80vh] bg-white border rounded-lg">
                            <div className="flex justify-between p-2">
                                <div className="flex items-center gap-1">
                                    <ButtonGroup>
                                        <Button
                                            theme={button_custom}
                                            size="xs"
                                            color="gray"
                                        >
                                            <div className="flex items-center gap-1">
                                                <RiDeleteBin6Line className="text-slate-500" />
                                                <span className="text-xs">
                                                    Delete
                                                </span>
                                            </div>
                                        </Button>

                                        <Button
                                            theme={button_custom}
                                            size="xs"
                                            color="gray"
                                            id="dropdownInformationButton"
                                            data-dropdown-toggle="dropdownNotification"
                                            type="button"
                                        >
                                            <div className="flex items-center gap-1">
                                                <TbColumns3 className="text-slate-500" />
                                                <span className="text-xs">
                                                    Columns
                                                </span>
                                            </div>
                                        </Button>
                                    </ButtonGroup>
                                    <Button
                                        theme={button_custom}
                                        size="xs"
                                        color="gray"
                                    >
                                        <div className="flex items-center gap-1">
                                            <PiExportBold className="text-slate-500" />
                                            <span className="text-xs">
                                                Export
                                            </span>
                                        </div>
                                    </Button>
                                </div>

                                <ButtonGroup className="">
                                    <Button
                                        theme={button_custom}
                                        size="xs"
                                        color="gray"
                                        onClick={() =>
                                            setIsOpenAdd(
                                                true
                                            )
                                        }
                                    >
                                        <div className="flex items-center gap-1">
                                            <IoMdAdd className="text-slate-500" />
                                            <span className="text-xs">
                                                Add Transaction
                                            </span>
                                        </div>
                                    </Button>
                                </ButtonGroup>
                            </div>

                            <div className="overflow-x-auto bg-white border rounded-lg">
                                <Table hoverable theme={table_custom}>
                                    <Table.Head className="bg-slate-100">
                                        <Table.HeadCell>
                                            <Checkbox color="blue"
                                                checked={isCheckAll}
                                                onChange={() =>
                                                    setIsCheckAll(!isCheckAll)
                                                }
                                            />
                                        </Table.HeadCell>

                                        <Table.HeadCell>Date & Time</Table.HeadCell>

                                        <Table.HeadCell>
                                            <div className="flex items-center justify-between gap-2">
                                                <h3>Customers</h3>
                                                <PiCaretUpDownBold />
                                            </div>
                                        </Table.HeadCell>
                                        <Table.HeadCell className="text-nowrap">Mode</Table.HeadCell>

                                        <Table.HeadCell>TXN Manager</Table.HeadCell>
                                        <Table.HeadCell>Lead Manager</Table.HeadCell>

                                        <Table.HeadCell>Invoice</Table.HeadCell>
                                        <Table.HeadCell>Amount</Table.HeadCell>
                                        <Table.HeadCell>Status</Table.HeadCell>
                                        <Table.HeadCell>Actions</Table.HeadCell>
                                    </Table.Head>

                                    <Table.Body className="divide-y">
                                        <Table.Row className="">
                                            <Table.Cell>
                                                <Checkbox color="blue" className="rowCheckBox" />
                                            </Table.Cell>
                                            <Table.Cell>
                                                <div className="flex flex-col">
                                                    <div className="text-blue-600 text-sm ">
                                                        24/03/2024
                                                    </div>
                                                    <div className="text-xs">
                                                        02:45 PM
                                                    </div>
                                                </div>
                                            </Table.Cell>
                                            <Table.Cell>
                                                <div className="flex flex-col">
                                                    <div className="text-blue-600 text-sm ">
                                                        Abhishek Bhattacharyya (8560)
                                                    </div>
                                                    <div className="text-xs">
                                                        Dunes Factory Pvt. Ltd.
                                                    </div>
                                                </div>
                                            </Table.Cell>
                                            <Table.Cell className="" onClick={() => setIsOpenInvoiceHistory(true)}>
                                                <img
                                                    src={showAsset("/assets/img/phonepe.png", '')}
                                                    alt=""
                                                    className="rounded-full size-8"
                                                />
                                            </Table.Cell>

                                            <Table.Cell>
                                                <Tooltip
                                                    content="Abhishek"
                                                    className="bg-slate-700 p-1"
                                                >
                                                    <img
                                                        src="https://img.freepik.com/premium-photo/profile-icon-white-background_941097-160810.jpg"
                                                        alt=""
                                                        className="rounded-full size-10"
                                                    />
                                                </Tooltip>
                                            </Table.Cell>
                                            <Table.Cell>
                                                <Tooltip
                                                    content="Abhishek"
                                                    className="bg-slate-700 p-1"
                                                >
                                                    <img
                                                        src="https://img.freepik.com/premium-photo/profile-icon-white-background_941097-160810.jpg"
                                                        alt=""
                                                        className="rounded-full size-10"
                                                    />
                                                </Tooltip>
                                            </Table.Cell>
                                            <Table.Cell className="text-blue-700" onClick={() => setIsOpenInvoiceHistory(true)}>
                                                IN-2425-361
                                            </Table.Cell>
                                            <Table.Cell>
                                                ₹ 11,256
                                            </Table.Cell>
                                            <Table.Cell>
                                                <Badge className="w-fit" color="success">Approved</Badge>
                                            </Table.Cell>
                                            <Table.Cell>
                                                <div className="flex items-center justify-between">
                                                    <div className="flex gap-2 h-fit text-nowrap">

                                                        <Button
                                                            theme={
                                                                button_custom
                                                            }
                                                            color="orange"
                                                            size="xs"
                                                            onClick={() =>
                                                                setIsOpenDocuments(
                                                                    true
                                                                )
                                                            }
                                                        >
                                                            <div className="flex items-center gap-1">
                                                                <MdOutlinePlagiarism />
                                                                <span className="text-xs">Approve</span>
                                                            </div>
                                                        </Button>
                                                        <Button
                                                            theme={
                                                                button_custom
                                                            }
                                                            // color="failure"
                                                            size="xs"
                                                            onClick={() =>
                                                                setIsOpenView(
                                                                    true
                                                                )
                                                            }
                                                        >
                                                            <div className="flex items-center gap-1">
                                                                <MdOutlineVisibility />
                                                                <span className="text-xs">View</span>
                                                            </div>
                                                        </Button>
                                                        {/* </Tooltip> */}
                                                        <Button
                                                            theme={
                                                                button_custom
                                                            }
                                                            color="green"
                                                            size="xs"
                                                            onClick={() =>
                                                                setIsOpenView(
                                                                    true
                                                                )
                                                            }
                                                        >
                                                            <div className="flex items-center gap-1">
                                                                <MdOutlinePlagiarism />
                                                                <span className="text-xs">Edit</span>
                                                            </div>
                                                        </Button>

                                                        <Button
                                                            theme={
                                                                button_custom
                                                            }
                                                            color="Fuchsia_custom"
                                                            size="xs"
                                                            onClick={() =>
                                                                setIsOpenDocuments(
                                                                    true
                                                                )
                                                            }
                                                        >
                                                            <div className="flex items-center gap-1">
                                                                <MdOutlineFileDownload />
                                                                <span className="text-xs">Download</span>
                                                            </div>
                                                        </Button>
                                                    </div>
                                                </div>
                                            </Table.Cell>
                                        </Table.Row>
                                    </Table.Body>
                                </Table>
                            </div>
                        </div>
                        <div className="float-end border absolute bottom-0 rounded-b-lg p-3 w-full bg-white ">
                            <div className="flex flex-wrap justify-between lg:gap-0 md:gap-0 gap-2">
                                <div className="flex gap-3 items-center">
                                    <div className="text-gray-400 text-sm ">
                                        Showing 1 to 25 of 62 entries
                                    </div>
                                    <div>
                                        <select
                                            id="countries"
                                            className="text-xs bg-gray-50 border border-gray-300 text-gray-900 rounded focus:ring-blue-500 focus:border-blue-500 block p-1 ps-2 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-blue-500 dark:focus:border-blue-500"
                                        >
                                            <option
                                                value={10}
                                                defaultValue={10}
                                            >
                                                10
                                            </option>
                                            <option value={15}>15</option>
                                            <option value={20}>20</option>
                                            <option value={25}>25</option>
                                            <option value={30}>30</option>
                                        </select>
                                    </div>
                                </div>
                                <div className="flex">
                                    <Button
                                        size="xs"
                                        color="gray"
                                        className="border-e-0 text-gray-400 px-2 rounded text-xs "
                                    >
                                        Previous
                                    </Button>
                                    <Button
                                        size="xs"
                                        color="blue"
                                        className="border text-white border-blue-600 bg-blue-600 px-2.5 rounded-none text-sm "
                                    >
                                        1
                                    </Button>
                                    <Button
                                        size="xs"
                                        color="gray"
                                        className="border text-blue-600 px-2.5 rounded-none text-xs "
                                    >
                                        2
                                    </Button>
                                    <Button
                                        size="xs"
                                        color="gray"
                                        className="border-s-0 text-blue-600 px-2 rounded text-xs "
                                    >
                                        Next
                                    </Button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            {/* Add Transaction */}
            <Drawer
                theme={customDrawer}
                open={isOpenAdd}
                onClose={() => setIsOpenAdd(false)}
                position="right"
                className="w-full xl:w-10/12 lg:w-full md:w-full"
            >
                <Drawer.Header
                    titleIcon={MdOutlinePointOfSale}
                    title="Add Transaction"
                />
                <Drawer.Items>
                    <Add />
                </Drawer.Items>
            </Drawer>
            {/* Transaction View Model */}
            <Drawer
                theme={customDrawer}
                open={isOpenView}
                onClose={() => setIsOpenView(false)}
                position="right"
                className="w-full xl:w-10/12 lg:w-full md:w-full p-0"
            >
                <Drawer.Header className="h-0" titleIcon="false" />
                <Drawer.Items>
                    <div className="bg-white p-2">
                        <div className="flex items-start justify-between flex-wrap">
                            <div className="flex flex-col">
                                <div className="text-blue-600 text-base font-medium flex items-center gap-2">
                                    <span>Kanhaiya Lal (5620)</span>
                                    <Badge color="indigo">Paid</Badge>
                                </div>
                                <span className="text-sm ">Dunes Factory Pvt. Ltd.</span>
                            </div>
                            <div>
                                <div className="flex items-start gap-2 me-7 pe-3">
                                    <div>
                                        <h4 className="text-base font-medium text-blue-600">
                                            24/03/2024
                                        </h4>
                                        <h6 className="text-xs">
                                            02:45 PM
                                        </h6>
                                    </div>
                                    <img
                                        className="rounded-full h-12 w-12"
                                        src="https://img.freepik.com/free-vector/businessman-character-avatar-isolated_24877-60111.jpg?t=st=1720614745~exp=1720618345~hmac=38ff343a9443982af738325ad4d54d1d39ba540be4c28013b03cf063347d255e&w=200"
                                        alt=""
                                    />
                                </div>
                            </div>
                        </div>
                    </div>
                    <View />
                </Drawer.Items>
            </Drawer>
        </Main >
    );
}
export default Index;


