import { Head, router, usePage } from "@inertiajs/react";
import { Ava<PERSON>, Badge, <PERSON>ton, Checkbox, Drawer, DrawerHeader, DrawerItems, Table, TableBody, TableCell, TableHead, TableHeadCell, TableRow, ToggleSwitch, Tooltip } from "flowbite-react";
import $ from "jquery";
import { Fragment, useEffect, useState } from "react";
import { IoClose } from "react-icons/io5";
import {
    MdDeleteOutline,
    MdOutlineContactPage,
    MdOutlineModeEdit,
} from "react-icons/md";
import { PiUserFocus } from "react-icons/pi";
import Details from "./Details";
import Documents from "./Documents";
import Edit from "./Edit";

import Paginate from "@/Components/HelperComponents/Paginate";
import PerPageDropdown from "@/Components/HelperComponents/PerPageDropdown";
import SortLink from "@/Components/SortLink";
import { button_custom, customDrawer, table_custom, toggle_custom } from "@/Pages/Helpers/DesignHelper";
import { changeFlag } from "@/Pages/Helpers/Helper";
import { FaAngleUp, FaRegTrashCan } from "react-icons/fa6";
import { IoMdAdd } from "react-icons/io";
import AdminMain from "../AdminMain";
import Add from "./Add";
import ConfirmBox from "@/Components/HelperComponents/ConfirmBox";

function Index({ collection }) {

    const { props } = usePage();
    const currentUser = props.auth.user;

    const currentPageRoute = "admin.users.index";

    const [isCheckAll, setIsCheckAll] = useState(false);

    const [selectedUser, setSelectedUser] = useState();
    // ----------------Edit --------------------
    const [isOpenEdit, setIsOpenEdit] = useState(false);
    // ----------------Add --------------------
    const [isOpenAdd, setIsOpenAdd] = useState(false);
    // ----------------Edit --------------------
    const [isOpenDocuments, setIsOpenDocuments] = useState(false);

    const [updatingId, setUpdatingId] = useState(null);

    const [toggleDetails, setToggleDetails] = useState({ parent: '', child: '' });

    const [checkValue, setCheckValue] = useState([]);
    const [isConfirmOpen, setConfirmOpen] = useState(false);


    const ToggleChild = (parent = 'empty', child = 'empty') => {
        var parentEle = $("#" + parent);
        $('.child-row').addClass('hidden')
        var ele = $("#" + child);
        if (ele.hasClass("hidden")) {
            parentEle.addClass("rotate-180");
            ele.removeClass("hidden");
        } else {
            parentEle.removeClass("rotate-180");
            ele.addClass("hidden");
        }
    };

    useEffect(() => {
        if (toggleDetails.parent) {
            ToggleChild(toggleDetails.parent, toggleDetails.child);
        } else {
            ToggleChild();
        }
    }, [toggleDetails]);


    function headerCheckBoxChecked(e) {
        let previousIds = [];
        if (
            e.target.checked &&
            e.target.id == 0 &&
            collection.users.data.length > 0
        ) {
            collection.users.data.map((auto, key) => {
                if (!previousIds.includes(auto.id)) {
                    previousIds.push(auto.id);
                    setCheckValue(previousIds);
                }
            });
            setIsCheckAll(true);
            $(".rowCheckBox").prop("checked", true);
        } else {
            setCheckValue(previousIds);
            setIsCheckAll(false);
            $(".rowCheckBox").prop("checked", false);
        }
    }
    function getCheckedIds(e) {
        let previousIds = checkValue;
        if (e.target.checked) {
            if (!previousIds.includes(e.target.id)) {
                previousIds.push(e.target.id);
                setCheckValue(previousIds);
            }
        } else {
            const newIds = previousIds.filter((item) => item !== e.target.id);
            setCheckValue(newIds);
        }
    }
    function checkAddCheckBoxChecked() {
        let allCheckBoxes = $(".rowCheckBox");
        let checkedCheckBoxes = $(".rowCheckBox:checked");

        if (allCheckBoxes.length == checkedCheckBoxes.length) {
            setIsCheckAll(true);
        } else {
            setIsCheckAll(false);
        }
    }
    function handleConfirmBoxResult(result) {
        if (checkValue.length > 0 && result) {
            setConfirmOpen(false);
            setCheckValue([]);
            router.delete(
                route("admin.users.destroy", { user: checkValue })
            );
            $(".rowCheckBox").prop("checked", false);
            setIsCheckAll(false);
        } else {
            setCheckValue([]);
            setIsCheckAll(false);
        }
        setConfirmOpen(false);
    }

    const HandleChangeFlag = async (table, Column, id, flag) => {
        try {
            changeFlag(table, Column, id, flag)
        } finally {
            console.error('error in flag change.');
        }
    };



    return (
        <AdminMain>
            <Head title="User" />

            <div className="mb-3 bg-white border rounded-lg ">
                <div className="flex justify-between p-2 ">
                    <div className="flex gap-2">
                        <Button
                            // className="border rounded-s-none"
                            size="xs"
                            color="gray"
                            theme={button_custom}
                            onClick={() =>
                                setConfirmOpen(true)
                            }
                        >
                            <div className="flex items-center gap-1">
                                <MdDeleteOutline className="text-slate-500" />
                                <span className="text-xs">
                                    Delete
                                </span>
                            </div>
                        </Button>
                    </div>
                    <div className="">
                        <Button
                            theme={button_custom}
                            size="xs"
                            color="gray"
                            onClick={() => setIsOpenAdd(true)}
                        >
                            <div className="flex items-center gap-1">
                                <IoMdAdd className="text-slate-500" />
                                <span className="text-xs">
                                    Add
                                </span>
                            </div>
                        </Button>

                    </div>
                </div>

                <div className="overflow-x-auto bg-white border rounded-lg ">
                    <Table hoverable theme={table_custom}>
                        <TableHead className=" bg-slate-100">
                            <TableHeadCell>
                                <Checkbox
                                    color="blue"
                                    checked={isCheckAll}
                                    id={0}
                                    onChange={(e) =>
                                        headerCheckBoxChecked(e)
                                    }
                                />
                            </TableHeadCell>
                            <TableHeadCell>
                                <SortLink routeName={currentPageRoute} column={'name'} sortType={collection?.getData?.sort == "asc" ? "desc" : "asc"} />
                            </TableHeadCell>

                            <TableHeadCell>
                                <h3>Mobile</h3>
                            </TableHeadCell>

                            <TableHeadCell>
                                <h3>Email</h3>
                            </TableHeadCell>

                            <TableHeadCell className="w-28">
                                <h3>Report To</h3>
                            </TableHeadCell>

                            <TableHeadCell>
                                <h3>Role</h3>
                            </TableHeadCell>
                            <TableHeadCell>
                                <SortLink showName={'Active'} routeName={currentPageRoute} column={'isActive'} sortType={collection?.getData?.sort == "asc" ? "desc" : "asc"} />
                            </TableHeadCell>
                            <TableHeadCell>
                                <h3>Actions</h3>
                            </TableHeadCell>
                        </TableHead>

                        <TableBody className="divide-y">
                            {collection?.users?.data.map((user, k) => {
                                return (
                                    <Fragment key={"t-row-user-" + k}>
                                        <TableRow className="bg-white dark:border-gray-700 dark:bg-gray-800" >
                                            <TableCell>
                                                {
                                                    (user.id != 1) &&
                                                    <Checkbox
                                                        color={"blue"}
                                                        className="rowCheckBox"
                                                        id={user.id}
                                                        onChange={(e) => {
                                                            getCheckedIds(e);
                                                            checkAddCheckBoxChecked();
                                                        }}
                                                    />
                                                }
                                            </TableCell>
                                            <TableCell>
                                                <div className="text-sm text-blue-500 text-nowrap">
                                                    {user.username ?? '-'}
                                                </div>
                                                <div className="text-xs text-nowrap">
                                                    Sales Executive
                                                </div>
                                            </TableCell>
                                            <TableCell>
                                                {user.mobile ?? '-'}
                                                {/* +91 8561024850 */}
                                            </TableCell>
                                            <TableCell>
                                                {user.email ?? '-'}
                                            </TableCell>
                                            <TableCell>
                                                <Tooltip
                                                    content={user.parent.username}
                                                    className="p-1 bg-slate-700"
                                                >
                                                    <Avatar rounded src={user.file ?? ''} />
                                                </Tooltip>
                                            </TableCell>
                                            <TableCell className="items-center m-auto">
                                                <div className="flex flex-wrap justify-center gap-2 " style={{ width: '180px' }}>
                                                    {user?.roles?.map((role, k) => <Badge key={'badge-' + k} className="text-center capitalize">{role.name}</Badge>)}
                                                </div>
                                            </TableCell>

                                            <TableCell>
                                                <ToggleSwitch checked={user.isActive}
                                                    disabled={(user.id == 1)}
                                                    onChange={() => {
                                                        HandleChangeFlag('user', 'isActive', user.id, (user.isActive == 1) ? 0 : 1);
                                                        setUpdatingId(user.id);
                                                    }}
                                                    color="blue" theme={toggle_custom}
                                                />

                                            </TableCell>

                                            <TableCell>
                                                <div className="flex justify-between gap-2">
                                                    <div className="flex gap-2">
                                                        {
                                                            (user.id != 1) && (currentUser.id != 1) &&
                                                            <>
                                                                <Tooltip
                                                                    content="Documents"
                                                                    className="p-1 px-2 bg-slate-700"
                                                                >
                                                                    <Button
                                                                        className="pb-1 text-white bg-orange-500"
                                                                        theme={
                                                                            button_custom
                                                                        }
                                                                        color=""
                                                                        size="xs"
                                                                        onClick={() =>
                                                                            setIsOpenDocuments(
                                                                                true
                                                                            )
                                                                        }
                                                                    >
                                                                        <MdOutlineContactPage />
                                                                    </Button>
                                                                </Tooltip>
                                                                {/*--------- Documents Drawer ------- */}
                                                                <Tooltip
                                                                    content="Edit"
                                                                    className="p-1 px-2 bg-slate-700"
                                                                >
                                                                    <Button
                                                                        theme={
                                                                            button_custom
                                                                        }
                                                                        color="success"
                                                                        size="xs"
                                                                        onClick={() => { setIsOpenEdit(true); setSelectedUser(user) }}
                                                                    >
                                                                        <MdOutlineModeEdit />
                                                                    </Button>
                                                                </Tooltip>
                                                                <Tooltip
                                                                    content="Delete"
                                                                    className="p-1 px-2 bg-slate-700"
                                                                >
                                                                    <Button
                                                                        theme={
                                                                            button_custom
                                                                        }
                                                                        color="failure"
                                                                        size="xs"
                                                                    >
                                                                        <MdDeleteOutline />
                                                                    </Button>
                                                                </Tooltip>
                                                            </>
                                                        }{
                                                            (currentUser.id == 1) && <>
                                                                <Tooltip
                                                                    content="Documents"
                                                                    className="p-1 px-2 bg-slate-700"
                                                                >
                                                                    <Button
                                                                        className="pb-1 text-white bg-orange-500"
                                                                        theme={
                                                                            button_custom
                                                                        }
                                                                        color=""
                                                                        size="xs"
                                                                        onClick={() =>
                                                                            setIsOpenDocuments(
                                                                                true
                                                                            )
                                                                        }
                                                                    >
                                                                        <MdOutlineContactPage />
                                                                    </Button>
                                                                </Tooltip>
                                                                {/*--------- Documents Drawer ------- */}
                                                                <Tooltip
                                                                    content="Edit"
                                                                    className="p-1 px-2 bg-slate-700"
                                                                >
                                                                    <Button
                                                                        theme={
                                                                            button_custom
                                                                        }
                                                                        color="success"
                                                                        size="xs"
                                                                        onClick={() => { setIsOpenEdit(true); setSelectedUser(user) }}
                                                                    >
                                                                        <MdOutlineModeEdit />
                                                                    </Button>
                                                                </Tooltip>
                                                                <Tooltip
                                                                    content="Delete"
                                                                    className="p-1 px-2 bg-slate-700"
                                                                >
                                                                    <Button
                                                                        theme={
                                                                            button_custom
                                                                        }
                                                                        color="failure"
                                                                        size="xs"
                                                                    >
                                                                        <MdDeleteOutline />
                                                                    </Button>
                                                                </Tooltip>
                                                            </>
                                                        }

                                                    </div>
                                                    <div>
                                                        <button
                                                            className={`${(toggleDetails.child === "t-row-user-" + k) ? ' -rotate-180 ' : '  '} duration-300 p-1.5`}
                                                            id={"parentID-" + k}
                                                            onClick={() => {
                                                                if (toggleDetails.parent == "parentID-" + k) {
                                                                    setToggleDetails({ parent: '', child: '' });
                                                                } else {
                                                                    setToggleDetails({ parent: "parentID-" + k, child: "t-row-user-" + k });
                                                                }
                                                            }}
                                                        >
                                                            <FaAngleUp className={` fill-gray-500`} />
                                                        </button>
                                                    </div>
                                                </div>
                                            </TableCell>
                                        </TableRow>
                                        <TableRow
                                            className="hidden duration-300 ease-in bg-gray-100 child-row"
                                            id={"t-row-user-" + k}
                                        >
                                            <TableCell colSpan={9}>
                                                <Details></Details>
                                            </TableCell>
                                        </TableRow>
                                    </Fragment>
                                )
                            })}
                        </TableBody>
                    </Table>
                </div>
            </div>
            <div className="w-full p-3 bg-white border rounded-lg shadow-sm float-end dark:border-gray-500 dark:rounded-lg dark:bg-gray-700">
                <div className="flex flex-wrap justify-between gap-2 lg:justify-between lg:gap-0 md:gap-0">
                    <div className="flex items-center gap-4">
                        <PerPageDropdown
                            getDataFields={
                                collection?.getData ?? null
                            }
                            routeName={"admin.users.index"}
                            data={collection?.users}
                        />
                    </div>

                    <Paginate tableData={collection?.users} />
                </div>
            </div>
            {isOpenDocuments &&
                <Drawer
                    theme={customDrawer}
                    open={
                        isOpenDocuments
                    }
                    onClose={
                        () => setIsOpenDocuments(false)
                    }
                    position="right"
                    className="w-full p-0 lg:w-4/5"
                >
                    <DrawerItems>
                        <div className="flex items-center justify-between px-4 py-2 mb-3 bg-white">
                            <div className="flex items-center gap-2">
                                <img
                                    className="w-12 h-12 rounded-full"
                                    src="https://img.freepik.com/free-vector/businessman-character-avatar-isolated_24877-60111.jpg?t=st=1720614745~exp=1720618345~hmac=38ff343a9443982af738325ad4d54d1d39ba540be4c28013b03cf063347d255e&w=200"
                                    alt=""
                                />
                                <div>
                                    <h4 className="text-base font-medium text-blue-600">
                                        Abhishek Chatterjee(DF25)
                                    </h4>
                                    <h6> Sales Manager</h6>
                                </div>
                            </div>
                            <button
                                className="bg-transparent"
                                onClick={
                                    () => setIsOpenDocuments(false)
                                }
                            >
                                <IoClose
                                    className="text-[24px] text-slate-400"
                                />
                            </button>
                        </div>
                        <Documents></Documents>
                    </DrawerItems>
                </Drawer>
            }
            {
                isOpenEdit &&
                <Drawer
                    theme={customDrawer}
                    open={isOpenEdit}
                    onClose={() => setIsOpenEdit(false)}
                    position="right"
                    className="w-full xl:w-3/5 lg:w-4/5 md:w-3/5"
                >
                    <DrawerHeader
                        title="Edit User"
                        titleIcon={
                            PiUserFocus
                        }
                    />
                    <DrawerItems>
                        <Edit user={selectedUser} onClose={() => setIsOpenEdit(false)} />
                    </DrawerItems>
                </Drawer>
            }
            {
                isOpenAdd &&
                <Drawer
                    theme={customDrawer}
                    open={isOpenAdd}
                    onClose={() => setIsOpenAdd(false)}
                    position="right"
                    className="w-full xl:w-3/5 lg:w-4/5 md:w-3/5"
                >
                    <DrawerHeader
                        title="Add User"
                        titleIcon={
                            PiUserFocus
                        }
                    />
                    <DrawerItems>
                        <Add onClose={() => setIsOpenAdd(false)} />
                    </DrawerItems>
                </Drawer>
            }

            {
                isConfirmOpen &&
                <ConfirmBox
                    isOpen={isConfirmOpen}
                    onClose={() => setConfirmOpen(false)} // Close the confirm box
                    onAction={handleConfirmBoxResult} // Handle the user's choice
                    title="Are you sure you want to delete this?"
                    message="This action cannot be undone."
                    confirmText="Yes, Delete!"
                    cancelText="No, Keep It"
                    confirmColor="orange"
                    cancelColor="gray"
                    icon={<FaRegTrashCan />}
                />
            }
        </AdminMain >
    );
}

export default Index;



