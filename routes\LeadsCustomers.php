<?php


use App\Http\Controllers\LeadsCustomers\LeadController;
use App\Http\Controllers\LeadsCustomers\LeadsCustomersController;
use Illuminate\Support\Facades\Route;

Route::name('leadsAndCustomers.')->prefix('leadsAndCustomers')->group(function () {
    Route::get('/', [LeadsCustomersController::class, 'index'])->name('index');

    // Leads Routes
    Route::resource('leads', LeadController::class)->except(["update", "edit", "create"])->names('leads');

    // Customers Routes
    Route::get('customers', [LeadsCustomersController::class, 'customers'])->name('customers');

    // Treasure Box Routes
    Route::get('treasurebox', [LeadsCustomersController::class, 'treasureBox'])->name('treasurebox');
    
});

// helper routes
Route::get('leads/get-lead-source', [LeadController::class, 'getLeadSource'])->name('leads.getLeadSource');
Route::get('leads/get-lead-for', [LeadController::class, 'getLeadFor'])->name('leads.getLeadFor');
Route::get('leads/get-agents', [LeadController::class, 'getAgents'])->name('leads.getLeadAgents');
Route::post("leads/assign/", [LeadController::class, "assignLeadToUser"])->name('leads.assign');
Route::get('leads/get-lead-user-assign-data', [LeadController::class, 'getLeadUserAssignData'])->name('leads.getLeadUserAssignData');
