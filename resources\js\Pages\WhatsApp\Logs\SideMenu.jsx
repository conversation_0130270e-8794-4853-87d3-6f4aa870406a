import { Link } from "@inertiajs/react";
import { Dropdown, DropdownItem } from "flowbite-react";
import { BiMessageDetail } from "react-icons/bi";
import { MdAutorenew, MdOutlineCall } from "react-icons/md";
import { PiPlugsConnected } from "react-icons/pi";

function SideMenu() {
    const menuDropdown = {
        arrowIcon: "ml-2 h-4 w-4",
        content: "py-1 focus:outline-none ",
        floating: {
            animation: "transition-opacity",
            arrow: {
                base: "absolute z-10 h-2 w-2 rotate-45",
                style: {
                    dark: "bg-gray-900 dark:bg-gray-700",
                    light: "bg-white",
                    auto: "bg-white dark:bg-gray-700",
                },
                placement: "-4px",
            },
            base: "z-10  divide-y divide-gray-100 rounded shadow focus:outline-none w-full",
            content: "py-1 text-sm text-gray-700 dark:text-gray-200",
            divider: "my-1 h-px bg-gray-100 dark:bg-gray-600",
            header: "block px-4 py-2 text-sm text-gray-700 dark:text-gray-200",
            hidden: "invisible opacity-0",
            item: {
                container: "w-full",
                base: "flex w-full cursor-pointer items-center justify-start px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 hover:bg-gray-100 focus:outline-none dark:text-gray-200 dark:hover:bg-gray-600 dark:hover:text-white dark:focus:bg-gray-600 dark:focus:text-white",
                icon: "mr-2 h-4 w-4",
            },
            style: {
                dark: "bg-gray-900 text-white dark:bg-gray-700",
                light: "border border-gray-200 bg-white text-gray-900",
                auto: "border border-gray-200 bg-white text-gray-900 dark:border-none dark:bg-gray-700 dark:text-white",
            },
            target: "w-full ",
        },
        inlineWrapper: "flex items-center",
    };
    const items = [
        {
            name: "API Logs",
            url: route("whatsapp.log.api"),
            active: route().current("whatsapp.log.api"),
            icon: <PiPlugsConnected className="text-orange-500" />,
            classes: {
                groupHover: " group-hover:text-orange-500 ",
                group: " hover:bg-orange-50 ",
                active: " bg-orange-50 ",
            },
        },
        {
            name: "Bots",
            url: route("whatsapp.log.autoreply"),
            active: route().current("whatsapp.log.autoreply"),
            icon: <MdAutorenew className="text-green-500" />,
            classes: {
                groupHover: " group-hover:text-green-500 ",
                group: " hover:bg-green-50 ",
                active: " bg-green-50 ",
            },
        },
        {
            name: "Messages",
            url: route("whatsapp.log.incomingMessages"),
            active: route().current("whatsapp.log.incomingMessages"),
            icon: <BiMessageDetail className="text-blue-500" />,
            classes: {
                groupHover: " group-hover:text-blue-500 ",
                group: " hover:bg-blue-50 ",
                active: " bg-blue-50 ",
            },
        },
    ];
    return (
        <div className="">
            <div className="bg-white rounded lg:hidden md:hidden">
                <Dropdown
                    label="Menu"
                    size="sm"
                    className="w-full"
                    theme={menuDropdown}
                    color="light"
                >
                    {items.map((item, k) => {
                        return (
                            <DropdownItem
                                key={"navItems-" + k}
                                as={Link}
                                href={item.url}
                            >
                                {item.name}
                            </DropdownItem>
                        );
                    })}
                </Dropdown>
            </div>
            <div className="hidden w-full lg:block md:block">
                {items.map((item, k) => {
                    return (
                        <Link href={item.url} key={k}>
                            <div
                                className={
                                    "border-b hover:border-transparent p-1.5 px-2 flex items-center gap-3 bg-white rounded rounded-b-none hover:rounded-b  group" +
                                    item.classes.group +
                                    (item.active ? item.classes.active : "")
                                }
                            >
                                {item.icon}
                                <span className={item.classes.groupHover}>
                                    {item.name}
                                </span>
                            </div>
                        </Link>
                    );
                })}
            </div>
        </div>
    );
}

export default SideMenu;
