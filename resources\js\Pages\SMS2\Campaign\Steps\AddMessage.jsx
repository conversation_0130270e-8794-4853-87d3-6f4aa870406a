import MessageBox from "@/Components/HelperComponents/MessageBox";
import { button_custom } from "@/Pages/Helpers/DesignHelper";
import { WaBaExtractVariables } from "@/Pages/Helpers/WabaHelper";
import { Head, useForm } from "@inertiajs/react";
import { Button, Label, Modal, Select, TextInput } from "flowbite-react";
import { useEffect, useState } from "react";
import { MdOutlineCampaign } from "react-icons/md";
import CreateMain from "./CreateMain";
import Templates from "./Templates";

function AddMessage({ collection, }) {
    const baseRoute = 'sms2';
    const { campaign } = collection;
    const [submitDisable, setSubmitDisable] = useState(true);
    const [variables, setVariables] = useState([]);
    const [openTemplate, setOpenTemplate] = useState(false);
    const variable_option = { name: 'name', mobile: 'mobile', email: 'email', var1: 'var1', var2: 'var2', var3: 'var3', var4: 'var4', var5: 'var5' };
    const [selectedTemplate, setSelectedTemplate] = useState(null);
    const [resetState, setResetState] = useState(0);
    const [openTemplateModal, setOpenTemplateModal] = useState(false);
    const { data, setData, post, processing, reset, errors } = useForm({
        msg: campaign.msg,
        campaign_id: campaign.id,
        varObject: null,
        template_id: '',
        message: '',
        variable_count: 0,
    });

    useEffect(() => {
        if (variables) {
            setData('variable_count', Object.keys(variables).length);
        }
    }, [variables])

    useEffect(() => {
        if (data.msg) {
            setData('message', data.msg);
            setVariables(WaBaExtractVariables(data.msg));
            setSubmitDisable(false);
        } else {
            setSubmitDisable(true);
        }
    }, [data.msg]);

    useEffect(() => {
        if (selectedTemplate != null) {
            setData(prev => ({
                ...prev,
                msg: selectedTemplate.body,
                template_id: selectedTemplate.id,
            }));
        }
        setResetState(pre => pre + 1)
    }, [selectedTemplate]);
    const handleVariableChange = (key, sub_key, value) => {
        setData(prevState => ({
            ...prevState,
            varObject: {
                ...prevState.varObject,
                [key]: {
                    [sub_key == 'column' ? 'column' : 'fixed']: value,
                    [sub_key == 'column' ? 'fixed' : 'column']: ""
                },
            }
        }));
    }

    const addCampaignMessage = () => {
        setSubmitDisable(false);
        post(route(baseRoute + ".campaign.message.store"), {
            onSuccess: () => { },
        });
    };

    return (
        <CreateMain >
            <Head title="Campaign Message" />
            <div className="flex items-center gap-2 pt-4">
                <MdOutlineCampaign className="text-3xl text-slate-400" />
                <span className="text-2xl capitalize">Compose Message</span>
            </div>
            <div>
                <div className="">
                    <div className="w-full p-2 bg-white border rounded-lg">
                        <h4 className="text-base">Compose Message</h4>
                        <p className="text-sm text-gray-400">
                            Write, edit, and personalize your message quickly.
                        </p>
                    </div>

                    <div className="flex justify-center p-0 mt-3 border-none bg-slate-100">
                        <div className="w-full bg-white border rounded-xl h-fit ">
                            <MessageBox
                                data={data.msg}
                                setData={setData}
                                errors={errors}
                                fileUpload={false}
                                allData={data}
                                addVariables={true}
                                isOfficial

                                openTemplateModel={() => setOpenTemplateModal(true)}
                            />
                        </div>

                    </div>


                    {data.variable_count > 0 &&
                        <div className="p-2 mt-3 mb-2 bg-white border rounded-lg">
                            <div className="flex flex-col w-full max-w-md gap-4">
                                {Object.entries(variables).map(([key, value]) =>
                                (<div key={`temp-inputs-${key}`}>
                                    <div className="block mb-2">
                                        <Label htmlFor={`var-id-${key}`} className="uppercase" value={key} />
                                    </div>
                                    <div className="flex gap-3">
                                        <TextInput
                                            className="w-full"
                                            id={`var-id-${key}`}
                                            type="text"
                                            sizing="sm"
                                            placeholder={key}

                                            disabled={data.varObject && data.varObject[key] && data.varObject[key]['column'].length > 0}
                                            onChange={(e) => handleVariableChange(key, "fixed", e.target.value)}
                                        />
                                        <Select onChange={(e) => handleVariableChange(key, "column", e.target.value)} sizing="sm"
                                            disabled={data.varObject && data.varObject[key] && data.varObject[key]['fixed'].length > 0}
                                        >
                                            <option value="">Select Column</option>
                                            {Object.values(variable_option).map((column, index) => (
                                                <option key={`col-${index}`} value={column}>
                                                    {column}
                                                </option>
                                            ))}
                                        </Select>
                                    </div>
                                    {errors[key] && <div className="text-red-600">{errors[key]}</div>}
                                </div>))
                                }


                                {errors.variable_error &&
                                    <div className="text-red-500">{errors.variable_error}</div>
                                }
                            </div>

                        </div>
                    }
                    <div className='flex justify-end p-3 mt-3 rounded-lg'>
                        <Button isProcessing={processing}
                            color="blue"
                            theme={button_custom}
                            size="sm"
                            type="submit"
                            disabled={submitDisable}
                            onClick={addCampaignMessage}
                        >Continue</Button>
                    </div>
                </div>
                <Modal show={openTemplateModal} onClose={() => setOpenTemplateModal(false)}>
                    <Modal.Header>Select Template</Modal.Header>
                    <Modal.Body>
                        <Templates setSelectedTemplate={setSelectedTemplate} selectedTemplate={selectedTemplate} />
                    </Modal.Body>


                </Modal>
            </div>
        </CreateMain>
    );
}

export default AddMessage;
