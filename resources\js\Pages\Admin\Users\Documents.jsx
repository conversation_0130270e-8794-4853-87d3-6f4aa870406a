import {
    Button,
    Tooltip,
    Checkbox,
    Table,
    ToggleSwitch,
    Label,
    TextInput,
    Select,
    FileInput,
    Modal,
    Textarea,
    Carousel,
} from "flowbite-react";
import {
    MdOutlineEdit,
    MdDeleteOutline,
    MdArrowDownward,
    MdOutlineBrokenImage,
    MdContentCopy,
} from "react-icons/md";
import { useState, useEffect } from "react";
import { IoClose,IoCloseSharp } from "react-icons/io5";
import $ from "jquery";
import { CiExport } from "react-icons/ci";
import { TbColumns3 } from "react-icons/tb";
import { BiCheckDouble } from "react-icons/bi";
import { GoDownload } from "react-icons/go";

export default function Documents() {
    const [zoom, setZoom] = useState(false);
    const [transformOrigin, setTransformOrigin] = useState({
        x: "50%",
        y: "50%",
    });

    const handleMouseMove = (event) => {
        const { offsetX, offsetY, target } = event.nativeEvent;
        const { offsetWidth, offsetHeight } = target;

        const deltaX = (offsetX / offsetWidth) * 100;
        const deltaY = (offsetY / offsetHeight) * 100;

        setTransformOrigin({ x: `${deltaX}%`, y: `${deltaY}%` });
    };
    const [switch2, setSwitch2] = useState(false);

    const table_custom = {
        head: {
            base: "group/head text-gray-700 dark:text-gray-400 ",
            cell: {
                base: "text-slate-900 dark:text-slate-300 font-medium border dark:border-slate-500 bg-slate-100 text-nowrap px-3 py-2 group-first/head:first:rounded-tl-lg group-first/head:last:rounded-tr-lg dark:bg-gray-700",
            },
        },
        body: {
            base: "group/body",
            cell: {
                base: "border-b px-3 py-1 group-first/body:group-first/row:first:rounded-tl-lg group-first/body:group-first/row:last:rounded-tr-lg group-last/body:group-last/row:first:rounded-bl-lg group-last/body:group-last/row:last:rounded-br-lg",
            },
        },
    };

    const button_custom = {
        color: {
            gray: ":ring-cyan-700 border border-gray-200 bg-white text-gray-900 f enabled:hover:bg-gray-100 dark:border-gray-600 dark:bg-transparent dark:text-gray-400 dark:enabled:hover:bg-gray-700 dark:enabled:hover:text-white",
        },
        pill: {
            off: "rounded",
            on: "rounded-full",
        },
        size: {
            xs: "p-0.5 text-lg",
            sm: "px-3 py-1.5 text-sm",
            md: "px-4 py-2 text-sm",
            lg: "px-5 py-2.5 text-base",
            xl: "px-6 py-3 text-base",
        },
    };

    const slider_custom = {
        root: {
            base: "relative h-full w-full",
            leftControl:
                "absolute left-0 top-0 flex h-full items-center justify-center focus:outline-none ",
            rightControl:
                "absolute right-0 top-0 flex h-full items-center justify-center focus:outline-none ",
        },
        indicators: {
            active: {
                off: "bg-black/50 hover:bg-black dark:bg-gray-800/50 dark:hover:bg-gray-800",
                on: "bg-black dark:bg-gray-800",
            },
            base: "h-3 w-3 rounded-full",
            wrapper:
                "absolute bottom-5 left-1/2 flex -translate-x-1/2 space-x-3",
        },
        item: {
            base: "absolute left-1/2 top-1/2 block w-full -translate-x-1/2 -translate-y-1/2 m-0",
            wrapper: {
                off: "w-full flex-shrink-0 transform cursor-default snap-center",
                on: "w-full flex-shrink-0 transform cursor-grab snap-center",
            },
        },
        control: {
            base: "inline-flex h-8 w-8 items-center justify-center rounded-full bg-black/30 group-hover:bg-black/50 group-focus:outline-none group-focus:ring-4 group-focus:ring-white dark:bg-gray-800/30 dark:group-hover:bg-gray-800/60 dark:group-focus:ring-gray-800/70 sm:h-10 sm:w-10",

            icon: "h-5 w-5 text-white dark:text-gray-800 sm:h-6 sm:w-6 ",
        },
        scrollContainer: {
            base: "flex h-full snap-mandatory overflow-y-hidden overflow-x-scroll scroll-smooth rounded-lg ",
            snap: "snap-x",
        },
    };
    // ----------open edit modal -------------
    const [openEditModal, setOpenEditModal] = useState(false);

    // ----------open View modal -------------
    const [openViewModal, setOpenViewModal] = useState(false);

    const [isCheckAll, setIsCheckAll] = useState(false);

    useEffect(() => {
        if (isCheckAll) {
            $(".rowCheckBox").prop("checked", true);
        } else {
            $(".rowCheckBox").prop("checked", false);
        }
    }, [isCheckAll]);

    return (
        <>
            <div className="bg-white h-[60vh] overflow-auto flex flex-col justify-between m-3">
                <div>
                    <div className="flex justify-between p-2">
                        <div className="flex ">
                            <Button
                                theme={button_custom}
                                size="xs"
                                color="gray"
                                className="border-e-0 rounded-e-none"
                            >
                                <div className="flex items-center gap-1">
                                    <CiExport className="text-slate-500" />
                                    <span className="text-xs">Export</span>
                                </div>
                            </Button>
                            <Button
                                className="border rounded-s-none"
                                size="xs"
                                color="gray"
                                theme={button_custom}
                            >
                                <div className="flex items-center gap-1">
                                    <MdDeleteOutline className="text-slate-500" />
                                    <span className="text-xs">Delete</span>
                                </div>
                            </Button>
                        </div>
                        <div className="">
                            <Button
                                theme={button_custom}
                                size="xs"
                                color="gray"
                                id="dropdownInformationButton"
                                data-dropdown-toggle="dropdownNotification"
                                type="button"
                            >
                                <div className="flex items-center gap-1">
                                    <TbColumns3 className="text-slate-500" />
                                    <span className="text-xs">Columns</span>
                                </div>
                            </Button>
                            {/* Dropdown menu */}
                            <div
                                id="dropdownNotification"
                                className="z-20 border shadow-lg hidden w-full max-w-sm bg-white divide-y divide-gray-100 rounded-lg  dark:bg-gray-800 dark:divide-gray-700"
                                aria-labelledby="dropdownNotificationButton"
                            >
                                <div className="block px-4 py-2 font-medium text-center text-gray-700 rounded-t-lg bg-gray-50 dark:bg-gray-800 dark:text-white">
                                    <div className="flex justify-between">
                                        <div className="">
                                            <div className="group  text-gray-400 p-1 flex items-center gap-1">
                                                <svg
                                                    xmlns="http://www.w3.org/2000/svg"
                                                    className=" fill-slate-400  "
                                                    height="24px"
                                                    viewBox="0 0 24 24"
                                                    width="24px"
                                                >
                                                    <rect
                                                        fill="none"
                                                        height="24"
                                                        width="24"
                                                    />
                                                    <path d="M20,4H4C2.9,4,2,4.9,2,6v12c0,1.1,0.9,2,2,2h16c1.1,0,2-0.9,2-2V6C22,4.9,21.1,4,20,4z M8,18H4V6h4V18z M14,18h-4V6h4V18z M20,18h-4V6h4V18z" />
                                                </svg>
                                                Column
                                            </div>
                                        </div>
                                        <div className="">
                                            <button className="p-1 flex text-blue-600">
                                                <svg
                                                    xmlns="http://www.w3.org/2000/svg"
                                                    height="24px"
                                                    viewBox="0 0 24 24"
                                                    width="24px"
                                                    className="fill-blue-600"
                                                >
                                                    <g>
                                                        <path
                                                            d="M0,0h24v24H0V0z"
                                                            fill="none"
                                                        />
                                                    </g>
                                                    <g>
                                                        <g>
                                                            <path d="M6,13c0-1.65,0.67-3.15,1.76-4.24L6.34,7.34C4.9,8.79,4,10.79,4,13c0,4.08,3.05,7.44,7,7.93v-2.02 C8.17,18.43,6,15.97,6,13z M20,13c0-4.42-3.58-8-8-8c-0.06,0-0.12,0.01-0.18,0.01l1.09-1.09L11.5,2.5L8,6l3.5,3.5l1.41-1.41 l-1.08-1.08C11.89,7.01,11.95,7,12,7c3.31,0,6,2.69,6,6c0,2.97-2.17,5.43-5,5.91v2.02C16.95,20.44,20,17.08,20,13z" />
                                                        </g>
                                                    </g>
                                                </svg>
                                                Reset
                                            </button>
                                        </div>
                                    </div>
                                </div>
                                <div className="divide-y divide-gray-100 dark:divide-gray-700">
                                    <div className="flex flex-col">
                                        <div className="relative overflow-x-auto shadow-md sm:rounded-lg ">
                                            <table className=" w-full text-sm text-left rtl:text-right text-gray-500 dark:text-gray-400">
                                                <thead className="text-xs text-gray-700 uppercase dark:text-gray-400">
                                                    <tr>
                                                        <th className="p-2 bg-gray-50 dark:bg-gray-800 w-14 text-center ">
                                                            List
                                                        </th>
                                                        <th className="p-2  bg-gray-50 dark:bg-gray-800 w-14 text-center">
                                                            Details
                                                        </th>
                                                        <th className="p-2"></th>
                                                    </tr>
                                                </thead>
                                                <tbody className=" overflow-auto">
                                                    <tr className="border-b border-gray-200 dark:border-gray-700">
                                                        <td className="text-center max-w-max p-2 font-medium text-gray-900 whitespace-nowrap bg-gray-50 dark:text-white dark:bg-gray-800">
                                                            <Checkbox></Checkbox>
                                                        </td>
                                                        <td className="text-center p-2 font-medium text-gray-900 whitespace-nowrap bg-gray-50 dark:text-white dark:bg-gray-800">
                                                            <Checkbox></Checkbox>
                                                        </td>
                                                        <td className=" max-w-max px-6 py-4">
                                                            Name
                                                        </td>
                                                    </tr>
                                                    <tr className="border-b border-gray-200 dark:border-gray-700">
                                                        <td className="text-center  max-w-max p-2 font-medium text-gray-900 whitespace-nowrap bg-gray-50 dark:text-white dark:bg-gray-800">
                                                            <Checkbox></Checkbox>
                                                        </td>
                                                        <td className="text-center p-2 font-medium text-gray-900 whitespace-nowrap bg-gray-50 dark:text-white dark:bg-gray-800">
                                                            <Checkbox></Checkbox>
                                                        </td>
                                                        <td className=" max-w-max px-6 py-4">
                                                            Name
                                                        </td>
                                                    </tr>
                                                    <tr className="border-b border-gray-200 dark:border-gray-700">
                                                        <td className="text-center  max-w-max p-2 font-medium text-gray-900 whitespace-nowrap bg-gray-50 dark:text-white dark:bg-gray-800">
                                                            <Checkbox></Checkbox>
                                                        </td>
                                                        <td className="text-center p-2 font-medium text-gray-900 whitespace-nowrap bg-gray-50 dark:text-white dark:bg-gray-800">
                                                            <Checkbox></Checkbox>
                                                        </td>
                                                        <td className="max-w-max px-6 py-4">
                                                            Name
                                                        </td>
                                                    </tr>
                                                    <tr className="border-b border-gray-200 dark:border-gray-700">
                                                        <td className="text-center max-w-max p-2 font-medium text-gray-900 whitespace-nowrap bg-gray-50 dark:text-white dark:bg-gray-800">
                                                            <Checkbox></Checkbox>
                                                        </td>
                                                        <td className="text-center p-2 font-medium text-gray-900 whitespace-nowrap bg-gray-50 dark:text-white dark:bg-gray-800">
                                                            <Checkbox></Checkbox>
                                                        </td>
                                                        <td className="max-w-max px-6 py-4">
                                                            Name
                                                        </td>
                                                    </tr>
                                                </tbody>
                                            </table>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    {/*--------- empty documents image ---------*/}
                    <div className="text-center pb-4">
                        <img
                            className="inline"
                            src="assets/img/noDocument.png"
                            alt=""
                        />
                        <h4 className="text-slate-500">
                            No Documents Uploaded Right Now!
                        </h4>
                    </div>

                    {/* documents table */}
                    <div className="overflow-x-auto bg-white border rounded-lg">
                        <Table hoverable theme={table_custom}>
                            <TableHead className=" bg-slate-100">
                                <TableHeadCell>
                                    <Checkbox
                                        checked={isCheckAll}
                                        onChange={() =>
                                            setIsCheckAll(!isCheckAll)
                                        }
                                    />
                                </TableHeadCell>

                                <TableHeadCell>
                                    <h3>Document</h3>
                                </TableHeadCell>

                                <TableHeadCell>
                                    <h3>File Type</h3>
                                </TableHeadCell>

                                <TableHeadCell>
                                    <h3>Document Pictures</h3>
                                </TableHeadCell>

                                <TableHeadCell>
                                    <h3>Status</h3>
                                </TableHeadCell>

                                <TableHeadCell className="w-28">
                                    <h3>Remark</h3>
                                </TableHeadCell>

                                <TableHeadCell>
                                    <h3>User Visibility</h3>
                                </TableHeadCell>
                                <TableHeadCell>
                                    <h3>Actions</h3>
                                </TableHeadCell>
                            </TableHead>

                            <TableBody className="divide-y">
                                <TableRow className="bg-white dark:border-gray-700 dark:bg-gray-800">
                                    <TableCell>
                                        <Checkbox className="rowCheckBox" />
                                    </TableCell>

                                    <TableCell className="font-medium text-gray-900 whitespace-nowrap dark:text-white">
                                        {"Aadhar Card"}
                                    </TableCell>
                                    <TableCell>
                                        <Tooltip
                                            content="Tooltip content"
                                            placement="right"
                                        >
                                            PDF
                                        </Tooltip>
                                    </TableCell>
                                    <TableCell>
                                        <button>
                                            <MdOutlineBrokenImage className="h-8 w-8 text-slate-400" />
                                        </button>
                                    </TableCell>
                                    <TableCell>
                                        <div className="py-0.5 px-2 my-1 border border-blue-600  bg-blue-50 text-blue-600 w-fit rounded">
                                            Pending
                                        </div>
                                    </TableCell>
                                    <TableCell>-</TableCell>
                                    <TableCell>
                                        <ToggleSwitch
                                            checked={switch2}
                                            onChange={setSwitch2}
                                        />
                                    </TableCell>

                                    <TableCell>
                                        <div className="flex items-center gap-3">
                                            <Tooltip content="Reject">
                                                <Button
                                                    theme={button_custom}
                                                    color="failure"
                                                    size="xs"
                                                >
                                                    <IoClose />
                                                </Button>
                                            </Tooltip>
                                            <Tooltip content="Approved">
                                                <Button
                                                    theme={button_custom}
                                                    size="xs"
                                                >
                                                    <BiCheckDouble />
                                                </Button>
                                            </Tooltip>
                                            <Tooltip content="Edit">
                                                <Button
                                                    theme={button_custom}
                                                    color="warning"
                                                    size="xs"
                                                    onClick={() =>
                                                        setOpenEditModal(true)
                                                    }
                                                >
                                                    <MdOutlineEdit />
                                                </Button>
                                            </Tooltip>
                                            <Tooltip content="Download">
                                                <Button
                                                    theme={button_custom}
                                                    color="blue"
                                                    size="xs"
                                                >
                                                    <MdArrowDownward />
                                                </Button>
                                            </Tooltip>
                                        </div>
                                    </TableCell>
                                </TableRow>
                                <TableRow className="bg-white dark:border-gray-700 dark:bg-gray-800">
                                    <TableCell>
                                        <Checkbox className="rowCheckBox" />
                                    </TableCell>

                                    <TableCell className="font-medium text-gray-900 whitespace-nowrap dark:text-white">
                                        {"PAN Card"}
                                    </TableCell>
                                    <TableCell>
                                        <Tooltip
                                            content="Tooltip content"
                                            placement="right"
                                        >
                                            JPEG
                                        </Tooltip>
                                    </TableCell>
                                    <TableCell>
                                        <button
                                            onClick={() =>
                                                setOpenViewModal(true)
                                            }
                                        >
                                            <img
                                                className="h-8 w-8 rounded"
                                                src="https://img.freepik.com/free-vector/businessman-character-avatar-isolated_24877-60111.jpg?t=st=1720614745~exp=1720618345~hmac=38ff343a9443982af738325ad4d54d1d39ba540be4c28013b03cf063347d255e&w=200"
                                                alt=""
                                            />
                                        </button>
                                    </TableCell>
                                    <TableCell>
                                        <div className="py-0.5 px-2 my-1 border border-red-600  bg-red-50 text-red-600 w-fit rounded">
                                            Reject
                                        </div>
                                    </TableCell>
                                    <TableCell className="text-nowrap">
                                        Picture quality is not clear
                                    </TableCell>
                                    <TableCell>
                                        <ToggleSwitch
                                            checked={switch2}
                                            onChange={setSwitch2}
                                        />
                                    </TableCell>

                                    <TableCell>
                                        <div className="flex items-center gap-3">
                                            <Tooltip content="Reject">
                                                <Button
                                                    theme={button_custom}
                                                    color="failure"
                                                    size="xs"
                                                >
                                                    <IoClose />
                                                </Button>
                                            </Tooltip>
                                            <Tooltip content="Approved">
                                                <Button
                                                    theme={button_custom}
                                                    size="xs"
                                                >
                                                    <BiCheckDouble />
                                                </Button>
                                            </Tooltip>
                                            <Tooltip content="Edit">
                                                <Button
                                                    theme={button_custom}
                                                    color="warning"
                                                    size="xs"
                                                >
                                                    <MdOutlineEdit />
                                                </Button>
                                            </Tooltip>
                                            <Tooltip content="Download">
                                                <Button
                                                    theme={button_custom}
                                                    color="blue"
                                                    size="xs"
                                                >
                                                    <MdArrowDownward />
                                                </Button>
                                            </Tooltip>
                                        </div>
                                    </TableCell>
                                </TableRow>
                                <TableRow className="bg-white dark:border-gray-700 dark:bg-gray-800">
                                    <TableCell>
                                        <Checkbox className="rowCheckBox" />
                                    </TableCell>

                                    <TableCell className="font-medium text-gray-900 whitespace-nowrap dark:text-white">
                                        {"Bank Passbook"}
                                    </TableCell>
                                    <TableCell>
                                        <Tooltip
                                            content="Tooltip content"
                                            placement="right"
                                        >
                                            JPEG
                                        </Tooltip>
                                    </TableCell>
                                    <TableCell>
                                        <MdOutlineBrokenImage className="h-8 w-8 text-slate-400" />
                                    </TableCell>
                                    <TableCell>
                                        <div className="py-0.5 px-2 my-1 border border-green-600  bg-green-50 text-green-600 w-fit rounded">
                                            Approved
                                        </div>
                                    </TableCell>
                                    <TableCell>-</TableCell>
                                    <TableCell>
                                        <ToggleSwitch
                                            checked={switch2}
                                            onChange={setSwitch2}
                                        />
                                    </TableCell>

                                    <TableCell>
                                        <div className="flex items-center gap-3">
                                            <Tooltip content="Reject">
                                                <Button
                                                    theme={button_custom}
                                                    color="failure"
                                                    size="xs"
                                                >
                                                    <IoClose />
                                                </Button>
                                            </Tooltip>
                                            <Tooltip content="Approved">
                                                <Button
                                                    theme={button_custom}
                                                    size="xs"
                                                >
                                                    <BiCheckDouble />
                                                </Button>
                                            </Tooltip>
                                            <Tooltip content="Edit">
                                                <Button
                                                    theme={button_custom}
                                                    color="warning"
                                                    size="xs"
                                                >
                                                    <MdOutlineEdit />
                                                </Button>
                                            </Tooltip>
                                            <Tooltip content="Download">
                                                <Button
                                                    theme={button_custom}
                                                    color="blue"
                                                    size="xs"
                                                >
                                                    <MdArrowDownward />
                                                </Button>
                                            </Tooltip>
                                        </div>
                                    </TableCell>
                                </TableRow>
                                <TableRow className="bg-white dark:border-gray-700 dark:bg-gray-800">
                                    <TableCell>
                                        <Checkbox className="rowCheckBox" />
                                    </TableCell>

                                    <TableCell className="font-medium text-gray-900 whitespace-nowrap dark:text-white">
                                        {"Last Degree"}
                                    </TableCell>
                                    <TableCell>
                                        <Tooltip
                                            content="Tooltip content"
                                            placement="right"
                                        >
                                            JPEG
                                        </Tooltip>
                                    </TableCell>
                                    <TableCell>
                                        <MdOutlineBrokenImage className="h-8 w-8 text-slate-400" />
                                    </TableCell>
                                    <TableCell>
                                        <div className="py-0.5 px-2 my-1 border border-green-600  bg-green-50 text-green-600 w-fit rounded">
                                            Approved
                                        </div>
                                    </TableCell>
                                    <TableCell>-</TableCell>
                                    <TableCell>
                                        <ToggleSwitch
                                            checked={switch2}
                                            onChange={setSwitch2}
                                        />
                                    </TableCell>

                                    <TableCell>
                                        <div className="flex items-center gap-3">
                                            <Tooltip content="Reject">
                                                <Button
                                                    theme={button_custom}
                                                    color="failure"
                                                    size="xs"
                                                >
                                                    <IoClose />
                                                </Button>
                                            </Tooltip>
                                            <Tooltip content="Approved">
                                                <Button
                                                    theme={button_custom}
                                                    size="xs"
                                                >
                                                    <BiCheckDouble />
                                                </Button>
                                            </Tooltip>
                                            <Tooltip content="Edit">
                                                <Button
                                                    theme={button_custom}
                                                    color="warning"
                                                    size="xs"
                                                >
                                                    <MdOutlineEdit />
                                                </Button>
                                            </Tooltip>
                                            <Tooltip content="Download">
                                                <Button
                                                    theme={button_custom}
                                                    color="blue"
                                                    size="xs"
                                                >
                                                    <MdArrowDownward />
                                                </Button>
                                            </Tooltip>
                                        </div>
                                    </TableCell>
                                </TableRow>
                            </TableBody>
                        </Table>
                    </div>
                </div>

                <div className="float-end border bottom-0 p-3 w-full bg-white ">
                    <div className="flex flex-wrap justify-between lg:gap-0 md:gap-0 gap-2">
                        <div className="flex gap-3 items-center">
                            <div className="text-gray-400 text-sm ">
                                Showing 1 to 25 of 62 entries
                            </div>
                            <div>
                                <select
                                    id="countries"
                                    className="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded focus:ring-blue-500 focus:border-blue-500 block p-1 ps-2 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-blue-500 dark:focus:border-blue-500"
                                >
                                    <option value={10} defaultValue={10}>
                                        10
                                    </option>
                                    <option value={15}>15</option>
                                    <option value={20}>20</option>
                                    <option value={25}>25</option>
                                    <option value={30}>30</option>
                                </select>
                            </div>
                        </div>
                        <div className="flex">
                            <Button
                                size="xs"
                                color="gray"
                                className="border-e-0 text-gray-400 px-2 rounded text-xs "
                            >
                                Previous
                            </Button>
                            <Button
                                size="xs"
                                color="blue"
                                className="border text-white border-blue-600 bg-blue-600 px-2.5 rounded-none text-sm "
                            >
                                1
                            </Button>
                            <Button
                                size="xs"
                                color="gray"
                                className="border text-blue-600 px-2.5 rounded-none text-xs "
                            >
                                2
                            </Button>
                            <Button
                                size="xs"
                                color="gray"
                                className="border-s-0 text-blue-600 px-2 rounded text-xs "
                            >
                                Next
                            </Button>
                        </div>
                    </div>
                </div>
            </div>
            <div className=" m-3">
                <div className="flex items-center gap-1 bg-[#F8F7FC] p-2 rounded-s rounded-e mt-2">
                    <MdContentCopy className="text-slate-400 text-lg" />
                    <h4 className="text-base">Add Document</h4>
                </div>
                <div className="bg-white pt-2 px-4">
                    <div className="">
                        <form className="flex items-end gap-5 flex-wrap">
                            <div>
                                <div className="mb-2 block">
                                    <Label
                                        htmlFor="documentName"
                                        value="Document"
                                    />
                                </div>
                                <TextInput
                                    id="documentName"
                                    type="email"
                                    placeholder="Document name"
                                    required
                                />
                            </div>
                            <div className="w-60">
                                <div className="mb-2 block">
                                    <Label
                                        htmlFor="fileType"
                                        value="Choose File Type"
                                    />
                                </div>
                                <Select id="fileType" required>
                                    <option>png</option>
                                    <option>jpeg</option>
                                    <option>jpg</option>
                                    <option>webep</option>
                                </Select>
                            </div>

                            <div id="fileUpload" className="max-w-md">
                                <div className="mb-2 block">
                                    <Label htmlFor="file" value="Upload File" />
                                </div>
                                <FileInput color="gray" id="file" />
                            </div>
                        </form>
                        <div className="pt-2">
                            <div className="mb-2 block">
                                <Label htmlFor="remark" value="Remark" />
                            </div>
                            <TextInput
                                id="remark"
                                type="email"
                                placeholder="Write Remark"
                                required
                            />
                        </div>

                        <div className="flex justify-end gap-5 py-3">
                            <Button
                                color="failure"
                                className="rounded"
                                size="xs"
                            >
                                <div className="flex items-center gap-0.5">
                                    <IoCloseSharp className="text-xl" />
                                    <span className="text-sm">Close</span>
                                </div>
                            </Button>
                            <Button color="blue" className="rounded" size="xs">
                                <div className="flex items-center gap-0.5">
                                    <BiCheckDouble className="text-xl" />
                                    <span className="text-sm">Save</span>
                                </div>
                            </Button>
                        </div>
                    </div>
                </div>
            </div>

            {/*---------- Edit Document Modal---------- */}
            <Modal show={openEditModal} onClose={() => setOpenEditModal(false)}>
                <ModalHeader className="p-1.5">
                    <div className="flex items-center gap-2">
                        <MdContentCopy className="text-slate-400" />
                        Edit Document
                    </div>
                </ModalHeader>
                <ModalBody className="bg-slate-100 py-3 px-4 rounded-b-lg">
                    <div className="space-y-6">
                        <div>
                            <label
                                className="block mb-2 text-sm font-medium text-slate-700 dark:text-white"
                                htmlFor="docName"
                            >
                                <span className="text-red-600">*</span> Document
                            </label>
                            <TextInput
                                id="docName"
                                type="email"
                                placeholder="Document name"
                                required
                                defaultValue="PAN Card"
                            />
                        </div>
                        <div>
                            <label
                                className="block mb-2 text-sm font-medium text-slate-700 dark:text-white"
                                htmlFor="filetype"
                            >
                                <span className="text-red-600">*</span> File
                                Type
                            </label>
                            <TextInput
                                id="filetype"
                                type="email"
                                placeholder="Document name"
                                required
                                defaultValue="JPEG"
                            />
                        </div>
                        <div>
                            <label
                                className="block mb-2 text-sm font-medium text-gray-900 dark:text-white"
                                htmlFor="uploadFile"
                            >
                                Upload File
                            </label>
                            <input
                                className="block w-full text-sm text-gray-900 border border-gray-300 rounded-lg cursor-pointer bg-gray-50 dark:text-gray-400 focus:outline-none dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400"
                                aria-describedby="uploadFile_help"
                                id="uploadFile"
                                type="file"
                            />
                        </div>
                        <div>
                            <label
                                className="block mb-2 text-sm font-medium text-slate-700 dark:text-white"
                                htmlFor="remark"
                            >
                                Remark
                            </label>
                            <Textarea
                                id="remark"
                                placeholder="Remark"
                                required
                                rows={2}
                            />
                        </div>
                    </div>
                    <div className="flex items-center gap-3 justify-end py-2 pt-5">
                        <Button
                            theme={button_custom}
                            size="xs"
                            className="rounded"
                            color="failure"
                            onClick={() => setOpenModal(false)}
                        >
                            <div className="flex items-center gap-0.5">
                                <IoClose className="text-xl" />
                                <span className="text-sm">Cancel</span>
                            </div>
                        </Button>
                        <Button
                            theme={button_custom}
                            size="xs"
                            className="rounded"
                            color="blue"
                            onClick={() => setOpenModal(false)}
                        >
                            <div className="flex items-center gap-1">
                                <MdOutlineEdit className="text-base" />
                                <span className="text-sm">Edit</span>
                            </div>
                        </Button>
                    </div>
                </ModalBody>
            </Modal>

            {/*---------- View Document Modal---------- */}
            <Modal show={openViewModal} onClose={() => setOpenViewModal(false)}>
                <ModalBody className="bg-[#474747] lg:py-3 pb-1 lg:0 pt-2 lg:px-4 px-1 rounded-b-lg">
                    <div className="flex items-center justify-end">
                        <Button size="xs" className="rounded" color="blue">
                            <div className="flex items-center gap-2">
                                <GoDownload className="text-[15px]" />
                                <span className="text-[13px]">Download</span>
                            </div>
                        </Button>

                        <button onClick={() => setOpenViewModal(false)}>
                            <IoClose className="text-white text-[35px]" />
                        </button>
                    </div>
                    <div className="h-full sm:h-full xl:h-full 2xl:h-full m-4">
                        <Carousel
                            theme={slider_custom}
                            slide={false}
                            className=""
                        >
                            <div
                                className={`relative w-full h-full overflow-hidden ${
                                    zoom ? "cursor-zoom-out" : "cursor-zoom-in"
                                }`}
                                onClick={() => setZoom(!zoom)}
                                onMouseMove={handleMouseMove}
                            >
                                <img
                                    src="assets/img/aadhar.png"
                                    alt="Aadhaar Image"
                                    className={` w-full h-full object-cover transition-transform duration-100 ${
                                        zoom ? "scale-150" : "scale-100"
                                    }`}
                                    style={{
                                        transformOrigin: `${transformOrigin.x} ${transformOrigin.y}`,
                                    }}
                                />
                            </div>
                            <img
                                src="https://flowbite.com/docs/images/carousel/carousel-2.svg"
                                alt="Another image"
                            />
                        </Carousel>
                    </div>
                </ModalBody>
            </Modal>
        </>
    );
}

