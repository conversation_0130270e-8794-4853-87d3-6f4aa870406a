import { FilePreview } from "@/Components/FilePreview";
import ConfirmBox from "@/Components/HelperComponents/ConfirmBox";
import NoRecord from "@/Components/HelperComponents/NoRecord";
import TinyEditor from "@/Components/TinyEditor";
import { button_custom, card_custom, table_custom } from "@/Pages/Helpers/DesignHelper";
import { changeFlag, isDateGreaterThanToday } from "@/Pages/Helpers/Helper";
import { router } from "@inertiajs/react";
import { Badge, Button, Card, Label, Table, Tooltip } from "flowbite-react";
import { useEffect, useRef, useState } from "react";
import { Chart } from "react-google-charts";
import { CgAttachment } from "react-icons/cg";
import { FaRegTrashCan, FaWhatsapp } from "react-icons/fa6";
import { IoMdAdd, IoMdClose } from "react-icons/io";
import { IoPauseOutline } from "react-icons/io5";
import { MdOutlineRocketLaunch } from "react-icons/md";
import { RiFileUserLine } from "react-icons/ri";
import { TbFileTypeCsv, TbHandStop, TbReport } from "react-icons/tb";

function Details({ campaign }) {
    console.log(campaign);

    const [campaignData, setCampaignData] = useState(null);
    const [isLoading, setIsLoading] = useState(true);
    const [disabledBtnId, setDisabledBtnId] = useState(null);
    const [campaignGateways, setCampaignGateways] = useState(campaign.gateways);
    const [attachments, setAttachments] = useState(null);
    const selectRef = useRef(null);
    console.log('attachments', attachments);

    function campaignShowData() {
        fetch(route("mail.campaign.show", { id: campaign.id }))
            .then((res) => {
                return res.json();
            })
            .then((data) => {
                // console.log('data', data);

                if (data.status == true) {
                    setCampaignData(data.collection);
                    setCampaignGateways(data.collection.campaign.gateways);
                    setAttachments(data.collection.campaign.attachments)
                    setIsLoading(false);
                    if (selectRef.current) {
                        selectRef.current.value = "";
                    }
                } else {
                    setCampaignData(null);
                    setIsLoading(false);
                    console.error("Error in fetching data.");
                }
            });
    }

    useEffect(() => {
        campaignShowData();
        updateChartData();
    }, []);

    const HandleChangeFlag = (Column, id, flag) => {
        let table = "mail_campaign";
        changeFlag(table, Column, id, flag);
        campaignShowData();
    };


    const statusCodes = {
        0: { bg: "purple", text: "Draft" },
        1: { bg: "pink", text: "Started" },
        2: { bg: "info", text: "Paused" },
        3: { bg: "success", text: "Completed" },
        4: { bg: "failure", text: "Terminated" },
        6: { bg: "indigo", text: "Scheduled" },
    };

    const [chartData, setChartData] = useState();
    const [chartOptions, setChartOptions] = useState();
    const [isConfirmOpen, setConfirmOpen] = useState(false);
    const [selectedGateways, setSelectedGateways] = useState([]);


    const [selectedGateway, setSelectedGateway] = useState();

    const updateChartData = () => {
        var pending = campaign.totalContacts - campaign.completedContacts;
        var failed = campaign.failed_count;
        var nonWapp = campaign.non_whatsapp_count;
        var complete = campaign.completedContacts - failed - nonWapp;

        setChartData([
            ["Status", "Sent", "Pending", "Failed", "Invalid Email Address."],
            ["Detail", complete ?? 0, pending ?? 0, failed ?? 0, nonWapp ?? 0],
        ]);
        setChartOptions({
            title:
                `${campaign.completedContacts ?? 0} Out of ${campaign.totalContacts ?? 0} Completed`,
            chartArea: { width: "70%", height: "100px" },
            colors: ["#28a745", "#c53da9", "#dc3545", "#ff6f00", "#dc3912"],
            isStacked: true,
            hAxis: {
                title: "Total Contacts",
                minValue: 0,
            },
        });
    };
    useEffect(() => {
        updateChartData();
    }, [campaignData]);

    const addChannelToCampaign = () => {
        if (selectedGateway) {
            router.post(route("mail.campaign.update.add.gateway"), {
                campaign: campaign.id,
                gateway: selectedGateway
            }, {
                preserveState: true,
                preserveScroll: true,
                onSuccess: (page) => {
                    campaignShowData();
                },
                onError: (errors) => {
                    console.error("Error:", errors);
                }
            });
            campaignShowData();
        } else {
            alert("Select Gateway.");
            return false;
        }
    };

    function deleteRecords() {
        if (selectedGateways.length > 0) {
            router.delete(route("mail.campaign.gateway.destroy", { campaign: selectedGateways.toLocaleString() }));
        }
    }

    const handleConfirmBoxResult = (res) => {
        if (res) {
            deleteRecords();
        }
        campaignShowData();
        setSelectedGateways([]);
        setConfirmOpen(false);
    };

    return (
        <>
            <div className="grid grid-flow-row-dense grid-cols-12 p-0 pt-2 border-none md:grid-cols-12 sm:grid-cols-1 lg:gap-2">
                <div className="relative flex flex-col gap-2 pt-0 dark:text-gray-400 lg:p-0 xl:col-span-12 lg:col-span-12 md:col-span-12 col-span-full">
                    {/* Status, Buttons */}
                    <div className="flex items-center justify-between gap-1">
                        <div className="flex items-center gap-1">
                            {campaign ? (
                                isDateGreaterThanToday(
                                    campaign.startTime
                                ) ? (
                                    [0].includes(
                                        campaign.status
                                    ) ? (
                                        <Badge className="cursor-default" color={statusCodes[6].bg} >{statusCodes[6].text} </Badge>
                                    ) : (
                                        <Badge className="cursor-default" color={statusCodes[campaign.status].bg} >
                                            {statusCodes[campaign.status].text}
                                        </Badge>
                                    )
                                ) : (
                                    <Badge className="cursor-default" color={statusCodes[campaign.status].bg} > {statusCodes[campaign.status].text} </Badge>
                                )
                            ) : (
                                <NoRecord loading={true} />
                            )}
                        </div>
                        {campaign ? (
                            <div className="relative flex items-center justify-between gap-3">
                                <div className="flex items-center gap-3 ">
                                    {campaign.isReportReady ==
                                        1 ? (
                                        <Tooltip content="Download Report">
                                            <Button theme={button_custom} color="blue" size="xs">
                                                <TbFileTypeCsv />
                                            </Button>
                                        </Tooltip>
                                    ) : (
                                        <></>
                                    )}
                                    <Tooltip content="Request Report">
                                        <Button
                                            onClick={() => {
                                                setDisabledBtnId(`generateReport${campaign.id}`);
                                                HandleChangeFlag("reportRequest", campaign.id, 1);
                                            }
                                            }
                                            theme={button_custom}
                                            color="blue"
                                            size="xs"
                                            disabled={!isLoading && (
                                                (
                                                    campaign.reportRequest == 1 || campaign.status != 3)
                                                || (disabledBtnId == `generateReport${campaign.id}`)) ? true : false}
                                        >
                                            <TbReport className="text-sm" />
                                            <span className="text-xs ms-1">
                                                Generate Report
                                            </span>
                                        </Button>
                                    </Tooltip>

                                    <Button
                                        onClick={() => {
                                            setDisabledBtnId(`pause${campaign.id}`)
                                            HandleChangeFlag(
                                                "status",
                                                campaign.id,
                                                2
                                            )
                                        }
                                        }
                                        theme={button_custom}
                                        color="orange"
                                        size="xs"
                                        disabled={
                                            ((![1].includes(
                                                campaign.status
                                            )) || (disabledBtnId == `pause${campaign.id}`)) ? true : false
                                        }
                                    >
                                        <IoPauseOutline className="text-sm" />
                                        <span className="text-xs ms-1">
                                            Pause
                                        </span>
                                    </Button>

                                    <Button
                                        onClick={() => {
                                            setDisabledBtnId(`start${campaign.id}`)
                                            HandleChangeFlag(
                                                "status",
                                                campaign.id,
                                                1
                                            )
                                        }
                                        }
                                        theme={button_custom}
                                        color="success"
                                        size="xs"
                                        disabled={
                                            ((![0, 2].includes(
                                                campaign.status
                                            )) || (disabledBtnId == `start${campaign.id}`)) ? true : false
                                        }
                                    >
                                        {/* <IoPlayOutline className="text-sm" /> */}
                                        <div className="flex items-center gap-1">
                                            <MdOutlineRocketLaunch className="" />
                                            <span className="text-xs">
                                                Play
                                            </span>
                                        </div>
                                    </Button>

                                    <Button
                                        onClick={() => {
                                            setDisabledBtnId(`stop${campaign.id}`)
                                            HandleChangeFlag(
                                                "status",
                                                campaign.id,
                                                4
                                            )
                                        }
                                        }
                                        theme={button_custom}
                                        color="failure"
                                        size="xs"
                                        disabled={
                                            (![1, 2].includes(
                                                (campaign.status
                                                )) || (disabledBtnId == `stop${campaign.id}`)) ? true : false
                                        }
                                    >
                                        <TbHandStop className="text-sm" />
                                        <span className="text-xs ms-1">
                                            Stop
                                        </span>
                                    </Button>
                                </div>
                            </div>
                        ) : (
                            <NoRecord loading={true} />
                        )}
                    </div>

                    {/* Template Name, Category */}
                    <div className="bg-white rounded-lg p-3 flex flex-col gap-1.5">
                        {/* <Progress color="blue" progress={45} /> */}
                        {/* <div id="example3.1" style={{ height: "200px" }}></div> */}
                        {campaign ? (
                            <div>
                                <Chart
                                    chartType="BarChart"
                                    width="100%"
                                    height="150px"
                                    data={chartData}
                                    options={chartOptions}
                                />
                            </div>
                        ) : (
                            <NoRecord loading={true} />
                        )}
                        <div className="p-2 mt-2 text-sm bg-white border rounded-lg">
                            <div className="grid grid-flow-row-dense grid-cols-12 gap-2 mt-2 border-none md:grid-cols-12 sm:grid-cols-1">
                                <div className="relative flex flex-col gap-2 p-2 border rounded-md lg:col-span-6 md:col-span-6 col-span-full">
                                    <div className="flex items-center justify-between w-full">
                                        <span>Start Time</span>
                                        <span className="text-gray-500">
                                            {campaign.startDatetime}
                                        </span>
                                    </div>
                                </div>
                                <div className="relative flex flex-col gap-2 p-2 border rounded-md lg:col-span-6 md:col-span-6 col-span-full">
                                    <div className="flex items-center justify-between w-full">
                                        <span>Stop Time</span>
                                        <span className="text-gray-500">

                                            {campaign.endDatetime}

                                        </span>
                                    </div>
                                </div>
                            </div>
                            <div className="flex items-center gap-1.5 w-full border p-2 mt-2 rounded-md">
                                <span>Sleep After </span>
                                <span className="text-gray-500">{campaign.sleepAfterMsgs}</span>
                                <span>Messages for </span>
                                <span className="text-gray-500">{campaign.sleepForSeconds}</span>
                                <span>Seconds. </span>
                            </div>
                        </div>
                    </div>

                    {/* Gateway */}
                    <div className="bg-white rounded-lg p-2 px-3 flex flex-col gap-1.5">
                        <div className="flex items-center justify-between gap-2">
                            <div className="flex items-center gap-2">
                                <FaWhatsapp className="text-slate-400" />
                                <span>Gateway</span>
                            </div>
                        </div>
                        <div className="overflow-x-auto border rounded-lg">
                            <Table hoverable theme={table_custom}>
                                <TableBody className="border-b divide-y">
                                    {!campaignGateways ? (
                                        <TableRow className="bg-white dark:border-gray-700 dark:bg-gray-800">
                                            <TableCell colSpan={2}>
                                                <NoRecord loading={isLoading} />
                                            </TableCell>
                                        </TableRow>
                                    ) : (
                                        campaignGateways.map((gateway, k) => (
                                            <TableRow
                                                className="bg-white dark:border-gray-700 dark:bg-gray-800"
                                                id={`campaign-gateway-2${k}`}
                                                key={k}
                                            >
                                                <TableCell className="font-medium text-gray-900 whitespace-nowrap dark:text-white">
                                                    {gateway.name}
                                                </TableCell>
                                                {campaign.status !== 4 && (
                                                    <TableCell>
                                                        <div className="flex items-center justify-end gap-2">

                                                            <Button
                                                                theme={button_custom}
                                                                color="gray"
                                                                size="xs"
                                                                onClick={() => {
                                                                    setConfirmOpen(true);
                                                                    setSelectedGateways([gateway.pivot.id]);
                                                                }}
                                                            >
                                                                <IoMdClose className="text-sm" />
                                                                <span className="text-xs ms-1">Remove</span>
                                                            </Button>
                                                        </div>
                                                    </TableCell>
                                                )}
                                            </TableRow>
                                        ))
                                    )}
                                </TableBody>
                            </Table>
                            {campaignData?.remainingGateways && campaignData?.remainingGateways.length > 0 &&
                                ![4, 3].includes(campaign.status) ? (
                                <div className="flex items-center gap-2 p-2">
                                    <Label>Add Gateway</Label>
                                    <div className="max-w-md">
                                        <select
                                            
                                            className="focus:ring-blue-600 focus:border-blue-600 rounded-md text-sm p-1.5 border-gray-300"
                                            id="Gateway"
                                            ref={selectRef}
                                            required
                                            onChange={(e) => setSelectedGateway(e.target.value)}
                                        >
                                            <option value="">Select Gateway</option>
                                            {campaignData.remainingGateways.map((ch, k) => (
                                                <option
                                                    value={ch.id}
                                                    key={`Select-Gateway-${k}`}
                                                >
                                                    {ch.name}
                                                </option>
                                            ))}
                                        </select>
                                    </div>
                                    <Button
                                        theme={button_custom}
                                        color="blue"
                                        size="xs"
                                        onClick={addChannelToCampaign}
                                    >
                                        <IoMdAdd />
                                    </Button>
                                </div>
                            ) : (
                                <NoRecord loading={isLoading} />
                            )}
                        </div>
                    </div>

                    {/* Contact List */}
                    <div className="bg-white rounded-lg p-2 px-3 flex flex-col gap-1.5 mb-2">
                        <div className="flex items-center gap-2">
                            <RiFileUserLine className="text-slate-400" />
                            <span>Contact List</span>
                        </div>
                        <div className="overflow-x-auto border rounded-lg">
                            <Table hoverable theme={table_custom}>
                                <TableBody className="divide-y">
                                    {campaign &&
                                        campaign ? (
                                        campaign.contact_lists.map(
                                            (cList, k) => {
                                                return (
                                                    <TableRow className="bg-white dark:border-gray-700 dark:bg-gray-800" key={k}>
                                                        <TableCell className="font-medium text-gray-900 whitespace-nowrap dark:text-white">
                                                            {
                                                                cList.name
                                                            }
                                                        </TableCell>
                                                    </TableRow>
                                                );
                                            }
                                        )
                                    ) : (
                                        <TableRow>
                                            <TableCell>
                                                <NoRecord loading={isLoading} />
                                            </TableCell>
                                        </TableRow>
                                    )}
                                </TableBody>
                            </Table>
                        </div>
                    </div>
                </div>
                <div className="col-span-12 xl:col-span-12 lg:col-span-12 md:col-span-12 sm:col-span-12 lg:flex flex-col gap-3">
                    <div
                        className="flex flex-wrap justify-start w-full h-full overflow-auto bg-white rounded-lg p-2"
                        style={{ maxHeight: "700px" }}
                    >
                        <Card
                            theme={card_custom}
                            className="relative w-full bg-slate-300"
                        >

                            <div className="absolute -left-0 top-6 w-0 h-0 border-t-[1px] border-t-transparent border-b-[12px] border-b-transparent border-r-[18px] border-r-white"></div>

                            <div
                                className="p-2 overflow-auto">
                                {campaign.msg != null &&
                                    <TinyEditor readOnly value={campaign.msg} id={"tinyEditor-" + campaign.id} />
                                }
                                {campaign.file &&
                                    <div className="flex items-center gap-1 px-2">
                                        <span className="text-sm text-blue-600">
                                            <CgAttachment />
                                        </span>
                                        <span className="text-sm text-blue-600">
                                            1
                                        </span>
                                    </div>
                                }
                            </div>
                        </Card>
                    </div>

                    {attachments &&
                        <div className="bg-white rounded-lg p-2 my-2.5">
                            <div className="mb-3">
                                Attachments
                            </div>
                            <div className="grid grid-cols-1 gap-3 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4">

                                {attachments.map((attach, key) => {
                                    return (
                                        <div className="relative" key={"old-attachment" + key}>
                                            <FilePreview object={attach.path} imageSize="xl" />
                                        </div>
                                    )
                                })}
                            </div>
                        </div>
                    }

                </div>
            </div>

            {isConfirmOpen &&
                <ConfirmBox
                    isOpen={isConfirmOpen}
                    onClose={() => setConfirmOpen(false)}
                    onAction={handleConfirmBoxResult}
                    title="Are you sure you want to delete this?"
                    message="This action cannot be undone."
                    confirmText="Yes, Delete!"
                    cancelText="No, Keep It"
                    confirmColor="orange"
                    cancelColor="gray"
                    icon={<FaRegTrashCan />}
                />
            }

        </>
    );
}

export default Details;

