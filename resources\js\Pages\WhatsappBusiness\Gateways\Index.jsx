import { Head, Link, router, useForm } from "@inertiajs/react";
import {
    <PERSON><PERSON>,
    <PERSON><PERSON>,
    Checkbox,
    Drawer,
    Modal,
    Table,
    Tooltip
} from "flowbite-react";
import { useEffect, useState } from "react";
import { FaPencilAlt } from "react-icons/fa";

import {
    MdDeleteOutline,
    MdOutlineEdit,
    MdOutlineHelpOutline,
    MdOutlinePowerOff
} from "react-icons/md";

import NoRecord from "@/Components/HelperComponents/NoRecord";
import Paginate from "@/Components/HelperComponents/Paginate";
import PerPageDropdown from "@/Components/HelperComponents/PerPageDropdown";
import {
    button_custom,
    customDrawer,
    page_badge_theme,
    table_custom
} from "@/Pages/Helpers/DesignHelper";
import $ from "jquery";
import {
    FaPowerOff,
    FaRegCopy,
    FaStar,
    FaWhatsapp
} from "react-icons/fa6";
import { HiOutlineTemplate } from "react-icons/hi";
import { IoMdAdd, IoMdPower } from "react-icons/io";
import { PiCaretUpDownBold } from "react-icons/pi";
import {
    RiDeleteBin5Line,
    RiDeleteBinLine
} from "react-icons/ri";
import WaMain from "../WaMain";
import Add from "./Add";
import Edit from "./Edit";
import ConfirmBox from "@/Components/HelperComponents/ConfirmBox";

export default function Index({ collection }) {

    // Gateways data from the collection prop
    const gateways = collection.gateways;
    const getData = collection.getData;
    const [isGatewayEditEnable, setIsGatewayEditEnable] = useState(false);
    const [isCheckAll, setIsCheckAll] = useState(false);
    const [AddChannel, setAddChannel] = useState(false);
    const [openChatId, setOpenChatId] = useState(0);
    const [chatList, setChatList] = useState(false);
    const [isDeleteConfirmOpen, setIsDeleteConfirmOpen] = useState();
    const [whatsappGateway, setWhatsappGateway] = useState(false);
    const [checkValue, setCheckValue] = useState([]);
    const [isUpdateDefaultConfirmOpen, setIsUpdateDefaultConfirmOpen] = useState();
    const [updateDefaultID, setUpdateDefaultID] = useState(null);
    // const [autoreplyDataFetch, setAutoreplyDataFetch] = useState(false);
    const [openModalClear, setOpenModalClear] = useState(false);
    const [openModalActivate, setOpenModalActivate] = useState(false);
    const [isMultipleDeleteConfirmOpen, SetIsMultipleDeleteConfirmOpen] = useState(false);
    const [openModalActivateFailed, setOpenModalActivateFailed] = useState(false);

    const {
        data,
        setData,
        delete: destroy,
        processing,
        errors,
    } = useForm({
        id: [],
    });

    // function fetchAutoReplyData() {
    //     return fetch(route("helper.getAutoReply"))
    //         .then((res) => res.json())
    //         .then((data) => {
    //             setAutoreplyDataFetch(data);
    //         })
    //         .catch((error) => {
    //             console.error("Error fetching badge data:", error);
    //         });
    // }

    // useEffect(() => {
    //     fetchAutoReplyData();
    // }, []);

    // ----------------Edit --------------------
    // const [isOpen, setIsOpen] = useState(false);
    // const handleClose = () => setIsOpen(false);

    // executes when user click table row checkbox
    function getCheckedIds(e) {
        let previousIds = checkValue;
        if (e.target.checked) {
            if (!previousIds.includes(e.target.id)) {
                previousIds.push(e.target.id);
                setCheckValue(previousIds);
            }
        } else {
            const newIds = previousIds.filter((item) => item !== e.target.id);
            setCheckValue(newIds);
        }
    }

    // executes when user click table header checkbox
    function headerCheckBoxChecked(e) {
        let previousIds = [];
        if (e.target.checked && e.target.id == 0 && gateways.data.length > 0) {
            gateways.data.map((channel, key) => {
                if (!previousIds.includes(channel.id)) {
                    previousIds.push(channel.id);
                    setCheckValue(previousIds);
                }
            });
            setIsCheckAll(true);
            $(".rowCheckBox").prop("checked", true);
        } else {
            setCheckValue(previousIds);
            setIsCheckAll(false);
            $(".rowCheckBox").prop("checked", false);
        }
    }

    // handle checkBox Check or uncheck
    function checkAddcheckBoxChecked() {
        let allCheckBoxes = $(".rowCheckBox");
        let checkedCheckBoxes = $(".rowCheckBox:checked");

        if (allCheckBoxes.length == checkedCheckBoxes.length) {
            setIsCheckAll(true);
        } else {
            setIsCheckAll(false);
        }
    }

    const updateDefault = () => {
        Promise.all([
            router.post(route("whatsappB.gateway.updateDefault"), { id: updateDefaultID })
        ]).then(() => {

        });

    }

    function handleConfirmUpdateDefault(res) {
        if (updateDefaultID && res) {
            updateDefault();
        }
        setIsUpdateDefaultConfirmOpen(false);
        setUpdateDefaultID(null);
    }

    function deleteRecords() {
        if (checkValue.length > 0) {
            destroy(route("whatsappB.gateway.destroy", { gateway: checkValue.toLocaleString() }));
        }
    }


    function handleMultipleDelete(res) {
        if (res) {
            deleteRecords();
        }
        setCheckValue([]);
        setIsCheckAll(false);
        $(".rowCheckBox").prop("checked", false);
        SetIsMultipleDeleteConfirmOpen(false);
    }
    return (
        <WaMain>
            <Head title="Gateways" />
            <div className="px-2 ">
                <div className="w-full mt-2 overflow-auto bg-white border rounded h-fit">
                    {/*----------- Advance Settings----------- */}

                    {/* -----------Table----------- */}
                    <div className="flex justify-between p-2">
                        {/* <Button.Group className="flex "> */}
                        <div>
                            {
                                collection.can_delete &&
                                <Button
                                    // className="border rounded"
                                    size="xs"
                                    color="gray"
                                    theme={button_custom}
                                    onClick={() => SetIsMultipleDeleteConfirmOpen(true)}
                                >
                                    <div className="flex items-center gap-1">
                                        <MdDeleteOutline className="text-slate-500" />
                                        <span className="text-xs">Delete</span>
                                    </div>
                                </Button>
                            }
                        </div>
                        {/* <Button
                                theme={button_custom}
                                size="xs"
                                color="gray"
                                id="dropdownInformationButton"
                                data-dropdown-toggle="dropdownNotification"
                                type="button"
                            >
                                <div className="flex items-center gap-1 text-xs ps-1">
                                    <TbColumns3 className="text-slate-500" />
                                    Columns
                                </div>
                            </Button> */}
                        {/* </Button.Group> */}
                        <div className="flex items-center gap-2">
                            {
                                collection.can_add &&
                                <Button
                                    className="border pe-1"
                                    size="xs"
                                    color="gray"
                                    theme={button_custom}
                                    onClick={() => setAddChannel(true)}
                                >
                                    <div className="flex items-center gap-1">
                                        <IoMdAdd className="text-slate-500" />
                                        <span className="text-xs">Add</span>
                                    </div>
                                </Button>
                            }
                        </div>

                    </div>
                    <div className="bg-white border rounded-lg">
                        {/* documents table */}
                        <div className="overflow-x-auto">
                            <Table hoverable theme={table_custom}>
                                <TableHead className=" bg-slate-100">
                                    <TableHeadCell>
                                        <Checkbox
                                            color="blue"
                                            checked={isCheckAll}
                                            id={0}
                                            onChange={(e) => {
                                                headerCheckBoxChecked(e);
                                            }}
                                        />
                                    </TableHeadCell>
                                    <TableHeadCell>
                                        <Link
                                            href={route(
                                                "whatsappB.gateway.index",
                                                {
                                                    column: "name",
                                                    sort:
                                                        getData.sort == "asc"
                                                            ? "desc"
                                                            : "asc",
                                                }
                                            )}
                                        >
                                            <div className="flex items-center justify-between gap-2">
                                                <span>Name</span>
                                                <PiCaretUpDownBold
                                                    className={
                                                        (getData.column == "id"
                                                            ? "  text-gray-900 dark:text-gray-200 "
                                                            : "text-gray-600 dark:text-gray-400 ") +
                                                        "  cursor-pointer "
                                                    }
                                                />
                                            </div>
                                        </Link>
                                    </TableHeadCell>

                                    <TableHeadCell>
                                        <Link
                                            href={route(
                                                "whatsappB.gateway.index",
                                                {
                                                    column: "phone_id",
                                                    sort:
                                                        getData.sort == "asc"
                                                            ? "desc"
                                                            : "asc",
                                                }
                                            )}

                                        >
                                            <div className="flex items-center justify-between gap-2">
                                                <span>Phone Number Id</span>
                                                <PiCaretUpDownBold
                                                    className={
                                                        (getData.column == "name"
                                                            ? "  text-gray-900 dark:text-gray-200 "
                                                            : "text-gray-600 dark:text-gray-400 ") +
                                                        "  cursor-pointer "
                                                    }
                                                />
                                            </div>
                                        </Link>
                                    </TableHeadCell>
                                    <TableHeadCell>
                                        Phone Number
                                    </TableHeadCell>
                                    <TableHeadCell>
                                        <Link
                                            href={route(
                                                "whatsappB.gateway.index",
                                                {
                                                    column: "user_id",
                                                    sort:
                                                        getData.sort == "asc"
                                                            ? "desc"
                                                            : "asc",
                                                }
                                            )}

                                        >
                                            <div className="flex items-center justify-between gap-2">
                                                <span>User</span>
                                                <PiCaretUpDownBold
                                                    className={
                                                        (getData.column == "user_id"
                                                            ? "  text-gray-900 dark:text-gray-200 "
                                                            : "text-gray-600 dark:text-gray-400 ") +
                                                        "  cursor-pointer "
                                                    }
                                                />
                                            </div>
                                        </Link>
                                    </TableHeadCell>

                                    <TableHeadCell>Actions</TableHeadCell>
                                </TableHead>

                                <TableBody className="divide-y">
                                    {gateways.data.length > 0 ? (
                                        gateways.data.map((gateway, key) => {
                                            return (
                                                <TableRow key={key}>
                                                    <TableCell>
                                                        <Checkbox
                                                            color={"blue"}
                                                            className="rowCheckBox"
                                                            id={gateway.id}
                                                            onChange={(e) => {
                                                                getCheckedIds(
                                                                    e
                                                                );
                                                                checkAddcheckBoxChecked();
                                                            }}
                                                        />
                                                    </TableCell>
                                                    <TableCell className="">
                                                        <div className="flex items-center justify-between gap-2">
                                                            <span>
                                                                {gateway.name}
                                                            </span>
                                                            {(gateway.isDefault == 1) ?
                                                                <>
                                                                    <Button
                                                                        theme={
                                                                            button_custom
                                                                        }
                                                                        color="transparentForTab"
                                                                        className="cursor-default"
                                                                    >
                                                                        <Badge theme={page_badge_theme} size="xs" color="info">Default</Badge>
                                                                    </Button>
                                                                </>
                                                                :
                                                                <>
                                                                    <Button
                                                                        theme={
                                                                            button_custom
                                                                        }
                                                                        color="transparentForTab"
                                                                        // onClick={() => updateDefault(channel.id)}
                                                                        onClick={() => {
                                                                            setUpdateDefaultID(gateway.id)
                                                                            setIsUpdateDefaultConfirmOpen(true);
                                                                        }}
                                                                    >
                                                                        <Badge theme={page_badge_theme} size="xs" color="indigo">Set Default</Badge>
                                                                    </Button>

                                                                </>
                                                            }
                                                        </div>
                                                    </TableCell>
                                                    <TableCell className="">
                                                        {gateway.phone_id}
                                                    </TableCell>
                                                    <TableCell className="">
                                                        {gateway.phoneNumber ?? '-'}
                                                    </TableCell>
                                                    <TableCell className="">
                                                        {gateway.user.username ?? '-'}
                                                    </TableCell>
                                                    <TableCell>
                                                        <div className="flex gap-2">
                                                            {
                                                                collection.can_edit &&
                                                                <Tooltip content="Edit">
                                                                    <Button
                                                                        theme={
                                                                            button_custom
                                                                        }
                                                                        color="success"
                                                                        size="xs"
                                                                        onClick={() => {
                                                                            setIsGatewayEditEnable(
                                                                                true
                                                                            );
                                                                            setWhatsappGateway(
                                                                                gateway
                                                                            );
                                                                        }}
                                                                    >
                                                                        <div className="flex items-center">
                                                                            <MdOutlineEdit className="text-sm" />
                                                                            <span className="text-xs ms-1">
                                                                                Edit
                                                                            </span>
                                                                        </div>
                                                                    </Button>
                                                                </Tooltip>
                                                            }
                                                            {/* <Tooltip content="Activate">
                                                                <Button
                                                                    theme={
                                                                        button_custom
                                                                    }
                                                                    color="blue"
                                                                    size="xs"
                                                                    onClick={()=>setOpenModalActivate(true)}
                                                                >
                                                                    <div className="flex items-center">
                                                                        <FaPowerOff className="text-sm ms-1" />
                                                                        <span className="text-xs ms-1">
                                                                            Activate
                                                                        </span>
                                                                    </div>
                                                                </Button>
                                                            </Tooltip> */}

                                                            <a
                                                                href={route('whatsappB.templates.index', { gateway: gateway.id })}
                                                                target="_blank"
                                                            >
                                                                <Button
                                                                    theme={
                                                                        button_custom
                                                                    }

                                                                    color="Fuchsia_custom"
                                                                    size="xs"
                                                                >
                                                                    <HiOutlineTemplate />
                                                                    <span className="text-xs ms-1">
                                                                        Templates
                                                                    </span>
                                                                </Button>

                                                            </a>
                                                            {
                                                                collection.can_delete &&
                                                                <Button
                                                                    theme={
                                                                        button_custom
                                                                    }
                                                                    color="failure"
                                                                    size="xs"
                                                                    onClick={() => {
                                                                        // setDeleteID(channel.id)
                                                                        setCheckValue([gateway.id])
                                                                        SetIsMultipleDeleteConfirmOpen(true);
                                                                    }}
                                                                >
                                                                    <RiDeleteBin5Line className="text-sm" />
                                                                    <span className="text-xs ms-1">
                                                                        Delete
                                                                    </span>
                                                                </Button>
                                                            }
                                                        </div>
                                                    </TableCell>
                                                </TableRow>
                                            );
                                        })
                                    ) : (
                                        <TableRow className="bg-white dark:border-gray-700 dark:bg-gray-800">
                                            <TableCell colSpan={17}>
                                                <NoRecord />
                                            </TableCell>
                                        </TableRow>
                                    )}
                                </TableBody>
                            </Table>
                        </div>
                    </div>

                    <div className="bottom-0 w-full p-3 bg-white rounded-lg float-end dark:border-gray-500 dark:rounded-lg dark:bg-gray-700">
                        <div className="flex flex-wrap justify-between gap-2 lg:justify-between lg:gap-0 md:gap-0">
                            <div className="flex items-center gap-4">
                                <PerPageDropdown
                                    getDataFields={getData ?? null}
                                    routeName={"whatsappB.gateway.index"}
                                    data={gateways}
                                    idName={"name"}
                                />
                            </div>

                            <Paginate tableData={gateways} />
                        </div>
                    </div>
                </div>
            </div>

            {/*-------------- Add Channel ------------*/}
            {AddChannel && (
                <Drawer
                    open={AddChannel}
                    onClose={() => setAddChannel(false)}
                    position="right"
                    theme={customDrawer}
                    className="w-full lg:w-4/5 md:w-4/5 xl:w-2/3"
                >
                    <DrawerHeader
                        title="Add Gateway"
                        titleIcon={FaPencilAlt}
                    />
                    <DrawerItems className="py-2 ">
                        <Add
                            onClose={() => setAddChannel(false)}
                        />
                    </DrawerItems>
                </Drawer>
            )}

            {/* --------------------Edit whatsappweb Channel-------------------------- */}
            {isGatewayEditEnable && (
                <Drawer
                    theme={customDrawer}
                    className="w-full lg:w-4/5 md:w-4/5 xl:w-2/3"
                    open={isGatewayEditEnable}
                    onClose={() => setIsGatewayEditEnable(false)}
                    position="right"
                >
                    <DrawerHeader
                        titleIcon={FaWhatsapp}
                        title="Edit Gateway"
                    />
                    <DrawerItems className="px-2 py-2">
                        <Edit
                            onClose={() => setIsGatewayEditEnable(false)}
                            gateway={whatsappGateway}
                        />
                    </DrawerItems>
                </Drawer>
            )}
            {/*-------------- eye chat table ------------*/}
            {/* {chatList == true && (
                <Drawer
                    open={chatList}
                    onClose={() => setChatList(false)}
                    position="right"
                    theme={customDrawer}
                    className="w-full lg:w-3/5 md:w-3/5"
                >
                    <DrawerHeader title="View Saved chats" titleIcon={FaEye} />
                    <DrawerItems className="py-4">
                        <div className="overflow-x-auto">
                            <ViewSavedChats data={{ channel: openChatId }} />
                        </div>
                    </DrawerItems>
                </Drawer>
            )} */}

            {/* {openModal ? (
                <Modal show={openModal} onClose={() => setOpenModal(false)}>
                    <ModalHeader>Login</ModalHeader>
                    <ModalBody>
                        <QRCode />
                    </ModalBody>
                </Modal>
            ) : (
                ""
            )} */}

            {
                openModalClear &&
                <Modal
                    show={openModalClear}
                    size="md"
                    onClose={() => setOpenModalClear(false)}
                    popup
                >
                    <ModalHeader />
                    <ModalBody>
                        <div className="text-center">
                            <div className="flex items-center justify-center w-12 h-12 mx-auto mb-4 bg-red-100 rounded-full dark:text-gray-200">
                                <RiDeleteBinLine className="text-2xl text-red-600" />
                            </div>
                            <h3 className="mb-5 text-base font-normal">
                                Are you sure?
                                <br /> Once you delete these 500 Campaign Reports,
                                the action cannot be reversed.
                            </h3>
                            <div className="flex justify-center gap-4">
                                <Button
                                    color="gray"
                                    onClick={() => setOpenModal(false)}
                                >
                                    No, Keep It.
                                </Button>
                                <Button
                                    color="failure"
                                    onClick={() => setOpenModal(false)}
                                >
                                    Yes, Delete!
                                </Button>
                            </div>
                        </div>
                    </ModalBody>
                </Modal>
            }
            {/* Activate */}
            {
                openModalActivate &&
                <Modal
                    show={openModalActivate}
                    size="xl"
                    onClose={() => setOpenModalActivate(false)}
                    popup
                >
                    <ModalHeader className="p-2 bg-slate-100">
                        <div className="flex items-center gap-2">
                            <IoMdPower className="text-slate-400" />
                            <h4 className="text-lg">Activate</h4>
                        </div>
                    </ModalHeader>
                    <ModalBody className="flex flex-col gap-3 px-4 pt-0 pb-4 rounded-b-lg bg-slate-100">
                        <div className="px-3 py-2 bg-white rounded-md">
                            <div className="flex items-center gap-3">
                                <div className="text-lg font-medium">
                                    Facebook App Setup
                                </div>
                                <MdOutlineHelpOutline className="text-2xl text-slate-400" />
                            </div>
                            <div>
                                <span>Callback URL</span>
                                <p className="text-gray-500">
                                    https://beta.wabhai.com/webhook/wpboxreceive/5WMYSzvMgRRUalsYBiDPNERbK3doijNB4fyFpTZGededa04f
                                    <button className="ms-2">
                                        <FaRegCopy className="text-blue-600" />
                                    </button>
                                </p>
                            </div>
                            <div>
                                <span>Verify Token</span>
                                <p className="text-gray-500">
                                    5WMYSzvMgRRUalsYBiDPNERbK3doijNB4fyFpTZGededa04f
                                    <button className="ms-2">
                                        <FaRegCopy className="text-blue-600" />
                                    </button>
                                </p>
                            </div>
                        </div>
                    </ModalBody>
                </Modal>
            }
            {/* Activation Failed */}
            {
                openModalActivateFailed &&
                <Modal
                    show={openModalActivateFailed}
                    size="md"
                    onClose={() => setOpenModalActivateFailed(false)}
                    popup
                >
                    <ModalHeader />
                    <ModalBody>
                        <div className="text-center">
                            <div className="flex items-center justify-center w-12 h-12 mx-auto mb-4 bg-red-100 rounded-full dark:text-gray-200">
                                <MdOutlinePowerOff className="text-2xl text-red-600" />
                            </div>
                            <h3 className="mb-5 text-base font-medium">
                                Activation Failed
                                <br /> Unable to activate the gateway. Please verify
                                the Callback URL and Verify Token for accuracy and
                                try again.
                            </h3>
                        </div>
                    </ModalBody>
                </Modal>
            }

            {isUpdateDefaultConfirmOpen &&
                (<ConfirmBox
                    isOpen={isUpdateDefaultConfirmOpen}
                    onClose={() => setIsUpdateDefaultConfirmOpen(false)} // Close the confirm box
                    onAction={handleConfirmUpdateDefault} // Handle the user's choice
                    title="Update Default Gateway "
                    message="Do you want to update default gateway."
                    confirmText="Confirm"
                    cancelText="Cancel"
                    confirmColor="text-yellow-400"
                    confirmBtnBgColor="bg-yellow-100"
                    confirmBtnTextColor="text-yellow-600"
                    iconBackground="bg-yellow-100"
                    iconColor="text-yellow-400"
                    icon={<FaStar />}
                />)
            }


            {/* {isDeleteConfirmOpen &&
                <ConfirmBox
                    isOpen={isDeleteConfirmOpen}
                    onClose={() => setIsDeleteConfirmOpen(false)} // Close the confirm box
                    onAction={handleConfirmDelete} // Handle the user's choice
                    title="Delete Gateway "
                    message="Do you want to Delete gateway."
                    confirmText="Confirm"
                    cancelText="Cancel"
                    confirmColor="text-red-400"
                    confirmBtnBgColor="bg-red-100"
                    confirmBtnTextColor="text-red-600"
                    iconBackground="bg-red-100"
                    iconColor="text-red-400"

                />} */}
            {isMultipleDeleteConfirmOpen &&
                <ConfirmBox
                    isOpen={isMultipleDeleteConfirmOpen}
                    onClose={() => SetIsMultipleDeleteConfirmOpen(false)} // Close the confirm box
                    onAction={handleMultipleDelete} // Handle the user's choice
                    title="Delete Gateway "
                    message="Do you want to Delete gateway."
                    confirmText="Yes, Delete!"
                    cancelText="No, Keep It"
                    confirmColor="orange"
                    cancelColor="gray"

                />
            }
        </WaMain>
    );
}



