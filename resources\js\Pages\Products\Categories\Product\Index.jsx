import { Head } from "@inertiajs/react";
import $ from "jquery";
import { <PERSON>lt<PERSON>, <PERSON>ton, ButtonGroup, Drawer, Table, Checkbox, Badge, Modal } from "flowbite-react";
import { TbColumns3, TbReportSearch } from "react-icons/tb";
import { useState } from "react";
import { IoChevronBack, IoClose } from "react-icons/io5";
import {
  MdOutlineCategory,
  MdOutlineEdit,
  MdOutlineRemoveRedEye,
} from "react-icons/md";
import Main from "@/Layouts/Main";
import { button_custom, customDrawer, table_custom } from "@/Pages/Helpers/DesignHelper";
import { IoIosArrowDown, IoMdAdd } from "react-icons/io";
import { RiDeleteBin6Line } from "react-icons/ri";
import { PiExportBold } from "react-icons/pi";
import Details from "./Details";
import TabBar from "../../TabBar";
import { <PERSON>Filter } from "react-icons/lu";
import AddProduct from "./AddProduct";
import AddProductGallery from "./AddProductGallery";

function Index() {
  const [isCheckAll, setIsCheckAll] = useState(false);
  // Add
  const [isOpen, setIsOpen] = useState(false);

  // Toggle Row
  const ToggleChild = (parent, child) => {
    // console.log(parent);
    var parentEle = $("#" + parent);
    var ele = $("#" + child);
    if (ele.hasClass("hidden")) {
      parentEle.addClass("rotate-180");
      ele.removeClass("hidden");
    } else {
      parentEle.removeClass("rotate-180");
      ele.addClass("hidden");
    }
  };

  // const ThTdPaddings = "px-2 py-1 text-nowrap";
  return (
    <Main>
      <Head title="User" />
      <div className="relative overflow-hidden">
        <div className="">
          <div className="col-span-full mb-2">
            <TabBar />
          </div>
          <div className="min-h-[80vh] max-h-[80vh] bg-white border rounded-lg m-2">
            <div className="flex items-center px-2 pt-2 gap-2">
              <Button
                theme={button_custom}
                size="xs"
                color="Fuchsia_custom"
              >
                <div className="flex items-center gap-1">
                  <IoChevronBack className=" text-sm" />
                  <span className="text-xs">
                    Back
                  </span>
                </div>
              </Button>
              <span className="text-xl font-medium">WhatsApp Marketing</span>
            </div>
            <div className="flex justify-between p-2">
              <div className="flex items-center gap-1">
                <ButtonGroup>
                  <Button
                    theme={button_custom}
                    size="xs"
                    color="gray"
                  >
                    <div className="flex items-center gap-1">
                      <RiDeleteBin6Line className="text-slate-400" />
                      <span className="text-xs">
                        Delete
                      </span>
                    </div>
                  </Button>

                  <Button
                    theme={button_custom}
                    size="xs"
                    color="gray"
                    id="dropdownInformationButton"
                    data-dropdown-toggle="dropdownNotification"
                    type="button"
                  >
                    <div className="flex items-center gap-1">
                      <TbColumns3 className="text-slate-400 text-base ms-1" />
                      <span className="text-xs">
                        Columns
                      </span>
                    </div>
                  </Button>
                </ButtonGroup>
                <ButtonGroup>
                  <Button
                    theme={button_custom}
                    size="xs"
                    color="gray"
                  >
                    <div className="flex items-center gap-1">
                      <PiExportBold className="text-slate-400" />
                      <span className="text-xs">
                        Export
                      </span>
                    </div>
                  </Button>
                  <Button
                    theme={button_custom}
                    size="xs"
                    color="gray">
                    <div className="flex items-center gap-1">
                      <LuFilter className="text-slate-400 text-sm" />
                      <span className="text-xs">
                        Filter
                      </span>
                    </div>
                  </Button>
                </ButtonGroup>
              </div>

              <ButtonGroup className="">
                <Button
                  theme={button_custom}
                  size="xs"
                  color="gray"
                  onClick={() =>
                    setIsOpen(
                      true
                    )
                  }
                >
                  <div className="flex items-center gap-1">
                    <IoMdAdd className="text-slate-500" />
                    <span className="text-xs">
                      Add
                    </span>
                  </div>
                </Button>
              </ButtonGroup>
            </div>

            <div className="overflow-x-auto bg-white border rounded-lg ">
              <Table hoverable theme={table_custom}>
                <Table.Head className="bg-slate-100">
                  <Table.HeadCell>
                    <Checkbox color="blue"
                      checked={isCheckAll}
                      onChange={() =>
                        setIsCheckAll(!isCheckAll)
                      }
                    />
                  </Table.HeadCell>
                  <Table.HeadCell>Products Name</Table.HeadCell>
                  <Table.HeadCell>Version</Table.HeadCell>
                  <Table.HeadCell>Interested Form</Table.HeadCell>
                  <Table.HeadCell>On Website</Table.HeadCell>
                  <Table.HeadCell>Active</Table.HeadCell>
                  <Table.HeadCell>Actions</Table.HeadCell>
                </Table.Head>

                <Table.Body className="divide-y">
                  <Table.Row className="">
                    <Table.Cell>
                      <Checkbox color="blue" className="rowCheckBox" />
                    </Table.Cell>
                    <Table.Cell>WaBhai Official WhatsApp Business</Table.Cell>
                    <Table.Cell>22</Table.Cell>
                    <Table.Cell>
                      <Badge className="w-fit" color="success">Active</Badge>
                    </Table.Cell>
                    <Table.Cell>
                      <Badge className="w-fit" color="success">Active</Badge>
                    </Table.Cell>
                    <Table.Cell>
                      <Badge className="w-fit" color="success">Yes</Badge>
                    </Table.Cell>
                    <Table.Cell>
                      <div className="flex items-center justify-between">
                        <div className="flex gap-2 h-fit text-nowrap">
                          {/* <Tooltip
                              content="Documents"
                              className="bg-slate-700 p-1 px-2"
                          > */}
                          <Button
                            theme={
                              button_custom
                            }
                            color="blue"
                            size="xs"
                            onClick={() =>
                              setIsOpenDocuments(
                                true
                              )
                            }
                          >
                            <div className="flex items-center gap-1">
                              <MdOutlineRemoveRedEye />
                              <span className="text-xs">View</span>
                            </div>
                          </Button>
                          {/* </Tooltip> */}
                          <Button
                            theme={
                              button_custom
                            }
                            color="green"
                            size="xs"
                            onClick={() =>
                              setIsOpenEditBillingAddress(
                                true
                              )
                            }
                          >
                            <div className="flex items-center gap-1">
                              <MdOutlineEdit />
                              <span className="text-xs">Edit</span>
                            </div>
                          </Button>
                          <Button
                            theme={
                              button_custom
                            }
                            color="failure"
                            size="xs"
                            onClick={() =>
                              setIsOpenDocuments(
                                true
                              )
                            }
                          >
                            <div className="flex items-center gap-1">
                              <RiDeleteBin6Line />
                              <span className="text-xs">Delete</span>
                            </div>
                          </Button>
                        </div>
                        <div>
                          <button
                            className="p-1.5"
                            id="parentID"
                            onClick={() =>
                              ToggleChild(
                                "parentID",
                                "childTr"
                              )
                            }
                          >
                            <IoIosArrowDown />

                          </button>
                        </div>
                      </div>
                    </Table.Cell>
                  </Table.Row>
                  <Table.Row
                    className="hidden bg-gray-100 p-0"
                    id="childTr"
                  >
                    <Table.Cell className="p-0" colSpan={9}>
                      <Details></Details>
                    </Table.Cell>
                  </Table.Row>

                </Table.Body>
              </Table>
            </div>
          </div>
          <div className="m-2 border rounded-lg p-3 bg-white ">
            <div className="flex flex-wrap justify-between lg:gap-0 md:gap-0 gap-2">
              <div className="flex gap-3 items-center">
                <div className="text-gray-400 text-sm ">
                  Showing 1 to 25 of 62 entries
                </div>
                <div>
                  <select
                    id="countries"
                    className="text-xs bg-gray-50 border border-gray-300 text-gray-900 rounded focus:ring-blue-500 focus:border-blue-500 block p-1 ps-2 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-blue-500 dark:focus:border-blue-500"
                  >
                    <option
                      value={10}
                      defaultValue={10}
                    >
                      10
                    </option>
                    <option value={15}>15</option>
                    <option value={20}>20</option>
                    <option value={25}>25</option>
                    <option value={30}>30</option>
                  </select>
                </div>
              </div>
              <div className="flex">
                <Button
                  size="xs"
                  color="gray"
                  className="border-e-0 text-gray-400 px-2 rounded text-xs "
                >
                  Previous
                </Button>
                <Button
                  size="xs"
                  color="blue"
                  className="border text-white border-blue-600 bg-blue-600 px-2.5 rounded-none text-sm "
                >
                  1
                </Button>
                <Button
                  size="xs"
                  color="gray"
                  className="border text-blue-600 px-2.5 rounded-none text-xs "
                >
                  2
                </Button>
                <Button
                  size="xs"
                  color="gray"
                  className="border-s-0 text-blue-600 px-2 rounded text-xs "
                >
                  Next
                </Button>
              </div>
            </div>
          </div>
        </div>

      </div>
      {/* Add / Edit Product */}
      <Drawer
        theme={customDrawer}
        open={isOpen}
        onClose={() => setIsOpen(false)}
        position="right"
        className="w-full xl:w-6/12 lg:w-full md:w-full"
      >
        <Drawer.Header
          titleIcon={MdOutlineCategory}
          title={
            <div className="flex items-center gap-1">
              Add Product <span className="text-blue-600">(Rapbooster Plus)</span>
            </div>
          }
        />
        <Drawer.Items>
          <AddProduct></AddProduct>
        </Drawer.Items>
      </Drawer>
      
      {/* Edit Billing Address */}

    </Main>
  );
}
export default Index;


