import ConfirmBox from "@/Components/HelperComponents/ConfirmBox";
import NoRecord from "@/Components/HelperComponents/NoRecord";
import Paginate from "@/Components/HelperComponents/Paginate";
import PerPageDropdown from "@/Components/HelperComponents/PerPageDropdown";
import { Head, Link, useForm } from "@inertiajs/react";
import { Badge, Button, Checkbox, Drawer, DrawerHeader, DrawerItems, Table, TableBody, TableCell, TableHead, TableHeadCell, TableRow, Tooltip } from "flowbite-react";
import $ from "jquery";
import { useState } from "react";
import { FaRegTrashCan } from "react-icons/fa6";
import { IoIosArrowBack, IoMdAdd, IoMdRepeat } from "react-icons/io";
import { MdDeleteOutline, MdOutlineModeEdit } from "react-icons/md";
import { RiDeleteBin6Line, RiListSettingsLine } from "react-icons/ri";

import { button_custom, customDrawerEdit, table_custom } from "@/Pages/Helpers/DesignHelper";
import WaMain from "../../WaMain";
import AddKeywordMessage from "./Message/AddKeywordMessage";
import AddKeyword from "./AddKeyword";
import EditKeyword from "./EditKeyword";

export default function Index({ collection }) {

    const { bot, getData, keywords } = collection;

    // -------- add campaign -------------
    const [isCheckAll, setIsCheckAll] = useState(false);
    const [checkValue, setCheckValue] = useState([]);
    const [isConfirmOpen, setConfirmOpen] = useState(false);

    const [KeywordData, setKeywordData] = useState();

    const [enableAddMessage, setEnableAddMessage] = useState(false);

    const [enableAddKeyword, setEnableAddKeyword] = useState(false);
    const [enableEditKeyword, setEnableEditKeyword] = useState(false);

    const { data, setData, delete: destroy, processing, errors } = useForm({
        id: [],
    });
    // executes when user click table row checkbox
    function getCheckedIds(e) {
        let previousIds = checkValue;
        if (e.target.checked) {
            if (!previousIds.includes(e.target.id)) {
                previousIds.push(e.target.id);
                setCheckValue(previousIds);
            }
        } else {
            const newIds = previousIds.filter((item) => item !== e.target.id);
            setCheckValue(newIds);
        }
    }

    // executes when user click table header checkbox
    function headerCheckBoxChecked(e) {
        let previousIds = [];
        if (e.target.checked && e.target.id == 0 && keywords.messages.data.length > 0) {
            keywords.messages.data.map((keyWords, key) => {
                if (!previousIds.includes(keyWords.id)) {
                    previousIds.push(keyWords.id);
                    setCheckValue(previousIds);
                }
            });
            setIsCheckAll(true);
            $(".rowCheckBox").prop("checked", true);
        } else {
            setCheckValue(previousIds);
            setIsCheckAll(false);
            $(".rowCheckBox").prop("checked", false);
        }
    }

    // handle checkBox Check or uncheck
    function checkAddCheckBoxChecked() {
        let allCheckBoxes = $(".rowCheckBox");
        let checkedCheckBoxes = $(".rowCheckBox:checked");

        if (allCheckBoxes.length == checkedCheckBoxes.length) {
            setIsCheckAll(true);
        } else {
            setIsCheckAll(false);
        }
    }

    function handleConfirmBoxResult(result) {
        if (checkValue.length > 0) {
            setData({ id: checkValue });
            destroy(
                route("whatsappB.bot.keyword.destroy", {
                    keyword: checkValue, bot: bot.id
                })
            );
            $(".rowCheckBox").prop("checked", false);
            setCheckValue([]);
            setIsCheckAll(false);
        } else {
            alert('Select at least one keyword to delete');
            setCheckValue([]);
            setIsCheckAll(false);
        }
        setConfirmOpen(false);
    }

    return (
        <WaMain>
            {/* <TabBar /> */}
            <Head title="Keywords" />
            <div className="mt-2.5 bg-white rounded-lg dark:bg-gray-800 mx-2">
                {/* <BackButton /> */}
                <div className="flex justify-between p-2">
                    <div className="flex items-center gap-2">
                        <Button
                            className="pe-1.5"
                            size="xs"
                            color="pink"
                            theme={button_custom}
                            onClick={() => window.history.back()}
                        >
                            <div className="flex items-center gap-1">
                                <IoIosArrowBack />
                                <span className="text-xs">Back</span>
                            </div>
                        </Button>
                        {
                            collection.can_delete &&
                            <Button
                                className=""
                                size="xs"
                                color="gray"
                                theme={button_custom}
                                onClick={() =>
                                    setConfirmOpen(true)
                                }
                            >
                                <div className="flex items-center gap-1">
                                    <MdDeleteOutline className="text-slate-500" />
                                    <span className="text-xs">Delete</span>
                                </div>
                            </Button>
                        }
                        {/* <Button.Group theme={buttongroup_custom} >
                            <Button
                                theme={button_custom}
                                size="xs"
                                color="gray"
                                id="dropdownInformationButton"
                                data-dropdown-toggle="dropdownNotification"
                                type="button"
                            >
                                <div className="flex items-center gap-1 ps-1">
                                    <TbColumns3 className="text-sm text-slate-500" />
                                    <span className="text-xs">Columns</span>
                                </div>
                            </Button>
                        </Button.Group> */}
                        <div className="">
                            Bot Name :
                        </div>
                        <Badge className="capitalize">
                            {bot.name}
                        </Badge>

                    </div>
                    <div className="flex items-center">
                        {
                            collection.can_add &&
                            <Button
                                className=""
                                size="xs"
                                color="gray"
                                theme={button_custom}
                                onClick={() => {
                                    setEnableAddKeyword(true);
                                }}
                            >
                                <div className="flex items-center gap-1">
                                    <IoMdAdd className="text-slate-500" />
                                    <span className="text-xs">Add</span>
                                </div>
                            </Button>
                        }
                    </div>
                </div>

                <div className="flex flex-col justify-between ">
                    <div className="overflow-x-auto border rounded-lg">
                        <Table hoverable theme={table_custom}>
                            <TableHead>
                                <TableHeadCell className="w-10 text-center">
                                    <Checkbox
                                        checked={isCheckAll}
                                        color="blue"
                                        id={0}
                                        onChange={(e) => {
                                            headerCheckBoxChecked(e);
                                        }}
                                    />
                                </TableHeadCell>
                                <TableHeadCell>
                                    <Link
                                        href={route(
                                            "whatsappB.bot.keyword.index",
                                            {
                                                column: "keyword",
                                                sort:
                                                    getData.sort == "asc"
                                                        ? "desc"
                                                        : "asc",
                                                bot: bot.id,
                                            }
                                        )}
                                    >
                                        <div className="flex items-center justify-between gap-2">
                                            <span>Keyword</span>
                                            <svg
                                                xmlns="http://www.w3.org/2000/svg"
                                                height="16px"
                                                viewBox="0 -960 960 960"
                                                width="20px"
                                                fill="#5f6368"
                                            >
                                                <path d="M480-120 300-300l58-58 122 122 122-122 58 58-180 180ZM358-598l-58-58 180-180 180 180-58 58-122-122-122 122Z" />
                                            </svg>
                                        </div>
                                    </Link>
                                </TableHeadCell>
                                <TableHeadCell>
                                    <span>Message</span>
                                </TableHeadCell>

                                <TableHeadCell>
                                    <h3>Actions</h3>
                                </TableHeadCell>
                            </TableHead>
                            <TableBody className="divide-y">
                                {keywords.messages.data.length > 0 ? (
                                    keywords.messages.data.map((list, key) => {
                                        return (
                                            <TableRow
                                                className="bg-white dark:border-gray-700 dark:bg-gray-800"
                                                key={key}
                                            >
                                                <TableCell className="text-center">
                                                    <Checkbox
                                                        className="rowCheckBox"
                                                        id={list.id}
                                                        color="blue"
                                                        onChange={(e) => {
                                                            getCheckedIds(e);
                                                            checkAddCheckBoxChecked();
                                                        }}
                                                    />
                                                </TableCell>
                                                <TableCell>
                                                    {list.keyword}
                                                </TableCell>
                                                <TableCell>
                                                    <div className="flex justify-between">

                                                        <div className="">
                                                            {list.messages_count}
                                                        </div>
                                                        <div className="flex gap-2">
                                                            <div className="">
                                                                {
                                                                    keywords.can_keywordMessage.view &&
                                                                    <Button
                                                                        theme={button_custom}
                                                                        as={Link}
                                                                        size={"xs"}
                                                                        color="blue"
                                                                        className="items-center rounded"
                                                                        href={route('whatsappB.bot.keyword.message.index', { keyword: list.id })}
                                                                    >
                                                                        <div className="flex items-center gap-1">
                                                                            <RiListSettingsLine />
                                                                            <span className="text-xs">
                                                                                Manage
                                                                            </span>
                                                                        </div>
                                                                    </Button>
                                                                }
                                                            </div>
                                                            {
                                                                keywords.can_keywordMessage.add &&
                                                                <div className="">
                                                                    <Button
                                                                        theme={button_custom}
                                                                        size={"xs"}
                                                                        color="blue"
                                                                        className="items-center rounded"
                                                                        onClick={() => {
                                                                            setKeywordData(list);
                                                                            setEnableAddMessage(true)
                                                                        }}
                                                                    >
                                                                        <div className="flex items-center gap-1">
                                                                            <IoMdAdd />
                                                                            <span className="text-xs">
                                                                                Add
                                                                            </span>
                                                                        </div>
                                                                    </Button>
                                                                </div>
                                                            }
                                                        </div>
                                                    </div>
                                                </TableCell>

                                                <TableCell>
                                                    <div className="flex items-center gap-3">
                                                        {
                                                            collection.can_edit &&
                                                            <Button

                                                                theme={
                                                                    button_custom
                                                                }
                                                                color="success"
                                                                size="xs"
                                                                onClick={() => {
                                                                    setEnableEditKeyword(true);
                                                                    setKeywordData(list);

                                                                }}
                                                            >
                                                                <MdOutlineModeEdit className="text-sm" />
                                                                <span className="text-xs ms-1">
                                                                    Edit
                                                                </span>
                                                            </Button>
                                                        }
                                                        {
                                                            collection.can_delete &&
                                                            <Tooltip content="Delete">
                                                                <Button
                                                                    theme={
                                                                        button_custom
                                                                    }
                                                                    color="failure"
                                                                    size="xs"
                                                                    onClick={() => {
                                                                        setCheckValue([list.id]);
                                                                        setConfirmOpen(true);
                                                                    }}
                                                                >
                                                                    <RiDeleteBin6Line className="text-sm" />
                                                                    <span className="text-xs ms-1">
                                                                        Delete
                                                                    </span>
                                                                </Button>
                                                            </Tooltip>
                                                        }
                                                    </div>
                                                </TableCell>
                                            </TableRow>
                                        );
                                    })
                                ) : (
                                    <TableRow className="bg-white dark:border-gray-700 dark:bg-gray-800">
                                        <TableCell
                                            colSpan={11}
                                            className="text-center"
                                        >
                                            <NoRecord />
                                        </TableCell>
                                    </TableRow>
                                )}
                            </TableBody>
                        </Table>
                    </div>
                    <div className="bottom-0 w-full p-3 bg-white rounded-lg float-end dark:border-gray-500 dark:rounded-lg dark:bg-gray-700">
                        <div className="flex flex-wrap justify-center gap-2 lg:justify-between lg:gap-0 md:gap-0">
                            <div className="flex items-center gap-3">
                                <div className="flex gap-3">
                                    <PerPageDropdown
                                        getDataFields={getData ?? null}
                                        routeName={
                                            "whatsappB.bot.keyword.index"
                                        }
                                        id={bot.id}
                                        data={keywords.messages}
                                    />
                                </div>
                            </div>
                            <Paginate tableData={keywords.messages} />
                        </div>
                    </div>
                </div>
            </div>


            {enableAddMessage && (
                <Drawer
                    theme={customDrawerEdit}
                    className="w-full lg:w-3/5 md:w-4/5"
                    open={enableAddMessage}
                    onClose={() => setEnableAddMessage(false)}
                    position="right"
                >
                    <DrawerHeader titleIcon={IoMdAdd} title="Add Keyword Message" />
                    <DrawerItems className="">
                        <AddKeywordMessage keyword={KeywordData}
                            onClose={() => setEnableAddMessage(false)}
                        />
                    </DrawerItems>
                </Drawer>
            )}

            {enableAddKeyword && (
                <Drawer
                    theme={customDrawerEdit}
                    className="w-full lg:w-3/5 md:w-4/5"
                    open={enableAddKeyword}
                    onClose={() => setEnableAddKeyword(false)}
                    position="right"
                >
                    <DrawerHeader titleIcon={IoMdRepeat} title="Add Keyword" />
                    <DrawerItems className="">
                        <AddKeyword bot={bot}
                            onClose={() => setEnableAddKeyword(false)}
                        />
                    </DrawerItems>
                </Drawer>
            )}


            {enableEditKeyword && (
                <Drawer
                    theme={customDrawerEdit}
                    className="w-full lg:w-3/5 md:w-4/5"
                    open={enableEditKeyword}
                    onClose={() => setEnableEditKeyword(false)}
                    position="right"
                >
                    <DrawerHeader titleIcon={IoMdRepeat} title="Edit Keyword" />
                    <DrawerItems className="">
                        <EditKeyword bot={bot} keyword={KeywordData}
                            onClose={() => setEnableEditKeyword(false)}
                        />
                    </DrawerItems>
                </Drawer>
            )}

            {/* confirm box popup */}
            {
                isConfirmOpen &&
                <ConfirmBox
                    isOpen={isConfirmOpen}
                    onClose={() => setConfirmOpen(false)} // Close the confirm box
                    onAction={handleConfirmBoxResult} // Handle the user's choice
                    title="Are you sure you want to delete this?"
                    message="This action cannot be undone."
                    confirmText="Yes, Delete!"
                    cancelText="No, Keep It"
                    confirmColor="orange"
                    cancelColor="gray"
                    icon={<FaRegTrashCan />}
                />
            }
        </WaMain>
    );
}



