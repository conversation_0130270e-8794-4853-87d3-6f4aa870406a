import { FilePreview } from "@/Components/FilePreview";
import ConfirmBox from "@/Components/HelperComponents/ConfirmBox";
import NoRecord from "@/Components/HelperComponents/NoRecord";
import Paginate from "@/Components/HelperComponents/Paginate";
import PerPageDropdown from "@/Components/HelperComponents/PerPageDropdown";
import TemplateBox from "@/Components/TemplateBox";
import {
    button_custom,
    customDrawer
} from "@/Pages/Helpers/DesignHelper";
import {
    textFormatting
} from "@/Pages/Helpers/Helper";
import { router } from "@inertiajs/react";
import { Button, Drawer, DrawerHeader, DrawerItems, TextInput } from "flowbite-react";
import { useState } from "react";
import { FaRegTrashCan } from "react-icons/fa6";
import { HiOutlineTemplate } from "react-icons/hi";
import { IoMdAdd } from "react-icons/io";
import { MdDeleteOutline, MdOutlineEdit } from "react-icons/md";
import Add from "./Add";
import Category from "./Category";
import Edit from "./Edit";
export default function Templates({ collection }) {
    console.log(collection);
    
    // Add Template
    const { templates, category_id, getData } = collection
    const currentPageRoute = "whatsapp.templates.index";
    const [isOpen, setIsOpen] = useState(false);
    const [isEdit, setIsEdit] = useState(false);
    const [editId, setEditId] = useState(null);
    const [templateData, setTemplateData] = useState(templates.data);
    const [selectedCategory, setSelectedCategory] = useState(category_id ?? "0");

    const [isConfirmOpen, setConfirmOpen] = useState(false);
    const [checkValue, setCheckValue] = useState([]);
    


   
    const removeObjectAndDeleteRecord = (table, id, objKey) => {
        router.delete(route("whatsapp.templates.destroy", { id: id }));
        const urlParams = new URLSearchParams(window.location.search);
        const perPageValue = urlParams.get("perPage") ?? 8;
        fetch(
            route("whatsapp.templates.fetch", {
                id: selectedCategory,
                perPage: perPageValue,
            })
        )
            .then((res) => {
                return res.json();
            })
            .then((result) => {
                setTemplateData(result.template.data);
            })
            .catch((error) => {
                console.error("Error fetching badge data:", error);
            });
    };


    function handleConfirmBoxResult(result) {
        if (checkValue.length > 0 && result) {
            removeObjectAndDeleteRecord("wa_template", checkValue);
        }
        setCheckValue([]);
        setConfirmOpen(false);
    }

    return (
        <>
            <div className="relative p-2 mt-1 overflow-hidden">
                <div className="grid grid-flow-row-dense grid-cols-12 gap-2 p-0 border-none md:grid-cols-12 sm:grid-cols-1 bg-slate-100">
                    <div className="hidden bg-white border rounded-lg xl:col-span-3 lg:col-span-3 md:col-span-2 sm:col-span-3 lg:flex">
                        <Category
                            category_id={category_id}
                            setSelectedCategory={setSelectedCategory}
                            selectedCategory={selectedCategory}
                        />
                    </div>
                    <div className="relative pt-0 dark:text-gray-400 lg:p-0 xl:col-span-9 lg:col-span-9 md:col-span-12 col-span-full">
                        <div>
                            <div className="h-full p-2 bg-white border rounded-lg">
                                {/* Welcome message  */}
                                <div className="flex items-start justify-between mb-2">
                                    <div className="flex items-center max-w-md gap-3">
                                      
                                    </div>
                                    <div>

                                        {collection.can_add &&

                                            <Button
                                                className="pe-1"
                                                size="xs"
                                                color="gray"
                                                theme={button_custom}
                                                onClick={() => setIsOpen(true)}
                                            >
                                                <div className="flex items-center gap-1">
                                                    <IoMdAdd className="text-slate-500" />
                                                    <span className="text-xs">
                                                        Add
                                                    </span>
                                                </div>
                                            </Button>
                                        }
                                    </div>
                                </div>
                                <div className="grid grid-flow-row-dense grid-cols-1 gap-4 2xl:grid:cols-4 xl:grid-cols-3 lg:grid-cols-2 md:grid-cols-3 sm:grid-cols-2">
                                    {/* Templates card */}
                                    {templateData.length > 0 ? (
                                        templateData.map((template, index) => {
                                            return (
                                                <div className="" key={index}>
                                                    <TemplateBox>
                                                        <FilePreview object={template.file} imageSize="xl" />
                                                        {template.body && (

                                                            <div className="break-all word-break"
                                                                dangerouslySetInnerHTML={{
                                                                    __html: textFormatting(template.body),
                                                                }}
                                                            />
                                                        )}
                                                    </TemplateBox>
                                                    <div
                                                        className="flex items-center justify-between gap-2 p-2 rounded-b-lg bg-slate-50"
                                                        style={{
                                                            marginTop: "-8px",
                                                        }}
                                                    >
                                                        <span>
                                                            {template.name}
                                                        </span>
                                                        <div className="flex items-center">

                                                            {collection.can_edit &&
                                                                <Button
                                                                    theme={
                                                                        button_custom
                                                                    }
                                                                    color="withoutBorder"
                                                                    size="xs"
                                                                    onClick={() => {
                                                                        setIsEdit(
                                                                            true
                                                                        );
                                                                        setEditId(
                                                                            template.id
                                                                        );
                                                                    }}
                                                                >
                                                                    <MdOutlineEdit className="text-slate-400" />
                                                                </Button>
                                                            }
                                                            {collection.can_delete &&
                                                                <Button
                                                                    theme={
                                                                        button_custom
                                                                    }
                                                                    color="withoutBorder"
                                                                    size="xs"
                                                                    onClick={() => {
                                                                        setCheckValue([template.id]);
                                                                        setConfirmOpen(true);
                                                                    }
                                                                    }
                                                                >
                                                                    <MdDeleteOutline className="text-slate-400" />
                                                                </Button>
                                                            }

                                                        </div>
                                                    </div>
                                                </div>
                                            );
                                        })
                                    ) : (
                                        <div className="col-span-12">
                                            <NoRecord />
                                        </div>
                                    )}
                                </div>
                            </div>
                            <div className="bottom-0 w-full mt-1.5 p-3 bg-white rounded-lg float-end dark:border-gray-500 dark:rounded-lg dark:bg-gray-700">
                                <div className="flex flex-wrap justify-between gap-2 lg:justify-between lg:gap-0 md:gap-0">
                                    <div className="flex items-center gap-4">
                                        <PerPageDropdown
                                            routeParams={getData}
                                            routeName={"whatsapp.templates.index"}
                                            data={templates}
                                            customPerPage={8}
                                        />
                                    </div>

                                    <Paginate tableData={templates} />
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            {isOpen && (
                <Drawer
                    theme={customDrawer}
                    open={isOpen}
                    onClose={() => setIsOpen(false)}
                    position="right"
                    className="w-full lg:w-5/6 md:w-4/5"
                >
                    <DrawerHeader
                        titleIcon={HiOutlineTemplate}
                        title="Add Template"
                    />
                    <DrawerItems>
                        <Add
                            // categories={categories}
                            onClose={() => setIsOpen(false)}
                            setTemplate={setTemplateData}
                            selectedCategory={selectedCategory}
                            setSelectedCategory={setSelectedCategory}
                        />
                    </DrawerItems>
                </Drawer>
            )}
            {isEdit && (
                <Drawer
                    theme={customDrawer}
                    open={isEdit}
                    onClose={() => setIsEdit(false)}
                    position="right"
                    className="w-full lg:w-5/6 md:w-4/5"
                >
                    <DrawerHeader
                        titleIcon={HiOutlineTemplate}
                        title="Edit Template"
                    />
                    <DrawerItems>
                        <Edit
                            id={editId}
                            onClose={() => setIsEdit(false)}
                            setTemplate={setTemplateData}
                            setSelectedCategory={setSelectedCategory}
                        />
                    </DrawerItems>
                </Drawer>
            )}
            {/* confirm box popup */}
            {isConfirmOpen && (
                <ConfirmBox
                    isOpen={isConfirmOpen}
                    onClose={() => setConfirmOpen(false)} // Close the confirm box
                    onAction={handleConfirmBoxResult} // Handle the user's choice
                    title="Are you sure you want to delete this?"
                    message="This action cannot be undone."
                    confirmText="Yes, Delete!"
                    cancelText="No, Keep It"
                    confirmColor="orange"
                    cancelColor="gray"
                    icon={<FaRegTrashCan />}
                />
            )}
        </>
    );
}



