<?php

namespace App\Traits;

use Illuminate\Support\Facades\Auth;

trait AccessibleByUser
{
    public function scopeAccessibleByUser($query, $option = [], $userKeyName = 'user_id')
    {
        $user = Auth::user();
        if (!$user) {
            return $query->whereRaw('1 = 0');
        }

        $tableName = $this->getTable();
        if ($user->hasPermissionTo("$tableName.admin") || $user->isAdmin()) {
            return $query;
        }

        $userId = $option[$userKeyName] ?? null;
        $isAdmin = $option['isAdmin'] ?? false;

        $userIds = $userId || $isAdmin
            ? $user->getAllChildUserIds(userId: $userId, isAdmin: $isAdmin)
            : $user->getAllChildUserIds();

        if ($userId && !in_array($userId, $userIds->toArray())) {
            return $query->whereRaw('1 = 0');
        }

        return $query->whereIn($userKeyName, $userIds);
    }
}
