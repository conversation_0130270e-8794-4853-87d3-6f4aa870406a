import React, { useEffect } from "react";
import {
    <PERSON>ge,
    Button,
    Checkbox,
    Table,
    ToggleSwitch,
    Tooltip,
} from "flowbite-react";
import { useState } from "react";
import { FaEye } from "react-icons/fa";
import { IoQrCode } from "react-icons/io5";

import {
    MdOutlineEdit,
} from "react-icons/md";
import Main from "../Main";
import $ from "jquery";
import {
    button_custom,
    table_custom,
    toggle_custom,
} from "@/Pages/Helpers/DesignHelper";
import { PiCaretUpDownBold } from "react-icons/pi";
// import QRCode from "./QRCode";

export default function Index() {
    const [isCheckAll, setIsCheckAll] = useState(false);

    useEffect(() => {
        if (isCheckAll) {
            $(".rowCheckBox").prop("checked", true);
        } else {
            $(".rowCheckBox").prop("checked", false);
        }
    }, [isCheckAll]);

    return (
        <Main>
            <div className="w-full mt-2 overflow-auto bg-white border rounded h-fit">
                {/* -----------Table----------- */}
                <div className="bg-white border rounded-lg">
                    {/* documents table */}
                    <div className="overflow-x-auto border rounded-lg">
                        <Table hoverable theme={table_custom}>
                            <TableHead className=" bg-slate-100">
                                <TableHeadCell>
                                    <Checkbox
                                        color="blue"
                                        checked={isCheckAll}
                                    />
                                </TableHeadCell>
                                <TableHeadCell>
                                    {/* <Link
                                                href={route(
                                                    "Integration.whatsapp.whatsappweb.index",
                                                    {
                                                        name: target,
                                                        column: "id",
                                                        sort:
                                                            getData.sort ==
                                                                "asc"
                                                                ? "desc"
                                                                : "asc",
                                                    }
                                                )}
                                            > */}
                                    <div className="flex items-center justify-between gap-2">
                                        <span>Id</span>
                                        <PiCaretUpDownBold
                                        // className={
                                        //     (getData.column ==
                                        //         "id"
                                        //         ? "  text-gray-900 dark:text-gray-200 "
                                        //         : "text-gray-600 dark:text-gray-400 ") +
                                        //     "  cursor-pointer "
                                        // }
                                        />
                                    </div>
                                    {/* </Link> */}
                                </TableHeadCell>

                                <TableHeadCell>
                                    {/* <Link
                                                href={route(
                                                    "Integration.whatsapp.whatsappweb.index",
                                                    {
                                                        name: target,
                                                        column: "name",
                                                        sort:
                                                            getData.sort ==
                                                                "asc"
                                                                ? "desc"
                                                                : "asc",
                                                    }
                                                )}
                                            > */}
                                    <div className="flex items-center justify-between gap-2">
                                        <span>Name</span>
                                        <PiCaretUpDownBold
                                        // className={
                                        //     (getData.column ==
                                        //         "name"
                                        //         ? "  text-gray-900 dark:text-gray-200 "
                                        //         : "text-gray-600 dark:text-gray-400 ") +
                                        //     "  cursor-pointer "
                                        // }
                                        />
                                    </div>
                                    {/* </Link> */}
                                </TableHeadCell>

                                <TableHeadCell>
                                    {/* <Link
                                                href={route(
                                                    "Integration.whatsapp.whatsappweb.index",
                                                    {
                                                        name: target,
                                                        column: "delaySeconds",
                                                        sort:
                                                            getData.sort ==
                                                                "asc"
                                                                ? "desc"
                                                                : "asc",
                                                    }
                                                )}
                                            > */}
                                    <div className="flex items-center justify-between gap-2">
                                        <span>Delay</span>
                                        <PiCaretUpDownBold
                                        // className={
                                        //     (getData.column ==
                                        //         "delaySeconds"
                                        //         ? "  text-gray-900 dark:text-gray-200 "
                                        //         : "text-gray-600 dark:text-gray-400 ") +
                                        //     "  cursor-pointer "
                                        // }
                                        />
                                    </div>
                                    {/* </Link> */}
                                </TableHeadCell>

                                <TableHeadCell>Connect</TableHeadCell>

                                <TableHeadCell>
                                    {/* <Link
                                                href={route(
                                                    "Integration.whatsapp.whatsappweb.index",
                                                    {
                                                        name: target,
                                                        column: "status",
                                                        sort:
                                                            getData.sort ==
                                                                "asc"
                                                                ? "desc"
                                                                : "asc",
                                                    }
                                                )}
                                            > */}
                                    <div className="flex items-center justify-between gap-2">
                                        <span>status</span>
                                        <PiCaretUpDownBold
                                        // className={
                                        //     (getData.column ==
                                        //         "status"
                                        //         ? "  text-gray-900 dark:text-gray-200 "
                                        //         : "text-gray-600 dark:text-gray-400 ") +
                                        //     "  cursor-pointer "
                                        // }
                                        />
                                    </div>
                                    {/* </Link> */}
                                </TableHeadCell>

                                <TableHeadCell>
                                    {/* <Link
                                                href={route(
                                                    "Integration.whatsapp.whatsappweb.index",
                                                    {
                                                        name: target,
                                                        column: "savechat",
                                                        sort:
                                                            getData.sort ==
                                                                "asc"
                                                                ? "desc"
                                                                : "asc",
                                                    }
                                                )}
                                            > */}
                                    <div className="flex items-center justify-between gap-2">
                                        <span>Save chat</span>
                                        <PiCaretUpDownBold
                                        // className={
                                        //     (getData.column ==
                                        //         "savechat"
                                        //         ? "  text-gray-900 dark:text-gray-200 "
                                        //         : "text-gray-600 dark:text-gray-400 ") +
                                        //     "  cursor-pointer "
                                        // }
                                        />
                                    </div>
                                    {/* </Link> */}
                                </TableHeadCell>
                                <TableHeadCell>
                                    <div className="flex items-center justify-between gap-2">
                                        <span>Last Status change</span>
                                        <svg
                                            xmlns="http://www.w3.org/2000/svg"
                                            height="16px"
                                            viewBox="0 0 24 24"
                                            width="16px"
                                            className="fill-gray-600"
                                        >
                                            <path
                                                d="M0 0h24v24H0V0z"
                                                fill="none"
                                            />
                                            <path d="M12 5.83L15.17 9l1.41-1.41L12 3 7.41 7.59 8.83 9 12 5.83zm0 12.34L8.83 15l-1.41 1.41L12 21l4.59-4.59L15.17 15 12 18.17z" />
                                        </svg>
                                    </div>
                                </TableHeadCell>

                                <TableHeadCell>
                                    Last Disconnect Reason
                                </TableHeadCell>
                                {/* <TableHeadCell>API</TableHeadCell> */}
                                <TableHeadCell>
                                    {/* <Link
                                                href={route(
                                                    "Integration.whatsapp.whatsappweb.index",
                                                    {
                                                        name: target,
                                                        column: "isActive",
                                                        sort:
                                                            getData.sort ==
                                                                "asc"
                                                                ? "desc"
                                                                : "asc",
                                                    }
                                                )}
                                            > */}
                                    <div className="flex items-center justify-between gap-2">
                                        <span>Active</span>
                                        <PiCaretUpDownBold
                                        // className={
                                        //     (getData.column ==
                                        //         "isActive"
                                        //         ? "  text-gray-900 dark:text-gray-200 "
                                        //         : "text-gray-600 dark:text-gray-400 ") +
                                        //     "  cursor-pointer "
                                        // }
                                        />
                                    </div>
                                    {/* </Link> */}
                                </TableHeadCell>
                                <TableHeadCell>User</TableHeadCell>
                                <TableHeadCell>Workers</TableHeadCell>
                                <TableHeadCell>Action</TableHeadCell>
                            </TableHead>

                            <TableBody className="divide-y">
                                <TableRow >
                                    <TableCell>
                                        <Checkbox
                                            color={"blue"}
                                            className="rowCheckBox"
                                        />
                                    </TableCell>
                                    <TableCell className="font-medium text-gray-900 whitespace-nowrap dark:text-white">
                                        #Id
                                    </TableCell>
                                    <TableCell>
                                        channel Name
                                    </TableCell>
                                    <TableCell>
                                        <div className="flex justify-center">
                                            <Tooltip content="Scan QR">
                                                <Button

                                                    theme={
                                                        button_custom
                                                    }
                                                    color="success"
                                                    size="xs"
                                                >
                                                    <IoQrCode />
                                                </Button>
                                            </Tooltip>
                                        </div>
                                    </TableCell>
                                    <TableCell>
                                        <div className="w-fit">
                                            {/* {channel.status} */}
                                            <Badge
                                                // color={
                                                //     statusColor[
                                                //         channel
                                                //             .status
                                                //     ].bg ??
                                                //     "info"
                                                // }
                                                className="capitalize"
                                            >
                                                channel status
                                            </Badge>
                                        </div>
                                    </TableCell>
                                    <TableCell>
                                        <div className="flex items-center gap-3">
                                            <Tooltip content="Save Chat">
                                                <ToggleSwitch
                                                    theme={
                                                        toggle_custom
                                                    }
                                                    sizing="xs"
                                                />
                                            </Tooltip>


                                            <Tooltip content="View Chat">
                                                <Button
                                                    color={
                                                        "gray"
                                                    }
                                                    theme={
                                                        button_custom
                                                    }
                                                    size="xs"
                                                >
                                                    <FaEye className="text-slate-500 text-sm" />
                                                    <span className="text-xs ms-1">
                                                        View
                                                    </span>
                                                </Button>
                                            </Tooltip>

                                        </div>
                                    </TableCell>

                                    <TableCell>
                                        status
                                    </TableCell>
                                    <TableCell>
                                        last
                                    </TableCell>
                                    <TableCell>
                                        autoreply
                                    </TableCell>
                                    <TableCell>
                                        <ToggleSwitch
                                            theme={
                                                toggle_custom
                                            }
                                            sizing="xs"
                                        />
                                    </TableCell>
                                    <TableCell>
                                        userName
                                    </TableCell>
                                    <TableCell>
                                        worker
                                    </TableCell>
                                    <TableCell>
                                        <div className="flex gap-2">
                                            <Button
                                                theme={
                                                    button_custom
                                                }
                                                color="gray"
                                                size="xs"
                                            >
                                                <MdOutlineEdit className="text-xs" />
                                                <span className="text-xs ms-1">
                                                    Edit
                                                </span>
                                            </Button>
                                        </div>
                                    </TableCell>
                                </TableRow>
                            </TableBody>
                        </Table>
                    </div>
                </div>

                {/* <div className="bottom-0 w-full p-3 bg-white rounded-lg float-end dark:border-gray-500 dark:rounded-lg dark:bg-gray-700">
                            <div className="flex flex-wrap justify-between gap-2 lg:justify-between lg:gap-0 md:gap-0">
                                <div className="flex items-center  gap-4">
                                    <Button
                                        className="border rounded"
                                        size="xs"
                                        color="gray"
                                        theme={button_custom}
                                        onClick={() =>
                                            window.confirm(
                                                "Do you really want to delete WhatsAppWeb Channel ?"
                                            ) && deleteRecords()
                                        }
                                    >
                                        <div className="flex items-center gap-1">
                                            <MdDeleteOutline className="text-slate-500" />
                                            <span className="text-xs">
                                                Delete
                                            </span>
                                        </div>
                                    </Button>
                                    <PerPageDropdown
                                        getDataFields={getData ?? null}
                                        routeName={
                                            "Integration.whatsapp.whatsappweb.index"
                                        }
                                        data={channelsData}
                                        idName={"name"}
                                    />
                                </div>

                                <Paginate tableData={channelsData} />
                            </div>
                        </div> */}
            </div>
        </Main>
    );
}

