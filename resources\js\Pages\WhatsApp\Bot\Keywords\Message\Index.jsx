import ConfirmBox from "@/Components/HelperComponents/ConfirmBox";
import Paginate from "@/Components/HelperComponents/Paginate";
import { Head, Link, useForm } from "@inertiajs/react";
import { Avatar, Badge, Button, Checkbox, Drawer, DrawerHeader, DrawerItems, Table, Tooltip } from "flowbite-react";
import $ from "jquery";
import { useState } from "react";
import { FaRegTrashCan } from "react-icons/fa6";
import { IoIosArrowBack, IoMdAdd, IoMdRepeat } from "react-icons/io";
import { MdDeleteOutline, MdOutlineModeEdit } from "react-icons/md";

import { button_custom, customDrawerEdit, table_custom } from "@/Pages/Helpers/DesignHelper";
import WaMain from "@/Pages/WhatsApp/WaMain";
import AddKeywordMessage from "./AddKeywordMessage";
import { PiCaretUpDownBold } from "react-icons/pi";
import { RiDeleteBin6Line } from "react-icons/ri";
import PerPageDropdown from "@/Components/HelperComponents/PerPageDropdown";
import { getIconForFile, isImage, removePrefix, showAsset } from "@/Pages/Helpers/Helper";
import Edit from "./Edit";
import { FilePreview } from "@/Components/FilePreview";
import NoRecord from "@/Components/HelperComponents/NoRecord";

function Index({ collection }) {

    const { messages, getData, keyword } = collection;
    // -------- add campaign -------------
    const [isCheckAll, setIsCheckAll] = useState(false);
    const [checkValue, setCheckValue] = useState([]);
    const [isConfirmOpen, setConfirmOpen] = useState(false);

    const [enableAddMessage, setEnableAddMessage] = useState(false);
    const [enableEditMessage, setEnableEditMessage] = useState(false);

    const [selectedMessage, setSelectedMessage] = useState();

    const { data, setData, delete: destroy, processing, errors } = useForm({
        id: [],
    });
    // executes when user click table row checkbox
    function getCheckedIds(e) {
        let previousIds = checkValue;
        if (e.target.checked) {
            if (!previousIds.includes(e.target.id)) {
                previousIds.push(e.target.id);
                setCheckValue(previousIds);
            }
        } else {
            const newIds = previousIds.filter((item) => item !== e.target.id);
            setCheckValue(newIds);
        }
    }

    // executes when user click table header checkbox
    function headerCheckBoxChecked(e) {
        let previousIds = [];
        if (e.target.checked && e.target.id == 0 && messages.data.length > 0) {
            // keywords.data.map((keyWords, key) => {
            //     if (!previousIds.includes(keyWords.id)) {
            //         previousIds.push(keyWords.id);
            //         setCheckValue(previousIds);
            //     }
            // });
            setIsCheckAll(true);
            $(".rowCheckBox").prop("checked", true);
        } else {
            setCheckValue(previousIds);
            setIsCheckAll(false);
            $(".rowCheckBox").prop("checked", false);
        }
    }

    // handle checkBox Check or uncheck
    function checkAddCheckBoxChecked() {
        let allCheckBoxes = $(".rowCheckBox");
        let checkedCheckBoxes = $(".rowCheckBox:checked");

        if (allCheckBoxes.length == checkedCheckBoxes.length) {
            setIsCheckAll(true);
        } else {
            setIsCheckAll(false);
        }
    }

    function handleConfirmBoxResult(result) {
        if (checkValue.length > 0) {
            setData({ id: checkValue });
            destroy(
                route("whatsapp.bot.keyword.message.destroy", {
                    message: checkValue, keyword: keyword.id
                })
            );
            $(".rowCheckBox").prop("checked", false);
            setCheckValue([]);
            setIsCheckAll(false);
        } else {
            setCheckValue([]);
            setIsCheckAll(false);
        }
        setConfirmOpen(false);
    }

    return (
        <WaMain>
            {/* <TabBar /> */}
            <Head title="Keywords" />
            <div className="mt-2.5 bg-white rounded-lg dark:bg-gray-800 mx-2">
                {/* <BackButton /> */}
                <div className="flex justify-between p-2">
                    <div className="flex items-center gap-2">
                        <Button
                            className="pe-1.5"
                            size="xs"
                            color="pink"
                            theme={button_custom}
                            onClick={() => window.history.back()}
                        >
                            <div className="flex items-center gap-1">
                                <IoIosArrowBack />
                                <span className="text-xs">Back</span>
                            </div>
                        </Button>
                        {
                            collection.can_delete &&
                            <Button
                                className=""
                                size="xs"
                                color="gray"
                                theme={button_custom}
                                onClick={() =>
                                    setConfirmOpen(true)
                                }
                            >
                                <div className="flex items-center gap-1">
                                    <MdDeleteOutline className="text-slate-500" />
                                    <span className="text-xs">Delete</span>
                                </div>
                            </Button>
                        }

                        <div className="">
                            Keyword Name :
                        </div>
                        <Badge className="capitalize">
                            {keyword.keyword}
                        </Badge>

                    </div>
                    {
                        collection.can_add &&
                        <div className="flex items-center">
                            <Button
                                className=""
                                size="xs"
                                color="gray"
                                theme={button_custom}
                                onClick={() => {
                                    setEnableAddMessage(true);
                                }}

                            >
                                <div className="flex items-center gap-1">
                                    <IoMdAdd className="text-slate-500" />
                                    <span className="text-xs">Add</span>
                                </div>
                            </Button>
                        </div>
                    }
                </div>

                <div className="flex flex-col justify-between ">
                    <div className="overflow-x-auto border rounded-lg">
                        <Table hoverable theme={table_custom}>
                            <Table.Head>
                                <Table.HeadCell className="w-10 text-center">
                                    <Checkbox
                                        checked={isCheckAll}
                                        color="blue"
                                        id={0}
                                        onChange={(e) => {
                                            headerCheckBoxChecked(e);
                                        }}
                                    />
                                </Table.HeadCell>
                                <Table.HeadCell>
                                    <Link
                                        href={route(
                                            "whatsapp.bot.keyword.message.index",
                                            {
                                                sortColumn: "body",
                                                sortBy:
                                                    getData.sortBy == "asc"
                                                        ? "desc"
                                                        : "asc",
                                                keyword: keyword.id,
                                            }
                                        )}
                                    >
                                        <div className="flex items-center justify-between gap-2">
                                            <span>Message</span>
                                            <PiCaretUpDownBold
                                                className={
                                                    (getData.column ==
                                                        "body"
                                                        ? "  text-gray-900 dark:text-gray-200 "
                                                        : " text-gray-600 dark:text-gray-400 ") +
                                                    "  cursor-pointer "
                                                }
                                            />
                                        </div>
                                    </Link>
                                </Table.HeadCell>
                                <Table.HeadCell>
                                    File
                                </Table.HeadCell>
                                <Table.HeadCell>
                                    <h3>Actions</h3>
                                </Table.HeadCell>

                            </Table.Head>

                            <Table.Body className="divide-y">
                                {messages.data.length > 0 ? (
                                    messages.data.map((message, key) => {
                                        return (
                                            <Table.Row
                                                className="bg-white dark:border-gray-700 dark:bg-gray-800"
                                                key={key}
                                            >
                                                <Table.Cell className="text-center">
                                                    <Checkbox
                                                        className="rowCheckBox"
                                                        id={message.id}
                                                        color="blue"
                                                        onChange={(e) => {
                                                            getCheckedIds(e);
                                                            checkAddCheckBoxChecked();
                                                        }}
                                                    />
                                                </Table.Cell>
                                                <Table.Cell className="text-center">
                                                    {message.body}
                                                </Table.Cell>

                                                <Table.Cell className="text-center">
                                                    {message.file &&
                                                        <FilePreview object={message.file} imageSize="lg" width=" w-fit" />
                                                    }
                                                </Table.Cell>

                                                <Table.Cell>
                                                    <div className="flex items-center gap-3">
                                                        {
                                                            collection.can_edit &&
                                                            <Button

                                                                theme={
                                                                    button_custom
                                                                }
                                                                color="success"
                                                                size="xs"
                                                                onClick={() => {
                                                                    setSelectedMessage(message)
                                                                    setEnableEditMessage(true)
                                                                }}
                                                            >
                                                                <MdOutlineModeEdit className="text-sm" />
                                                                <span className="text-xs ms-1">
                                                                    Edit
                                                                </span>
                                                            </Button>
                                                        }
                                                        {
                                                            collection.can_delete &&
                                                            <Tooltip content="Delete">
                                                                <Button
                                                                    theme={
                                                                        button_custom
                                                                    }
                                                                    color="failure"
                                                                    size="xs"
                                                                    onClick={() => {
                                                                        setCheckValue([message.id]);
                                                                        setConfirmOpen(true);
                                                                    }}
                                                                >
                                                                    <RiDeleteBin6Line className="text-sm" />
                                                                    <span className="text-xs ms-1">
                                                                        Delete
                                                                    </span>
                                                                </Button>
                                                            </Tooltip>
                                                        }
                                                    </div>
                                                </Table.Cell>
                                            </Table.Row>
                                        );
                                    })
                                ) : (
                                    <Table.Row className="bg-white dark:border-gray-700 dark:bg-gray-800">
                                        <Table.Cell
                                            colSpan={11}
                                            className="text-center"
                                        >
                                            <NoRecord />
                                        </Table.Cell>
                                    </Table.Row>
                                )}
                            </Table.Body>
                        </Table>
                    </div>
                    <div className="bottom-0 w-full p-3 bg-white rounded-lg float-end dark:border-gray-500 dark:rounded-lg dark:bg-gray-700">
                        <div className="flex flex-wrap justify-center gap-2 lg:justify-between lg:gap-0 md:gap-0">
                            <div className="flex items-center gap-3">
                                <div className="flex gap-3">
                                    <PerPageDropdown
                                        getDataFields={getData ?? null}
                                        routeName={
                                            "whatsapp.bot.keyword.message.index"
                                        }
                                        routeParams={{ keyword: keyword.id }}
                                        data={messages}
                                    />
                                </div>
                            </div>
                            {/* <Paginate tableData={keywords} /> */}
                        </div>
                    </div>
                </div>
            </div>


            {enableAddMessage && (
                <Drawer
                    theme={customDrawerEdit}
                    className="w-full lg:w-3/5 md:w-4/5"
                    open={enableAddMessage}
                    onClose={() => setEnableAddMessage(false)}
                    position="right"
                >
                    <DrawerHeader titleIcon={IoMdAdd} title="Add Keyword Message" />
                    <DrawerItems className="">
                        <AddKeywordMessage keyword={keyword}
                            onClose={() => setEnableAddMessage(false)}
                        />
                    </DrawerItems>
                </Drawer>
            )}

            {enableEditMessage && (
                <Drawer
                    theme={customDrawerEdit}
                    className="w-full lg:w-4/5 md:w-4/5"
                    open={enableEditMessage}
                    onClose={() => setEnableEditMessage(false)}
                    position="right"
                >
                    <DrawerHeader titleIcon={IoMdAdd} title="Edit Keyword Message" />
                    <DrawerItems className="">
                        <Edit keyword={keyword} message={selectedMessage}
                            onClose={() => setEnableEditMessage(false)}
                        />
                    </DrawerItems>
                </Drawer>
            )}
            {/* confirm box popup */}
            {isConfirmOpen && (
                <ConfirmBox
                    isOpen={isConfirmOpen}
                    onClose={() => setConfirmOpen(false)} // Close the confirm box
                    onAction={handleConfirmBoxResult} // Handle the user's choice
                    title="Are you sure you want to delete this?"
                    message="This action cannot be undone."
                    confirmText="Yes, Delete!"
                    cancelText="No, Keep It"
                    confirmColor="orange"
                    cancelColor="gray"
                    icon={<FaRegTrashCan />}
                />
            )}
        </WaMain>
    )
}

export default Index
