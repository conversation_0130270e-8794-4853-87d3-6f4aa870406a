import Main from "@/Layouts/Main";
import React from "react";
import TabBar from "../TabBar";
import { Badge, Button, ButtonGroup, Checkbox, Table } from "flowbite-react";
import { button_custom, table_custom } from "@/Pages/Helpers/DesignHelper";
import { RiDeleteBin6Line } from "react-icons/ri";
import { TbColumns3 } from "react-icons/tb";
import { PiExportBold } from "react-icons/pi";
import { IoMdAdd } from "react-icons/io";
import { MdOutlineCategory, MdOutlineEdit } from "react-icons/md";
import { LuFilter } from "react-icons/lu";
import { Link } from "@inertiajs/react";

export default function Index() {
  return (
    <Main>
      <div className="overflow-hidden">
        <TabBar />
        <div className="px-2 pb-2 rounded-lg">
          <div className="h-fit bg-white rounded border overflow-auto w-full mt-2.5">
            <div className="flex justify-between p-2">
              <div className="flex items-center gap-1">
                <ButtonGroup>
                  <Button
                    theme={button_custom}
                    size="xs"
                    color="gray"
                  >
                    <div className="flex items-center gap-1">
                      <RiDeleteBin6Line className="text-slate-400" />
                      <span className="text-xs">
                        Delete
                      </span>
                    </div>
                  </Button>

                  <Button
                    theme={button_custom}
                    size="xs"
                    color="gray"
                    id="dropdownInformationButton"
                    data-dropdown-toggle="dropdownNotification"
                    type="button"
                  >
                    <div className="flex items-center gap-1">
                      <TbColumns3 className="text-slate-400 text-base ms-1" />
                      <span className="text-xs">
                        Columns
                      </span>
                    </div>
                  </Button>
                </ButtonGroup>
                <ButtonGroup>
                  <Button
                    theme={button_custom}
                    size="xs"
                    color="gray"
                  >
                    <div className="flex items-center gap-1">
                      <PiExportBold className="text-slate-400" />
                      <span className="text-xs">
                        Export
                      </span>
                    </div>
                  </Button>
                  <Button
                    theme={button_custom}
                    size="xs"
                    color="gray">
                    <div className="flex items-center gap-1">
                      <LuFilter className="text-slate-400 text-sm" />
                      <span className="text-xs">
                        Filter
                      </span>
                    </div>
                  </Button>
                </ButtonGroup>
              </div>

              <ButtonGroup className="">
                <Button
                  theme={button_custom}
                  size="xs"
                  color="gray"
                // onClick={() =>
                //   setIsOpen(
                //     true
                //   )
                // }
                >
                  <div className="flex items-center gap-1">
                    <IoMdAdd className="text-slate-500" />
                    <span className="text-xs">
                      Add
                    </span>
                  </div>
                </Button>
              </ButtonGroup>
            </div>

            <div className="overflow-x-auto bg-white border rounded-lg">
              <Table hoverable theme={table_custom}>
                <TableHead className="bg-slate-100">
                  <TableHeadCell>
                    <Checkbox color="blue"
                    // checked={isCheckAll}
                    // onChange={() =>
                    //   setIsCheckAll(!isCheckAll)
                    // }
                    />
                  </TableHeadCell>

                  <TableHeadCell>Category Name</TableHeadCell>

                  <TableHeadCell className="text-nowrap">Number of Products</TableHeadCell>

                  <TableHeadCell>Active</TableHeadCell>
                  <TableHeadCell>On Website</TableHeadCell>
                  <TableHeadCell>Action</TableHeadCell>
                </TableHead>

                <TableBody className="divide-y">
                  <TableRow className="">
                    <TableCell>
                      <Checkbox color="blue" className="rowCheckBox" />
                    </TableCell>
                    <TableCell>WhatsApp Marketing</TableCell>
                    <TableCell>22</TableCell>
                    <TableCell>
                      <Badge className="w-fit" color="success">Yes</Badge>
                    </TableCell>
                    <TableCell>
                      <Badge className="w-fit" color="success">Active</Badge>
                    </TableCell>
                    <TableCell>
                      <div className="flex gap-2 h-fit text-nowrap">
                        {/* <Tooltip
                               content="Documents"
                               className="bg-slate-700 p-1 px-2"
                           > */}
                        {/* </Tooltip> */}
                        <Button
                          theme={
                            button_custom
                          }
                          color="blue"
                          size="xs"
                          as={Link}
                          href={route("products.categories.product")}
                        >
                          <div className="flex items-center gap-1">
                            <MdOutlineCategory />
                            <span className="text-xs">Products</span>
                          </div>
                        </Button>
                        <Button
                          theme={
                            button_custom
                          }
                          color="green"
                          size="xs"
                        // onClick={() =>
                        //   setIsOpenEditBillingAddress(
                        //     true
                        //   )
                        // }
                        >
                          <div className="flex items-center gap-1">
                            <MdOutlineEdit />
                            <span className="text-xs">Edit</span>
                          </div>
                        </Button>
                        <Button
                          theme={
                            button_custom
                          }
                          color="failure"
                          size="xs"
                        // onClick={() =>
                        //   setIsOpenDocuments(
                        //     true
                        //   )
                        // }
                        >
                          <div className="flex items-center gap-1">
                            <RiDeleteBin6Line />
                            <span className="text-xs">Delete</span>
                          </div>
                        </Button>
                      </div>
                    </TableCell>
                  </TableRow>

                </TableBody>
              </Table>
            </div>
          </div>
          <div className="mt-1 rounded-lg p-3  bg-white ">
            <div className="flex flex-wrap justify-between lg:gap-0 md:gap-0 gap-2">
              <div className="flex gap-3 items-center">
                <div className="text-gray-400 text-sm ">
                  Showing 1 to 25 of 62 entries
                </div>
                <div>
                  <select
                    id="countries"
                    className="text-xs bg-gray-50 border border-gray-300 text-gray-900 rounded focus:ring-blue-500 focus:border-blue-500 block p-1 ps-2 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-blue-500 dark:focus:border-blue-500"
                  >
                    <option
                      value={10}
                      defaultValue={10}
                    >
                      10
                    </option>
                    <option value={15}>15</option>
                    <option value={20}>20</option>
                    <option value={25}>25</option>
                    <option value={30}>30</option>
                  </select>
                </div>
              </div>
              <div className="flex">
                <Button
                  size="xs"
                  color="gray"
                  className="border-e-0 text-gray-400 px-2 rounded text-xs "
                >
                  Previous
                </Button>
                <Button
                  size="xs"
                  color="blue"
                  className="border text-white border-blue-600 bg-blue-600 px-2.5 rounded-none text-sm "
                >
                  1
                </Button>
                <Button
                  size="xs"
                  color="gray"
                  className="border text-blue-600 px-2.5 rounded-none text-xs "
                >
                  2
                </Button>
                <Button
                  size="xs"
                  color="gray"
                  className="border-s-0 text-blue-600 px-2 rounded text-xs "
                >
                  Next
                </Button>
              </div>
            </div>
          </div>
        </div>
      </div>
      
    </Main>
  );
}

