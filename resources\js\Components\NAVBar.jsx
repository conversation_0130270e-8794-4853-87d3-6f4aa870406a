import React, { memo, useState, useMemo, useCallback } from "react";
import { inHouseNavIcons } from "@/Global/Icons";
import { button_custom, custom_dropdown, navbar_custom } from "@/Pages/Helpers/DesignHelper";
import { showAsset } from "@/Pages/Helpers/Helper";
import { Link, usePage } from "@inertiajs/react";
import {
    Button,
    Drawer,
    DrawerItems,
    Dropdown,
    DropdownItem,
    Navbar,
    NavbarBrand
} from "flowbite-react";
import { HiMagnifyingGlass } from "react-icons/hi2";
import { IoClose } from "react-icons/io5";
import { RxHamburgerMenu } from "react-icons/rx";

// Memoized navigation item component
const NavItem = memo(({ item, index, isDesktop = true }) => {
    const isActive = route().current(item.routeForActive);
    const sizeClass = isDesktop ? "lg:h-9 lg:w-9 md:h-7 md:w-7" : "h-7 w-7";
    const iconClass = isDesktop ? "text-white lg:text-2xl text-lg" : "text-white text-xl";

    if (item.type !== 1) return null;

    return (
        <div className={`${isDesktop ? 'mt-2 pb-0.5' : 'me-1.5 mt-2 pb-0.5'} relative`} key={`nav-item-${index}`}>
            <Link
                href={item.url}
                type="button"
                className="relative inline-flex items-center text-sm font-medium text-center text-black rounded-lg"
            >
                <div className={`pb-0.5 rounded-full ${sizeClass} flex justify-center text-center items-center ${item.bg}`}>
                    {inHouseNavIcons(item.icon, iconClass)}
                </div>
            </Link>
            {isActive && isDesktop && (
                <div className="absolute w-0 h-0 transform -translate-x-1/2 border-b-8 border-l-8 border-r-8 border-transparent top-full left-1/2 border-b-white"></div>
            )}
            <div className="absolute inline-flex items-center justify-center mt-0.5 w-4 h-4 text-xs font-bold text-white bg-red-600 rounded-full -top-2 -end-2 dark:border-gray-900">
                5
            </div>
        </div>
    );
});

// Memoized static navigation items
const StaticNavItems = memo(({ isDesktop = true }) => {
    const iconClass = isDesktop ? "lg:text-2xl text-lg text-gray-400" : "text-xl text-gray-400";
    const containerClass = isDesktop ? "mt-3.5 pb-0.5 relative" : "mt-3.5 pb-0.5 relative";

    const staticItems = [
        { route: 'notifications.index', icon: 'FaRegBell', routePattern: 'notifications*' },
        { route: 'settings.index', icon: 'RiSettings3Line', routePattern: 'settings*' },
        { route: 'notifications.index', icon: 'TbClockBolt', routePattern: 'notifications*' }
    ];

    return (
        <>
            {staticItems.map((item, index) => {
                const isActive = route().current(item.routePattern);
                return (
                    <div className={containerClass} key={`static-nav-${index}`}>
                        <Link
                            href={route(item.route)}
                            type="button"
                            className="relative inline-flex items-center text-sm font-medium text-center text-black rounded-lg"
                        >
                            <div className="flex">
                                {inHouseNavIcons(item.icon, iconClass)}
                            </div>
                            <div className="absolute inline-flex items-center justify-center mt-0.5 w-4 h-4 text-xs font-bold text-white bg-red-600 rounded-full -top-2 -end-2 dark:border-gray-900">
                                {index === 0 ? 5 : 3}
                            </div>
                        </Link>
                        {isActive && isDesktop && (
                            <div className="absolute w-0 h-0 transform -translate-x-1/2 border-b-8 border-l-8 border-r-8 border-transparent top-full left-1/2 border-b-white"></div>
                        )}
                    </div>
                );
            })}
        </>
    );
});

// Memoized user dropdown
const UserDropdown = memo(({ auth }) => (
    <div className="pb-0.5 mt-1 relative">
        <div className="flex md:order-2">
            <Dropdown
                arrowIcon={false}
                inline={true}
                label={
                    <div className="w-10 p-2 bg-pink-700 rounded-full">
                        <span className="text-white capitalize">
                            {auth?.user?.username?.[0] || 'U'}
                        </span>
                    </div>
                }
            >
                <DropdownItem disabled className="text-nowrap">
                    {auth?.user?.username ?? '--'}
                </DropdownItem>
                <DropdownItem onClick={() => window.location.href = route("logout")} className="text-nowrap">
                    Sign out
                </DropdownItem>
            </Dropdown>
        </div>
    </div>
));

const NAVBar = memo(function NAVBar({ items, sidebar_items }) {
    const [isOpen, setIsOpen] = useState(false);
    const { auth, logo } = usePage().props;

    // Memoize handlers to prevent unnecessary re-renders
    const handleDrawerOpen = useCallback(() => setIsOpen(true), []);
    const handleDrawerClose = useCallback(() => setIsOpen(false), []);

    // Memoize filtered navigation items
    const navigationItems = useMemo(() =>
        items?.filter(item => item.type === 1) || [],
        [items]
    );

    // Memoize search form to prevent recreation
    const searchForm = useMemo(() => (
        <form className="w-auto">
            <label
                htmlFor="default-search"
                className="mb-2 text-sm font-medium text-gray-900 sr-only dark:text-white"
            >
                Search
            </label>
            <div className="relative">
                <div className="absolute inset-y-0 flex items-center pointer-events-none start-0 ps-3">
                    <HiMagnifyingGlass className="w-4 h-4 text-gray-500 dark:text-gray-400" />
                </div>
                <input
                    type="search"
                    className="block w-full py-1 text-sm text-white rounded ps-8 bg-slate-700 focus:ring-transparent focus:border-transparent dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-transparent dark:focus:border-transparent"
                    placeholder="Search"
                />
            </div>
        </form>
    ), []);

    return (
        <>
            <Navbar className="lg:h-14 md:h-14" theme={navbar_custom} rounded>
                <div className="flex items-center gap-3">
                    <NavbarBrand href={route("dashboard")}>
                        <div className="flex items-center w-12 lg:w-20 sm:w-12 lg:ps-0 sm:ps-3 lg:bg-slate-700 h-14">
                            <img
                                src={logo}
                                className="h-6 m-auto sm:h-7"
                                alt="Logo"
                                loading="eager"
                            />
                        </div>
                    </NavbarBrand>

                    <div className="flex items-center gap-2 lg:hidden">
                        <Button
                            size="xs"
                            color="slate"
                            theme={button_custom}
                            onClick={handleDrawerOpen}
                        >
                            <RxHamburgerMenu />
                        </Button>
                    </div>

                    <div>
                        {searchForm}
                    </div>
                </div>
                <div className="flex items-center justify-end gap-2 px-2 lg:gap-3 md:order-2">
                    <div className="items-center hidden lg:gap-4 md:gap-3 lg:flex">
                        {navigationItems.map((item, index) => (
                            <NavItem key={`main-${index}`} item={item} index={index} isDesktop={true} />
                        ))}

                        <hr className="w-px h-8 bg-gray-300" />
                        <StaticNavItems isDesktop={true} />
                    </div>

                    <UserDropdown auth={auth} />
                </div>
            </Navbar>

            {isOpen && (
                <Drawer
                    className="h-full p-0"
                    theme={custom_dropdown}
                    open={isOpen}
                    onClose={handleDrawerClose}
                >
                    <div className="flex items-center justify-between p-4">
                        <img
                            src={showAsset("/assets/img/clatoslogo.png", '')}
                            alt="Logo"
                            loading="lazy"
                        />
                        <Button size="" color="gray" onClick={handleDrawerClose}>
                            <IoClose className="size-5" />
                        </Button>
                    </div>

                    <DrawerItems className="h-full">
                        <div className="h-full">
                            <div className="flex gap-2 items-center border-b pb-2 border-slate-400 px-3.5 overflow-auto">
                                {navigationItems.map((item, index) => (
                                    <NavItem key={`mobile-${index}`} item={item} index={index} isDesktop={false} />
                                ))}

                                <hr className="w-px h-8 bg-gray-300" />
                                <StaticNavItems isDesktop={false} />
                            </div>
                            <div className="flex flex-col justify-between h-full">
                                <div className="flex flex-col gap-3 px-3 pt-3 pb-4">
                                    {sidebar_items?.map((sItem, index) => (
                                        <Link
                                            key={`sideMenuItems${index}`}
                                            href={sItem.url}
                                            className="flex items-center gap-2 text-white"
                                        >
                                            <div className={`rounded-lg ${route().current(sItem.routeForActive) ? "bg-blue-600 text-white" : "text-gray-500"}`}>
                                                {inHouseNavIcons(sItem.icon, "")}
                                            </div>
                                            <span className="text-base">{sItem.name}</span>
                                        </Link>
                                    ))}
                                </div>

                                <div className="flex p-2 bg-slate-900">
                                    <div className="flex items-center gap-3 p-2 rounded shadow-sm">
                                        <button
                                            type="button"
                                            className="relative inline-flex items-center text-sm font-medium text-center text-black rounded-lg"
                                        >
                                            <div className="bg-green-700 pb-0.5 rounded-full flex justify-center text-center items-center">
                                                <img
                                                    src={showAsset("/assets/img/dunesdev.png", '')}
                                                    alt="Workspace"
                                                    loading="lazy"
                                                />
                                            </div>
                                            <div className="absolute inline-flex items-center justify-center mt-0.5 w-4 h-4 text-xs font-bold text-white bg-red-600 rounded-full -top-2 -end-2 dark:border-gray-900">
                                                5
                                            </div>
                                        </button>
                                        <div className="flex flex-col text-left">
                                            <span className="text-lg font-medium text-white">
                                                Dunes Development
                                            </span>
                                            <span className="text-sm text-gray-300">
                                                dunes_development.crm.com
                                            </span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </DrawerItems>
                </Drawer>
            )}
        </>
    );
});

export default NAVBar;
