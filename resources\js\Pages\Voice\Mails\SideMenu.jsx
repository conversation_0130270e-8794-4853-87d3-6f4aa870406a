import { button_custom } from "@/Pages/Helpers/DesignHelper";
import { Link } from "@inertiajs/react";
import { <PERSON><PERSON>, <PERSON><PERSON>, Dropdown, DropdownItem } from "flowbite-react";
import { BiChevronLeft, BiMessageDetail } from "react-icons/bi";
import { LuMailOpen } from "react-icons/lu";
import { MdAutorenew, MdBlockFlipped, MdEdit, MdOutlineCall, MdOutlineFolderDelete, MdOutlineMarkEmailRead, MdOutlineMoveToInbox } from "react-icons/md";
import { PiPlugsConnected } from "react-icons/pi";
import { RiSendPlane2Line } from "react-icons/ri";

function SideMenu() {
    const menuDropdown = {
        arrowIcon: "ml-2 h-4 w-4",
        content: "py-1 focus:outline-none ",
        floating: {
            animation: "transition-opacity",
            arrow: {
                base: "absolute z-10 h-2 w-2 rotate-45",
                style: {
                    dark: "bg-gray-900 dark:bg-gray-700",
                    light: "bg-white",
                    auto: "bg-white dark:bg-gray-700",
                },
                placement: "-4px",
            },
            base: "z-10  divide-y divide-gray-100 rounded shadow focus:outline-none w-full",
            content: "py-1 text-sm text-gray-700 dark:text-gray-200",
            divider: "my-1 h-px bg-gray-100 dark:bg-gray-600",
            header: "block px-4 py-2 text-sm text-gray-700 dark:text-gray-200",
            hidden: "invisible opacity-0",
            item: {
                container: "w-full",
                base: "flex w-full cursor-pointer items-center justify-start px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 hover:bg-gray-100 focus:outline-none dark:text-gray-200 dark:hover:bg-gray-600 dark:hover:text-white dark:focus:bg-gray-600 dark:focus:text-white",
                icon: "mr-2 h-4 w-4",
            },
            style: {
                dark: "bg-gray-900 text-white dark:bg-gray-700",
                light: "border border-gray-200 bg-white text-gray-900",
                auto: "border border-gray-200 bg-white text-gray-900 dark:border-none dark:bg-gray-700 dark:text-white",
            },
            target: "w-full ",
        },
        inlineWrapper: "flex items-center",
    };
    const items = [
        {
            name: "All",
            url: route("mail.mails.inbox"),
            active: route().current("mail.mails.inbox"),
            icon: <LuMailOpen className="text-blue-500" />,
            classes: {
                groupHover: " group-hover:text-blue-500 ",
                group: " hover:bg-blue-50 ",
                active: " bg-blue-50 ",
            },
        },
        {
            name: "Inbox",
            url: route("mail.mails.inbox"),
            active: route().current("mail.mails.inbox"),
            icon: <MdOutlineMarkEmailRead className="text-green-500" />,
            classes: {
                groupHover: "group-hover:text-green-500 ",
                group: " hover:bg-green-50 ",
                active: "bg-green-50 ",
            },
        },
        {
            name: "Sent",
            url: route("mail.mails.sent"),
            active: route().current("mail.mails.sent"),
            icon: <RiSendPlane2Line className="text-cyan-500" />,
            classes: {
                groupHover: " group-hover:text-cyan-500 ",
                group: " hover:bg-cyan-50 ",
                active: " bg-cyan-50 ",
            },
        },
        {
            name: "Spam",
            url: route("mail.mails.archive"),
            active: route().current("mail.mails.archive"),
            icon: <MdBlockFlipped className="text-red-500" />,
            classes: {
                groupHover: " group-hover:text-red-500 ",
                group: " hover:bg-red-50 ",
                active: " bg-red-50 ",
            },
        },
        {
            name: "Archive",
            url: route("mail.mails.archive"),
            active: route().current("mail.mails.archive"),
            icon: <MdOutlineMoveToInbox className="text-fuchsia-500" />,
            classes: {
                groupHover: " group-hover:text-fuchsia-500 ",
                group: " hover:bg-fuchsia-50 ",
                active: " bg-fuchsia-50 ",
            },
        },
    ];
    return (
        <div className="w-full bg-white rounded ">
            <div className="p-2">
                <Button theme={button_custom} size="xxs" color="Fuchsia_custom">
                    <div className="flex items-center">
                        <BiChevronLeft className="text-xl" />
                        <span>Back</span>
                    </div>
                </Button>
            </div>
            <div className="">
                <Button className="w-full" theme={button_custom} size="xs"
                    as={Link}
                    href={route("mail.mails.compose")} 
                >
                    <div className="flex items-center gap-1 text-base">
                        <MdEdit />
                        <span>Compose</span>
                    </div>
                </Button>
            </div>
            <div className="">
                <select
                    className="w-full rounded-md text-sm border-gray-200 mt-2 p-2 px-3 hover:bg-gray-50"
                    name="cars"
                    id="cars"
                >
                    <option value="volvo">Filter Gateway</option>
                    <option value="saab">Saab</option>
                    <option value="opel">Opel</option>
                    <option value="audi">
                        dfdfdfsfsfdfs
                    </option>
                </select>
            </div>
            <div className="lg:hidden md:hidden">
                <Dropdown
                    label="Menu"
                    size="sm"
                    className="w-full"
                    theme={menuDropdown}
                    color="light"
                >
                    {items.map((item, k) => {
                        return (
                            <DropdownItem
                                key={"navItems-" + k}
                                as={Link}
                                href={item.url}
                            >
                                {item.name}
                            </DropdownItem>
                        );
                    })}
                </Dropdown>
            </div>
            <div className="hidden w-full lg:block md:block">
                {items.map((item, k) => {
                    return (
                        <Link href={item.url}>
                            <div
                                className={
                                    "border-b hover:border-transparent p-1.5 px-2 flex items-center justify-between gap-3 bg-white rounded rounded-b-none hover:rounded-b  group" +
                                    item.classes.group +
                                    (item.active ? item.classes.active : "")
                                }
                            >
                                <div className="flex items-center gap-3">
                                    {item.icon}
                                    <span className={item.classes.groupHover}>
                                        {item.name}
                                    </span>
                                </div>
                                <Badge>5</Badge>
                            </div>
                        </Link>
                    );
                })}
            </div>
        </div>
    );
}

export default SideMenu;
