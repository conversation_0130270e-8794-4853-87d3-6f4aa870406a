import NoRecord from "@/Components/HelperComponents/NoRecord";
import useFetch from "@/Global/useFetch";
import { button_custom, table_custom } from "@/Pages/Helpers/DesignHelper";
import { useForm } from "@inertiajs/react";
import {
    Button,
    Checkbox,
    Table,
    Tooltip
} from "flowbite-react";
import $ from "jquery";
import { useEffect, useState } from "react";
import { CgSpinnerAlt } from "react-icons/cg";
import {
    MdOutlineVpnKey
} from "react-icons/md";
import { RiCheckDoubleFill } from "react-icons/ri";


export default function Permissions({ role }) {
    const { data, setData, post, processing, errors, reset } = useForm({
        permissions: [],
    });


    const [oldPermissions, setOldPermissions] = useState([]);

    const [selectedItems, setSelectedItems] = useState([]); // State to store selected items
    useEffect(() => {
        setData('permissions', selectedItems);
    }, [selectedItems]);

    const { data: roleData, loading: roleLoading, error: roleError } = useFetch(route('admin.roles.show', { id: role.id }));

    useEffect(() => {
        setOldPermissions(roleData?.data?.permissions);
        setSelectedItems(roleData?.data?.permissions);
    }, [roleData]);

    const handleSubmit = (e) => {
        e.preventDefault();
        post(route('admin.roles.addpermission', { id: role.id }), {
            onSuccess: () => {
                reset();
            }
        });
    };

    const handleCheckAll = (e, child) => {
        const { checked } = e.target;
        const childCheckboxes = $(`.${child}`);
        childCheckboxes.prop("checked", checked);

        if (checked) {
            // Add all child checkbox values to selectedItems
            const allValues = childCheckboxes.map((_, checkbox) => checkbox.value).get();
            setSelectedItems((prevSelectedItems) => [...new Set([...prevSelectedItems, ...allValues])]);
        } else {
            // Remove all child checkbox values from selectedItems
            const allValues = childCheckboxes.map((_, checkbox) => checkbox.value).get();
            setSelectedItems((prevSelectedItems) => prevSelectedItems.filter((item) => !allValues.includes(item)));
        }
    }

    const handleCheckboxChange = (event) => {
        const { value, checked } = event.target;
        if (checked) {
            // Add the selected value to the array
            setSelectedItems([...selectedItems, value]);
        } else {
            // Remove the unselected value from the array
            setSelectedItems(selectedItems.filter((item) => item != value));
        }
    };

    const { data: collection, loading: permissionsLoading, error } = useFetch(route('admin.permission.index'));


    // const button_custom = {
    //     color: {
    //         gray: ":ring-cyan-700 border border-gray-200 bg-white text-gray-900 f enabled:hover:bg-gray-100 dark:border-gray-600 dark:bg-transparent dark:text-gray-400 dark:enabled:hover:bg-gray-700 dark:enabled:hover:text-white",
    //     },
    //     pill: {
    //         off: "rounded",
    //         on: "rounded-full",
    //     },
    //     size: {
    //         xs: "p-0.5 text-[18px]",
    //         sm: "px-3 py-1.5 text-sm",
    //         md: "px-4 py-2 text-sm",
    //         lg: "px-5 py-2.5 text-base",
    //         xl: "px-6 py-3 text-base",
    //     },
    // };
    return (
        <form onSubmit={handleSubmit}>
            <div className="">
                <div className="flex justify-between gap-2 pb-3">
                    <div className="">

                        <MdOutlineVpnKey className="text-[23px] text-slate-400" />
                        <h3 className="text-[20px] text-slate-800 font-semibold">
                            Set Permissions ({role.name})
                        </h3>
                    </div>
                    <div className="flex justify-end gap-5 py-3 pe-4">

                        {role.id != 1 &&
                            <Button theme={button_custom} disabled={permissionsLoading || processing} color="blue" type="submit" className="rounded" size="xs" isProcessing={processing}>
                                <div className="flex items-center gap-0.5">
                                    <RiCheckDoubleFill className="text-xl" />
                                    <span className="text-sm">Save</span>
                                </div>
                            </Button>
                        }
                    </div>

                </div>
                <div className="overflow-x-auto   h-[80vh] bg-white border rounded-lg">
                    <Table className="" hoverable theme={table_custom}>
                        <TableHead className="">
                            <TableHeadCell>Permissions</TableHeadCell>
                            <TableHeadCell>All</TableHeadCell>
                            <TableHeadCell>List/View</TableHeadCell>
                            <TableHeadCell>Edit</TableHeadCell>
                            <TableHeadCell>Add</TableHeadCell>
                            <TableHeadCell>Delete</TableHeadCell>

                        </TableHead>
                        <TableBody className="divide-y">
                            {!permissionsLoading ?
                                collection.hasOwnProperty('permissions') &&
                                Object.keys(collection.permissions).map((key, kid) => {
                                    return (
                                        <TableRow key={'tr-' + kid} className="">
                                            <TableCell>
                                                {key
                                                    .replace('wa_', 'whatsapp Unofficial ')
                                                    .replace('waba_', 'whatsapp Official ')
                                                    .replaceAll('l_', ' ')
                                                    .replaceAll('_', ' ')
                                                }
                                            </TableCell>
                                            <TableCell>
                                                <Checkbox
                                                    color="blue"
                                                    id={"all-" + key}
                                                    onChange={(e) => handleCheckAll(e, `clicked-all-${key}`)}

                                                />
                                            </TableCell>
                                            {collection.permissions[key].sort((a, b) => a.name > b.name).map(pn => {
                                                return (
                                                    <TableCell key={pn.id}>
                                                        <Tooltip content={pn.name.split('.')[1]} theme={{ "base": "absolute z-10 inline-block rounded-lg px-1.5 py-1 text-xs font-medium shadow-sm", }}>

                                                            <Checkbox
                                                                color="blue"
                                                                defaultChecked={oldPermissions?.includes(pn.id)}
                                                                // defaultChecked={oldPermissions.find((perm) => perm == pn.id)}
                                                                className={`clicked-all-${key}`}
                                                                value={pn.id}
                                                                onChange={handleCheckboxChange}
                                                            />
                                                        </Tooltip>
                                                        {/* {pn.name} */}
                                                    </TableCell>
                                                )
                                            })}
                                        </TableRow>
                                    )
                                })
                                :
                                <TableRow className="">
                                    <TableCell colSpan={6}>
                                        {permissionsLoading ?
                                            <div className="m-auto text-center">
                                                <CgSpinnerAlt className={`animate-spin text-3xl text-blue-400 m-auto`} />

                                            </div>
                                            :
                                            <NoRecord />
                                        }
                                    </TableCell>
                                </TableRow>
                            }
                        </TableBody>
                    </Table>
                </div>
                <div className="flex justify-end gap-5 py-3 pe-4">

                    {role.id != 1 &&
                        <Button theme={button_custom} disabled={permissionsLoading || processing} color="blue" type="submit" className="rounded" size="xs" isProcessing={processing}>
                            <div className="flex items-center gap-0.5">
                                <RiCheckDoubleFill className="text-xl" />
                                <span className="text-sm">Save</span>
                            </div>
                        </Button>
                    }
                </div>

            </div >
        </form>
    );
}

