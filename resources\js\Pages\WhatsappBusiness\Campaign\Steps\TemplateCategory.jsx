import { button_custom } from "@/Pages/Helpers/DesignHelper";
import Badge from "@mui/material/Badge";
import { styled } from "@mui/material/styles";
import { Button, Modal } from "flowbite-react";
import { useEffect, useState } from "react";
import { MdOutlineAdd } from "react-icons/md";
// import AddCategory from "../Templates/AddCategory";
// import EditCategory from "../Templates/EditCategory";

export default function TemplateCategory({ setTemplates }) {
    function fetchCategoryData(id = 0) {
        return fetch(route("whatsapp.templates.show", { template: id }), {
            headers: {
                Accept: "application/json",
            },
        })
            .then((res) => res.json())
            .then((data) => {
                setTemplates(data.templates.data);
                setCategoryData(data.categories);
            })
            .catch((error) => {
                console.error("Error fetching badge data:", error);
            });
    }

    useEffect(() => {
        fetchCategoryData();
    }, []);

    const StyledBadge = styled(Badge)(({ theme }) => ({
        "& .MuiBadge-badge": {
            right: -8,
            top: 4,
            border: `2px solid ${theme.palette.background.paper}`,
            padding: "px 4px px 4px",
            // backgroundColor: "#1976d2",
            // color: "#1a1a1aa8"
        },
    }));
    const [openModal, setOpenModal] = useState(false);
    const [openEditModal, setOpenEditModal] = useState(false);

    const [categoryData, setCategoryData] = useState(null);
    const [activeCategory, setActiveCategory] = useState(0);

    return (
        <div className="w-full h-full bg-white">
            <div className="flex items-center justify-between px-3 py-2">
                <span>Categories</span>
                {/* <Button
                    theme={button_custom}
                    color="gray"
                    size="xs"
                    onClick={() => setOpenModal(true)}
                >
                    <div className="flex items-center ">
                        <MdOutlineAdd className="text-slate-500" />
                        <span className="text-xs">Add</span>
                    </div>
                </Button> */}
            </div>
            <div className="flex bg-white lg:flex-col flex-row text-nowrap overflow-y-auto max-h-[90vh]">
                <div
                    className={`flex items-center justify-between p-3 border-b border-t text-sm cursor-pointer  ${0 == activeCategory ? "bg-blue-100" : ""
                        }`}
                    onClick={() => {
                        setActiveCategory(0);
                        fetchCategoryData(0);
                    }}
                >
                    All&nbsp;
                </div>
                {categoryData &&
                    categoryData.map((category, key) => {
                        return (
                            <div
                                className={`flex border-e items-center justify-between p-3 border-b cursor-pointer text-sm ${category.id == activeCategory
                                    ? "bg-blue-100"
                                    : ""
                                    }`}
                                key={key}
                                onClick={() => {
                                    setActiveCategory(category.id);
                                    fetchCategoryData(category.id);
                                }}
                            >
                                <div className={`w-full`}>
                                    {/* <StyledBadge badgeContent={category.template_count} color="primary">
                                </StyledBadge> */}
                                    <div className="flex">
                                        {category.name}&nbsp;
                                        <div className="text-sm text-slate-400">
                                            ({category.template_count})
                                        </div>
                                    </div>
                                </div>
                            </div>
                        );
                    })}
                {/* {openModal && (
                    <Modal
                        show={openModal}
                        size="md"
                        onClose={() => setOpenModal(false)}
                        popup
                    >
                        <ModalHeader className="p-2">
                            <h4 className="text-lg">Add Category</h4>
                        </ModalHeader>
                        <ModalBody className="px-4 py-3 rounded-b-lg bg-slate-100">
                            <AddCategory
                                onClose={() => setOpenModal(false)}
                            ></AddCategory>
                        </ModalBody>
                    </Modal>
                )}
                {openEditModal && (
                    <Modal
                        show={openEditModal}
                        size="md"
                        onClose={() => setOpenEditModal(false)}
                        popup
                    >
                        <ModalHeader className="p-2">
                            <h4 className="text-lg">Edit Category</h4>
                        </ModalHeader>
                        <ModalBody className="px-4 py-3 rounded-b-lg bg-slate-100">
                            <EditCategory
                                onClose={() => setOpenEditModal(false)}
                                editId={editModalId}
                            ></EditCategory>
                        </ModalBody>
                    </Modal>
                )} */}
            </div>
        </div>
    );
}

