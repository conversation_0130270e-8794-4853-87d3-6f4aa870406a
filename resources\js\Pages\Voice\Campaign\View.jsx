import { Button, Table, TableBody, TableCell, TableHead, TableHeadCell, TableRow, Tabs } from "flowbite-react";
import { useCallback, useEffect, useState } from "react";
import { FaWhatsapp } from "react-icons/fa";
import { LuScrollText } from "react-icons/lu";
import NoRecord from "@/Components/HelperComponents/NoRecord";
import { button_custom, custom_tabs, table_custom } from "@/Pages/Helpers/DesignHelper";
import { deleteSingle } from "@/Pages/Helpers/Helper";
import { IoMdAdd, IoMdClose } from "react-icons/io";
import { MdOutlineDashboard } from "react-icons/md";
import AddChannel from "./AddChannel";
import AddContactList from "./AddContactList";

function View({ campaign }) {
    const [showConfirmBox, setShowConfirmBox] = useState("");
    const [campaignData, setCampaignData] = useState(null);
    const [isLoading, setIsLoading] = useState(true);

    useEffect(() => {
        campaignShowData();
    }, []);
    function campaignShowData() {
        fetch(route("whatsapp.campaign.show", { id: campaign }))
            .then((res) => {
                return res.json();
            })
            .then((data) => {
                setCampaignData(data.data);
                setIsLoading(false);
            });
    }
    const handleChange = useCallback(() => {
        setIsAddChannelActive(false);
        setIsAddContactListActive(false);
        campaignShowData();
    }, []);

    const removeObjectAndDeleteRecord = (table, id, objKey) => {
        deleteSingle(table, id);
        let elementToRemove = document.querySelector("#" + objKey);
        elementToRemove.classList.add("hidden");
    };
    const [isAddChannelActive, setIsAddChannelActive] = useState(false);
    const [isAddContactListActive, setIsAddContactListActive] = useState(false);
    return (
        <div>
            <div className="mt-2 mb-3 ">
                <h2 className="text-lg font-medium dark:text-white ">
                    {campaignData && campaignData.hasOwnProperty("campaign") ? (
                        campaignData.campaign.name
                    ) : (
                        <></>
                    )}
                </h2>
            </div>
            <Tabs aria-label="Tabs" theme={custom_tabs}>
                <Tabs.Item icon={MdOutlineDashboard} title="Dashboard">
                    <div className="">
                        <div className="flex flex-col items-center justify-center p-5 text-white bg-gradient-to-bl from-teal-400 to-blue-500">
                            <h1 className="text-5xl">
                                We are <b>Almost</b> there!
                            </h1>
                            <p>Stay tuned for something amazing!!!</p>
                            {campaignData &&
                                campaignData.hasOwnProperty("campaign") ? (
                                <div className="grid grid-cols-1 gap-10 mt-10 sm:grid-cols-2 lg:grid-cols-3 lg:mt-20 ">
                                    <div className="text-center bg-transparent border">
                                        <p className="px-10 py-5 text-5xl">
                                            {
                                                campaignData.campaign
                                                    .completedContacts
                                            }
                                        </p>
                                        <hr />
                                        <p className="px-10 py-5">Sent</p>
                                    </div>

                                    <div className="text-center bg-transparent border">
                                        <p className="px-10 py-5 text-5xl">
                                            {campaignData.campaign
                                                .totalContacts -
                                                campaignData.campaign
                                                    .completedContacts}
                                        </p>
                                        <hr />
                                        <p className="px-10 py-5">Pending</p>
                                    </div>
                                    <div className="text-center bg-transparent border">
                                        <p className="px-10 py-5 text-5xl">
                                            35
                                        </p>
                                        <hr />
                                        <p className="px-10 py-5">Total</p>
                                    </div>
                                    <div className="text-center bg-transparent border">
                                        <p className="px-10 py-5 text-5xl">
                                            200
                                        </p>
                                        <hr />
                                        <p className="px-10 py-5">
                                            Non Whatsapp
                                        </p>
                                    </div>
                                    <div className="text-center bg-transparent border">
                                        <p className="px-10 py-5 text-5xl">
                                            200
                                        </p>
                                        <hr />
                                        <p className="px-10 py-5">Failed</p>
                                    </div>
                                </div>
                            ) : (
                                ""
                            )}
                        </div>
                    </div>
                </Tabs.Item>

                <Tabs.Item active icon={FaWhatsapp} title="Channels">
                    <>
                        <div className="overflow-auto bg-white border rounded-lg">
                            <Table theme={table_custom}>
                                <TableHead>
                                    <TableHeadCell>Name</TableHeadCell>
                                    <TableHeadCell>Status</TableHeadCell>
                                    <TableHeadCell className="min-w-52">
                                        Action
                                    </TableHeadCell>
                                </TableHead>
                                <TableBody className="divide-y">
                                    {campaignData &&
                                        campaignData.hasOwnProperty("campaign") ? (
                                        campaignData.campaign.channels.map(
                                            (channel, k) => {
                                                return (
                                                    <TableRow
                                                        key={
                                                            "campaign-channel" +
                                                            k
                                                        }
                                                        id={
                                                            "campaign-channel" +
                                                            k
                                                        }
                                                    >
                                                        <TableCell className="font-medium text-gray-900 whitespace-nowrap dark:text-white">
                                                            {
                                                                channel.channel
                                                                    .name
                                                            }
                                                        </TableCell>
                                                        <TableCell>
                                                            {
                                                                channel.channel
                                                                    .status
                                                            }
                                                        </TableCell>
                                                        <TableCell className="text-center">
                                                            <div className="flex">
                                                                {showConfirmBox ===
                                                                    "campaign-channel-confirmBox" +
                                                                    k ? (
                                                                    <div className="flex gap-3">
                                                                        <Button

                                                                            color={
                                                                                "failure"
                                                                            }
                                                                            size={
                                                                                "xs"
                                                                            }
                                                                            onClick={() =>
                                                                                removeObjectAndDeleteRecord(
                                                                                    "wa_campaign_gateway",
                                                                                    channel.id,
                                                                                    "campaign-channel" +
                                                                                    k
                                                                                )
                                                                            }
                                                                        >
                                                                            Confirm
                                                                        </Button>
                                                                        <Button

                                                                            color={
                                                                                "blue"
                                                                            }
                                                                            size={
                                                                                "xs"
                                                                            }
                                                                            onClick={() =>
                                                                                setShowConfirmBox(
                                                                                    ""
                                                                                )
                                                                            }
                                                                        >
                                                                            Cancel
                                                                        </Button>
                                                                    </div>
                                                                ) : (
                                                                    <Button

                                                                        color={
                                                                            "failure"
                                                                        }
                                                                        outline
                                                                        size={
                                                                            "xs"
                                                                        }
                                                                        onClick={() =>
                                                                            setShowConfirmBox(
                                                                                "campaign-channel-confirmBox" +
                                                                                k
                                                                            )
                                                                        }
                                                                    >
                                                                        Remove
                                                                    </Button>
                                                                )}
                                                            </div>
                                                        </TableCell>
                                                    </TableRow>
                                                );
                                            }
                                        )
                                    ) : (
                                        <TableRow>
                                            <TableCell colSpan={3}>
                                                <NoRecord loading={isLoading} />
                                            </TableCell>
                                        </TableRow>
                                    )}
                                </TableBody>
                            </Table>
                        </div>

                        {campaignData &&
                            campaignData.campaign.status != 3 &&
                            campaignData.campaign.status != 4 &&
                            campaignData.hasOwnProperty("campaign") ? (
                            <>
                                <div className="flex justify-end p-2 mt-3 ">
                                    <Button
                                        theme={button_custom}
                                        size={"sm"}
                                        color={"blue"}
                                        onClick={() =>
                                            setIsAddChannelActive(
                                                !isAddChannelActive
                                            )
                                        }
                                    >
                                        <div className="flex items-center gap-1">
                                            {isAddChannelActive ? (
                                                <IoMdClose className="text-lg text-white" />
                                            ) : (
                                                <IoMdAdd className="text-lg text-white" />
                                            )}

                                            {isAddChannelActive
                                                ? "Cancel"
                                                : "Add Channel"}
                                        </div>
                                    </Button>
                                </div>
                                {isAddChannelActive ? (
                                    <div className="p-3 mt-3 bg-white border rounded-lg">
                                        <div className="">
                                            <AddChannel
                                                campaignID={
                                                    campaignData.campaign.id
                                                }
                                                handleChange={handleChange}
                                            />
                                        </div>
                                    </div>
                                ) : (
                                    <></>
                                )}
                            </>
                        ) : (
                            <></>
                        )}
                    </>
                </Tabs.Item>
                <Tabs.Item icon={LuScrollText} title="Contact List">
                    <>
                        <div className="overflow-auto bg-white border rounded-lg">
                            <Table theme={table_custom}>
                                <TableHead>
                                    <TableHeadCell className="max-w-52">
                                        Name
                                    </TableHeadCell>
                                    <TableHeadCell>
                                        Total Contacts
                                    </TableHeadCell>
                                    <TableHeadCell className="min-w-52">
                                        Action
                                    </TableHeadCell>
                                </TableHead>
                                <TableBody className="divide-y ">
                                    {campaignData &&
                                        campaignData.hasOwnProperty("campaign") ? (
                                        campaignData.campaign.contact_lists.map(
                                            (cList, k) => {
                                                return (
                                                    <TableRow
                                                        key={
                                                            "campaign-ContactList" +
                                                            k
                                                        }
                                                        id={
                                                            "campaign-ContactList" +
                                                            k
                                                        }
                                                        className="text-wrap"
                                                    >
                                                        <TableCell className="font-medium text-gray-900 whitespace-nowrap dark:text-white text-ellipsis">
                                                            {
                                                                cList
                                                                    .contact_list
                                                                    .name
                                                            }
                                                        </TableCell>
                                                        <TableCell>
                                                            {
                                                                cList
                                                                    .contact_list
                                                                    .totalContacts
                                                            }
                                                        </TableCell>
                                                        <TableCell className="">
                                                            <div className="flex">
                                                                {showConfirmBox ===
                                                                    "campaign-ContactList-confirmBox" +
                                                                    k ? (
                                                                    <div className="flex gap-3">
                                                                        <Button

                                                                            color={
                                                                                "failure"
                                                                            }
                                                                            size={
                                                                                "xs"
                                                                            }
                                                                            onClick={() =>
                                                                                removeObjectAndDeleteRecord(
                                                                                    "wa_campaign_contact_list",
                                                                                    cList.id,
                                                                                    "campaign-ContactList" +
                                                                                    k
                                                                                )
                                                                            }
                                                                        >
                                                                            Confirm
                                                                        </Button>
                                                                        <Button

                                                                            color={
                                                                                "blue"
                                                                            }
                                                                            size={
                                                                                "xs"
                                                                            }
                                                                            onClick={() =>
                                                                                setShowConfirmBox(
                                                                                    ""
                                                                                )
                                                                            }
                                                                        >
                                                                            Cancel
                                                                        </Button>
                                                                    </div>
                                                                ) : (
                                                                    <Button
                                                                        // theme={button_custom}
                                                                        color={
                                                                            "failure"
                                                                        }
                                                                        outline
                                                                        size={
                                                                            "xs"
                                                                        }
                                                                        onClick={() =>
                                                                            setShowConfirmBox(
                                                                                "campaign-ContactList-confirmBox" +
                                                                                k
                                                                            )
                                                                        }
                                                                    >
                                                                        Remove
                                                                    </Button>
                                                                )}
                                                            </div>
                                                        </TableCell>
                                                    </TableRow>
                                                );
                                            }
                                        )
                                    ) : (
                                        <TableRow>
                                            <TableCell colSpan={3}>
                                                <NoRecord loading={isLoading} />
                                            </TableCell>
                                        </TableRow>
                                    )}
                                </TableBody>
                            </Table>
                        </div>
                        {campaignData &&
                            (campaignData.status !== 3 ||
                                campaignData.status !== 4) &&
                            campaignData.hasOwnProperty("campaign") ? (
                            <>
                                <div className="flex justify-end p-2 mt-3">
                                    <Button
                                        theme={button_custom}
                                        size={"sm"}
                                        color={"blue"}
                                        onClick={() =>
                                            setIsAddContactListActive(
                                                !isAddContactListActive
                                            )
                                        }
                                    >
                                        <div className="flex items-center gap-1">
                                            {isAddContactListActive ? (
                                                <IoMdClose className="text-lg text-white" />
                                            ) : (
                                                <IoMdAdd className="text-lg text-white" />
                                            )}
                                            {isAddContactListActive
                                                ? "Cancel"
                                                : "Add ContactList"}
                                        </div>
                                    </Button>
                                </div>
                                {isAddContactListActive ? (
                                    <div className="p-3 mt-3 bg-white border rounded-lg">
                                        <div className="">
                                            <AddContactList
                                                campaignID={
                                                    campaignData.campaign.id
                                                }
                                                handleChange={handleChange}
                                            />
                                        </div>
                                    </div>
                                ) : (
                                    <></>
                                )}
                            </>
                        ) : (
                            <></>
                        )}
                    </>
                </Tabs.Item>
            </Tabs>
        </div>
    );
}
export default View;

