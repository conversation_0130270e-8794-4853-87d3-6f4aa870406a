import { button_custom, table_custom } from "@/Pages/Helpers/DesignHelper";
import { showAsset } from "@/Pages/Helpers/Helper";
import { Button, Table, Tooltip } from "flowbite-react";
import React from "react";
import { BiDownload } from "react-icons/bi";
import { CgDetailsMore } from "react-icons/cg";
import { FiDownload } from "react-icons/fi";
import { MdOutlineImage, MdOutlineInventory } from "react-icons/md";

export default function View() {
    return (
        <div className="w-full p-2 ">
            <div className="bg-white p-2 rounded-md border">
                <div className="flex items-center gap-2">
                    <CgDetailsMore className="text-xl text-slate-400" />
                    <span className="text-base font-medium">Details</span>
                </div>
                <div className="flex gap-7 px-2 text-base  text-gray-600 w-full">
                    <div className="flex w-full gap-16 flex-wrap">
                        <div className="flex items-center gap-6">
                            <div>
                                <div className="flex gap-3 ">
                                    <div className="font-medium text-sm">
                                        Transaction Id:
                                    </div>
                                    <div className="text-sm">TND5200001232</div>
                                </div>
                                <div className="flex gap-3 items-center">
                                    <div className="font-medium text-sm">
                                        Payment Screenshot:
                                    </div>
                                    <MdOutlineImage className="text-xl text-slate-400" />
                                </div>
                            </div>
                        </div>
                        <div>
                            <div className="flex gap-3   ">
                                <div className="font-medium text-sm">
                                    Payment Mode:
                                </div>
                                <img className="size-5" src={showAsset("/assets/img/phonepe.png", '')} alt="" />
                            </div>
                            <div className="flex gap-3 ">
                                <div className="font-medium text-sm">
                                    Remark:
                                </div>
                                <div className="text-sm">
                                    Payment received for annual subscription.
                                </div>
                            </div>

                        </div>
                        <div>
                            <div className="flex gap-3 ">
                                <div className="font-medium text-sm">
                                    Payment Purpose:
                                </div>
                                <div className="text-sm">Membership Renewal</div>
                            </div>

                        </div>

                    </div>
                </div>
            </div>
            <div className="flex items-center gap-2 p-2">
                <MdOutlineInventory className="text-2xl text-slate-400" />
                <span className="font-medium text-xl">Invoice</span>
            </div>
            <div className="w-full  bg-white rounded shadow-current">
                <div className="flex pt-2 px-2 items-center gap-2">
                    <CgDetailsMore className="text-xl text-slate-400" />
                    <span className="text-base font-medium">Details</span>
                </div>

                <div className="flex gap-7 px-2 text-base py-1 text-gray-600 w-full">
                    <div className="flex w-full gap-16 flex-wrap">
                        <div className="flex items-center gap-6">
                            <div>
                                <div className="flex gap-3 ">
                                    <div className="font-medium text-sm">
                                        Taxable:
                                    </div>
                                    <div className="text-sm">Yes</div>
                                </div>
                                <div className="flex gap-3 ">
                                    <div className="font-medium text-sm">
                                        Including GST:
                                    </div>
                                    <div className="text-sm">
                                        Yes
                                    </div>
                                </div>
                                <div className="flex gap-3 ">
                                    <div className="font-medium text-sm">
                                        Billing Address:
                                    </div>
                                    <div className="text-sm">Rani Bazar, Bikaner, India</div>
                                </div>
                                <div className="flex gap-3 ">
                                    <div className="font-medium text-sm">
                                        Billing Address To:
                                    </div>
                                    <div className="text-sm">India</div>
                                </div>
                                <div className="flex gap-3 ">
                                    <div className="font-medium text-sm">
                                        Currency:
                                    </div>
                                    <div className="text-sm">Indian Rupees</div>
                                </div>
                            </div>
                        </div>
                        <div>
                            <div className="flex gap-3 ">
                                <div className="font-medium text-sm">
                                    Business:
                                </div>
                                <div className="text-sm">Gold Store</div>
                            </div>
                            <div className="flex gap-3 ">
                                <div className="font-medium text-sm">
                                    Mobile:
                                </div>
                                <div className="text-sm">+91 8561045220</div>
                            </div>
                            <div className="flex gap-3 ">
                                <div className="font-medium text-sm">
                                    Email:
                                </div>
                                <div className="text-sm"><EMAIL></div>
                            </div>
                            <div className="flex gap-3 ">
                                <div className="font-medium text-sm">
                                    Place of Supply:
                                </div>
                                <div className="text-sm"> India</div>
                            </div>
                        </div>
                        <div>
                            <div className="flex gap-3   ">
                                <div className="font-medium text-sm">
                                    Address:
                                </div>
                                <div className="text-sm">Rani Bazar, Bikaner, India</div>
                            </div>
                            <div className="flex gap-3 ">
                                <div className="font-medium text-sm">
                                    City:
                                </div>
                                <div className="text-sm">
                                    Bikaner
                                </div>
                            </div>
                            <div className="flex gap-3 ">
                                <div className="font-medium text-sm">
                                    State:
                                </div>
                                <div className="text-sm">
                                    Rajasthan
                                </div>
                            </div><div className="flex gap-3 ">
                                <div className="font-medium text-sm">
                                    Pin Code:
                                </div>
                                <div className="text-sm">
                                    334001
                                </div>
                            </div>
                        </div>
                        <div>
                            <div className="flex gap-3   ">
                                <div className="font-medium text-sm">
                                    Approved by user:
                                </div>
                                <div className="text-sm">Bhawna Tanwar</div>
                            </div>
                            <div className="flex gap-3 ">
                                <div className="font-medium text-sm">
                                    Coupon:
                                </div>
                                <div className="text-sm">
                                    -
                                </div>
                            </div>
                            <div className="flex gap-3 ">
                                <div className="font-medium text-sm">
                                    Payment Date:
                                </div>
                                <div className="text-sm">
                                    24/03/24 | 05:32 PM
                                </div>
                            </div><div className="flex gap-3 ">
                                <div className="font-medium text-sm">
                                    Refund Amount:
                                </div>
                                <div className="text-sm">
                                    ₹0
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div className="overflow-x-auto my-2 border rounded-md">
                    <Table theme={table_custom} className="border-b">
                        <TableHead>
                            <TableHeadCell>Items (2)</TableHeadCell>
                            <TableHeadCell>Time</TableHeadCell>
                            <TableHeadCell>Quantity</TableHeadCell>
                            <TableHeadCell>Rate</TableHeadCell>
                            <TableHeadCell>
                                Total
                            </TableHeadCell>
                        </TableHead>
                        <TableBody className="divide-y">
                            <TableRow className="bg-white dark:border-gray-700 dark:bg-gray-800">
                                <TableCell>
                                    Rapbooster Pro
                                </TableCell>
                                <TableCell>1 Year</TableCell>
                                <TableCell>2</TableCell>
                                <TableCell>₹ 5,750</TableCell>
                                <TableCell>
                                    ₹ 5,750
                                </TableCell>
                            </TableRow>
                            <TableRow className="bg-white dark:border-gray-700 dark:bg-gray-800">
                                <TableCell>
                                    Rapbooster Pro
                                </TableCell>
                                <TableCell>1 Year</TableCell>
                                <TableCell>2</TableCell>
                                <TableCell>₹ 5,750</TableCell>
                                <TableCell>
                                    ₹ 5,750
                                </TableCell>
                            </TableRow>
                        </TableBody>
                    </Table>
                    <div className="float-end p-2">
                        Final Total:
                        ₹ 5,750
                    </div>
                </div>
            </div>
            <Button theme={button_custom} size="sm" color="blue" className="float-end">
                <div className="flex items-center gap-2">
                    <FiDownload className="text-xl"/>
                    <span className="">Download Invoice</span>
                </div>
            </Button>
        </div>
    );
}

