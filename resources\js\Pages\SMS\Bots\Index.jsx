import Main from '@/Layouts/Main'
import React from 'react'
import TabBar from '../TabBar'
import { Button, Checkbox, Drawer, Table } from 'flowbite-react'
import { button_custom, customDrawerEdit, table_custom } from '@/Pages/Helpers/DesignHelper'
import { MdChecklistRtl, MdDeleteOutline, MdOutlineModeEdit } from 'react-icons/md'
import { TbColumns3 } from 'react-icons/tb'
import { IoMdAdd, IoMdRepeat } from 'react-icons/io'
import { PiCaretUpDownBold } from 'react-icons/pi'
import { RiDeleteBin5Line } from 'react-icons/ri'
import { Link } from '@inertiajs/react'
import { TruncatedCell } from '@/Components/ExpandMsg'
import { useState } from 'react'
import Add from './Add'
import Edit from './Edit'

export default function Index() {
    const [isAddOpen, setIsAddOpen] = useState(false);
    const [isEditOpen, setIsEditOpen] = useState(false);

    return (
        <Main>
            <div className="p-2 overflow-hidden">
                <TabBar />
                <div className="pb-2 rounded-lg">
                    <div className="h-fit bg-white rounded border overflow-auto w-full mt-2.5">
                        <div className="flex justify-between p-2">
                            <ButtonGroup className="">
                                <Button
                                    className=""
                                    size="xs"
                                    color="gray"
                                    theme={button_custom}
                                >
                                    <div className="flex items-center gap-1">
                                        <MdDeleteOutline className="text-slate-500" />
                                        <span className="text-xs">Delete</span>
                                    </div>
                                </Button>
                                <Button
                                    theme={button_custom}
                                    size="xs"
                                    color="gray"
                                    id="dropdownInformationButton"
                                    data-dropdown-toggle="dropdownNotification"
                                    type="button"
                                >
                                    <div className="flex items-center gap-1 text-xs">
                                        <TbColumns3 className="text-slate-500 ms-1" />
                                        <span className="text-xs">
                                            Columns
                                        </span>
                                    </div>
                                </Button>

                            </ButtonGroup>
                            <div className="">
                                <Button
                                    className="pe-1"
                                    size="xs"
                                    color="gray"
                                    theme={button_custom}
                                onClick={() => setIsAddOpen(true)}
                                >
                                    <div className="flex items-center gap-1">
                                        <IoMdAdd className="text-slate-500 text-xs" />
                                        <span className="text-xs">Add</span>
                                    </div>
                                </Button>

                            </div>
                        </div>
                        <div className="overflow-x-auto bg-white border rounded-lg">
                            <Table theme={table_custom}>
                                <Table.Head>
                                    <Table.HeadCell>
                                        <Checkbox
                                            color="blue"
                                            defaultChecked
                                        />
                                    </Table.HeadCell>
                                    <Table.HeadCell><div className="flex items-center justify-between gap-2">
                                        <h3>Id</h3>
                                        <PiCaretUpDownBold />
                                    </div></Table.HeadCell>
                                    <Table.HeadCell>User</Table.HeadCell>
                                    <Table.HeadCell><div className="flex items-center justify-between gap-2">
                                        <h3>Name</h3>
                                        <PiCaretUpDownBold />
                                    </div></Table.HeadCell>
                                    <Table.HeadCell className="w-80">Welcome Msg / Wrong Keyword</Table.HeadCell>
                                    <Table.HeadCell>
                                        Action
                                    </Table.HeadCell>
                                </Table.Head>
                                <Table.Body className="divide-y">
                                    <Table.Row className="bg-white dark:border-gray-700 dark:bg-gray-800">
                                        <Table.Cell>
                                            <Checkbox
                                                color={"blue"}
                                                className="rowCheckBox"
                                            // id={channel.id}
                                            // onChange={getCheckedIds}
                                            />
                                        </Table.Cell>

                                        <Table.Cell>13</Table.Cell>
                                        <Table.Cell>cuzivyruky</Table.Cell>
                                        <Table.Cell className='text-blue-600'>Welcome Message</Table.Cell>
                                        <Table.Cell className="w-40">
                                            <TruncatedCell
                                                content={
                                                    " jsdkb sd kbs dkg k dbgk sd  bv ksbhkb vhsd  bvh dv hds kvkjs vjsdvhj sdvhjsvdhjv  jdsvfsd vfvs djfsdj fvsdv fvsdv fjdsv fdv fvs df jvsdjfvsj dfvjsdvff ffff ffffff fffff fffff fffff ffdsj vffy afqw has ssss ssvnfF."
                                                }
                                            />
                                        </Table.Cell>

                                        <Table.Cell>
                                            <Table.Cell>
                                                <div className="flex items-center gap-3 pt-1">
                                                    <div className="relative flex me-1.5">
                                                        <Button
                                                            className="items-center"
                                                            as={
                                                                Link
                                                            }
                                                            href={route(
                                                                "sms.bots.listkeyword",
                                                            )}
                                                            theme={
                                                                button_custom
                                                            }
                                                            size={
                                                                "xs"
                                                            }
                                                            color="blue"
                                                        >
                                                            <MdChecklistRtl className="text-sm ms-1" />
                                                            <span className="text-xs ms-1">
                                                                Keyword
                                                            </span>
                                                        </Button>

                                                        <div className="absolute inline-flex items-center justify-center mt-0.5 w-4 h-4 min-w-max min-h-max text-xs font-bold text-white bg-red-600 rounded-full -top-2 -end-3 dark:border-gray-900">
                                                            5  
                                                        </div>

                                                    </div>
                                                    <Button
                                                        className=""
                                                        theme={
                                                            button_custom
                                                        }
                                                        color="success"
                                                        size="xs"
                                                        onClick={() => setIsEditOpen(true)}
                                                    >
                                                        <MdOutlineModeEdit className="text-sm" />
                                                        <span className="text-xs ms-1">
                                                            Edit
                                                        </span>
                                                    </Button>
                                                    <Button
                                                        theme={
                                                            button_custom
                                                        }
                                                        color="failure"
                                                        size="xs"
                                                    >
                                                        <RiDeleteBin5Line className="text-sm" />
                                                        <span className="text-xs ms-1">
                                                            Delete
                                                        </span>
                                                    </Button>
                                                </div>
                                            </Table.Cell>
                                        </Table.Cell>
                                    </Table.Row>
                                </Table.Body>
                            </Table>
                        </div>
                    </div>
                </div>
            </div>
            <Drawer
                theme={customDrawerEdit}
                className="w-full lg:w-4/5"
                open={isAddOpen}
                onClose={() => setIsAddOpen(false)}
                position="right"
            >
                <Drawer.Header titleIcon={IoMdRepeat} title="Add Bot" />
                <Drawer.Items className=" rounded">
                    <Add onClose={() => setIsAddOpen(false)}></Add>
                </Drawer.Items>
            </Drawer>
            <Drawer
                theme={customDrawerEdit}
                className="w-full lg:w-4/5"
                open={isEditOpen}
                onClose={() => setIsEditOpen(false)}
                position="right"
            >
                <Drawer.Header
                    titleIcon={IoMdRepeat}
                    title={"Edit Bot (kljklj)"}
                />
                <Drawer.Items className="">
                    <Edit
                        // autoReplyEditData={autoReplyEditData}
                        // onClose={() => setIsEditAutoReply(false)}
                    ></Edit>
                </Drawer.Items>
            </Drawer>
        </Main>
    )
}


