
import NoRecord from "@/Components/HelperComponents/NoRecord";
import Paginate from "@/Components/HelperComponents/Paginate";
import {
    button_custom,
    card_custom,
    customDrawer
} from "@/Pages/Helpers/DesignHelper";
import Search from "@/Pages/Users/<USER>";
import { Head, router } from "@inertiajs/react";
import { <PERSON><PERSON>, Card, Drawer, DrawerHeader, DrawerItems, Popover } from "flowbite-react";
import { useState } from "react";
import { CgAttachment } from "react-icons/cg";
import { HiOutlineTemplate } from "react-icons/hi";
import { IoIosArrowDown, IoMdAdd } from "react-icons/io";
import {
    MdDeleteOutline,
    MdOutlineEdit
} from "react-icons/md";
import { RiFileSearchLine } from "react-icons/ri";
import MailMain from "../MailMain";
import Add from "./Add";
import Category from "./Category";
import Edit from "./Edit";
import View from "./View";
import ConfirmBox from "@/Components/HelperComponents/ConfirmBox";
import TinyEditor from "@/Components/TinyEditor";

export default function Index({ collection }) {
    let templates = collection?.templates;
    // console.log(templates[0]);

    const [selectedCategory, setSelectedCategory] = useState("0");
    const [isMultipleDeleteConfirmOpen, SetIsMultipleDeleteConfirmOpen] = useState(false);
    const [modalState, setModalState] = useState({ openAdd: false, openEdit: false, openView: false, selected: null });
    const [selectedTemplates, setSelectedTemplates] = useState([]);

    function deleteRecords() {
        if (selectedTemplates.length > 0) {
            router.delete(route("voice.templates.destroy", { template: selectedTemplates.toLocaleString() }));
        }
    }
    const handleMultipleDelete = (res) => {
        if (res) {
            deleteRecords(); // Your function to delete records (ensure it handles the selection).
        }
        // Reset checkboxes and state
        setSelectedTemplates([]); // Clear selected checkboxes
        SetIsMultipleDeleteConfirmOpen(false); // Close the confirmation dialog
    };
    return (
        <MailMain>
            <>
                <Head title="Mail-Templates" />

                <div className="h-full pt-3 ">
                    <div className="grid h-full grid-flow-row-dense grid-cols-11 gap-2 p-0 border-none md:grid-cols-12 sm:grid-cols-1 bg-slate-100">
                        <div className="hidden h-full bg-white rounded-lg xl:col-span-3 lg:col-span-3 md:col-span-2 sm:col-span-3 lg:flex">
                            <Category
                                setSelectedCategory={setSelectedCategory}
                                selectedCategory={selectedCategory}
                            />
                        </div>
                        <div className="relative h-full p-2 pt-0 bg-white rounded-lg dark:text-gray-400 lg:p-0 xl:col-span-9 lg:col-span-9 md:col-span-12 col-span-full">
                            <div>
                                {/* Welcome message  */}
                                <div className="flex items-center justify-between p-2 ">
                                    <div className="flex items-center gap-2">
                                        <div className="max-w-md">
                                            <Search
                                                className=""
                                                inputPlaceholder="Search a Template..."
                                            ></Search>
                                        </div>
                                        <div className="flex lg:hidden">
                                            <Popover
                                                aria-labelledby="Category-popover"
                                                content={
                                                    <Category
                                                        setSelectedCategory={setSelectedCategory}
                                                        selectedCategory={selectedCategory}
                                                    />
                                                }
                                            >
                                                <Button color="gray" size="xs" className="items-center group">Category
                                                    <IoIosArrowDown className="transition delay-150 self-center text-md ms-2 group-aria-[expanded=false]:rotate-180" />
                                                </Button>
                                            </Popover>
                                        </div>
                                    </div>
                                    <div>
                                        <Button
                                            size="xs"
                                            color="gray"
                                            theme={button_custom}
                                            onClick={() => setModalState({ openAdd: !modalState.openAdd })}

                                        >
                                            <div className="flex items-center gap-1 text-xs">
                                                <IoMdAdd className="text-sm text-slate-500" />
                                                <span>Add</span>
                                            </div>
                                        </Button>
                                    </div>
                                </div>
                                <div className="h-full px-2 pb-2">
                                    <div className="grid grid-flow-row-dense grid-cols-1 gap-4 2xl:grid:cols-2 xl:grid-cols-2 lg:grid-cols-1 md:grid-cols-1 sm:grid-cols-1">
                                        {templates?.data?.length > 0 ?
                                            templates?.data?.map((template, ky) =>
                                                <Card key={'card-' + ky}
                                                    theme={card_custom}
                                                    className="relative w-full bg-slate-300"
                                                >
                                                    {template.body != null &&
                                                        <div className="absolute -left-0 top-6 w-0 h-0 border-t-[1px] border-t-transparent border-b-[12px] border-b-transparent border-r-[18px] border-r-white"></div>
                                                    }
                                                    <div className="flex flex-col justify-between h-full">
                                                        <div
                                                            className="p-2 overflow-auto">
                                                            {template.body != null &&
                                                                <TinyEditor key={ky} readOnly={true} value={template.body} onChange={setData} id={"tinyEditor-" + ky} />
                                                            }
                                                            {template.file &&
                                                                <div className="flex items-center gap-1 px-2">
                                                                    <span className="text-sm text-blue-600">
                                                                        <CgAttachment />
                                                                    </span>
                                                                    <span className="text-sm text-blue-600">
                                                                        1
                                                                    </span>
                                                                </div>
                                                            }
                                                        </div>
                                                        <div className="p-1 bg-slate-100">
                                                            <div className="flex items-center justify-between gap-2 rounded-b-lg">

                                                                <div className="truncate">
                                                                    <div className="text-sm">
                                                                        {template.name}
                                                                    </div>
                                                                    <div className="text-xs text-slate-500">
                                                                        {template.mail_template_category_name}
                                                                    </div>
                                                                </div>

                                                                <div className="flex items-center">
                                                                    <Button
                                                                        theme={
                                                                            button_custom
                                                                        }
                                                                        color="withoutBorder"
                                                                        size="xs"
                                                                        onClick={() => setModalState({ openView: !modalState.openView, selected: template })}

                                                                    >
                                                                        <RiFileSearchLine className="text-slate-400" />
                                                                    </Button>

                                                                    <Button
                                                                        theme={button_custom}
                                                                        color="withoutBorder"
                                                                        size="xs"
                                                                        onClick={() => setModalState({ openEdit: !modalState.openEdit, selected: template })}
                                                                    >
                                                                        <MdOutlineEdit className="text-slate-400" />
                                                                    </Button>
                                                                    <Button
                                                                        theme={
                                                                            button_custom
                                                                        }
                                                                        color="withoutBorder"
                                                                        size="xs"
                                                                        onClick={() => {
                                                                            SetIsMultipleDeleteConfirmOpen(!isMultipleDeleteConfirmOpen);
                                                                            setSelectedTemplates([template.id]);
                                                                        }}

                                                                    >
                                                                        <MdDeleteOutline className="text-slate-400" />
                                                                    </Button>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </Card>
                                            )
                                            :
                                            <NoRecord />
                                        }
                                    </div>
                                </div>
                                <div className="bottom-0 w-full mt-1.5 p-3 bg-white rounded-lg float-end dark:border-gray-500 dark:rounded-lg dark:bg-gray-700">
                                    <div className="flex flex-wrap justify-between gap-2 lg:justify-between lg:gap-0 md:gap-0">
                                        <div className="flex items-center gap-4">
                                            {/* <PerPageDropdown
                                                    // getDataFields={
                                                    //     getData ?? null
                                                    // }
                                                    routeName={
                                                        "whatsapp.templates.index"
                                                    }
                                                    data={templates}
                                                    customPerPage={8}
                                                /> */}
                                        </div>
                                        {templates?.links &&
                                            <Paginate tableData={templates} />
                                        }
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                {modalState.openAdd && (
                    <Drawer
                        theme={customDrawer}
                        open={modalState.openAdd}
                        onClose={() => setModalState({ openAdd: !modalState.openAdd })}
                        position="right"
                        className="w-full lg:w-5/6 md:w-4/5"
                    >
                        <DrawerHeader
                            titleIcon={HiOutlineTemplate}
                            title="Add Template"
                        />
                        <DrawerItems>
                            <Add
                                onClose={() => setModalState({ openAdd: !modalState.openAdd })}
                            />
                        </DrawerItems>
                    </Drawer>
                )}
                {modalState.openEdit && (
                    <Drawer
                        theme={customDrawer}
                        open={modalState.openEdit}
                        onClose={() => setModalState({ openEdit: !modalState.openEdit })}
                        position="right"
                        className="w-full lg:w-5/6 md:w-4/5"
                    >
                        <DrawerHeader
                            titleIcon={HiOutlineTemplate}

                            title={"Edit Template (" + modalState.selected.name + ")"}
                        />
                        <DrawerItems>
                            <Edit

                                onClose={() => setModalState({ openEdit: !modalState.openEdit })}
                                template={modalState.selected}
                            />
                        </DrawerItems>
                    </Drawer>
                )}
                {modalState.openView &&
                    <Drawer
                        theme={customDrawer}
                        open={modalState.openView}
                        onClose={() => setModalState({ openView: !modalState.openView })}
                        position="right"
                        className="w-full lg:w-4/5 md:w-4/5"

                    >
                        <DrawerHeader
                            title={"View Template (" + modalState.selected.name + ")"}
                            titleIcon={RiFileSearchLine}

                        />
                        <DrawerItems>
                            <View
                                onClose={() => setModalState({ openView: !modalState.openView })}
                                template={modalState.selected}
                            />
                        </DrawerItems>
                    </Drawer>
                }
                {isMultipleDeleteConfirmOpen &&
                    <ConfirmBox
                        isOpen={isMultipleDeleteConfirmOpen}
                        onClose={() => SetIsMultipleDeleteConfirmOpen(false)} // Close the confirm box
                        onAction={handleMultipleDelete} // Handle the user's choice
                        title="Delete Gateway "
                        message="Do you want to Delete gateway."
                        confirmText="Yes, Delete!"
                        cancelText="No, Keep It"
                        confirmColor="orange"
                        cancelColor="gray"

                    />
                }
            </>
        </MailMain >
    );
}
