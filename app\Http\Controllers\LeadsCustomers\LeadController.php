<?php

namespace App\Http\Controllers\LeadsCustomers;

use App\Models\Lead\LeadBusiness;
use App\Models\Lead\LeadContact;
use App\Models\Lead\LeadFor;
use App\Models\Lead\LeadSource;
use Exception;
use Inertia\Inertia;
use App\Models\Lead\Lead;
use Illuminate\Http\Request;
use App\Http\Controllers\Controller;
use App\Models\Lead\LeadUserAssign;
use App\Models\User;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;

class LeadController extends Controller
{

    private $tableName;

    public function __construct()
    {
        $this->tableName = (new Lead())->getTable();
    }


    /**
     * Display a listing of the resource.
     */
    public function index()
    {

        if (!request()->user()->can($this->tableName . ".view")) {
            abort(403, "unauthorized");
        }

        try {
            $totalCount = $_GET['perPage'] ?? 15;
            $column = $_GET['column'] ?? 'id';
            $sortBy = $_GET['sort'] ?? 'desc';
            $leads = Lead::orderBy($column, $sortBy)->with('leadSource', 'assignedTo', 'createdBy', 'referredBy', "leadContacts", "leadPrimaryContact", "leadBusiness:id,lead_id,name", "leadPrimaryBusiness:id,lead_id,name", "leadNextFollowup:id,lead_id,nextFollowup")->accessibleByUser("createdBy_id")->paginate($totalCount)->withQueryString();
            foreach ($leads as $lead) {
                $leadForIDs = explode(',', $lead->lead_for_id);
                $lead->leadFor = LeadFor::whereIn('id', $leadForIDs)->get();
            }
            $data['leads'] = $leads;
            $data['getData'] = $_GET;
            $data["can_add"] = request()->user()->can($this->tableName . ".add");
            $data["can_edit"] = request()->user()->can($this->tableName . ".edit");
            $data["can_delete"] = request()->user()->can($this->tableName . ".delete");
            $data["can_assign"] = request()->user()->can($this->tableName . ".assign");

            return Inertia::render('LeadsCustomers/Index', ['collection' => $data]);
        } catch (Exception $e) {
            return (request()->header('Accept') == 'application/json') ?
                response()->json(['message' => errorMessage($e->getCode()), 'status' => false])
                : redirect()->back()->with(["message" => errorMessage($e->getCode()), "type" => "failure"]);
        }
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        //
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        if (!request()->user()->can($this->tableName . '.add')) {
            abort(403, "unauthorized");
        }

        $request->validate([
            "name" => "required",
            "mobile" => "required_without:email|min:10|max:15|unique:lead_contact,mobile",
            "email" => "required_without:mobile|email|unique:lead_contact,email",
            "pinCode" => "min:6|max:6",
            "ProfilePic.0" => "mimes:jpg,jpeg,png|max:2048|nullable"
        ], [
            "name.required" => "Name is required",
            "mobile.required_without" => "Mobile number is required when email is not provided",
            "email.required_without" => "Email is required when mobile is not provided",
            "email.email" => "Please enter a valid email address",
            "pinCode.min" => "Pin Code must be 6 digits",
            "pinCode.max" => "Pin Code must be 6 digits"
        ], [
            "name" => "Name",
            "mobile" => "Mobile",
            "email" => "Email",
            "pinCode" => "Pin Code",
            "ProfilePic" => "Profile Picture"
        ]);


        try {
            DB::beginTransaction();
            // lead table data
            $lead = new Lead();
            $lead->lead_source_id = $request->leadSource["id"];
            if ($request->leadFor && count($request->leadFor) > 0)
                $lead->lead_for_id = collect($request->leadFor)->pluck('id')->join(',');

            $lead->assignedTo_id = $request->assignTo["id"];
            $lead->remark = $request->remark;
            $lead->createdBy_id = Auth::id();
            $lead->save();

            // lead Contact Table Data
            $leadContact = new LeadContact();
            $leadContact->lead_id = $lead->id;
            $leadContact->name = $request->name;
            $leadContact->mobile = $request->mobile;
            $leadContact->email = $request->email;
            $leadContact->isPrimary = 1;
            if ($request->hasFile('ProfilePic')) {
                $file = $request->file('ProfilePic');
                $fileName = time() . '-' . $file[0]->getClientOriginalName();
                $path = storeFile('uploads/' . $this->tableName . "/LeadContact", $file[0], $fileName);
                $leadContact->image = $path;
            }
            $leadContact->save();

            // lead business table Data
            $leadBusiness = new LeadBusiness();
            $leadBusiness->lead_id = $lead->id;
            $leadBusiness->name = $request->business;
            $leadBusiness->country = $request->country;
            $leadBusiness->state = $request->state;
            $leadBusiness->city = $request->city;
            $leadBusiness->pinCode = $request->pinCode;
            $leadBusiness->isPrimary = 1;
            $leadBusiness->save();

            DB::commit();
            return (request()->header('Accept') == 'application/json') ?
                response()->json(['message' => 'Lead Created', 'status' => true])
                : redirect()->back()->with(["message" => 'Lead Created', "type" => "success"]);
        } catch (Exception $e) {
            DB::rollback();
            return (request()->header('Accept') == 'application/json') ?
                response()->json(['message' => errorMessage($e->getCode()), 'status' => false])
                : redirect()->back()->with(["message" => errorMessage($e->getCode()), "type" => "failure"]);
        }
    }

    /**
     * Display the specified resource.
     */
    public function show(string $id)
    {
        //
        return Inertia::render('LeadsCustomers/LeadDetails/Layout/Index');
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(string $id)
    {
        dd("edit");
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, string $id)
    {
        dd("update");
    }

    /**
     * Remove the specified resource from storage.  
     */
    public function destroy(string $id)
    {
        if (!request()->user()->can($this->tableName . '.delete')) {
            abort(403, "unauthorized");
        }

        try {
            Lead::whereIn('id', explode(',', $id))->delete();
            return (request()->header('Accept') == 'application/json') ?
                response()->json(['message' => 'Lead Deleted', 'status' => true])
                : redirect()->back()->with(["message" => 'Lead Deleted', "type" => "success"]);
        } catch (Exception $e) {
            return (request()->header('Accept') == 'application/json') ?
                response()->json(['message' => errorMessage($e->getCode()), 'status' => false])
                : redirect()->back()->with(["message" => errorMessage($e->getCode()), "type" => "failure"]);
        }
    }


    // helper functions
    public function getLeadSource()
    {
        try {
            $perPage = request()->get('perPage', 5);
            $page = request()->get('page', 1);

            $leadSource = cache()->remember('lead_sources_' . $page . '_' . $perPage, 300, function () use ($perPage) {
                return LeadSource::select(['id', 'name'])->paginate($perPage)->withQueryString()->toArray();
            });

            if (!$leadSource)
                return response()->json(['message' => 'No lead sources found', 'status' => false]);

            return response()->json([
                'leadSource' => $leadSource,
                'status' => true
            ]);
        } catch (Exception $e) {
            return request()->header('Accept') == 'application/json'
                ? response()->json([
                    'message' => $e->getMessage(),
                    'status' => false
                ])
                : redirect()->back()->with([
                    'message' => $e->getMessage(),
                    'type' => 'error'
                ]);
        }
    }
    public function getLeadFor()
    {
        try {
            $perPage = request()->get('perPage', 5);
            $page = request()->get('page', 1);

            $leadFor = cache()->remember('lead_for_' . $page . '_' . $perPage, 300, function () use ($perPage) {
                return LeadFor::select(['id', 'name'])->paginate($perPage)->withQueryString()->toArray();
            });

            if (!$leadFor)
                return response()->json(['message' => 'No lead sources found', 'status' => false]);

            return response()->json([
                'leadFor' => $leadFor,
                'status' => true
            ]);
        } catch (Exception $e) {
            return request()->header('Accept') == 'application/json'
                ? response()->json([
                    'message' => $e->getMessage(),
                    'status' => false
                ])
                : redirect()->back()->with([
                    'message' => $e->getMessage(),
                    'type' => 'error'
                ]);
        }
    }
    public function getAgents()
    {
        try {
            $perPage = request()->get('perPage', 5);
            $page = request()->get('page', 1);

            $agents = cache()->remember('agents_' . $page . '_' . $perPage, 300, function () use ($perPage) {
                return User::select(['id', DB::raw('username as name'), "image"])->paginate($perPage)->withQueryString()->toArray();
            });

            if (!$agents)
                return response()->json(['message' => 'No agents found', 'status' => false]);

            return response()->json([
                'leadAgents' => $agents,
                'status' => true
            ]);
        } catch (Exception $e) {
            return request()->header('Accept') == 'application/json'
                ? response()->json([
                    'message' => $e->getMessage(),
                    'status' => false
                ])
                : redirect()->back()->with([
                    'message' => $e->getMessage(),
                    'type' => 'error'
                ]);
        }
    }

    public function getLeadUserAssignData()
    {
        if (!request()->user()->can($this->tableName . '.assign')) {
            abort(403, "unauthorized");
        }
        try {
            $perPage = request()->get('perPage', 15);
            $search = request()->get('search');
            $query = LeadUserAssign::query();

            if ($search) {
                $query->where(function ($q) use ($search) {
                    $q->where('id', 'like', '%' . $search . '%')
                        ->orWhere('name', 'like', '%' . $search . '%');
                });
            }

            $agents = $query->paginate($perPage)->withQueryString()->toArray();

            if (!$agents)
                return response()->json(['message' => 'No agents found', 'status' => false]);
            return response()->json([
                'leadUserAssign' => $agents,
                'status' => true
            ]);
        } catch (Exception $e) {
            return request()->header('Accept') == 'application/json'
                ? response()->json([
                    'message' => $e->getMessage(),
                    'status' => false
                ])
                : redirect()->back()->with([
                    'message' => $e->getMessage(),
                    'type' => 'error'
                ]);
        }
    }



    public function assignLeadToUser(Request $request)
    {
        if (!request()->user()->can($this->tableName . '.assign')) {
            abort(403, "unauthorized");
        }

        try {
            if (count($request->leads) == 0)
                return request()->header('Accept') == 'application/json' ? response()->json(['message' => 'No leads selected', 'status' => false]) : redirect()->back()->with(['message' => 'No leads selected', 'type' => 'error']);

            if ($request->type == 'single') {
                foreach ($request->leads as $leadId) {
                    $lead = Lead::find($leadId);
                    if (!$lead)
                        // if lead Id not found 
                        continue;
                    $lead->assignedTo_id = $request->assignId[0];
                    $lead->assignedAt = date('Y-m-d H:i:s');
                    $lead->save();
                }
            } else {
                $leadCount = count($request->leads);
                $assignCount = count($request->assignId);
                $baseCount = intdiv($leadCount, $assignCount);
                $extra = $leadCount % $assignCount;

                $index = 0;
                foreach ($request->assignId as $i => $assigneeId) {
                    $assignSize = $baseCount + ($i < $extra ? 1 : 0);
                    $assignedLeads = array_slice($request->leads, $index, $assignSize);

                    foreach ($assignedLeads as $leadId) {
                        if ($lead = Lead::find($leadId)) {
                            $lead->assignedTo_id = $assigneeId;
                            $lead->assignedAt = now();
                            $lead->save();
                        }
                    }
                    $index += $assignSize;
                }
            }

            return request()->header('Accept') == 'application/json' ? response()->json(['message' => 'Lead assigned successfully', 'status' => true]) : redirect()->back()->with(['message' => 'Lead assigned successfully', 'type' => 'success']);
        } catch (Exception $e) {
            return request()->header('Accept') == 'application/json' ? response()->json(['message' => $e->getMessage(), 'status' => false]) : redirect()->back()->with(['message' => $e->getMessage(), 'type' => 'error']);
        }
    }
}
