@import 'tailwindcss';

@config '../../tailwind.config.js';

/*
  The default border color has changed to `currentcolor` in Tailwind CSS v4,
  so we've added these compatibility styles to make sure everything still
  looks the same as it did with Tailwind CSS v3.

  If we ever want to remove these styles, we need to add an explicit border
  color utility to any element that depends on these defaults.
*/
@layer base {
  *,
  ::after,
  ::before,
  ::backdrop,
  ::file-selector-button {
    border-color: var(--color-gray-200, currentcolor);
  }
}

/*--------------------SCROLLBAR ------------------*/
html {
    scroll-behavior: auto;
}

/* width */
::-webkit-scrollbar {
    width: 5px;
    height: 5px;
    border-radius: 5%;
}

/* Handle */
::-webkit-scrollbar-thumb {
    transition-duration: all 1s ease-in-out;
    background: #ffffff;
    border-radius: 5px;
}

/* Handle on hover */
*:hover::-webkit-scrollbar-thumb {
    background: #8c96a6;
}

/*--------------------SCROLLBAR ------------------*/

/*--------------------TEXT COLORS ------------------*/

/* .chatBox-cover {
    background-image: url("/assets/img/bgwtsp.png");
    background-size: cover;
    background-position: center;
} */

.inbox-screen {
    max-width: 1400px;
    margin: auto;
    /* height: 80vh; */
}

*,
.word-break {
    overflow-wrap: break-word;
}

/* Chat Bubble CSS */
/* talk bubble contents */
.talktext {
    padding: 1em;
    text-align: left;
    line-height: 1.5em;
}
.talktext p {
    /* remove webkit p margins */
    -webkit-margin-before: 0em;
    -webkit-margin-after: 0em;
}
.talk-bubble {
	margin: 40px;
  display: inline-block;
  position: relative;
	width: 200px;
	height: auto;
	background-color: lightyellow;
}

.react-international-phone-input {
    width: 100% !important; /* Ensure full width */
  }
