import NoRecord from "@/Components/HelperComponents/NoRecord";
import Paginate from "@/Components/HelperComponents/Paginate";
import PerPageDropdown from "@/Components/HelperComponents/PerPageDropdown";
import Main from "@/Layouts/Main";
import { Head, Link, router, useForm } from "@inertiajs/react";
import {
    Button,
    ButtonGroup,
    Checkbox,
    Drawer,
    Dropdown,
    Modal,
    Table
} from "flowbite-react";
import $ from "jquery";
import { useState } from "react";
import { BiImport } from "react-icons/bi";
import { CgImport } from "react-icons/cg";
import { FaRegRectangleList, FaRegTrashCan } from "react-icons/fa6";
import { HiOutlineUsers } from "react-icons/hi2";
import { IoIosArrowBack, IoMdAdd, IoMdClose } from "react-icons/io";
import { LuMails } from "react-icons/lu";
import { MdDeleteOutline, MdOutlineEdit } from "react-icons/md";
import { PiColumnsPlusRight, PiExport, PiGlobeHemisphereEastLight, PiPhoneListThin } from "react-icons/pi";
import { RiContactsBook3Line } from "react-icons/ri";
import { TbFileTypeCsv } from "react-icons/tb";
import {
    button_custom,
    buttongroup_custom,
    customDrawer,
    table_custom,
} from "../Helpers/DesignHelper";
import AddContacts from "./AddContacts";
import AddCountryCode from "./AddCountryCode";
import AppendField from "./AppendField";
import EditContact from "./EditContact";
import ImportCSV from "./ImportCSV";
import ImportManually from "./ImportManually";
import ImportManuallyEmail from "./ImportManuallyEmail";
import ConfirmBox from "@/Components/HelperComponents/ConfirmBox";

export default function Index({ collection }) {
    const [openModal, setOpenModal] = useState(false);
    const { contactList, contacts, getData } = collection;

    const {
        data,
        setData,
        delete: destroy,
        processing,
        errors,
    } = useForm({
        id: [],
    });

    const [isCheckAll, setIsCheckAll] = useState(false);
    const [checkValue, setCheckValue] = useState([]);
    const [isAddContact, setIsAddContact] = useState(false);
    const [contactListDrawerData, setContactListDrawerData] = useState(null);
    const [isImportManually, setImportManually] = useState(false);
    const [isImportManuallyEmail, setIsImportManuallyEmail] = useState(false);
    const [importCSV, setimportCSV] = useState(false);
    const [editContactDrawer, setEditContactDrawer] = useState(false);
    const [editContactDrawerData, setEditContactDrawerData] = useState({});
    const [isCountryCodeModalOpen, setIsCountryCodeModalOpen] = useState(false);
    const [isConfirmOpen, setConfirmOpen] = useState(false);
    const [isExportDocumentLoading, setIsExportDocumentLoading] = useState(false);
    const [isRemoveDuplicateLoading, setIsRemoveDuplicateLoading] = useState(false);


    // executes when user click table row checkbox
    function getCheckedIds(e) {
        let previousIds = checkValue;
        if (e.target.checked) {
            if (!previousIds.includes(e.target.id)) {
                previousIds.push(e.target.id);
                setCheckValue(previousIds);
            }
        } else {
            const newIds = previousIds.filter((item) => item !== e.target.id);
            setCheckValue(newIds);
        }
    }

    // executes when user click table header checkbox
    function headerCheckBoxChecked(e) {
        let previousIds = [];
        if (
            e.target.checked &&
            e.target.id == 0 &&
            contacts.data.length > 0
        ) {
            contacts.data.map((contacts, key) => {
                if (!previousIds.includes(contacts.id)) {
                    previousIds.push(contacts.id);
                    setCheckValue(previousIds);
                }
            });
            setIsCheckAll(true);
            $(".rowCheckBox").prop("checked", true);
        } else {
            setCheckValue(previousIds);
            setIsCheckAll(false);
            $(".rowCheckBox").prop("checked", false);
        }
    }

    // handle checkBox Check or uncheck
    function checkAddcheckBoxChecked() {
        let allCheckBoxes = $(".rowCheckBox");
        let checkedCheckBoxes = $(".rowCheckBox:checked");

        if (allCheckBoxes.length == checkedCheckBoxes.length) {
            setIsCheckAll(true);
        } else {
            setIsCheckAll(false);
        }
    }

    function handleConfirmBoxResult(result) {
        if (checkValue.length > 0) {
            setData({ id: checkValue });
            destroy(route("contacts.destroy", { contact: checkValue }));
            on: "rounded-full", setCheckValue([]);
            setIsCheckAll(false);
            $(".rowCheckBox").prop("checked", false);
        }
    }

    const customDrawerIconEditContact = {
        root: {
            base: "fixed z-50 overflow-y-auto bg-white p-4 transition-transform dark:bg-gray-800 bg-slate-100 ",
            backdrop: "fixed inset-0 z-40 bg-gray-900/50 dark:bg-gray-900/80",
            edge: "bottom-16",
            position: {
                top: {
                    on: "left-0 right-0 top-0 w-full transform-none",
                    off: "left-0 right-0 top-0 w-full -translate-y-full",
                },
                right: {
                    on: "right-0 top-0 h-screen w-80 transform-none",
                    off: "right-0 top-0 h-screen w-80 translate-x-full",
                },
                bottom: {
                    on: "bottom-0 left-0 right-0 w-full transform-none",
                    off: "bottom-0 left-0 right-0 w-full translate-y-full",
                },
                left: {
                    on: "left-0 top-0 h-screen w-80 transform-none",
                    off: "left-0 top-0 h-screen w-80 -translate-x-full",
                },
            },
        },
        header: {
            inner: {
                closeButton:
                    "absolute end-2.5 top-2.5 flex h-8 w-8 items-center justify-center rounded-lg bg-transparent text-sm text-gray-400 hover:bg-gray-200 hover:text-gray-900 dark:hover:bg-gray-600 dark:hover:text-white",
                closeIcon: "h-6 w-6",
                titleIcon: "me-2.5 h-6 w-6 text-slate-400",
                titleText:
                    "mb-4 inline-flex items-center text-xl font-semibold text-slate-800 dark:text-gray-400",
            },
            collapsed: {
                on: "hidden",
                off: "block",
            },
        },
        items: {
            base: "rounded-lg bg-white dark:!bg-gray-700",
        },
    };


    const removeDuplicates = async (id, column = null) => {
        try {
            setIsRemoveDuplicateLoading(true);
            const res = await fetch(route('contactList.removeDuplicates', { id, column }));
            if (!res.ok) {
                throw new Error('Failed to remove duplicates');
            }
            router.visit(route("contactList.show", { contact: id }));
        } catch (error) {
            console.error('Error removing duplicates:', error);
        } finally {
            setIsRemoveDuplicateLoading(false);
        }
    }

    return (
        <Main>
            <Head title="Contacts" />
            <div className="px-2 pb-2 rounded-lg">
                <div className="flex items-center gap-1 p-2 text-lg font-medium">
                    <RiContactsBook3Line className="text-xl text-slate-400" />
                    {contactList.name}
                </div>
                <div className="w-full overflow-auto bg-white rounded-lg h-fit">
                    <div className="flex justify-between p-2">
                        <div className="flex items-center gap-2">
                            {/* <BackButton /> */}
                            <div className="flex gap-1">
                                <Button
                                    as={Link}
                                    className="border pe-1.5"
                                    size="xs"
                                    color="pink"
                                    theme={button_custom}
                                    href={route('contactList.index')}
                                >
                                    <div className="flex items-center gap-1">
                                        <IoIosArrowBack />
                                        <span className="text-xs">Back</span>
                                    </div>
                                </Button>
                                {
                                    collection.can_delete &&
                                    <Button
                                        // className="border rounded"
                                        size="xs"
                                        color="gray"
                                        theme={button_custom}
                                        onClick={() =>
                                            setConfirmOpen(true)
                                        }
                                    >
                                        <div className="flex items-center gap-1">
                                            <MdDeleteOutline className="text-slate-500" />
                                            <span className="text-xs">Delete</span>
                                        </div>
                                    </Button>
                                }
                            </div>

                            <ButtonGroup>
                                <div className="flex items-center border rounded-tl rounded-bl ps-1 pe-1">
                                    {
                                        collection.can_edit &&
                                        <Dropdown
                                            className="border-t border-b "
                                            arrowIcon={true}
                                            inline
                                            label={
                                                <div className="flex items-center gap-1">
                                                    <BiImport className="text-slate-500" />
                                                    <span className="text-xs ms-1">
                                                        Import
                                                    </span>
                                                </div>
                                            }
                                        >
                                            <DropdownItem
                                                onClick={() => {
                                                    setimportCSV(true);
                                                    setContactListDrawerData({
                                                        id: contactList.id,
                                                        name: contactList.name,
                                                    });
                                                }}
                                            >
                                                <div className="flex gap-1">
                                                    <FaRegRectangleList className="text-slate-500" />
                                                    <span className="text-xs ms-1">
                                                        CSV Import
                                                    </span>
                                                </div>
                                            </DropdownItem>

                                            <DropdownItem
                                                onClick={() => {
                                                    setImportManually(true);
                                                    setContactListDrawerData({
                                                        id: contactList.id,
                                                        name: contactList.name,
                                                    });
                                                }}
                                            >
                                                <div className="flex items-center gap-1">
                                                    {/* <LuContact className="text-slate-500" /> */}
                                                    <PiPhoneListThin className="text-slate-500" />

                                                    <span className="text-xs ms-1">
                                                        Manually Import Number
                                                    </span>
                                                </div>
                                            </DropdownItem>
                                            <DropdownItem
                                                onClick={() => {
                                                    setIsImportManuallyEmail(true);
                                                    setContactListDrawerData({
                                                        id: contactList.id,
                                                        name: contactList.name,
                                                    });
                                                }}
                                            >
                                                <div className="flex items-center gap-1">
                                                    {/* <LuContact className="text-slate-500" /> */}
                                                    <LuMails className="text-slate-500" />

                                                    <span className="text-xs ms-1">
                                                        Manually Import Email
                                                    </span>
                                                </div>
                                            </DropdownItem>
                                        </Dropdown>
                                    }
                                </div>

                                {
                                    collection.can_edit &&
                                    <Button
                                        download={true}
                                        as={'a'}
                                        href={route('contactList.exportcsv', { id: contactList.id })}
                                        size="xs"
                                        color="gray"
                                        theme={button_custom}
                                        isProcessing={isExportDocumentLoading}
                                        onClick={() => {
                                            setIsExportDocumentLoading(true)
                                            setTimeout(() => {
                                                setIsExportDocumentLoading(false)
                                            }, 3000)
                                        }
                                        }

                                    >
                                        <div className="flex items-center gap-1">
                                            <PiExport className="text-slate-500" />
                                            <span className="text-xs">Export</span>
                                        </div>
                                    </Button>
                                }
                            </ButtonGroup>
                            <ButtonGroup>
                                {
                                    collection.can_edit &&
                                    <>
                                        <Button
                                            theme={button_custom}
                                            color="gray"
                                            size="xs"
                                            className="text-nowrap"
                                            onClick={() => setOpenModal(true)}
                                        >
                                            <div className="flex items-center gap-1">
                                                <PiColumnsPlusRight className="text-slate-500" />
                                                <span className="text-xs ms-1">
                                                    Append Field
                                                </span>
                                            </div>
                                        </Button>

                                        {/* <Button
                                            theme={button_custom}
                                            color="gray"
                                            size="xs"
                                            className="text-nowrap"
                                            onClick={() => {
                                                setIsCountryCodeModalOpen(true);
                                            }}
                                        >
                                            <div className="flex items-center gap-1">
                                                <PiGlobeHemisphereEastLight className="text-slate-500" />
                                                <span className="text-xs ms-1">
                                                    Add Country Code
                                                </span>
                                            </div>
                                        </Button> */}

                                        <Dropdown
                                            arrowIcon={true}
                                            size="xs"
                                            color="gray"
                                            theme={
                                                {
                                                    "floating": {

                                                        "target": "w-fit rounded-s-none rounded border-s-0 text-nowrap p-0"
                                                    }
                                                }
                                            }
                                            isProcessing={isRemoveDuplicateLoading}
                                            label={
                                                <div className="flex items-center gap-1">
                                                    <IoMdClose className="text-slate-500" />
                                                    <span className="text-xs ms-1">
                                                        Remove Duplicates
                                                    </span>
                                                </div>
                                            }
                                        >
                                            <DropdownItem
                                                onClick={() => removeDuplicates(contactList.id, 'email')}
                                            >
                                                <div className="flex gap-1">
                                                    <FaRegRectangleList className="text-slate-500" />
                                                    <span className="text-xs ms-1">
                                                        Emails
                                                    </span>
                                                </div>
                                            </DropdownItem>

                                            <DropdownItem
                                                onClick={() => removeDuplicates(contactList.id, 'mobile')}
                                            >
                                                <div className="flex items-center gap-1">
                                                    {/* <LuContact className="text-slate-500" /> */}
                                                    <PiPhoneListThin className="text-slate-500" />

                                                    <span className="text-xs ms-1">
                                                        Mobile Number
                                                    </span>
                                                </div>
                                            </DropdownItem>

                                        </Dropdown>
                                    </>
                                }
                            </ButtonGroup>
                        </div>
                        <div className="flex gap-5">
                            {/* contactList action btns */}

                            {/* add and columns dropdown  */}
                            <div className="">
                                {
                                    collection.can_add &&
                                    <Button
                                        className="border pe-1"
                                        size="xs"
                                        color="gray"
                                        theme={button_custom}
                                        onClick={() => setIsAddContact(true)}
                                    >
                                        <div className="flex items-center gap-1">
                                            <IoMdAdd className="text-slate-500" />
                                            <span className="text-xs">Add</span>
                                        </div>
                                    </Button>
                                }

                            </div>
                        </div>
                    </div>
                    <div className="overflow-auto bg-white border-b-0 rounded-lg rounded-b-none h-ull">
                        {/* documents table */}
                        <div className="overflow-x-auto bg-white border rounded-lg">
                            <Table hoverable theme={table_custom}>
                                <Table.Head className=" bg-slate-100">
                                    <Table.HeadCell>
                                        <Checkbox
                                            color="blue"
                                            checked={isCheckAll}
                                            id={0}
                                            onChange={(e) => {
                                                headerCheckBoxChecked(e);
                                            }}
                                        />
                                    </Table.HeadCell>

                                    <Table.HeadCell>
                                        <Link
                                            href={route("contactList.show", {
                                                column: "name",
                                                sort:
                                                    getData.sort == "asc"
                                                        ? "desc"
                                                        : "asc",
                                                contact: contactList.id,
                                            })}
                                        >
                                            <div className="flex items-center justify-between">
                                                <span>Name</span>
                                                <svg
                                                    xmlns="http://www.w3.org/2000/svg"
                                                    height="16px"
                                                    viewBox="0 0 24 24"
                                                    width="16px"
                                                    className="fill-gray-600"
                                                >
                                                    <path
                                                        d="M0 0h24v24H0V0z"
                                                        fill="none"
                                                    />
                                                    <path d="M12 5.83L15.17 9l1.41-1.41L12 3 7.41 7.59 8.83 9 12 5.83zm0 12.34L8.83 15l-1.41 1.41L12 21l4.59-4.59L15.17 15 12 18.17z" />
                                                </svg>
                                            </div>
                                        </Link>
                                    </Table.HeadCell>
                                    <Table.HeadCell>Mobile</Table.HeadCell>
                                    <Table.HeadCell>Email</Table.HeadCell>
                                    <Table.HeadCell>Var 1</Table.HeadCell>
                                    <Table.HeadCell>Var 2</Table.HeadCell>
                                    <Table.HeadCell>Var 3</Table.HeadCell>
                                    <Table.HeadCell>Var 4</Table.HeadCell>
                                    <Table.HeadCell>Var 5</Table.HeadCell>
                                    <Table.HeadCell>Actions</Table.HeadCell>
                                </Table.Head>

                                <Table.Body className="divide-y text-nowrap">
                                    {contacts.data.length > 0 ? (
                                        contacts.data.map((el, key) => {
                                            return (
                                                <Table.Row
                                                    className="bg-white dark:border-gray-700 dark:bg-gray-800"
                                                    key={key}
                                                >
                                                    <Table.Cell>
                                                        <Checkbox
                                                            color={"blue"}
                                                            className="rowCheckBox"
                                                            id={el.id}
                                                            onChange={(e) => {
                                                                getCheckedIds(
                                                                    e
                                                                );
                                                                checkAddcheckBoxChecked();
                                                            }}
                                                        />
                                                    </Table.Cell>
                                                    <Table.Cell className="font-medium text-gray-900 whitespace-nowrap dark:text-white">
                                                        {el.name ?? "-"}
                                                    </Table.Cell>
                                                    <Table.Cell>
                                                        {el.mobile ?? "-"}
                                                    </Table.Cell>
                                                    <Table.Cell>
                                                        {el.email ?? "-"}
                                                    </Table.Cell>
                                                    <Table.Cell>
                                                        {el.var1 ?? "-"}
                                                    </Table.Cell>
                                                    <Table.Cell>
                                                        {el.var2 ?? "-"}
                                                    </Table.Cell>
                                                    <Table.Cell>
                                                        {el.var3 ?? "-"}
                                                    </Table.Cell>
                                                    <Table.Cell>
                                                        {el.var4 ?? "-"}
                                                    </Table.Cell>
                                                    <Table.Cell>
                                                        {el.var5 ?? "-"}
                                                    </Table.Cell>
                                                    <Table.Cell>
                                                        <div className="flex items-center gap-2">
                                                            {
                                                                collection.can_edit &&
                                                                <Button
                                                                    onClick={() => {
                                                                        setEditContactDrawer(
                                                                            true
                                                                        );
                                                                        setEditContactDrawerData(
                                                                            {
                                                                                name: el.name,
                                                                                id: el.id,
                                                                                mobile: el.mobile,
                                                                                email: el.email,
                                                                                contactListName:
                                                                                    contactList.name,
                                                                                var1: el.var1,
                                                                                var2: el.var2,
                                                                                var3: el.var3,
                                                                                var4: el.var4,
                                                                                var5: el.var5,
                                                                            }
                                                                        );
                                                                    }}
                                                                    theme={
                                                                        button_custom
                                                                    }
                                                                    color="success"
                                                                    size="xs"
                                                                >
                                                                    <MdOutlineEdit className="text-xs" />
                                                                    <span className="text-xs ms-1">
                                                                        Edit
                                                                    </span>
                                                                </Button>
                                                            }
                                                        </div>
                                                    </Table.Cell>
                                                </Table.Row>
                                            );
                                        })
                                    ) : (
                                        <Table.Row className="bg-white dark:border-gray-700 dark:bg-gray-800">
                                            <Table.Cell
                                                colSpan={11}
                                                className="text-center"
                                            >
                                                <NoRecord />
                                            </Table.Cell>
                                        </Table.Row>
                                    )}
                                </Table.Body>
                            </Table>
                        </div>
                    </div>
                    <div className="bottom-0 w-full p-3 bg-white rounded-lg float-end dark:border-gray-500 dark:rounded-lg dark:bg-gray-700">
                        <div className="flex flex-wrap gap-2 lg:justify-between lg:gap-0 md:gap-0">
                            <div className="flex items-center gap-3">
                                <div className="flex items-center gap-4">
                                    <PerPageDropdown
                                        getDataFields={getData ?? null}
                                        routeName={"contactList.show"}
                                        id={contactList.id}
                                        idName={"contact"}
                                        data={contacts}
                                    />
                                </div>
                            </div>
                            <Paginate tableData={contacts} />
                        </div>
                    </div>
                </div>
                {isAddContact && (
                    <Drawer
                        theme={customDrawer}
                        className="w-full lg:w-2/5 md:w-3/5"
                        open={isAddContact}
                        onClose={() => setIsAddContact(false)}
                        position="right"
                    >
                        <Drawer.Header
                            titleIcon={HiOutlineUsers}
                            title={
                                <span>Add Contact ({contactList.name})</span>
                            }
                        />
                        <Drawer.Items className="py-2">
                            <AddContacts
                                contactListData={contactList.id}
                                onClose={() => setIsAddContact(false)}
                            ></AddContacts>
                        </Drawer.Items>
                    </Drawer>
                )}
                {editContactDrawer && (
                    <Drawer
                        theme={customDrawer}
                        className="w-full lg:w-2/5 md:w-3/5"
                        open={editContactDrawer}
                        onClose={() => setEditContactDrawer(false)}
                        position="right"
                    >
                        <Drawer.Header
                            titleIcon={HiOutlineUsers}
                            title={
                                <span>Edit Contact ({contactList.name})</span>
                            }
                        />
                        <Drawer.Items className="py-2">
                            <EditContact
                                onClose={() => setEditContactDrawer(false)}
                                editContactDrawerData={editContactDrawerData}
                            ></EditContact>
                        </Drawer.Items>
                    </Drawer>
                )}

                {isImportManually === true ? (
                    <Drawer
                        theme={customDrawerIconEditContact}
                        className="w-full lg:w-2/5 md:w-3/5"
                        open={isImportManually}
                        onClose={() => setImportManually(false)}
                        position="right"
                    >
                        <Drawer.Header
                            titleIcon={CgImport}
                            title={
                                <span>Manual Import Numbers&nbsp;({contactList.name})</span>
                            }
                        />
                        <Drawer.Items className="px-4 py-2">
                            <ImportManually
                                contactListId={contactListDrawerData}
                                onClose={() => setImportManually(false)}
                            ></ImportManually>
                        </Drawer.Items>
                    </Drawer>
                ) : (
                    <></>
                )}


                {isImportManuallyEmail === true ? (
                    <Drawer
                        theme={customDrawerIconEditContact}
                        className="w-full lg:w-2/5 md:w-3/5"
                        open={isImportManuallyEmail}
                        onClose={() => setIsImportManuallyEmail(false)}
                        position="right"
                    >
                        <Drawer.Header
                            titleIcon={CgImport}
                            title={
                                <span>Manual Import Emails&nbsp;({contactList.name})</span>
                            }
                        />
                        <Drawer.Items className="px-4 py-2">
                            <ImportManuallyEmail
                                contactListId={contactListDrawerData}
                                onClose={() => setIsImportManuallyEmail(false)}
                            ></ImportManuallyEmail>
                        </Drawer.Items>
                    </Drawer>
                ) : (
                    <></>
                )}
                {/*---------------add country code list drawer---------------*/}
                {/* {editContactList === true ? (
                    <Drawer
                        open={editContactList}
                        onClose={() => setEditContactList(false)}
                        position="right"
                        theme={customDrawer}
                        className="w-full lg:w-2/5 md:w-3/5"
                    >
                        <Drawer.Header
                            title={
                                <span>
                                    Add Country Code ({contactList.name})
                                </span>
                            }
                            titleIcon={IoMdGlobe}
                        />
                        <Drawer.Items className="px-4 py-2 mt-4 bg-white">
                            <Edit
                                onClose={() => setEditContactList(false)}
                                contactListData={contactListDrawerData}
                            />
                        </Drawer.Items>
                    </Drawer>
                ) : (
                    <></>
                )} */}
                {/* ---------------import CSV drawer--------------- */}
                {importCSV === true ? (
                    <Drawer
                        theme={customDrawer}
                        className="w-full lg:w-2/5 md:w-3/5"
                        open={importCSV}
                        onClose={() => setimportCSV(false)}
                        position="right"
                    >
                        <Drawer.Header
                            titleIcon={TbFileTypeCsv}
                            title={<span>Import CSV ({contactList.name})</span>}
                        />
                        <Drawer.Items className="px-4 py-2 mt-4 bg-white">
                            <ImportCSV
                                contactListId={contactListDrawerData}
                                onClose={() => setimportCSV(false)}
                            ></ImportCSV>
                        </Drawer.Items>
                    </Drawer>
                ) : (
                    <></>
                )}
                {
                    openModal &&
                    <Modal show={openModal} onClose={() => setOpenModal(false)}>
                        <Modal.Header className="p-2">
                            <div className="flex items-center gap-2">
                                <PiColumnsPlusRight className="text-slate-500" />
                                <h4 className="text-lg">Append Field</h4>
                            </div>
                        </Modal.Header>
                        <Modal.Body className="px-4 py-3 rounded-b-lg bg-slate-100">
                            <AppendField onClose={() => setOpenModal(false)} contactListId={contactList.id} ></AppendField>
                        </Modal.Body>
                    </Modal>
                }
                {
                    isCountryCodeModalOpen &&
                    <Modal
                        show={
                            isCountryCodeModalOpen
                        }
                        onClose={() => setIsCountryCodeModalOpen(false)}
                    >
                        <Modal.Header className="p-2">
                            <div className="flex items-center gap-2">
                                <MdOutlineEdit />
                                <h4 className="text-lg">
                                    Add Country Code
                                </h4>
                            </div>
                        </Modal.Header>
                        <Modal.Body className="px-4 py-3 rounded-b-lg bg-slate-100">
                            <AddCountryCode onClose={() => setIsCountryCodeModalOpen(false)} contactListId={collection.contactList.id} />
                        </Modal.Body>
                    </Modal>

                }
                {/* confirm box popup */}
                {isConfirmOpen && (
                    <ConfirmBox
                        isOpen={isConfirmOpen}
                        onClose={() => setConfirmOpen(false)} // Close the confirm box
                        onAction={handleConfirmBoxResult} // Handle the user's choice
                        title="Are you sure you want to delete this?"
                        message="This action cannot be undone."
                        confirmText="Yes, Delete!"
                        cancelText="No, Keep It"
                        confirmColor="orange"
                        cancelColor="gray"
                        icon={<FaRegTrashCan />}
                    />
                )}
            </div>
        </Main >
    );
}


