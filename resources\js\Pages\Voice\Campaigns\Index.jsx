import { Link } from "@inertiajs/react";
import {
    <PERSON><PERSON>,
    Button,
    ButtonGroup,
    Checkbox,
    Drawer,
    Progress,
    Table,
} from "flowbite-react";
import { useState } from "react";
import { FiPlay } from "react-icons/fi";
import { IoMdAdd } from "react-icons/io";
import { IoPauseOutline } from "react-icons/io5";
import {
    MdDeleteOutline,
    MdFilterList,
    MdOutlineCampaign,
    MdOutlineIndeterminateCheckBox,
} from "react-icons/md";
import { PiCaretUpDownBold } from "react-icons/pi";
import { TbColumns3 } from "react-icons/tb";
import {
    button_custom,
    customDrawer,
    table_custom
} from "../../Helpers/DesignHelper";
import MailMain from "../MailMain";
import Add from "./Add";

export default function Campaign() {

    const [modalState, setModalState] = useState({ openAdd: false, openEdit: false, openView: false, selected: null });
    return (
        <MailMain>

            <div className="px-2 pb-2 rounded-lg">
                <div className="h-fit bg-white rounded border overflow-auto w-full mt-2.5">
                    <div className="flex justify-between p-2">
                        <ButtonGroup className="">
                            <Button
                                className="border rounded"
                                size="xs"
                                color="gray"
                                theme={button_custom}
                            >
                                <div className="flex items-center gap-1">
                                    <MdDeleteOutline className="text-slate-500" />
                                    <span className="text-xs">Delete</span>
                                </div>
                            </Button>
                            <Button
                                theme={button_custom}
                                size="xs"
                                color="gray"
                                id="dropdownInformationButton"
                                data-dropdown-toggle="dropdownNotification"
                                type="button"
                            >
                                <div className="flex items-center gap-1 text-xs">
                                    <TbColumns3 className="text-slate-500 ms-1" />
                                    <span className="text-xs">Columns</span>
                                </div>
                            </Button>
                        </ButtonGroup>
                        <div className="">
                            <Button
                                className="pe-1"
                                size="xs"
                                color="gray"
                                theme={button_custom}
                                onClick={() => setModalState({ openAdd: !modalState.openAdd })}
                            >
                                <div className="flex items-center gap-1">
                                    <IoMdAdd className="text-slate-500" />
                                    <span className="text-xs">Add</span>
                                </div>
                            </Button>

                            {/* Dropdown menu */}

                        </div>
                    </div>
                    <div className="h-full">
                        {/* documents table */}
                        <div className="overflow-x-auto bg-white border rounded-lg text-nowrap">
                            <Table hoverable theme={table_custom}>
                                <Table.Head className="bg-slate-100">
                                    <Table.HeadCell>
                                        <Checkbox color={"blue"} />
                                    </Table.HeadCell>
                                    <Table.HeadCell>
                                        <Link
                                        >
                                            <div className="flex items-center justify-between gap-2">
                                                <h3>Id</h3>
                                                <PiCaretUpDownBold
                                                />
                                            </div>
                                        </Link>
                                    </Table.HeadCell>
                                    <Table.HeadCell>
                                        <Link
                                        >
                                            <div className="flex items-center justify-between gap-2">
                                                <h3>Campaign</h3>
                                                <PiCaretUpDownBold
                                                />
                                            </div>
                                        </Link>
                                    </Table.HeadCell>
                                    <Table.HeadCell>
                                        Progress
                                    </Table.HeadCell>

                                    <Table.HeadCell>
                                        <Link
                                        >
                                            <div className="flex items-center justify-between gap-2">
                                                <h3>Status</h3>
                                                <PiCaretUpDownBold
                                                />
                                            </div>
                                        </Link>
                                    </Table.HeadCell>
                                    <Table.HeadCell>
                                        <Link
                                        >
                                            <div className="flex items-center justify-between gap-2">
                                                <h3>Start Time</h3>
                                                <PiCaretUpDownBold
                                                />
                                            </div>
                                        </Link>
                                    </Table.HeadCell>
                                    <Table.HeadCell>
                                        <Link
                                        >
                                            <div className="flex items-center justify-between gap-2">
                                                <h3>Sleep After</h3>
                                                <PiCaretUpDownBold
                                                />
                                            </div>
                                        </Link>
                                    </Table.HeadCell>
                                    <Table.HeadCell>
                                        <Link
                                        >
                                            <div className="flex items-center justify-between gap-2">
                                                <h3>Sleep For</h3>
                                                <PiCaretUpDownBold
                                                />
                                            </div>
                                        </Link>
                                    </Table.HeadCell>
                                    <Table.HeadCell>
                                        <div className="flex items-center justify-between gap-2">
                                            <h3>User</h3>
                                            <MdFilterList />
                                        </div>
                                    </Table.HeadCell>
                                    <Table.HeadCell>
                                        <h3>Actions</h3>
                                    </Table.HeadCell>
                                </Table.Head>
                                <Table.Body className="divide-y">
                                    <Table.Row
                                        className="items-center bg-white dark:border-gray-700 dark:bg-gray-800"
                                    >
                                        <Table.Cell>
                                            <Checkbox color={"blue"} />
                                        </Table.Cell>
                                        <Table.Cell
                                            className="font-medium text-gray-900 cursor-pointer whitespace-nowrap dark:text-white"
                                        >
                                            73
                                            {/* #{campaign.id} */}
                                        </Table.Cell>
                                        <Table.Cell
                                            onClick={() => {
                                                setIsOpenDetail(true);
                                                setCampaignID({
                                                    id: "73",
                                                    name: "Dana Gates",
                                                    status: "Terminated",
                                                });
                                            }}
                                            className="cursor-pointer "
                                        >
                                            Dana Gates
                                            {/* {campaign.name} */}
                                        </Table.Cell>
                                        <Table.Cell>
                                            <div className="flex flex-col gap-1 py-1">
                                                <Progress
                                                    color="blue"
                                                />
                                                <span className="">
                                                    {/* {
                                                            campaign.completedContacts
                                                        }
                                                        &nbsp;Out of&nbsp;
                                                        {campaign.totalContacts} */}
                                                    0 Out of 450501
                                                    Completed
                                                </span>
                                            </div>
                                        </Table.Cell>
                                        <Table.Cell>
                                            <div className="flex">
                                                <Badge
                                                    className="cursor-default"
                                                >
                                                    Terminated
                                                    {
                                                    }
                                                </Badge>
                                            </div>
                                        </Table.Cell>
                                        <Table.Cell>
                                            11/1/2024 5:21
                                            {/* {convertDateFormat(
                                                    campaign.startTime
                                                )} */}
                                        </Table.Cell>
                                        <Table.Cell>
                                            41
                                            {/* {campaign.sleepAfterMsgs} */}
                                        </Table.Cell>
                                        <Table.Cell>
                                            26
                                            {/* {campaign.sleepForSeconds} */}
                                        </Table.Cell>
                                        <Table.Cell>
                                            flslsd
                                            {/* {campaign.user.username} */}
                                        </Table.Cell>
                                        <Table.Cell>
                                            <div className="relative flex items-center gap-2">
                                                <Button
                                                    theme={button_custom}
                                                    color="success"
                                                    size="xs"
                                                >
                                                    <IoPauseOutline className="text-sm" />
                                                    <span className="text-xs ms-1">
                                                        Pause
                                                    </span>
                                                </Button>

                                                <Button
                                                    theme={button_custom}
                                                    color="orange"
                                                    size="xs"
                                                >
                                                    <FiPlay className="text-sm" />
                                                    <span className="text-xs ms-1">
                                                        Play
                                                    </span>
                                                </Button>
                                                <Button
                                                    theme={button_custom}
                                                    color="failure"
                                                    size="xs"
                                                >
                                                    <MdOutlineIndeterminateCheckBox className="text-sm" />
                                                    <span className="text-xs ms-1">
                                                        Stop
                                                    </span>
                                                </Button>
                                            </div>
                                        </Table.Cell>
                                    </Table.Row>
                                </Table.Body>
                            </Table>
                        </div>
                    </div>

                    <div className="bottom-0 w-full p-3 bg-white border rounded-b-lg float-end">
                        <div className="flex flex-wrap justify-between gap-2 lg:gap-0 md:gap-0">
                            <div className="flex items-center gap-3">
                                <div className="text-sm text-gray-400 ">
                                    Showing 1 to 25 of 62 entries
                                </div>
                                <div>
                                    <select
                                        id="countries"
                                        className="block p-1 text-xs text-gray-900 border border-gray-300 rounded bg-gray-50 focus:ring-blue-500 focus:border-blue-500 ps-2 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-blue-500 dark:focus:border-blue-500"
                                    >
                                        <option
                                            value={10}
                                            defaultValue={10}
                                        >
                                            10
                                        </option>
                                        <option value={15}>15</option>
                                        <option value={20}>20</option>
                                        <option value={25}>25</option>
                                        <option value={30}>30</option>
                                    </select>
                                </div>
                            </div>

                        </div>
                    </div>
                </div>
            </div>

            {modalState.openAdd ? (
                <Drawer
                    theme={customDrawer}
                    className="w-full lg:w-4/6 md:w-4/5"
                    open={modalState.openAdd}

                    onClose={() => setModalState({ openAdd: !modalState.openAdd })}
                    position="right"
                >
                    <DrawerHeader
                        titleIcon={MdOutlineCampaign}
                        title={<span>Add Campaign</span>}
                    />
                    <DrawerItems className="py-2">
                        <Add onClose={() => setModalState({ openAdd: !modalState.openAdd })} />
                    </DrawerItems>
                </Drawer>
            ) : (
                <></>
            )}
        </MailMain>
    );
}
