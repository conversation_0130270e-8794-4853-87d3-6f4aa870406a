import { Link, usePage } from "@inertiajs/react";
import React from "react";
import { useState } from "react";
import { LuListFilter } from "react-icons/lu";
import { IoSettingsOutline } from "react-icons/io5";
import { ImWhatsapp } from "react-icons/im";
import { PiPlugsConnected } from "react-icons/pi";
import { MdAutorenew } from "react-icons/md";
import { TbMessage2Down } from "react-icons/tb";
import SideMenu from "./SideMenu";

export function TabBar() {
    const customTheme = {
        root: {
            position: {
                right: {
                    on: "right-0 top-0 h-screen w-[70vw] transform-none bg-slate-100",
                    off: "right-0 top-0 h-screen w-80 translate-x-full",
                },
            },
        },
        header: {
            inner: {
                closeButton:
                    "absolute end-2.5 top-2.5 flex h-8 w-8 items-center justify-center rounded-lg bg-transparent text-sm text-gray-400 hover:bg-gray-200 hover:text-gray-900 dark:hover:bg-gray-600 dark:hover:text-white",
                closeIcon: "h-4 w-4",
                titleIcon: "me-2.5 h-6 w-6 text-gray-400",
                titleText:
                    "mb-4 inline-flex items-center text-lg font-semibold text-sky-950 dark:text-gray-400",
            },
        },
    };
    const button_custom = {
        pill: {
            off: "rounded",
            on: "rounded-full",
        },
        size: {
            xs: "p-0.5 text-lg",
            sm: "text-[28px]",
            md: "px-4 py-2 text-sm",
            lg: "px-5 py-2.5 text-base",
            xl: "px-6 py-3 text-base",
        },
    };
    const dropdown_custom = {
        arrowIcon: "ml-2 h-4 w-4",
        content: "focus:outline-none",
        floating: {
            animation: "transition-opacity",
            arrow: {
                base: "absolute z-10 h-2 w-2 rotate-45",
                style: {
                    dark: "bg-gray-900 dark:bg-gray-700",
                    light: "bg-white",
                    auto: "bg-white dark:bg-gray-700",
                },
                placement: "-4px",
            },
            base: "z-10 w-fit divide-y divide-gray-100 rounded shadow focus:outline-none",
            content: "py-1 text-sm text-gray-700 dark:text-gray-200",
            divider: "my-1 h-px bg-gray-100 dark:bg-gray-600",
            header: "block text-sm text-gray-700 dark:text-gray-200",
            hidden: "invisible opacity-0",
            item: {
                container: "",
                base: "flex w-full cursor-pointer items-center justify-start text-sm text-gray-700 hover:bg-gray-100 focus:bg-gray-100 focus:outline-none dark:text-gray-200 dark:hover:bg-gray-600 dark:hover:text-white dark:focus:bg-gray-600 dark:focus:text-white",
                icon: "mr-2 h-4 w-4",
            },
            style: {
                dark: "bg-gray-900 text-white dark:bg-gray-700",
                light: "border border-gray-200 bg-white text-gray-900",
                auto: "border border-gray-200 bg-white text-gray-900 dark:border-none dark:bg-gray-700 dark:text-white",
            },
            target: "w-fit",
        },
        inlineWrapper: "flex items-center",
    };

    const iconClass = "flex-shrink-0 w-12 px-3 py-0 text-gray-500 transition duration-75 h-9 rounded-xl hover:text-white group-hover:bg-blue-600 group-hover:text-white dark:text-gray-400 dark:group-hover:text-white";
    const ActiveIconClass = " bg-blue-600 text-white ";

    let menuItems = [
        // {
        //     name: 'General',
        //     icon: <IoSettingsOutline
        //         // className={`text-lg ${
        //         //     route().current(
        //         //         "Integration.whatsapp.whatsappweb.index"
        //         //     )
        //         //         ? "text-white"
        //         //         : "text-slate-400"
        //         // }`}
        //         className={"text-lg text-slate-400"}
        //     />,
        //     order: 1, routeName: "Integration.index"
        // },
        {
            name: 'WhatsApp API',
            icon: <ImWhatsapp
                className={`text-lg ${
                    route().current(
                        "Integration.whatsapp.whatsappapi"
                    )
                        ? "text-white"
                        : "text-slate-400"
                }`}
            />,
            order: 2, url: route('Integration.whatsapp.whatsappapi'), routeName: "Integration.whatsapp.whatsappapi"
        },
        {
            name: 'WhatsApp Web',
            icon: <ImWhatsapp
                className={`text-lg flex ${route().current(
                    "Integration.whatsapp.whatsappweb.index"
                )
                    ? "text-white"
                    : "text-slate-400"
                    }`}
            />,
            order: 3, url: route('Integration.whatsapp.whatsappweb.index'), routeName: "Integration.whatsapp.whatsappweb.index"
        }
    ].sort((a, b) => a.order - b.order);



    const [isOpen, setIsOpen] = useState(false);

    return (
        <div className="flex justify-center w-full gap-2">
            {/* side Menu */}
            <div className="lg:hidden">
                <div className="relative inline-block text-left">
                    {/* Dropdown Button */}
                    <button
                        onClick={() => setIsOpen(!isOpen)}
                        className="inline-flex justify-center w-full p-2 text-sm font-medium text-white bg-blue-700 rounded-md shadow-sm"
                    >
                        <LuListFilter className="text-2xl" />
                    </button>

                    {/* Dropdown Menu */}
                    {isOpen && (
                        <div className="absolute z-50 w-56 mt-2 origin-top-right bg-white rounded-md shadow-lg ring-1 ring-black ring-opacity-5">
                            <div className="bg-white shadow-sm rounded-lg border dark:bg-gray-900 w-full max-h-[calc(100vh-12rem)] overflow-y-auto">
                                <SideMenu/>
                            </div>
                        </div>
                    )}
                </div>
            </div>

            <div className="flex items-center justify-between w-full gap-2 pb-0 overflow-auto bg-white rounded-lg">
                <div className="flex text-nowrap">
                    <Link
                        href={route("Integration.whatsapp.index")}
                        className={
                            `p-2 flex text-black ${(route().current("Integration.whatsapp.index")
                                ? "bg-blue-700 rounded-s-lg text-white"
                                : "bg-white")}`

                        }
                    >
                        <div className="flex px-2">
                            <span className="self-center me-1">
                                <IoSettingsOutline
                                    className={`text-lg ${
                                        route().current(
                                            "Integration.whatsapp.index"
                                        )
                                            ? "text-white"
                                            : "text-slate-400"
                                    }`}
                                />
                            </span>
                            <div className="self-center text-sm">General</div>
                        </div>
                    </Link> 

                   

                    {/*-------------WhatApp Web----------------*/}
                    {
                        menuItems.map((menuObject, key) => {
                            return (
                                <Link
                                    href={(menuObject.routeName != "") ? menuObject.url : "#"}
                                    className={`p-2.5 flex text-black ${route().current(
                                        menuObject.routeName
                                    )
                                        ? "bg-blue-700 text-white flex"
                                        : "bg-white flex"
                                        }`}
                                >
                                    <div className="flex px-2">
                                        <span className="self-center me-1">
                                            {menuObject.icon}
                                        </span>
                                        <div className="self-center text-sm">
                                            {menuObject.name}
                                        </div>
                                    </div>
                                </Link>
                            )
                        })
                    }


                    {/*-------------API Log----------------*/}
                    {/* <Link
                        href={route("Integration.whatsapp.log.api")}
                        className={`p-2.5 flex text-black ${
                            route().current("Integration.whatsapp.log.api")
                                ? "bg-blue-700 text-white flex"
                                : "bg-white flex"
                        }`}
                    >
                        <div className="flex px-2">
                            <span className="self-center me-1">
                                <PiPlugsConnected
                                    className={`text-lg flex ${
                                        route().current(
                                            "Integration.whatsapp.log.api"
                                        )
                                            ? "text-white"
                                            : "text-slate-400"
                                    }`}
                                />
                            </span>
                            <div className="self-center text-sm">API Log</div>
                        </div>
                    </Link> */}

                    {/*-------------Autoreply Log----------------*/}
                    {/* <Link
                        href={route("Integration.whatsapp.log.autoreply")}
                        className={`p-2.5 flex text-black ${
                            route().current(
                                "Integration.whatsapp.log.autoreply"
                            )
                                ? "bg-blue-700 text-white flex"
                                : "bg-white flex"
                        }`}
                    >
                        <div className="flex px-2">
                            <span className="self-center me-1">
                                <MdAutorenew
                                    className={`text-lg flex ${
                                        route().current(
                                            "Integration.whatsapp.log.autoreply"
                                        )
                                            ? "text-white"
                                            : "text-slate-400"
                                    }`}
                                />
                            </span>
                            <div className="self-center text-sm">
                                Autoreply Log
                            </div>
                        </div>
                    </Link> */}

                    {/*-------------Incomming message Log----------------*/}
                    {/* <Link
                        href={route(
                            "Integration.whatsapp.log.incomingMessages"
                        )}
                        className={`p-2.5 flex text-black ${
                            route().current(
                                "Integration.whatsapp.log.incomingMessages*"
                            )
                                ? "bg-blue-700 text-white flex"
                                : "bg-white flex"
                        }`}
                    >
                        <div className="flex px-2">
                            <span className="self-center me-1">
                                <TbMessage2Down
                                    className={`text-lg flex ${
                                        route().current(
                                            "Integration.whatsapp.log.incomingMessages*"
                                        )
                                            ? "text-white"
                                            : "text-slate-400"
                                    }`}
                                />
                            </span>
                            <div className="self-center text-sm">
                                Incoming Messages
                            </div>
                        </div>
                    </Link> */}

                    {/* github tmp code  */}
                    {/* {addButtonRoutes != undefined ? (
                        <div className="m-1.5">
                            <Button
                                color="blue"
                                theme={button_custom}
                                size="xs"
                                onClick={() => setAddButtonDrawer(true)}
                            >
                                <IoMdAdd />
                            </Button>
                            {addButtonDrawer ? (
                                <Drawer
                                    theme={customDrawer}
                                    className={"w-auto " + addButtonRoutes.size}
                                    open={addButtonDrawer}
                                    onClose={() => setAddButtonDrawer(false)}
                                    position="right"
                                >
                                    <DrawerHeader
                                        titleIcon={addButtonRoutes["titleIcon"]}
                                        title={addButtonRoutes["title"]}
                                    />
                                    <DrawerItems className="py-2">
                                        {addButtonRoutes["component"]}
                                    </DrawerItems>
                                </Drawer>
                            ) : (
                                <></>
                            )}
                        </div>
                    ) : (
                        ""
                    )} */}
                </div>
            </div>
        </div>
    );
}

export default TabBar;



