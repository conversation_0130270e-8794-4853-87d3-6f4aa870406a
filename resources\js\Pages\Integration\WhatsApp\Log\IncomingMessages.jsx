import React, { useEffect } from "react";
import {
    Badge,
    Button,
    ButtonGroup,
    Checkbox,
    Table,
    ToggleSwitch,
    Tooltip,
} from "flowbite-react";
import { useState } from "react";
import { FaEye } from "react-icons/fa";
import { IoQrCode } from "react-icons/io5";

import {
    MdOutlineEdit,
} from "react-icons/md";
import { TbColumns3 } from "react-icons/tb";
import Main from "../Main";
import $ from "jquery";
import {
    button_custom,
    buttongroup_custom,
    table_custom,
    toggle_custom,
} from "@/Pages/Helpers/DesignHelper";
import { IoMdAdd } from "react-icons/io";
import { PiCaretUpDownBold } from "react-icons/pi";
// import QRCode from "./QRCode";

export default function Index() {
    const [isCheckAll, setIsCheckAll] = useState(false);

    useEffect(() => {
        if (isCheckAll) {
            $(".rowCheckBox").prop("checked", true);
        } else {
            $(".rowCheckBox").prop("checked", false);
        }
    }, [isCheckAll]);

    return (
        <Main>
            <div className="w-full mt-2 overflow-auto bg-white border rounded h-fit">
                {/* -----------Table----------- */}
                <div className="flex justify-between p-2">
                    <div className="flex "></div>
                    <ButtonGroup theme={buttongroup_custom} outline>
                        <Button
                            className="border pe-1"
                            size="xs"
                            color="gray"
                            theme={button_custom}
                        >
                            <div className="flex items-center gap-1">
                                <IoMdAdd className="text-slate-500" />
                                <span className="text-xs">Add</span>
                            </div>
                        </Button>
                        <Button
                            theme={button_custom}
                            size="xs"
                            color="gray"
                            id="dropdownInformationButton"
                            data-dropdown-toggle="dropdownNotification"
                            type="button"
                        >
                            <div className="flex items-center gap-1 text-xs ps-1">
                                <TbColumns3 className="text-slate-500" />
                                Columns
                            </div>
                        </Button>
                    </ButtonGroup>
                    {/* Dropdown menu */}
                    <div
                        id="dropdownNotification"
                        className="z-20 hidden w-full max-w-sm bg-white border divide-y divide-gray-100 rounded-lg shadow-lg dark:bg-gray-800 dark:divide-gray-700"
                        aria-labelledby="dropdownNotificationButton"
                    >
                        <div className="block px-4 py-2 font-medium text-center text-gray-700 rounded-t-lg bg-gray-50 dark:bg-gray-800 dark:text-white">
                            <div className="flex justify-between">
                                <div className="">
                                    <div className="flex items-center gap-1 p-1 text-gray-400 group">
                                        <svg
                                            xmlns="http://www.w3.org/2000/svg"
                                            className=" fill-slate-400"
                                            height="24px"
                                            viewBox="0 0 24 24"
                                            width="24px"
                                        >
                                            <rect
                                                fill="none"
                                                height="24"
                                                width="24"
                                            />
                                            <path d="M20,4H4C2.9,4,2,4.9,2,6v12c0,1.1,0.9,2,2,2h16c1.1,0,2-0.9,2-2V6C22,4.9,21.1,4,20,4z M8,18H4V6h4V18z M14,18h-4V6h4V18z M20,18h-4V6h4V18z" />
                                        </svg>
                                        Column
                                    </div>
                                </div>
                                <div className="">
                                    <button className="flex p-1 text-blue-600">
                                        <svg
                                            xmlns="http://www.w3.org/2000/svg"
                                            height="24px"
                                            viewBox="0 0 24 24"
                                            width="24px"
                                            className="fill-blue-600"
                                        >
                                            <g>
                                                <path
                                                    d="M0,0h24v24H0V0z"
                                                    fill="none"
                                                />
                                            </g>
                                            <g>
                                                <g>
                                                    <path d="M6,13c0-1.65,0.67-3.15,1.76-4.24L6.34,7.34C4.9,8.79,4,10.79,4,13c0,4.08,3.05,7.44,7,7.93v-2.02 C8.17,18.43,6,15.97,6,13z M20,13c0-4.42-3.58-8-8-8c-0.06,0-0.12,0.01-0.18,0.01l1.09-1.09L11.5,2.5L8,6l3.5,3.5l1.41-1.41 l-1.08-1.08C11.89,7.01,11.95,7,12,7c3.31,0,6,2.69,6,6c0,2.97-2.17,5.43-5,5.91v2.02C16.95,20.44,20,17.08,20,13z" />
                                                </g>
                                            </g>
                                        </svg>
                                        Reset
                                    </button>
                                </div>
                            </div>
                        </div>
                        <div className="divide-y divide-gray-100 dark:divide-gray-700">
                            <div className="flex flex-col">
                                <div className="relative overflow-x-auto shadow-md sm:rounded-lg ">
                                    <table className="w-full text-sm text-left text-gray-500 rtl:text-right dark:text-gray-400">
                                        <thead className="text-xs text-gray-700 uppercase dark:text-gray-400">
                                            <tr>
                                                <th className="p-2 text-center bg-gray-50 dark:bg-gray-800 w-14 ">
                                                    List
                                                </th>
                                                <th className="p-2 text-center bg-gray-50 dark:bg-gray-800 w-14">
                                                    Details
                                                </th>
                                                <th className="p-2"></th>
                                            </tr>
                                        </thead>
                                        <tbody className="overflow-auto ">
                                            <tr className="border-b border-gray-200 dark:border-gray-700">
                                                <td className="p-2 font-medium text-center text-gray-900 max-w-max whitespace-nowrap bg-gray-50 dark:text-white dark:bg-gray-800">
                                                    <Checkbox></Checkbox>
                                                </td>
                                                <td className="p-2 font-medium text-center text-gray-900 whitespace-nowrap bg-gray-50 dark:text-white dark:bg-gray-800">
                                                    <Checkbox></Checkbox>
                                                </td>
                                                <td className="px-6 py-4 max-w-max">
                                                    Name
                                                </td>
                                            </tr>
                                            <tr className="border-b border-gray-200 dark:border-gray-700">
                                                <td className="p-2 font-medium text-center text-gray-900 max-w-max whitespace-nowrap bg-gray-50 dark:text-white dark:bg-gray-800">
                                                    <Checkbox></Checkbox>
                                                </td>
                                                <td className="p-2 font-medium text-center text-gray-900 whitespace-nowrap bg-gray-50 dark:text-white dark:bg-gray-800">
                                                    <Checkbox></Checkbox>
                                                </td>
                                                <td className="px-6 py-4 max-w-max">
                                                    Name
                                                </td>
                                            </tr>
                                            <tr className="border-b border-gray-200 dark:border-gray-700">
                                                <td className="p-2 font-medium text-center text-gray-900 max-w-max whitespace-nowrap bg-gray-50 dark:text-white dark:bg-gray-800">
                                                    <Checkbox></Checkbox>
                                                </td>
                                                <td className="p-2 font-medium text-center text-gray-900 whitespace-nowrap bg-gray-50 dark:text-white dark:bg-gray-800">
                                                    <Checkbox></Checkbox>
                                                </td>
                                                <td className="px-6 py-4 max-w-max">
                                                    Name
                                                </td>
                                            </tr>
                                            <tr className="border-b border-gray-200 dark:border-gray-700">
                                                <td className="p-2 font-medium text-center text-gray-900 max-w-max whitespace-nowrap bg-gray-50 dark:text-white dark:bg-gray-800">
                                                    <Checkbox></Checkbox>
                                                </td>
                                                <td className="p-2 font-medium text-center text-gray-900 whitespace-nowrap bg-gray-50 dark:text-white dark:bg-gray-800">
                                                    <Checkbox></Checkbox>
                                                </td>
                                                <td className="px-6 py-4 max-w-max">
                                                    Name
                                                </td>
                                            </tr>
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div className="bg-white border rounded-lg">
                    {/* documents table */}
                    <div className="overflow-x-auto">
                        <Table hoverable theme={table_custom}>
                            <Table.Head className=" bg-slate-100">
                                <Table.HeadCell>
                                    <Checkbox
                                        color="blue"
                                        checked={isCheckAll}
                                    />
                                </Table.HeadCell>
                                <Table.HeadCell>
                                    {/* <Link
                                                href={route(
                                                    "Integration.whatsapp.whatsappweb.index",
                                                    {
                                                        name: target,
                                                        column: "id",
                                                        sort:
                                                            getData.sort ==
                                                                "asc"
                                                                ? "desc"
                                                                : "asc",
                                                    }
                                                )}
                                            > */}
                                    <div className="flex items-center justify-between gap-2">
                                        <span>Id</span>
                                        <PiCaretUpDownBold
                                        // className={
                                        //     (getData.column ==
                                        //         "id"
                                        //         ? "  text-gray-900 dark:text-gray-200 "
                                        //         : "text-gray-600 dark:text-gray-400 ") +
                                        //     "  cursor-pointer "
                                        // }
                                        />
                                    </div>
                                    {/* </Link> */}
                                </Table.HeadCell>

                                <Table.HeadCell>
                                    {/* <Link
                                                href={route(
                                                    "Integration.whatsapp.whatsappweb.index",
                                                    {
                                                        name: target,
                                                        column: "name",
                                                        sort:
                                                            getData.sort ==
                                                                "asc"
                                                                ? "desc"
                                                                : "asc",
                                                    }
                                                )}
                                            > */}
                                    <div className="flex items-center justify-between gap-2">
                                        <span>Name</span>
                                        <PiCaretUpDownBold
                                        // className={
                                        //     (getData.column ==
                                        //         "name"
                                        //         ? "  text-gray-900 dark:text-gray-200 "
                                        //         : "text-gray-600 dark:text-gray-400 ") +
                                        //     "  cursor-pointer "
                                        // }
                                        />
                                    </div>
                                    {/* </Link> */}
                                </Table.HeadCell>

                                <Table.HeadCell>
                                    {/* <Link
                                                href={route(
                                                    "Integration.whatsapp.whatsappweb.index",
                                                    {
                                                        name: target,
                                                        column: "delaySeconds",
                                                        sort:
                                                            getData.sort ==
                                                                "asc"
                                                                ? "desc"
                                                                : "asc",
                                                    }
                                                )}
                                            > */}
                                    <div className="flex items-center justify-between gap-2">
                                        <span>Delay</span>
                                        <PiCaretUpDownBold
                                        // className={
                                        //     (getData.column ==
                                        //         "delaySeconds"
                                        //         ? "  text-gray-900 dark:text-gray-200 "
                                        //         : "text-gray-600 dark:text-gray-400 ") +
                                        //     "  cursor-pointer "
                                        // }
                                        />
                                    </div>
                                    {/* </Link> */}
                                </Table.HeadCell>

                                <Table.HeadCell>Connect</Table.HeadCell>

                                <Table.HeadCell>
                                    {/* <Link
                                                href={route(
                                                    "Integration.whatsapp.whatsappweb.index",
                                                    {
                                                        name: target,
                                                        column: "status",
                                                        sort:
                                                            getData.sort ==
                                                                "asc"
                                                                ? "desc"
                                                                : "asc",
                                                    }
                                                )}
                                            > */}
                                    <div className="flex items-center justify-between gap-2">
                                        <span>status</span>
                                        <PiCaretUpDownBold
                                        // className={
                                        //     (getData.column ==
                                        //         "status"
                                        //         ? "  text-gray-900 dark:text-gray-200 "
                                        //         : "text-gray-600 dark:text-gray-400 ") +
                                        //     "  cursor-pointer "
                                        // }
                                        />
                                    </div>
                                    {/* </Link> */}
                                </Table.HeadCell>

                                <Table.HeadCell>
                                    {/* <Link
                                                href={route(
                                                    "Integration.whatsapp.whatsappweb.index",
                                                    {
                                                        name: target,
                                                        column: "savechat",
                                                        sort:
                                                            getData.sort ==
                                                                "asc"
                                                                ? "desc"
                                                                : "asc",
                                                    }
                                                )}
                                            > */}
                                    <div className="flex items-center justify-between gap-2">
                                        <span>Save chat</span>
                                        <PiCaretUpDownBold
                                        // className={
                                        //     (getData.column ==
                                        //         "savechat"
                                        //         ? "  text-gray-900 dark:text-gray-200 "
                                        //         : "text-gray-600 dark:text-gray-400 ") +
                                        //     "  cursor-pointer "
                                        // }
                                        />
                                    </div>
                                    {/* </Link> */}
                                </Table.HeadCell>
                                <Table.HeadCell>
                                    <div className="flex items-center justify-between gap-2">
                                        <span>Last Status change</span>
                                        <svg
                                            xmlns="http://www.w3.org/2000/svg"
                                            height="16px"
                                            viewBox="0 0 24 24"
                                            width="16px"
                                            className="fill-gray-600"
                                        >
                                            <path
                                                d="M0 0h24v24H0V0z"
                                                fill="none"
                                            />
                                            <path d="M12 5.83L15.17 9l1.41-1.41L12 3 7.41 7.59 8.83 9 12 5.83zm0 12.34L8.83 15l-1.41 1.41L12 21l4.59-4.59L15.17 15 12 18.17z" />
                                        </svg>
                                    </div>
                                </Table.HeadCell>

                                <Table.HeadCell>
                                    Last Disconnect Reason
                                </Table.HeadCell>
                                {/* <Table.HeadCell>API</Table.HeadCell> */}
                                <Table.HeadCell>
                                    {/* <Link
                                                href={route(
                                                    "Integration.whatsapp.whatsappweb.index",
                                                    {
                                                        name: target,
                                                        column: "isActive",
                                                        sort:
                                                            getData.sort ==
                                                                "asc"
                                                                ? "desc"
                                                                : "asc",
                                                    }
                                                )}
                                            > */}
                                    <div className="flex items-center justify-between gap-2">
                                        <span>Active</span>
                                        <PiCaretUpDownBold
                                        // className={
                                        //     (getData.column ==
                                        //         "isActive"
                                        //         ? "  text-gray-900 dark:text-gray-200 "
                                        //         : "text-gray-600 dark:text-gray-400 ") +
                                        //     "  cursor-pointer "
                                        // }
                                        />
                                    </div>
                                    {/* </Link> */}
                                </Table.HeadCell>
                                <Table.HeadCell>User</Table.HeadCell>
                                <Table.HeadCell>Workers</Table.HeadCell>
                                <Table.HeadCell>Action</Table.HeadCell>
                            </Table.Head>

                            <Table.Body className="divide-y">
                                <Table.Row >
                                    <Table.Cell>
                                        <Checkbox
                                            color={"blue"}
                                            className="rowCheckBox"
                                        />
                                    </Table.Cell>
                                    <Table.Cell className="font-medium text-gray-900 whitespace-nowrap dark:text-white">
                                        #Id
                                    </Table.Cell>
                                    <Table.Cell>
                                        channel Name
                                    </Table.Cell>
                                    <Table.Cell>
                                        <div className="flex justify-center">
                                            <Tooltip content="Scan QR">
                                                <Button

                                                    theme={
                                                        button_custom
                                                    }
                                                    color="success"
                                                    size="xs"
                                                >
                                                    <IoQrCode />
                                                </Button>
                                            </Tooltip>
                                        </div>
                                    </Table.Cell>
                                    <Table.Cell>
                                        <div className="w-fit">
                                            {/* {channel.status} */}
                                            <Badge
                                                // color={
                                                //     statusColor[
                                                //         channel
                                                //             .status
                                                //     ].bg ??
                                                //     "info"
                                                // }
                                                className="capitalize"
                                            >
                                                channel status
                                            </Badge>
                                        </div>
                                    </Table.Cell>
                                    <Table.Cell>
                                        <div className="flex items-center gap-3">
                                            <Tooltip content="Save Chat">
                                                <ToggleSwitch
                                                    theme={
                                                        toggle_custom
                                                    }
                                                    sizing="xs"
                                                />
                                            </Tooltip>


                                            <Tooltip content="View Chat">
                                                <Button
                                                    color={
                                                        "gray"
                                                    }
                                                    theme={
                                                        button_custom
                                                    }
                                                    size="xs"
                                                >
                                                    <FaEye className="text-slate-500 text-sm" />
                                                    <span className="text-xs ms-1">
                                                        View
                                                    </span>
                                                </Button>
                                            </Tooltip>

                                        </div>
                                    </Table.Cell>

                                    <Table.Cell>
                                        status
                                    </Table.Cell>
                                    <Table.Cell>
                                        last
                                    </Table.Cell>
                                    <Table.Cell>
                                        autoreply
                                    </Table.Cell>
                                    <Table.Cell>
                                        <ToggleSwitch
                                            theme={
                                                toggle_custom
                                            }
                                            sizing="xs"
                                        />
                                    </Table.Cell>
                                    <Table.Cell>
                                        userName
                                    </Table.Cell>
                                    <Table.Cell>
                                        worker
                                    </Table.Cell>
                                    <Table.Cell>
                                        <div className="flex gap-2">
                                            <Button
                                                theme={
                                                    button_custom
                                                }
                                                color="gray"
                                                size="xs"
                                            >
                                                <MdOutlineEdit className="text-xs" />
                                                <span className="text-xs ms-1">
                                                    Edit
                                                </span>
                                            </Button>
                                        </div>
                                    </Table.Cell>
                                </Table.Row>
                            </Table.Body>
                        </Table>
                    </div>
                </div>

                {/* <div className="bottom-0 w-full p-3 bg-white rounded-lg float-end dark:border-gray-500 dark:rounded-lg dark:bg-gray-700">
                            <div className="flex flex-wrap justify-between gap-2 lg:justify-between lg:gap-0 md:gap-0">
                                <div className="flex items-center  gap-4">
                                    <Button
                                        className="border rounded"
                                        size="xs"
                                        color="gray"
                                        theme={button_custom}
                                        onClick={() =>
                                            window.confirm(
                                                "Do you really want to delete WhatsAppWeb Channel ?"
                                            ) && deleteRecords()
                                        }
                                    >
                                        <div className="flex items-center gap-1">
                                            <MdDeleteOutline className="text-slate-500" />
                                            <span className="text-xs">
                                                Delete
                                            </span>
                                        </div>
                                    </Button>
                                    <PerPageDropdown
                                        getDataFields={getData ?? null}
                                        routeName={
                                            "Integration.whatsapp.whatsappweb.index"
                                        }
                                        data={channelsData}
                                        idName={"name"}
                                    />
                                </div>

                                <Paginate tableData={channelsData} />
                            </div>
                        </div> */}
            </div>
        </Main>
    );
}
