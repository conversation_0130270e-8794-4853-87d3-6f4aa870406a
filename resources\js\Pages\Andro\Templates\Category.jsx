import ConfirmBox from "@/Components/HelperComponents/ConfirmBox";
import useFetch from "@/Global/useFetch";
import { button_custom } from "@/Pages/Helpers/DesignHelper";
import { Link, router } from "@inertiajs/react";
import { <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>dal<PERSON>, ModalHeader } from "flowbite-react";
import { useState } from "react";
import { FaRegTrashCan } from "react-icons/fa6";
import { MdDeleteOutline, MdOutlineAdd, MdOutlineEdit } from "react-icons/md";
import AddCategory from "./AddCategory";
import EditCategory from "./EditCategory";
import { CgSpinner } from "react-icons/cg";

export default function Category({
    setSelectedCategory,
    selectedCategory,
}) {
    const baseRoute='andro';
    const [reFetch, setReFetch] = useState(0);
    const { data: collectionData, loading: collectionLoading, error } = useFetch(route('whatsapp.templates.category.index'), reFetch);

    const [openModal, setOpenModal] = useState(false);
    const [openEditModal, setOpenEditModal] = useState(false);
    const [editModalId, setEditModalId] = useState(null);
    const [isConfirmOpen, setConfirmOpen] = useState(false);
    const [checkValue, setCheckValue] = useState([]);

    function handleConfirmBoxResult(result) {
        if (checkValue.length > 0 && result) {
            setConfirmOpen(false);
            router.delete(route(baseRoute+".templates.category.destroy", { template: checkValue }), {
                onSuccess: () => {
                    setReFetch(prev => prev + 1);
                }
            });
            setCheckValue([]);
        } else {
            setCheckValue([]);
        }
        setConfirmOpen(false);
    }

    return (
        <div className="w-full bg-white rounded-md h-fit">
            <div className="flex items-center justify-between px-3 py-2">
                <span>Categories</span>

                {!collectionLoading && collectionData?.collection?.can_add &&
                    <Button
                        theme={button_custom}
                        color="gray"
                        size="xs"
                        onClick={() => setOpenModal(true)}
                    >
                        <div className="flex items-center ">
                            <MdOutlineAdd className="text-slate-500" />
                            <span className="text-xs">Add</span>
                        </div>
                    </Button>
                }
            </div>
            <Link
                href={route(baseRoute+".templates.index")}
                className={`flex items-center justify-between p-3 border-b border-t text-sm  ${0 == selectedCategory ? "bg-blue-100" : ""
                    }`}
                onClick={() => setSelectedCategory(0)}
            >
                All&nbsp;
            </Link>

            <div className="flex flex-col h-full overflow-y-auto bg-white lg:flex-col md:flex-col">
                {!collectionLoading ?
                    collectionData?.collection?.categories?.map((category, key) => {
                        return (
                            <div
                                className={`flex items-center justify-between border-b  text-sm ${category.id == selectedCategory ? "bg-blue-100" : ""}`}
                                key={key + category.name}
                            >
                                <Link
                                    href={route(baseRoute+".templates.show", {
                                        template: category.id,
                                    })}
                                    className={`w-full`}
                                    onClick={() =>
                                        setSelectedCategory(category.id)
                                    }
                                >
                                    <div className="flex items-center gap-2 p-3">
                                        {category.name}&nbsp;
                                        <div className="px-1 text-xs text-blue-600 bg-blue-100 rounded">
                                            {category.template_count}
                                        </div>
                                    </div>
                                </Link>
                                <div>
                                    <div className="flex items-center gap-2">
                                        {!collectionLoading && collectionData?.collection?.can_edit &&
                                            <Button
                                                theme={button_custom}
                                                color="withoutBorder"
                                                size="xxs"
                                                value={category.id}
                                                onClick={() => {
                                                    setOpenEditModal(true);
                                                    setEditModalId(category.id);
                                                }}
                                            >
                                                <MdOutlineEdit className="text-slate-400" />
                                            </Button>
                                        }
                                        {!collectionLoading && collectionData?.collection?.can_delete &&
                                            <Button
                                                theme={button_custom}
                                                color="withoutBorder"
                                                size="xxs"
                                                onClick={() => {
                                                    setCheckValue([category.id]);
                                                    setConfirmOpen(true);
                                                }
                                                }
                                            >
                                                <MdDeleteOutline className="text-slate-400" />
                                            </Button>}
                                    </div>
                                </div>
                            </div>
                        );
                    }) : (
                        <div className="flex justify-center p-2">
                            <CgSpinner className="text-3xl text-blue-400 ease-in-out animate-spin" />
                        </div>
                    )
                }
            </div>
            {!collectionLoading && collectionData?.collection?.can_add && openModal && (
                <Modal
                    show={openModal}
                    size="md"
                    onClose={() => setOpenModal(false)}
                    popup
                >
                    <ModalHeader className="p-2">
                        <h4 className="text-lg">Add Category</h4>
                    </ModalHeader>
                    <ModalBody className="px-4 py-3 rounded-b-lg bg-slate-100">
                        <AddCategory
                            onClose={() => setOpenModal(false)}
                            setReFetch={setReFetch}
                        ></AddCategory>
                    </ModalBody>
                </Modal>
            )}
            {!collectionLoading && collectionData?.collection?.can_edit && openEditModal && (
                <Modal
                    show={openEditModal}
                    size="md"
                    onClose={() => setOpenEditModal(false)}
                    popup
                >
                    <ModalHeader className="p-2">
                        <h4 className="text-lg">Edit Category</h4>
                    </ModalHeader>
                    <ModalBody className="px-4 py-3 rounded-b-lg bg-slate-100">
                        <EditCategory
                            onClose={() => setOpenEditModal(false)}
                            editId={editModalId}
                            setReFetch={setReFetch}
                        ></EditCategory>
                    </ModalBody>
                </Modal>
            )}
            {/* confirm box popup */}
            {!collectionLoading && collectionData?.collection?.can_delete &&
                isConfirmOpen &&
                <ConfirmBox
                    isOpen={isConfirmOpen}
                    onClose={() => setConfirmOpen(false)} // Close the confirm box
                    onAction={handleConfirmBoxResult} // Handle the user's choice
                    title="Are you sure you want to delete this?"
                    message="This action cannot be undone."
                    confirmText="Yes, Delete!"
                    cancelText="No, Keep It"
                    confirmColor="orange"
                    cancelColor="gray"
                    icon={<FaRegTrashCan />}
                />
            }
        </div>

    );
}

