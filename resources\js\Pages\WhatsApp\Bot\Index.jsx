import ConfirmBox from "@/Components/HelperComponents/ConfirmBox";
import NoRecord from "@/Components/HelperComponents/NoRecord";
import Paginate from "@/Components/HelperComponents/Paginate";
import PerPageDropdown from "@/Components/HelperComponents/PerPageDropdown";
import SortLink from "@/Components/SortLink";
import { Head, Link, useForm } from "@inertiajs/react";
import {
    Button, Checkbox,
    Drawer,
    DrawerHeader,
    DrawerItems,
    Table,
    TableBody,
    TableCell,
    TableHead,
    TableHeadCell,
    TableRow,
    Tooltip
} from "flowbite-react";
import $ from "jquery";
import { useState } from "react";
import { FaRegTrashCan } from "react-icons/fa6";
import { IoMdAdd, IoMdRepeat } from "react-icons/io";
import {
    MdDeleteOutline, MdOutlineModeEdit
} from "react-icons/md";
import { RiDeleteBin6Line, RiListSettingsLine } from "react-icons/ri";
import {
    button_custom,
    customDrawer,
    table_custom
} from "../../Helpers/DesignHelper";
import WaMain from "../WaMain";
import Add from "./Add";
import Edit from "./Edit";
import AddKeyword from "./Keywords/AddKeyword";
import AddWelcome from "./WelcomeMessage/AddWelcome";

export default function Index({ collection }) {

    const { getData, can_keyword, can_keywordMessage, can_welcomeMessage ,bots} = collection
    const currentRoute = route().current();

    const [isCheckAll, setIsCheckAll] = useState(false);
    const [isAddOpen, setIsAddOpen] = useState(false);

    const [isEditAutoReplyOpen, setIsEditAutoReply] = useState(false);
    const [autoReplyEditData, setAutoReplyEditData] = useState({});
    const [checkValue, setCheckValue] = useState([]);

    const [enableAddKeyword, setEnableAddKeyword] = useState(false);
    const [enableAddWelcome, setEnableAddWelcome] = useState(false);

    const {
        data,
        setData,
        delete: destroy,
        processing,
        errors,
    } = useForm({
        id: [],
    });

    const [isConfirmOpen, setConfirmOpen] = useState(false);

    function getCheckedIds(e) {
        let previousIds = checkValue;
        if (e.target.checked) {
            if (!previousIds.includes(e.target.id)) {
                previousIds.push(e.target.id);
                setCheckValue(previousIds);
            }
        } else {
            const newIds = previousIds.filter((item) => item !== e.target.id);
            setCheckValue(newIds);
        }
    }

    // executes when user click table header checkbox
    function headerCheckBoxChecked(e) {
        let previousIds = [];
        if (
            e.target.checked &&
            e.target.id == 0 &&
            bots.data.length > 0
        ) {
            bots.data.map((auto, key) => {
                if (!previousIds.includes(auto.id)) {
                    previousIds.push(auto.id);
                    setCheckValue(previousIds);
                }
            });
            setIsCheckAll(true);
            $(".rowCheckBox").prop("checked", true);
        } else {
            setCheckValue(previousIds);
            setIsCheckAll(false);
            $(".rowCheckBox").prop("checked", false);
        }
    }

    // handle checkBox Check or uncheck
    function checkAddCheckBoxChecked() {
        let allCheckBoxes = $(".rowCheckBox");
        let checkedCheckBoxes = $(".rowCheckBox:checked");

        if (allCheckBoxes.length == checkedCheckBoxes.length) {
            setIsCheckAll(true);
        } else {
            setIsCheckAll(false);
        }
    }


    function handleConfirmBoxResult(result) {
        if (checkValue.length > 0 && result) {
            setData({ id: checkValue });
            setConfirmOpen(false);
            destroy(route("whatsapp.bot.destroy", { bot: checkValue }), {
                onSuccess: () => {
                    // onClose(true);
                    setCheckValue([]);
                    $(".rowCheckBox").prop("checked", false);
                    setIsCheckAll(false);
                },
            });
        } else {
            setCheckValue([]);
            $(".rowCheckBox").prop("checked", false);

            setIsCheckAll(false);
        }
        setConfirmOpen(false);
    }

    return (
        <WaMain>
            <Head title="Bots" />
            {/* <TabBar /> */}
            <div className="px-2 pb-2 rounded-lg">
                <div className="h-fit bg-white rounded border overflow-auto w-full mt-2.5">
                    <div className="flex justify-between p-2">
                        <div>
                            {
                                collection.can_delete &&
                                <Button
                                    className=""
                                    size="xs"
                                    color="gray"
                                    theme={button_custom}
                                    onClick={() =>
                                        setConfirmOpen(true)
                                    }
                                >
                                    <div className="flex items-center gap-1">
                                        <MdDeleteOutline className="text-slate-500" />
                                        <span className="text-xs">Delete</span>
                                    </div>
                                </Button>
                            }
                        </div>


                        {
                            collection.can_add &&
                            <div className="flex">
                                <Button
                                    // as={Link}
                                    className="border pe-1"
                                    size="xs"
                                    color="gray"
                                    theme={button_custom}
                                    // href={route("whatsapp.bot.create")}
                                    onClick={() => setIsAddOpen(true)}
                                >
                                    <div className="flex items-center gap-1">
                                        <IoMdAdd className="text-slate-500" />
                                        <span className="text-xs">Add</span>
                                    </div>
                                </Button>
                            </div>
                        }
                    </div>
                    <div className="h-full overflow-auto bg-white">
                        {/* documents table */}
                        <div className="overflow-x-auto border rounded-lg">
                            <Table hoverable theme={table_custom}>
                                <TableHead className=" bg-slate-100">
                                    <TableHeadCell className="w-10">
                                        <Checkbox
                                            color="blue"
                                            checked={isCheckAll}
                                            id={0}
                                            onChange={(e) =>
                                                headerCheckBoxChecked(e)
                                            }
                                        />
                                    </TableHeadCell>
                                    <TableHeadCell>
                                        <SortLink showName={"Name"} routeName={currentRoute} column={"name"} sortType={getData.sort == "asc" ? "desc" : "asc"} />
                                    </TableHeadCell>
                                    <TableHeadCell className="w-80">
                                        Welcome Msg / Wrong Keyword
                                    </TableHeadCell>
                                    <TableHeadCell className="w-80">
                                        Keywords
                                    </TableHeadCell>
                                    <TableHeadCell>
                                        <SortLink showName={"User"} routeName={currentRoute} column={"user_id"} sortType={getData.sort == "asc" ? "desc" : "asc"} />
                                    </TableHeadCell>
                                    <TableHeadCell>Actions</TableHeadCell>
                                </TableHead>

                                <TableBody className="divide-y">
                                    {bots.data.length > 0
                                        ?
                                        (
                                            bots.data.map(
                                                (auto, key) => {
                                                    return (
                                                        <TableRow
                                                            className="bg-white dark:border-gray-700 dark:bg-gray-800"
                                                            key={key}
                                                        >
                                                            <TableCell>
                                                                <Checkbox
                                                                    color={"blue"}
                                                                    className="rowCheckBox"
                                                                    id={auto.id}
                                                                    onChange={(e) => {
                                                                        getCheckedIds(e);
                                                                        checkAddCheckBoxChecked();
                                                                    }}
                                                                />
                                                            </TableCell>

                                                            <TableCell>
                                                                {auto.name}
                                                            </TableCell>
                                                            <TableCell className="max-w-40">
                                                                <div className="flex flex-wrap items-center justify-between gap-3 pt-1">
                                                                    <div className="">
                                                                        {auto.message_count ?? "-"}
                                                                    </div>
                                                                    <div className="flex items-center justify-between gap-3">

                                                                        {can_welcomeMessage.view &&
                                                                            <div className="">
                                                                                <Button
                                                                                    theme={button_custom}
                                                                                    as={Link}
                                                                                    size={"xs"}
                                                                                    color="blue"
                                                                                    className="items-center rounded"
                                                                                    href={route('whatsapp.bot.welcome.index', { bot: auto.id })}
                                                                                >
                                                                                    <div className="flex items-center gap-1">
                                                                                        <RiListSettingsLine />
                                                                                        <span className="text-xs">
                                                                                            Manage
                                                                                        </span>
                                                                                    </div>
                                                                                </Button>
                                                                            </div>
                                                                        }
                                                                        {
                                                                            can_welcomeMessage.add &&
                                                                            <div className="">
                                                                                <Button
                                                                                    theme={button_custom}
                                                                                    size={"xs"}
                                                                                    color="blue"
                                                                                    className="items-center rounded"
                                                                                    onClick={() => {
                                                                                        setAutoReplyEditData(auto);
                                                                                        setEnableAddWelcome(true)
                                                                                    }}
                                                                                >
                                                                                    <div className="flex items-center gap-1">
                                                                                        <IoMdAdd />
                                                                                        <span className="text-xs">
                                                                                            Add
                                                                                        </span>
                                                                                    </div>
                                                                                </Button>
                                                                            </div>
                                                                        }
                                                                    </div>
                                                                </div>

                                                            </TableCell>
                                                            <TableCell>
                                                                <div className="flex flex-wrap items-center justify-between gap-3 pt-1">
                                                                    <div className="">
                                                                        {auto.rules_count ?? "-"}
                                                                    </div>
                                                                    <div className="flex items-center justify-between gap-3 ">

                                                                        {can_keyword.view &&
                                                                            <div className="">
                                                                                <Button theme={button_custom}
                                                                                    as={Link}
                                                                                    size={"xs"}
                                                                                    color="blue"
                                                                                    className="items-center rounded"
                                                                                    href={route('whatsapp.bot.keyword.index', { bot: auto.id })}
                                                                                >
                                                                                    <div className="flex items-center gap-1">
                                                                                        <RiListSettingsLine />
                                                                                        <span className="text-xs">
                                                                                            Manage
                                                                                        </span>
                                                                                    </div>
                                                                                </Button>
                                                                            </div>
                                                                        }
                                                                        {can_keyword.add &&
                                                                            <div className="">
                                                                                <Button theme={button_custom}
                                                                                    size={"xs"}
                                                                                    color="blue"
                                                                                    className="items-center rounded"
                                                                                    onClick={() => {
                                                                                        setAutoReplyEditData(auto);
                                                                                        setEnableAddKeyword(true)
                                                                                    }}
                                                                                >
                                                                                    <div className="flex items-center gap-1">
                                                                                        <IoMdAdd />
                                                                                        <span className="text-xs">
                                                                                            Add
                                                                                        </span>
                                                                                    </div>
                                                                                </Button>
                                                                            </div>
                                                                        }
                                                                    </div>
                                                                </div>

                                                            </TableCell>
                                                            <TableCell className="font-medium text-gray-900 whitespace-nowrap dark:text-white">
                                                                {auto.user.username}
                                                            </TableCell>
                                                            <TableCell>
                                                                <div className="flex items-center gap-3 pt-1">
                                                                    {
                                                                        collection.can_edit &&
                                                                        <Tooltip content="Edit">
                                                                            <Button
                                                                                theme={
                                                                                    button_custom
                                                                                }
                                                                                color="success"
                                                                                size="xs"
                                                                                // href={route('whatsapp.bot.edit', { id: auto.id })}
                                                                                onClick={() => {
                                                                                    setIsEditAutoReply(
                                                                                        true
                                                                                    );
                                                                                    setAutoReplyEditData(auto);
                                                                                }}
                                                                            >
                                                                                <MdOutlineModeEdit className="text-sm" />
                                                                                <span className="text-xs ms-1">
                                                                                    Edit
                                                                                </span>
                                                                            </Button>
                                                                        </Tooltip>
                                                                    }
                                                                    {
                                                                        collection.can_delete &&
                                                                        <Tooltip content="Delete">
                                                                            <Button
                                                                                theme={
                                                                                    button_custom
                                                                                }
                                                                                color="failure"
                                                                                size="xs"
                                                                                onClick={() => {
                                                                                    setCheckValue([auto.id]);
                                                                                    setConfirmOpen(true);
                                                                                }}
                                                                            // onClick={() => {
                                                                            //     deleteSingleRecords(auto.id)
                                                                            // }}
                                                                            >
                                                                                <RiDeleteBin6Line className="text-sm" />
                                                                                <span className="text-xs ms-1">
                                                                                    Delete
                                                                                </span>
                                                                            </Button>
                                                                        </Tooltip>
                                                                    }
                                                                </div>
                                                            </TableCell>
                                                        </TableRow>
                                                    );
                                                }
                                            )
                                        ) : (
                                            <TableRow className="bg-white dark:border-gray-700 dark:bg-gray-800">
                                                <TableCell
                                                    colSpan={11}
                                                    className="text-center"
                                                >
                                                    <NoRecord />
                                                </TableCell>
                                            </TableRow>
                                        )}
                                </TableBody>
                            </Table>
                        </div>
                    </div>

                    <div className="bottom-0 w-full p-3 bg-white rounded-lg float-end dark:border-gray-500 dark:rounded-lg dark:bg-gray-700">
                        <div className="flex flex-wrap justify-between gap-2 lg:justify-between lg:gap-0 md:gap-0">
                            <div className="flex items-center gap-4">
                                <PerPageDropdown
                                    getDataFields={
                                        getData ?? null
                                    }
                                    routeName={"whatsapp.bot.index"}
                                    data={bots}
                                />
                            </div>

                            <Paginate tableData={bots} />
                        </div>
                    </div>
                </div>
            </div>
            {/*--------Add AutoReply---------*/}

            {isAddOpen && (
                <Drawer
                    theme={customDrawer}
                    className="w-full lg:w-4/5"
                    open={isAddOpen}
                    onClose={() => setIsAddOpen(false)}
                    position="right"
                >
                    <DrawerHeader titleIcon={IoMdRepeat} title="Add Bot" />
                    <DrawerItems className="py-2">
                        <Add onClose={() => setIsAddOpen(false)} can_add_welcome={can_welcomeMessage.add} />
                    </DrawerItems>
                </Drawer>

            )}

            {/*-------- edit autoreply ---------*/}

            {isEditAutoReplyOpen === true && (
                <Drawer
                    theme={customDrawer}
                    className="w-full lg:w-3/5 md:w-4/5"
                    open={isEditAutoReplyOpen}
                    onClose={() => setIsEditAutoReply(false)}
                    position="right"
                >
                    <DrawerHeader
                        titleIcon={IoMdRepeat}
                        title={"Edit Bot (" + autoReplyEditData.name + ")"}
                    />
                    <DrawerItems className="pb-2">
                        <Edit
                            bot={autoReplyEditData}
                            onClose={() => setIsEditAutoReply(false)}
                        ></Edit>
                    </DrawerItems>
                </Drawer>
            )}

            {/* add keyword */}

            {enableAddKeyword && (
                <Drawer
                    theme={customDrawer}
                    className="w-full lg:w-3/5 md:w-4/5"
                    open={enableAddKeyword}
                    onClose={() => setEnableAddKeyword(false)}
                    position="right"
                >
                    <DrawerHeader titleIcon={IoMdRepeat} title="Add Keyword" />
                    <DrawerItems className="">
                        <AddKeyword bot={autoReplyEditData}
                            onClose={() => setEnableAddKeyword(false)}
                        />
                    </DrawerItems>
                </Drawer>
            )}

            {/* add welcome */}

            {enableAddWelcome && (
                <Drawer
                    theme={customDrawer}
                    className="w-full lg:w-5/6 md:w-6/6 xl:w-3/4"
                    open={enableAddWelcome}
                    onClose={() => setEnableAddWelcome(false)}
                    position="right"
                >
                    <DrawerHeader titleIcon={IoMdRepeat} title="Add welcome" />
                    <DrawerItems className="px-4 pt-2 pb-4 ">
                        <AddWelcome
                            // bot={autoReplyEditData}
                            botId={autoReplyEditData.id}
                            onClose={() => setEnableAddWelcome(false)}
                        />
                    </DrawerItems>
                </Drawer>
            )}

            {/* confirm box popup */}
            {isConfirmOpen && (
                <ConfirmBox
                    isOpen={isConfirmOpen}
                    onClose={() => setConfirmOpen(false)} // Close the confirm box
                    onAction={handleConfirmBoxResult} // Handle the user's choice
                    title="Are you sure you want to delete this?"
                    message="This action cannot be undone."
                    confirmText="Yes, Delete!"
                    cancelText="No, Keep It"
                    confirmColor="orange"
                    cancelColor="gray"
                    icon={<FaRegTrashCan />}
                />
            )}
        </WaMain >
    );
}



