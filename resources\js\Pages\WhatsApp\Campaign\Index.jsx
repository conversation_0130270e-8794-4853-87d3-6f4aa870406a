import { <PERSON>, <PERSON>, router } from "@inertiajs/react";
import {
    <PERSON><PERSON>,
    Button,
    Checkbox,
    Drawer,
    Progress,
    Table,
    Tooltip,
} from "flowbite-react";
import { useState } from "react";
import { IoMdAdd } from "react-icons/io";
import {
    MdOutlineCampaign,
    MdOutlineEdit
} from "react-icons/md";
import { PiCaretUpDownBold } from "react-icons/pi";
import { TbFileTypeCsv, TbHandStop, TbReport } from "react-icons/tb";

import {
    changeFlag,
    convertDateFormat,
    isDateGreaterThanToday,
} from "@/Pages/Helpers/Helper";

import NoRecord from "@/Components/HelperComponents/NoRecord";
import Paginate from "@/Components/HelperComponents/Paginate";
import PerPageDropdown from "@/Components/HelperComponents/PerPageDropdown";
import $ from "jquery";
import { MdDeleteOutline } from "react-icons/md";

import ConfirmBox from "@/Components/HelperComponents/ConfirmBox";
import {
    button_custom,
    customDrawer,
    table_custom
} from "@/Pages/Helpers/DesignHelper";
import { FaRegTrashCan } from "react-icons/fa6";
import { FiPlay } from "react-icons/fi";
import { IoPauseOutline } from "react-icons/io5";
import { RiDeleteBin6Line } from "react-icons/ri";
import WaMain from "../WaMain";
import Details from "./Details";
import View from "./View";


export default function Index({ collection }) {

    const getData = collection.getData;
    const currentPageRoute = 'whatsapp.campaign.index';
    const campaigns = collection.campaigns;

    const [disabledBtnId, setDisabledBtnId] = useState(null);
    const [isCheckAll, setIsCheckAll] = useState(false);
    const [isConfirmOpen, setConfirmOpen] = useState(false);
    const [isWarningConfirmOpen, setWarningConfirmOpen] = useState(false);
    // ----------------Edit --------------------
    const [isDetailsOpen, setIsDetailsOpen] = useState(false);
    const [campaignID, setCampaignID] = useState();
    const [checkValue, setCheckValue] = useState([]);


    const [isOpenDetail, setIsOpenDetail] = useState(false);
    const statusCodes = {
        0: { bg: "gray", text: "Draft" },
        1: { bg: "success", text: "Started" },
        2: { bg: "pink", text: "Paused" },
        3: { bg: "success", text: "Completed" },
        4: { bg: "failure", text: "Terminated" },
        6: { bg: "indigo", text: "Scheduled" },
    };

    // executes when user click table row checkbox
    function getCheckedIds(e) {
        let previousIds = checkValue;
        if (e.target.checked) {
            if (!previousIds.includes(e.target.id)) {
                previousIds.push(e.target.id);
                setCheckValue(previousIds);
            }
        } else {
            const newIds = previousIds.filter((item) => item !== e.target.id);
            setCheckValue(newIds);
        }
    }

    // executes when user click table header checkbox
    function headerCheckBoxChecked(e) {
        let previousIds = [];
        if (e.target.checked && e.target.id == 0 && campaigns.data.length > 0) {
            campaigns.data.map((campaign, key) => {
                if (!previousIds.includes(campaign.id)) {
                    previousIds.push(campaign.id);
                    setCheckValue(previousIds);
                }
            });
            setIsCheckAll(true);
            $(".rowCheckBox").prop("checked", true);
        } else {
            setCheckValue(previousIds);
            setIsCheckAll(false);
            $(".rowCheckBox").prop("checked", false);
        }
    }

    // handle checkBox Check or uncheck
    function checkAddcheckBoxChecked() {
        let allCheckBoxes = $(".rowCheckBox");
        let checkedCheckBoxes = $(".rowCheckBox:checked");

        if (allCheckBoxes.length == checkedCheckBoxes.length) {
            setIsCheckAll(true);
        } else {
            setIsCheckAll(false);
        }
    }
    const HandleChangeFlag = (Column, id, flag) => {
        let table = "wa_campaign";
        changeFlag(table, Column, id, flag);
    };

    function handleConfirmBoxResult(result) {
        if (checkValue.length > 0 && result) {
            setConfirmOpen(false);
            setCheckValue([]);
            router.delete(
                route("whatsapp.campaign.destroy", { campaign: checkValue }),
                {
                    onSuccess: () => { setDisabledBtnId(null); }
                }
            );
            $(".rowCheckBox").prop("checked", false);
            setIsCheckAll(false);
        } else {
            setCheckValue([]);
            setIsCheckAll(false);
        }
        setConfirmOpen(false);
    }

    function handleWarningConfirmBoxResult(result) {
        if (checkValue.length > 0 && result) {
            setWarningConfirmOpen(false);
            setCheckValue([]);
            HandleChangeFlag(
                "status",
                checkValue,
                4
            )
        } else {
            setCheckValue([]);
        }
        setWarningConfirmOpen(false);
    }


    return (
        <WaMain>
            <Head title="Campaigns" />
            <div className="px-2 pb-2 rounded-lg">
                <div className="h-fit bg-white rounded border overflow-auto w-full mt-2.5">
                    <div className="flex justify-between p-2">
                        <div>
                            {/* <Button.Group className=""> */}
                            {
                                collection.can_delete &&
                                <Button
                                    size="xs"
                                    color="gray"
                                    theme={button_custom}
                                    onClick={() =>
                                        setConfirmOpen(true)
                                    }
                                >
                                    <div className="flex items-center gap-1">
                                        <MdDeleteOutline className="text-slate-500" />
                                        <span className="text-xs">Delete</span>
                                    </div>
                                </Button>
                            }
                        </div>
                        <div className="flex items-center gap-2">

                            {
                                collection.can_add &&
                                <div>
                                    <Button
                                        // className="pe-1"
                                        as={Link}
                                        size="xs"
                                        color="gray"
                                        theme={button_custom}
                                        href={route("whatsapp.campaign.create")}

                                    >
                                        <div className="flex items-center gap-1 text-xs">
                                            <IoMdAdd className="text-sm text-slate-500" />
                                            <span>Add</span>
                                        </div>
                                    </Button>
                                </div>
                            }
                        </div>
                    </div>
                    <div className="h-full">
                        <div className="overflow-x-auto bg-white border rounded-lg text-nowrap">
                            <Table hoverable theme={table_custom}>
                                <Table.Head className="bg-slate-100">
                                    <Table.HeadCell>
                                        <Checkbox
                                            color={"blue"}
                                            checked={isCheckAll}
                                            id={0}
                                            onChange={(e) => {
                                                headerCheckBoxChecked(e);
                                            }}
                                        />
                                    </Table.HeadCell>
                                    <Table.HeadCell>
                                        <Link
                                            href={route(
                                                "whatsapp.campaign.index",
                                                {
                                                    column: "startTime",
                                                    sort:
                                                        getData.sort == "asc"
                                                            ? "desc"
                                                            : "asc",
                                                    perPage: getData.perPage,
                                                }
                                            )}
                                        >
                                            <div className="flex items-center justify-between gap-2">
                                                <h3>Start Time</h3>
                                                <PiCaretUpDownBold
                                                    className={
                                                        (getData.column ==
                                                            "startTime"
                                                            ? "  text-gray-900 dark:text-gray-200 "
                                                            : "text-gray-600 dark:text-gray-400 ") +
                                                        "  cursor-pointer "
                                                    }
                                                />
                                            </div>
                                        </Link>
                                    </Table.HeadCell>

                                    <Table.HeadCell>
                                        <Link
                                            href={route(
                                                "whatsapp.campaign.index",
                                                {
                                                    column: "name",
                                                    sort:
                                                        getData.sort == "asc"
                                                            ? "desc"
                                                            : "asc",
                                                    perPage: getData.perPage,
                                                }
                                            )}
                                        >
                                            <div className="flex items-center justify-between gap-2">
                                                <h3>Name</h3>
                                                <PiCaretUpDownBold
                                                    className={
                                                        (getData.column ==
                                                            "name"
                                                            ? "  text-gray-900 dark:text-gray-200 "
                                                            : "text-gray-600 dark:text-gray-400 ") +
                                                        "  cursor-pointer "
                                                    }
                                                />
                                            </div>
                                        </Link>
                                    </Table.HeadCell>
                                    <Table.HeadCell>Progress</Table.HeadCell>

                                    <Table.HeadCell>
                                        <Link
                                            href={route(
                                                "whatsapp.campaign.index",
                                                {
                                                    column: "status",
                                                    sort:
                                                        getData.sort == "asc"
                                                            ? "desc"
                                                            : "asc",
                                                    perPage: getData.perPage,
                                                }
                                            )}
                                        >
                                            <div className="flex items-center justify-between gap-2">
                                                <h3>Status</h3>
                                                <PiCaretUpDownBold
                                                    className={
                                                        (getData.column ==
                                                            "status"
                                                            ? "  text-gray-900 dark:text-gray-200 "
                                                            : "text-gray-600 dark:text-gray-400 ") +
                                                        "  cursor-pointer "
                                                    }
                                                />
                                            </div>
                                        </Link>
                                    </Table.HeadCell>

                                    <Table.HeadCell>
                                        <Link
                                            href={route(
                                                "whatsapp.campaign.index",
                                                {
                                                    column: "sleepAfterMsgs",
                                                    sort:
                                                        getData.sort == "asc"
                                                            ? "desc"
                                                            : "asc",
                                                    perPage: getData.perPage,
                                                }
                                            )}
                                        >
                                            <div className="flex items-center justify-between gap-2">
                                                <h3>Sleep After</h3>
                                                <PiCaretUpDownBold
                                                    className={
                                                        (getData.column ==
                                                            "sleepAfterMsgs"
                                                            ? "  text-gray-900 dark:text-gray-200 "
                                                            : "text-gray-600 dark:text-gray-400 ") +
                                                        "  cursor-pointer "
                                                    }
                                                />
                                            </div>
                                        </Link>
                                    </Table.HeadCell>
                                    <Table.HeadCell>
                                        <Link
                                            href={route(
                                                "whatsapp.campaign.index",
                                                {
                                                    column: "sleepForSeconds",
                                                    sort:
                                                        getData.sort == "asc"
                                                            ? "desc"
                                                            : "asc",
                                                    perPage: getData.perPage,
                                                }
                                            )}
                                        >
                                            <div className="flex items-center justify-between gap-2">
                                                <span>Sleep For</span>
                                                <PiCaretUpDownBold
                                                    className={
                                                        (getData.column ==
                                                            "sleepForSeconds"
                                                            ? "  text-gray-900 dark:text-gray-200 "
                                                            : "text-gray-600 dark:text-gray-400 ") +
                                                        "  cursor-pointer "
                                                    }
                                                />
                                            </div>
                                        </Link>
                                    </Table.HeadCell>
                                    <Table.HeadCell>User</Table.HeadCell>
                                    <Table.HeadCell>
                                        <h3>Actions</h3>
                                    </Table.HeadCell>
                                </Table.Head>
                                <Table.Body className="divide-y">
                                    {campaigns.data.length != 0 ? (
                                        campaigns.data.map((campaign, k) => {
                                            return (
                                                <Table.Row
                                                    // onClick={() => {
                                                    //     setIsOpenDetail(true);
                                                    //     setCampaignID({
                                                    //         id: campaign.id,
                                                    //         name: campaign.name,
                                                    //         status: campaign.status,
                                                    //     });
                                                    // }}
                                                    className="items-center bg-white dark:border-gray-700 dark:bg-gray-800"
                                                    key={"campaign" + k}
                                                >
                                                    <Table.Cell>
                                                        <Checkbox
                                                            color={"blue"}
                                                            className="rowCheckBox"
                                                            id={campaign.id}
                                                            onChange={(e) => {
                                                                getCheckedIds(
                                                                    e
                                                                );
                                                                checkAddcheckBoxChecked();
                                                            }}
                                                        />
                                                    </Table.Cell>
                                                    <Table.Cell>
                                                        <div className="flex flex-col">
                                                            <span>
                                                                {
                                                                    convertDateFormat(
                                                                        campaign.startTime
                                                                        , "date")
                                                                }
                                                            </span>
                                                            <span>
                                                                {
                                                                    convertDateFormat(
                                                                        campaign.startTime
                                                                        , "time")
                                                                }
                                                            </span>
                                                        </div>
                                                    </Table.Cell>

                                                    <Table.Cell
                                                        onClick={() => {
                                                            setIsOpenDetail(
                                                                true
                                                            );
                                                            setCampaignID({
                                                                id: campaign.id,
                                                                name: campaign.name,
                                                                status: campaign.status,
                                                            });
                                                        }}
                                                        className="text-blue-600 cursor-pointer "
                                                    >
                                                        {campaign.name}
                                                    </Table.Cell>
                                                    <Table.Cell>
                                                        <div className="flex flex-col gap-1 py-1">
                                                            <Progress
                                                                color="blue"
                                                                progress={
                                                                    (campaign.completedContacts == 0) ? 0 :
                                                                        ((campaign.completedContacts /
                                                                            campaign.totalContacts) *
                                                                            100)
                                                                }
                                                            />
                                                            <span className="">
                                                                {
                                                                    campaign.completedContacts
                                                                }
                                                                &nbsp;Out
                                                                of&nbsp;
                                                                {
                                                                    campaign.totalContacts
                                                                }
                                                                &nbsp;Completed
                                                            </span>
                                                        </div>
                                                    </Table.Cell>
                                                    <Table.Cell>
                                                        <div className="flex">
                                                            {isDateGreaterThanToday(
                                                                campaign.startTime
                                                            ) ? (
                                                                [0].includes(
                                                                    campaign.status
                                                                ) ? (
                                                                    <Badge
                                                                        className="cursor-default"
                                                                        color={
                                                                            statusCodes[6]
                                                                                .bg
                                                                        }
                                                                    >
                                                                        {
                                                                            statusCodes[6]
                                                                                .text
                                                                        }
                                                                    </Badge>
                                                                ) : (
                                                                    <Badge
                                                                        className="cursor-default"
                                                                        color={
                                                                            statusCodes[
                                                                                campaign
                                                                                    .status
                                                                            ].bg
                                                                        }
                                                                    >
                                                                        {
                                                                            statusCodes[
                                                                                campaign
                                                                                    .status
                                                                            ]
                                                                                .text
                                                                        }
                                                                    </Badge>
                                                                )
                                                            ) : (
                                                                <Badge
                                                                    className="cursor-default"
                                                                    color={
                                                                        statusCodes[
                                                                            campaign
                                                                                .status
                                                                        ].bg
                                                                    }
                                                                >
                                                                    {
                                                                        statusCodes[
                                                                            campaign
                                                                                .status
                                                                        ].text
                                                                    }
                                                                </Badge>
                                                            )}
                                                        </div>
                                                    </Table.Cell>

                                                    <Table.Cell>
                                                        {
                                                            campaign.sleepAfterMsgs
                                                        }
                                                        sec.
                                                    </Table.Cell>
                                                    <Table.Cell>
                                                        {
                                                            campaign.sleepForSeconds
                                                        }
                                                        sec.
                                                    </Table.Cell>
                                                    <Table.Cell>
                                                        {campaign.user.username}
                                                    </Table.Cell>
                                                    <Table.Cell>
                                                        <div className="relative flex items-center justify-between gap-3">
                                                            <div className="flex items-center gap-3 ">
                                                                {collection.can_edit && [0, 2].includes(campaign.status) &&
                                                                    <Tooltip placement="left-end"
                                                                        content="Edit Campaign"
                                                                    >
                                                                        <Button
                                                                            theme={
                                                                                button_custom
                                                                            }
                                                                            color="success"
                                                                            size="xs"
                                                                            as={Link}
                                                                            href={route('whatsapp.campaign.edit', { campaign: campaign.id })}
                                                                        >
                                                                            <MdOutlineEdit className="text-sm" />
                                                                            <span className="text-xs ms-1">
                                                                                Edit
                                                                            </span>
                                                                        </Button>
                                                                    </Tooltip>
                                                                }
                                                                {[3].includes(campaign.status)
                                                                    ? (
                                                                        <>
                                                                            <Tooltip
                                                                                placement="left"
                                                                                content={"CSV Report Download"}
                                                                            >

                                                                                {(campaign.reportUrl == null) ?
                                                                                    <Button
                                                                                        theme={
                                                                                            button_custom
                                                                                        }
                                                                                        onClick={() => setDisabledBtnId(`export${campaign.id}`)}
                                                                                        color="blue"
                                                                                        size="xs"
                                                                                        isProcessing={campaign.isReportReady == 0}
                                                                                        disabled={
                                                                                            ((campaign.isReportReady == 0)
                                                                                                ||
                                                                                                (disabledBtnId == `export${campaign.id}`)
                                                                                            ) ? true : false
                                                                                        }
                                                                                    >
                                                                                        <TbFileTypeCsv className="text-sm" />
                                                                                        <span className="text-xs ms-1">
                                                                                            Export
                                                                                        </span>
                                                                                    </Button>
                                                                                    :
                                                                                    <Button
                                                                                        theme={
                                                                                            button_custom
                                                                                        }
                                                                                        onClick={() => setDisabledBtnId(`export${campaign.id}`)}
                                                                                        color="blue"
                                                                                        size="xs"
                                                                                        isProcessing={campaign.isReportReady == 0}
                                                                                        disabled={
                                                                                            ((campaign.isReportReady == 0)
                                                                                                ||
                                                                                                (disabledBtnId == `export${campaign.id}`)
                                                                                            ) ? true : false
                                                                                        }
                                                                                    >
                                                                                        <TbFileTypeCsv className="text-sm" />
                                                                                        <span className="text-xs ms-1">
                                                                                            Export
                                                                                        </span>
                                                                                    </Button>
                                                                                }

                                                                            </Tooltip>

                                                                            {campaign.reportRequest ==
                                                                                0 ? (
                                                                                <Tooltip
                                                                                    content={" Generate Report"}
                                                                                >
                                                                                    <Button
                                                                                        onClick={() => {
                                                                                            setDisabledBtnId(`generateReport${campaign.id}`)
                                                                                            HandleChangeFlag("reportRequest", campaign.id, 1)
                                                                                        }
                                                                                        }
                                                                                        theme={
                                                                                            button_custom
                                                                                        }
                                                                                        color="blue"
                                                                                        disabled={(disabledBtnId == `generateReport${campaign.id}`) ? true : false}
                                                                                        size="xs"
                                                                                    >
                                                                                        <TbReport className="text-sm" />
                                                                                        <span className="text-xs ms-1">
                                                                                            Generate
                                                                                            Report
                                                                                        </span>
                                                                                    </Button>
                                                                                </Tooltip>
                                                                            ) : (
                                                                                <>

                                                                                </>
                                                                            )}
                                                                        </>
                                                                    ) : (
                                                                        <></>
                                                                    )}

                                                                {[1].includes(
                                                                    campaign.status
                                                                ) ? (
                                                                    <Tooltip
                                                                        placement="left"
                                                                        content={"Pause Campaign"}
                                                                    >
                                                                        <Button
                                                                            onClick={() => {
                                                                                setDisabledBtnId(`pause${campaign.id}`);
                                                                                HandleChangeFlag("status", campaign.id, 2)
                                                                            }
                                                                            }
                                                                            disabled={(disabledBtnId == `pause${campaign.id}`) ? true : false}
                                                                            theme={
                                                                                button_custom
                                                                            }
                                                                            color="Fuchsia_custom"
                                                                            size="xs"
                                                                        >
                                                                            <IoPauseOutline className="text-sm" />
                                                                            <span className="text-xs ms-1">
                                                                                Pause
                                                                            </span>
                                                                        </Button>
                                                                    </Tooltip>
                                                                ) : (
                                                                    <></>
                                                                )}
                                                                {[
                                                                    0, 2,
                                                                ].includes(
                                                                    campaign.status
                                                                ) ? (
                                                                    <Tooltip content="Play">
                                                                        <Button
                                                                            onClick={() => {
                                                                                setDisabledBtnId(`play${campaign.id}`);
                                                                                HandleChangeFlag(
                                                                                    "status",
                                                                                    campaign.id,
                                                                                    1
                                                                                )
                                                                            }
                                                                            }
                                                                            disabled={(disabledBtnId == `play${campaign.id}`) ? true : false}
                                                                            theme={
                                                                                button_custom
                                                                            }
                                                                            color="orange"
                                                                            size="xs"
                                                                        >
                                                                            <FiPlay className="text-sm" />
                                                                            <span className="text-xs ms-1">
                                                                                Play
                                                                            </span>
                                                                        </Button>
                                                                    </Tooltip>
                                                                ) : (
                                                                    <></>
                                                                )}
                                                                {[
                                                                    1, 2,
                                                                ].includes(
                                                                    campaign.status
                                                                ) ? (
                                                                    <Tooltip content="Stop">
                                                                        <Button
                                                                            onClick={() => {
                                                                                setWarningConfirmOpen(true);
                                                                                setCheckValue([campaign.id]);
                                                                                setDisabledBtnId(`stop${campaign.id}`);
                                                                            }
                                                                            }
                                                                            disabled={(disabledBtnId == `stop${campaign.id}`) ? true : false}
                                                                            theme={
                                                                                button_custom
                                                                            }
                                                                            color="Pink_custom"
                                                                            size="xs"
                                                                        >
                                                                            <TbHandStop className="text-sm" />
                                                                            <span className="text-xs ms-1">
                                                                                Stop
                                                                            </span>
                                                                        </Button>
                                                                    </Tooltip>
                                                                ) : (
                                                                    <></>
                                                                )}
                                                                {
                                                                    ([
                                                                        1
                                                                    ].includes(
                                                                        campaign.status
                                                                    )) ? <></> : collection.can_delete && <Tooltip content="Delete">
                                                                        <Button
                                                                            theme={
                                                                                button_custom
                                                                            }
                                                                            disabled={(disabledBtnId == `delete${campaign.id}`) ? true : false}
                                                                            color="failure"
                                                                            size="xs"
                                                                            onClick={() => { setDisabledBtnId(`delete${campaign.id}`); setCheckValue([campaign.id]); setConfirmOpen(true); }}
                                                                        >
                                                                            <RiDeleteBin6Line className="text-sm" />
                                                                            <span className="text-xs ms-1">
                                                                                Delete
                                                                            </span>
                                                                        </Button>
                                                                    </Tooltip>
                                                                }
                                                            </div>

                                                        </div>
                                                    </Table.Cell>
                                                </Table.Row>
                                            );
                                        })
                                    ) : (
                                        <Table.Row>
                                            <Table.Cell colSpan={10}>
                                                <NoRecord />
                                            </Table.Cell>
                                        </Table.Row>
                                    )}
                                </Table.Body>
                            </Table>
                        </div>
                    </div>

                    <div className="w-full p-3 bg-white">
                        <div className="flex flex-wrap justify-between gap-2 lg:gap-0 md:gap-0">
                            <div className="flex items-center gap-4">
                                <PerPageDropdown
                                    getDataFields={getData ?? null}
                                    routeName={"whatsapp.campaign.index"}
                                    data={campaigns}
                                />
                            </div>
                            <Paginate tableData={campaigns} />
                        </div>
                    </div>
                </div>
            </div>

            {
                isDetailsOpen ? (
                    <Drawer
                        theme={customDrawer}
                        className="w-full lg:w-4/6 md:w-4/5"
                        open={isDetailsOpen}
                        onClose={() => setIsDetailsOpen(false)}
                        position="right"
                    >
                        <DrawerHeader
                            titleIcon={MdOutlineCampaign}
                            title={<span>Details ({campaignID.name})</span>}
                        />
                        <DrawerItems className="py-2">
                            <View campaign={campaignID.id}></View>
                        </DrawerItems>
                    </Drawer>
                ) : (
                    <></>
                )
            }
            {
                isOpenDetail ? (
                    <Drawer
                        theme={customDrawer}
                        className="w-full lg:w-5/6 md:w-4/5"
                        open={isOpenDetail}
                        onClose={() => setIsOpenDetail(false)}
                        position="right"
                    >
                        <DrawerHeader
                            titleIcon={MdOutlineCampaign}
                            title={
                                <div className="flex items-center gap-2">
                                    Campaign Details ({campaignID.name})

                                </div>
                            }
                        />
                        <DrawerItems className="pb-2">
                            <Details campaign={campaignID.id}></Details>
                        </DrawerItems>
                    </Drawer>
                ) : (
                    <></>
                )
            }

            {/* delete confirm box popup */}
            {
                isConfirmOpen &&
                <ConfirmBox
                    isOpen={isConfirmOpen}
                    onClose={() => setConfirmOpen(false)} // Close the confirm box
                    onAction={handleConfirmBoxResult} // Handle the user's choice
                    title="Are you sure you want to delete this?"
                    message="This action cannot be undone."
                    confirmText="Yes, Delete!"
                    cancelText="No, Keep It"
                    confirmColor="orange"
                    cancelColor="gray"
                    icon={<FaRegTrashCan />}
                />
            }
            {/* warning confirm box popup */}
            {
                isWarningConfirmOpen &&
                <ConfirmBox
                    isOpen={isWarningConfirmOpen}
                    onClose={() => setWarningConfirmOpen(false)} // Close the confirm box
                    onAction={handleWarningConfirmBoxResult} // Handle the user's choice
                    title="Terminate Campaign ?"
                    message="Are you sure you want to terminate this campaign?"
                    confirmText="Yes, Terminate!"
                    cancelText="No, Keep It"
                    confirmColor="orange"
                    cancelColor="gray"
                    icon={<FaRegTrashCan />}
                />
            }
        </WaMain >
    );
}
