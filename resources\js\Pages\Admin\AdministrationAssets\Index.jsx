// import UserRoleTabBar from "@/Components/UserRoleTabBar";
import { Head } from "@inertiajs/react";
import {
    <PERSON><PERSON>,
    Drawer,
    DrawerItems,
    Badge,
} from "flowbite-react";

import { useState } from "react";
import { IoClose } from "react-icons/io5";
import Filters from "../../Users/<USER>";
import AssignAssets from "./AssignAssets";
import Main from "@/Layouts/Main";
import { button_custom } from "@/Pages/Helpers/DesignHelper";
import SideMenu from "../SideMenu";
import { showAsset } from "@/Pages/Helpers/Helper";

function Index() {
    // ----------------Drawer Assign Assets --------------------
    const [isOpenAssignAssets, setIsOpenAssignAssets] = useState(false);
    const handleCloseAssignAssets = () => setIsOpenAssignAssets(false);
    const customTheme = {
        root: {
            position: {
                right: {
                    on: "right-0 top-0 h-screen w-[70vw] transform-none bg-slate-100",
                    off: "right-0 top-0 h-screen w-80 translate-x-full",
                },
            },
        },
        header: {
            inner: {
                closeButton:
                    "absolute end-2.5 top-2.5 flex h-8 w-8 items-center justify-center rounded-lg bg-transparent text-sm text-gray-400 hover:bg-gray-200 hover:text-gray-900 dark:hover:bg-gray-600 dark:hover:text-white",
                closeIcon: "h-4 w-4",
                titleIcon: "me-2.5 h-6 w-6 text-gray-400",
                titleText:
                    "mb-4 inline-flex items-center text-lg font-semibold text-sky-950 dark:text-gray-400",
            },
        },
    };

    return (
        <Main>
            <Head title="User" />
            <div className="relative overflow-hidden p-2">
                <div className="grid grid-flow-row-dense grid-cols-12 gap-2 p-0 border-none md:grid-cols-12 sm:grid-cols-1 bg-slate-100">
                    <div className="hidden bg-white border rounded-lg shadow-sm xl:col-span-2 lg:col-span-3 md:col-span-3 sm:col-span-3 lg:flex">
                        <div className="w-full overflow-auto dark:bg-gray-900">
                            {/* <Filter /> */}
                            <SideMenu></SideMenu>
                        </div>
                    </div>
                    <div className="relative pt-0 dark:text-gray-400 lg:p-0 xl:col-span-10 lg:col-span-9 md:col-span-12 col-span-full">
                        {/* <div className="w-full mb-2 col-span-full">
                                        <UserRoleTabBar />
                                    </div> */}
                        <div className="overflow-auto bg-white border rounded-lg h-fit">
                            <div className=" dark:text-gray-400 lg:p-0 pt-0 col-span-full relative">
                                <div className=" bg-white border rounded-lg overflow-auto">
                                    <div className="grid 2xl:grid-cols-7 xl:grid-cols-6 lg:grid-cols-4 md:grid-cols-4 sm:grid-cols-3 grid-cols-2 gap-4 p-3">
                                        <div className="flex flex-col justify-between ">
                                            <div>
                                                <div className="border border-red-600 mb-2 h-28 flex flex-col justify-center p-2">
                                                    <div className="flex justify-end">
                                                        <Badge className="rounded-full bg-red-700 text-white h-5 w-5 justify-center">
                                                            2
                                                        </Badge>
                                                    </div>
                                                    <div className="flex justify-center">
                                                        <img
                                                            className="h-20"
                                                            src={showAsset("/assets/img/keyboard.png", '')}
                                                            alt=""
                                                        />
                                                    </div>
                                                </div>
                                                <div>
                                                    <h4 className="text-sm font-semibold text-slate-600">
                                                        Keyboard (5)
                                                    </h4>
                                                    <h6 className="text-xs text-gray-600">
                                                        Brand: Dell
                                                    </h6>
                                                </div>
                                            </div>
                                            <div className="flex items-center gap-2 pt-2">
                                                <Button className="w-full"
                                                    theme={button_custom}
                                                    size="xxs"
                                                    color="blue"
                                                    onClick={() =>
                                                        setIsOpenAssignAssets(true)
                                                    }
                                                >
                                                    Assign
                                                </Button>
                                                <Button className="w-full"
                                                    theme={button_custom}
                                                    size="xxs"
                                                    color="green"
                                                    onClick={() =>
                                                        setIsOpenAssignAssets(true)
                                                    }
                                                >
                                                    Manage
                                                </Button>
                                            </div>
                                        </div>
                                        <div className="flex flex-col justify-between ">
                                            <div>
                                                <div className="border border-slate-300 mb-2 h-28 flex flex-col justify-center p-2">
                                                    <div className="flex justify-center">
                                                        <img
                                                            className="h-20"
                                                            src={showAsset("/assets/img/mouse.png", '')}
                                                            alt=""
                                                        />
                                                    </div>
                                                </div>
                                                <div>
                                                    <h4 className="text-sm font-semibold text-slate-600">
                                                        Mouse (20)
                                                    </h4>
                                                    <h6 className="text-xs text-gray-600">
                                                        Brand: HP
                                                    </h6>
                                                </div>
                                            </div>
                                            <div className="flex items-center gap-2 pt-2">
                                                <Button className="w-full"
                                                    theme={button_custom}
                                                    size="xxs"
                                                    color="blue"
                                                    onClick={() =>
                                                        setIsOpenAssignAssets(true)
                                                    }
                                                >
                                                    Assign
                                                </Button>
                                                <Button className="w-full"
                                                    theme={button_custom}
                                                    size="xxs"
                                                    color="green"
                                                    onClick={() =>
                                                        setIsOpenAssignAssets(true)
                                                    }
                                                >
                                                    Manage
                                                </Button>
                                            </div>
                                        </div>
                                        <div className="flex flex-col justify-between ">
                                            <div>
                                                <div className="border border-red-600 mb-2 h-28 flex flex-col justify-center p-2">
                                                    <div className="flex justify-end">
                                                        <Badge className="rounded-full bg-red-700 text-white h-5 w-5 justify-center">
                                                            2
                                                        </Badge>
                                                    </div>
                                                    <div className="flex justify-center">
                                                        <img
                                                            className="h-20"
                                                            src={showAsset("/assets/img/sim.png", '')}
                                                            alt=""
                                                        />
                                                    </div>
                                                </div>
                                                <div>
                                                    <h4 className="text-sm font-semibold text-slate-600">
                                                        SIM(4)
                                                    </h4>
                                                    <h6 className="text-xs text-gray-600">
                                                        Brand: JIO
                                                    </h6>
                                                </div>
                                            </div>
                                            <div className="flex items-center gap-2 pt-2">
                                                <Button className="w-full"
                                                    theme={button_custom}
                                                    size="xxs"
                                                    color="blue"
                                                    onClick={() =>
                                                        setIsOpenAssignAssets(true)
                                                    }
                                                >
                                                    Assign
                                                </Button>
                                                <Button className="w-full"
                                                    theme={button_custom}
                                                    size="xxs"
                                                    color="green"
                                                    onClick={() =>
                                                        setIsOpenAssignAssets(true)
                                                    }
                                                >
                                                    Manage
                                                </Button>
                                            </div>
                                        </div>
                                        <div className="flex flex-col justify-between ">
                                            <div>
                                                <div className="border border-slate-300 mb-2 h-28 flex flex-col justify-center p-2">
                                                    <div className="flex justify-center">
                                                        <img
                                                            className="h-20"
                                                            src={showAsset("/assets/img/mobile.png", '')}
                                                            alt=""
                                                        />
                                                    </div>
                                                </div>
                                                <div>
                                                    <h4 className="text-sm font-semibold text-slate-600">
                                                        Mobile (10)
                                                    </h4>
                                                    <h6 className="text-xs text-gray-600">
                                                        Brand: Realme, 3GB RAM, 128 GB
                                                        Storage
                                                    </h6>
                                                </div>
                                            </div>
                                            <div className="flex items-center gap-2 pt-2">
                                                <Button className="w-full"
                                                    theme={button_custom}
                                                    size="xxs"
                                                    color="blue"
                                                    onClick={() =>
                                                        setIsOpenAssignAssets(true)
                                                    }
                                                >
                                                    Assign
                                                </Button>
                                                <Button className="w-full"
                                                    theme={button_custom}
                                                    size="xxs"
                                                    color="green"
                                                    onClick={() =>
                                                        setIsOpenAssignAssets(true)
                                                    }
                                                >
                                                    Manage
                                                </Button>
                                            </div>
                                        </div>
                                        <div className="flex flex-col justify-between ">
                                            <div>
                                                <div className="border border-slate-300 mb-2 h-28 flex flex-col justify-center p-2">
                                                    <div className="flex justify-center">
                                                        <img
                                                            className="h-20"
                                                            src={showAsset("/assets/img/earbuds.png", '')}
                                                            alt=""
                                                        />
                                                    </div>
                                                </div>
                                                <div>
                                                    <h4 className="text-sm font-semibold text-slate-600">
                                                        Earphone (2)
                                                    </h4>
                                                    <h6 className="text-xs text-gray-600">
                                                        Brand: Boat, Model: 150
                                                    </h6>
                                                </div>
                                            </div>
                                            <div className="flex items-center gap-2 pt-2">
                                                <Button className="w-full"
                                                    theme={button_custom}
                                                    size="xxs"
                                                    color="blue"
                                                    onClick={() =>
                                                        setIsOpenAssignAssets(true)
                                                    }
                                                >
                                                    Assign
                                                </Button>
                                                <Button className="w-full"
                                                    theme={button_custom}
                                                    size="xxs"
                                                    color="green"
                                                    onClick={() =>
                                                        setIsOpenAssignAssets(true)
                                                    }
                                                >
                                                    Manage
                                                </Button>
                                            </div>
                                        </div>
                                        <div className="flex flex-col justify-between ">
                                            <div>
                                                <div className="border border-slate-300 mb-2 h-28 flex flex-col justify-center p-2">
                                                    <div className="flex justify-center">
                                                        <img
                                                            className="h-20"
                                                            src={showAsset("/assets/img/laptop.png", '')}
                                                            alt=""
                                                        />
                                                    </div>
                                                </div>
                                                <div>
                                                    <h4 className="text-sm font-semibold text-slate-600">
                                                        Laptop (0)
                                                    </h4>
                                                    <h6 className="text-xs text-gray-600">
                                                        Brand: Lenovo, 12GB RAM, 256GB
                                                        SSD
                                                    </h6>
                                                </div>
                                            </div>
                                            <div className="flex items-center gap-2 pt-2">
                                                <Button className="w-full"
                                                    theme={button_custom}
                                                    size="xxs"
                                                    color="blue"
                                                    onClick={() =>
                                                        setIsOpenAssignAssets(true)
                                                    }
                                                >
                                                    Assign
                                                </Button>
                                                <Button className="w-full"
                                                    theme={button_custom}
                                                    size="xxs"
                                                    color="green"
                                                    onClick={() =>
                                                        setIsOpenAssignAssets(true)
                                                    }
                                                >
                                                    Manage
                                                </Button>
                                            </div>
                                        </div>
                                        <div className="flex flex-col justify-between ">
                                            <div>
                                                <div className="border border-slate-300 mb-2 h-28 flex flex-col justify-center p-2">
                                                    <div className="flex justify-center">
                                                        <img
                                                            className="h-20"
                                                            src={showAsset("/assets/img/adaptor.png", '')}
                                                            alt=""
                                                        />
                                                    </div>
                                                </div>
                                                <div>
                                                    <h4 className="text-sm font-semibold text-slate-600">
                                                        Laptop Adapter (1)
                                                    </h4>
                                                    <h6 className="text-xs text-gray-600">
                                                        Brand: Lenovo, 65W, 20V, 3.25A
                                                    </h6>
                                                </div>
                                            </div>
                                            <div className="flex items-center gap-2 pt-2">
                                                <Button className="w-full"
                                                    theme={button_custom}
                                                    size="xxs"
                                                    color="blue"
                                                    onClick={() =>
                                                        setIsOpenAssignAssets(true)
                                                    }
                                                >
                                                    Assign
                                                </Button>
                                                <Button className="w-full"
                                                    theme={button_custom}
                                                    size="xxs"
                                                    color="green"
                                                    onClick={() =>
                                                        setIsOpenAssignAssets(true)
                                                    }
                                                >
                                                    Manage
                                                </Button>
                                            </div>
                                        </div>
                                        <div className="flex flex-col justify-between ">
                                            <div>
                                                <div className="border border-slate-300 mb-2 h-28 flex flex-col justify-center p-2">
                                                    <div className="flex justify-center">
                                                        <img
                                                            className="h-20"
                                                            src={showAsset("/assets/img/cpu.png", '')}
                                                            alt=""
                                                        />
                                                    </div>
                                                </div>
                                                <div>
                                                    <h4 className="text-sm font-semibold text-slate-600">
                                                        CPU (30)
                                                    </h4>
                                                    <h6 className="text-xs text-gray-600">
                                                        Brand: Dell, i5 6th generation,
                                                        16GB RAM, 512GB SSD
                                                    </h6>
                                                </div>
                                            </div>
                                            <div className="flex items-center gap-2 pt-2">
                                                <Button className="w-full"
                                                    theme={button_custom}
                                                    size="xxs"
                                                    color="blue"
                                                    onClick={() =>
                                                        setIsOpenAssignAssets(true)
                                                    }
                                                >
                                                    Assign
                                                </Button>
                                                <Button className="w-full"
                                                    theme={button_custom}
                                                    size="xxs"
                                                    color="green"
                                                    onClick={() =>
                                                        setIsOpenAssignAssets(true)
                                                    }
                                                >
                                                    Manage
                                                </Button>
                                            </div>
                                        </div>
                                        <div className="flex flex-col justify-between ">
                                            <div>
                                                <div className="border border-slate-300 mb-2 h-28 flex flex-col justify-center p-2">
                                                    <div className="flex justify-center">
                                                        <img
                                                            className="h-20"
                                                            src={showAsset("/assets/img/cpuAdaptor.png", '')}
                                                            alt=""
                                                        />
                                                    </div>
                                                </div>
                                                <div>
                                                    <h4 className="text-sm font-semibold text-slate-600">
                                                        CPU Adapter (1)
                                                    </h4>
                                                    <h6 className="text-xs text-gray-600">
                                                        Brand: Lenovo, 150W, 19.5V, 7.7A
                                                    </h6>
                                                </div>
                                            </div>
                                            <div className="flex items-center gap-2 pt-2">
                                                <Button className="w-full"
                                                    theme={button_custom}
                                                    size="xxs"
                                                    color="blue"
                                                    onClick={() =>
                                                        setIsOpenAssignAssets(true)
                                                    }
                                                >
                                                    Assign
                                                </Button>
                                                <Button className="w-full"
                                                    theme={button_custom}
                                                    size="xxs"
                                                    color="green"
                                                    onClick={() =>
                                                        setIsOpenAssignAssets(true)
                                                    }
                                                >
                                                    Manage
                                                </Button>
                                            </div>
                                        </div>
                                        <div className="flex flex-col justify-between ">
                                            <div>
                                                <div className="border border-slate-300 mb-2 h-28 flex flex-col justify-center p-2">
                                                    <div className="flex justify-center">
                                                        <img
                                                            className="h-20"
                                                            src={showAsset("/assets/img/monitor.png", '')}
                                                            alt=""
                                                        />
                                                    </div>
                                                </div>
                                                <div>
                                                    <h4 className="text-sm font-semibold text-slate-600">
                                                        Monitor (35)
                                                    </h4>
                                                    <h6 className="text-xs text-gray-600">
                                                        Brand: Acer, 18”
                                                    </h6>
                                                </div>
                                            </div>
                                            <div className="flex items-center gap-2 pt-2">
                                                <Button className="w-full"
                                                    theme={button_custom}
                                                    size="xxs"
                                                    color="blue"
                                                    onClick={() =>
                                                        setIsOpenAssignAssets(true)
                                                    }
                                                >
                                                    Assign
                                                </Button>
                                                <Button className="w-full"
                                                    theme={button_custom}
                                                    size="xxs"
                                                    color="green"
                                                    onClick={() =>
                                                        setIsOpenAssignAssets(true)
                                                    }
                                                >
                                                    Manage
                                                </Button>
                                            </div>
                                        </div>
                                        <div className="flex flex-col justify-between ">
                                            <div>
                                                <div className="border border-slate-300 mb-2 h-28 flex flex-col justify-center p-2">
                                                    <div className="flex justify-center">
                                                        <img
                                                            className="h-20"
                                                            src={showAsset("/assets/img/notebook.png", '')}
                                                            alt=""
                                                        />
                                                    </div>
                                                </div>
                                                <div>
                                                    <h4 className="text-sm font-semibold text-slate-600">
                                                        Note Book (100)
                                                    </h4>
                                                    <h6 className="text-xs text-gray-600">
                                                        200 Pages (14.5cm x 22.5cm)
                                                    </h6>
                                                </div>
                                            </div>
                                            <div className="flex items-center gap-2 pt-2">
                                                <Button className="w-full"
                                                    theme={button_custom}
                                                    size="xxs"
                                                    color="blue"
                                                    onClick={() =>
                                                        setIsOpenAssignAssets(true)
                                                    }
                                                >
                                                    Assign
                                                </Button>
                                                <Button className="w-full"
                                                    theme={button_custom}
                                                    size="xxs"
                                                    color="green"
                                                    onClick={() =>
                                                        setIsOpenAssignAssets(true)
                                                    }
                                                >
                                                    Manage
                                                </Button>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                {/* <div className="grid grid-cols-12 md:grid-cols-12 sm:grid-cols-1 gap-2 grid-flow-row-dense bg-slate-100 p-0 border-none">

                </div> */}
            </div>

            {/*--------- Documents Drawer ------- */}
            <Drawer
                theme={customTheme}
                open={isOpenAssignAssets}
                onClose={handleCloseAssignAssets}
                position="right"
                className="w-full lg:w-4/5 p-0"
            >
                <DrawerItems>
                    <div className="flex items-center justify-between mb-3 bg-white px-4 py-2">
                        <div className="flex items-center gap-2">
                            <div className="h-12 w-12 rounded-full text-center bg-gray-200 flex justify-center items-center">
                                <img
                                    className="max-h-full max-w-full"
                                    src="assets/img/assignAssetsCircle.png"
                                    alt=""
                                />
                            </div>
                            <div>
                                <h4 className="text-base font-medium text-blue-600">
                                    CPU Adapter
                                </h4>
                                <h6 className="text-xs">
                                    Brand: Lenovo, 150W, 19.5V, 7.7A
                                </h6>
                            </div>
                        </div>
                        <button
                            className="bg-transparent"
                            onClick={handleCloseAssignAssets}
                        >
                            <IoClose
                                className="text-[24px] text-slate-400
                                                                    "
                            />
                        </button>
                    </div>
                    <AssignAssets></AssignAssets>
                </DrawerItems>
            </Drawer>
        </Main>
    );
}

export default Index;



