<?php

namespace App\Http\Controllers\Andro;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Inertia\Inertia;

class SMSController extends Controller
{
    public function index()
    {
        return Inertia::render('Andro/Index');
    }

    public function dashboard()
    {
        return Inertia::render('Andro/Dashboard');
    }
    public function campaign()
    {
        return Inertia::render('Andro/Campaign/Index');
    }
    public function addCampaign()
    {
        return Inertia::render('Andro/Campaign/AddCampaign');
    }
    public function messages()
    {
        return Inertia::render('Andro/Messages/Index');
    }
    public function bots()
    {
        return Inertia::render('Andro/Bots/Index');
    }

    public function listKeyword()
    {
        return Inertia::render('Andro/Bots/ListKeyword');
    }

    public function templates()
    {
        return Inertia::render('Andro/Templates/Index');
    }

    public function contacts()
    {
        return Inertia::render('Andro/Contacts');
    }
    public function gateways()
    {
        return Inertia::render('Andro/Gateways/Index');
    }
}
