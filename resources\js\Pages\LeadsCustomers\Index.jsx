import ConfirmBox from "@/Components/HelperComponents/ConfirmBox";
import NoRecord from "@/Components/HelperComponents/NoRecord";
import Paginate from "@/Components/HelperComponents/Paginate";
import PerPageDropdown from "@/Components/HelperComponents/PerPageDropdown";
import Main from "@/Layouts/Main";
import { button_custom, customDrawer, table_custom } from "@/Pages/Helpers/DesignHelper";
import { Head, Link, router } from "@inertiajs/react";
import { Avatar, Badge, Button, Checkbox, DrawerHeader, DrawerItems, Table, Tooltip } from "flowbite-react";
import { useEffect, useState } from "react";
import { FaLongArrowAltRight } from "react-icons/fa";
import { FaRegTrashCan } from "react-icons/fa6";
import { IoIosAddCircle, IoIosArrowDown, IoMdAdd } from "react-icons/io";
import {
    MdOutlineGroupAdd,
    MdOutlinePermContactCalendar
} from "react-icons/md";
import { RiDeleteBin6Line, RiHistoryFill } from "react-icons/ri";
import { TbListDetails } from "react-icons/tb";
import Add from "./Add";
import AssignLead from "./AssignLead";
import IndexDetails from "./Details/IndexDetails";
import Edit from "./Edit";
import Followup from "./Followup";
import LeadContact from "./LeadContact";
import SideMenu from "./SideMenu";
import TabBar from "./TabBar";

const statusArry = [
    { label: "New Lead", color: "blue", id: 0 },
    { label: "Connected", color: "success", id: 1 },
    { label: "International", color: "indigo", id: 2 },
    { label: "Attempted", color: "warning", id: 3 },
    { label: "Call not Picked", color: "failure", id: 4 },
    { label: "Follow up", color: "info", id: 5 },
    { label: "Merge", color: "yellow", id: 6 },
    { label: "Grab", color: "pink", id: 7 }
];

function Index({ collection }) {

    const [leadData, setLeadData] = useState(collection?.leads?.data || []);
    const [selectedLeads, setSelectedLeads] = useState([]);
    const [disableAssignbtn, setDisableAssignBtn] = useState(true);
    const [isConfirmOpen, setConfirmOpen] = useState(false);

    const [isDrawerOpen, setIsDrawerOpen] = useState({
        leadContact: false,
        followup: false,
        details: false,
        assignLead: false,
        addLead: false,
        editLead: false,
    });

    const [DrawerData, setDrawerData] = useState({
        leadContact: null,
        followup: null,
        details: null,
        addLead: null,
        editLead: null,
    })

    useEffect(() => {
        if (selectedLeads.length >= 500) {
            setSelectedLeads(prevSelectedLeads => prevSelectedLeads.slice(0, -1));
            alert("Cannot select more than 500 leads at once");
        }
        if (selectedLeads.some(item => Object.values(item)[0] != null)) {
            setDisableAssignBtn(true);
        } else {
            (selectedLeads.length > 0) ? setDisableAssignBtn(false) : setDisableAssignBtn(true);
        }
    }, [selectedLeads]);

    function handleConfirmBoxResult(result) {
        if (selectedLeads.length > 0 && result) {
            setConfirmOpen(false);
            setSelectedLeads([])

            router.delete(
                route("leadsAndCustomers.leads.destroy", { lead: selectedLeads.map(item => Object.keys(item)[0]).join(",") })
            );
        } else {
            setSelectedLeads([]);
        }
        setConfirmOpen(false);
    }

    return (
        <Main>
            <Head title="Leads & Customers" />
            <div className="relative overflow-hidden p-2">
                <div className="grid grid-cols-12 md:grid-cols-12 sm:grid-cols-1 gap-2 grid-flow-row-dense bg-slate-100 p-0 border-none">
                    <div className="lg:col-span-2 md:col-span-3 sm:col-span-3 lg:flex hidden">
                        <div className="bg-white shadow-sm rounded-lg border dark:bg-gray-900 w-full overflow-auto min-h-[90vh] max-h-[90vh]">
                            <SideMenu />
                        </div>
                    </div>

                    <div className="dark:text-gray-400 lg:p-0 pt-0 lg:col-span-10 md:col-span-12 col-span-full relative">
                        <div className="col-span-full mb-2 ">
                            <TabBar />
                        </div>
                        <div className="min-h-[80vh] max-h-[80vh]  border rounded-lg">
                            <div className="flex justify-between p-2 bg-white">
                                <div className="flex items-center gap-1">
                                    <Button.Group>
                                        {
                                            collection?.can_delete ?
                                                <Button
                                                    theme={button_custom}
                                                    size="xs"
                                                    color="gray"
                                                    onClick={() =>
                                                        setConfirmOpen(true)
                                                    }
                                                >
                                                    <div className="flex items-center gap-1">
                                                        <RiDeleteBin6Line className="text-slate-500" />
                                                        <span className="text-xs">
                                                            Delete
                                                        </span>
                                                    </div>
                                                </Button> : <></>
                                        }

                                        {/* <Button
                                            theme={button_custom}
                                            size="xs"
                                            color="gray"
                                            id="dropdownInformationButton"
                                            data-dropdown-toggle="dropdownNotification"
                                            type="button"
                                        >
                                            <div className="flex items-center gap-1">
                                                <TbColumns3 className="text-slate-500" />
                                                <span className="text-xs">
                                                    Columns
                                                </span>
                                            </div>
                                        </Button> */}
                                    </Button.Group>
                                    {/* <Button
                                        theme={button_custom}
                                        size="xs"
                                        color="gray"
                                    >
                                        <div className="flex items-center gap-1">
                                            <PiExportBold className="text-slate-500" />
                                            <span className="text-xs">
                                                Export
                                            </span>
                                        </div>
                                    </Button> */}
                                </div>

                                <Button.Group className="">
                                    {
                                        collection?.can_assign ?
                                            <Tooltip
                                                content="Please select leads to assign"
                                                className="bg-slate-700 p-1"
                                                hidden={!disableAssignbtn}
                                            >
                                                <Button
                                                    theme={button_custom}
                                                    size="xs"
                                                    color="gray"
                                                    disabled={disableAssignbtn}
                                                    onClick={() => {
                                                        setIsDrawerOpen((prev) => ({
                                                            ...prev,
                                                            assignLead: !prev.assignLead
                                                        }))
                                                    }
                                                    }
                                                >
                                                    <div className="flex items-center gap-1">
                                                        <IoMdAdd className="text-slate-500" />
                                                        <span className="text-xs">
                                                            Assign
                                                        </span>
                                                    </div>
                                                </Button>
                                            </Tooltip> : <></>
                                    }
                                    {
                                        collection?.can_add ?
                                            <Button
                                                theme={button_custom}
                                                size="xs"
                                                color="gray"
                                                onClick={() =>
                                                    setIsDrawerOpen((prev) => ({
                                                        ...prev,
                                                        addLead: !prev.addLead
                                                    }))
                                                }
                                            >
                                                <div className="flex items-center gap-1">
                                                    <IoMdAdd className="text-slate-500" />
                                                    <span className="text-xs">
                                                        Add Lead
                                                    </span>
                                                </div>
                                            </Button> : <></>
                                    }
                                </Button.Group>
                            </div>

                            <div className="table-responsive overflow-x-scroll bg-white overflow-auto h-[70vh] rounded-lg mb-3 relative">
                                <Table hoverable theme={table_custom} className="text-nowrap">
                                    <Table.Head className="sticky top-0 z-10">
                                        <Table.HeadCell>
                                            <Checkbox color="blue"
                                                checked={selectedLeads.length === leadData.length}
                                                onChange={() => {
                                                    if (selectedLeads.length === leadData.length) {
                                                        setSelectedLeads([]);
                                                    } else {
                                                        setSelectedLeads(leadData.map(lead => ({ [lead.id]: lead.assignedTo_id })));
                                                    }
                                                }}
                                            />
                                        </Table.HeadCell>
                                        <Table.HeadCell>Name</Table.HeadCell>
                                        <Table.HeadCell>Assigned</Table.HeadCell>
                                        <Table.HeadCell>Next Follow up</Table.HeadCell>
                                        <Table.HeadCell>Status</Table.HeadCell>
                                        <Table.HeadCell>Remark</Table.HeadCell>
                                        <Table.HeadCell className="col-span-2 "></Table.HeadCell>
                                        <Table.HeadCell></Table.HeadCell>
                                    </Table.Head>

                                    <Table.Body className="divide-y">
                                        {
                                            leadData && leadData.length > 0 ?
                                                leadData.map((lead, index) => {

                                                    const datetime = new Date(lead?.lead_next_followup?.nextFollowup);
                                                    const formattedDate = (datetime == "Invalid Date") ? "N/A" : datetime.toLocaleDateString("en-GB", {
                                                        day: "2-digit",
                                                        month: "2-digit",
                                                        year: "numeric"
                                                    });

                                                    // Format time: hh:mm AM/PM
                                                    const formattedTime = (datetime == "Invalid Date") ? "N/A" : datetime.toLocaleTimeString("en-GB", {
                                                        hour: "numeric",
                                                        minute: "2-digit"
                                                    });

                                                    return (
                                                        <Table.Row key={index}>
                                                            <Table.Cell>
                                                                <Checkbox color="blue" className="rowCheckBox" checked={selectedLeads.some(item => Object.keys(item)[0] === lead.id.toString())} onChange={() => {
                                                                    if (selectedLeads.some(item => Object.keys(item)[0] === lead.id.toString())) {
                                                                        setSelectedLeads(selectedLeads.filter(item => Object.keys(item)[0] !== lead.id.toString()));
                                                                    } else {
                                                                        setSelectedLeads([...selectedLeads, { [lead.id]: lead.assignedTo_id }]);
                                                                    }
                                                                }} />
                                                            </Table.Cell>
                                                            <Table.Cell className="whitespace-nowrap dark:text-white">
                                                                <div className="flex justify-between ">
                                                                    <div className="flex flex-col">
                                                                        <Link
                                                                            href={route('leadsAndCustomers.leads.show', lead.id)}
                                                                            target="_blank"
                                                                            className="text-blue-600 text-sm hover:underline"
                                                                        >
                                                                            {lead?.name} ({lead.id})
                                                                        </Link>
                                                                        <div className="text-xs">
                                                                            {lead?.businessName}
                                                                        </div>
                                                                    </div>
                                                                    <Tooltip
                                                                        className="p-1 px-2 bg-slate-700"
                                                                        content="Approval Pending">
                                                                        <div className="bg-red-600 h-fit p-0.5 rounded-full">
                                                                            <RiHistoryFill className="text-white text-xl" />
                                                                        </div>
                                                                    </Tooltip>

                                                                </div>
                                                            </Table.Cell>
                                                            <Table.Cell>
                                                                <div className="relative">
                                                                    {
                                                                        collection?.can_assign &&
                                                                            lead?.status == 7 ?
                                                                            <div className="flex gap-1 items-center">
                                                                                <Tooltip //fix this later at time of grab setup
                                                                                    content="Abhishek"
                                                                                    className="bg-slate-700 p-1"
                                                                                >
                                                                                    <Avatar size="md" img={"https://img.freepik.com/premium-photo/profile-icon-white-background_941097-160810.jpg"} alt="avatar of Jese" rounded statusPosition="top-right" status="online" />
                                                                                </Tooltip>
                                                                                <FaLongArrowAltRight className="text-red-600" />
                                                                                <Tooltip  //fix this later at time of grab setup
                                                                                    content="Abhishek"
                                                                                    className="bg-slate-700 p-1"
                                                                                >
                                                                                    <Avatar size="md" img={"https://img.freepik.com/premium-photo/profile-icon-white-background_941097-160810.jpg"} alt="avatar of Jese" rounded statusPosition="top-right" status="online" />
                                                                                </Tooltip>
                                                                            </div>
                                                                            :
                                                                            !lead?.assigned_to ?
                                                                                <Button
                                                                                    size="xxs"
                                                                                    color="transparentForTab"
                                                                                    className="mx-auto"
                                                                                    onClick={() => {
                                                                                        setIsDrawerOpen((prev) => ({
                                                                                            ...prev,
                                                                                            assignLead: !prev.assignLead
                                                                                        }));
                                                                                        setSelectedLeads([lead.id]);
                                                                                    }
                                                                                    }
                                                                                >
                                                                                    <IoIosAddCircle className="text-blue-600 text-4xl" />
                                                                                </Button> :
                                                                                <div className="flex justify-center">
                                                                                    <Tooltip //fix this later at time of grab setup
                                                                                        content={lead?.assigned_to?.username}
                                                                                        className="bg-slate-700 p-1"
                                                                                    >
                                                                                        <Avatar size="md" img={lead?.assigned_to?.image || "https://img.freepik.com/premium-photo/profile-icon-white-background_941097-160810.jpg"} alt="avatar of Jese" rounded statusPosition="top-right" status="online" />
                                                                                    </Tooltip>
                                                                                </div>
                                                                    }
                                                                </div>
                                                            </Table.Cell>
                                                            <Table.Cell onClick={() =>
                                                                setIsDrawerOpen((prev) => ({
                                                                    ...prev,
                                                                    followup: !prev.followup
                                                                }))
                                                            } className="cursor-pointer whitespace-nowrap dark:text-white">
                                                                <div className="flex flex-col">
                                                                    <div className="text-blue-600 text-sm ">
                                                                        {formattedDate}
                                                                    </div>
                                                                    <div className="text-xs">
                                                                        {formattedTime}
                                                                    </div>
                                                                </div>
                                                            </Table.Cell>

                                                            <Table.Cell>
                                                                <Badge className="w-fit text-nowrap" color={statusArry[lead.status].color}>{statusArry[lead.status].label}</Badge>
                                                            </Table.Cell>
                                                            <Table.Cell>
                                                                <div className="flex gap-2 justify-between ">
                                                                    <div className="">
                                                                        <div className="text-sm">
                                                                            {lead.remark}
                                                                        </div>
                                                                    </div>
                                                                </div>
                                                            </Table.Cell>
                                                            <Table.Cell>
                                                                <div className="flex gap-2 h-fit text-nowrap">
                                                                    {[
                                                                        {
                                                                            icon: <MdOutlinePermContactCalendar />,
                                                                            text: "Lead Contact",
                                                                            color: "blue",
                                                                            permission: true,
                                                                            onClick: () => setIsDrawerOpen((prev) => ({
                                                                                ...prev,
                                                                                leadContact: !prev.leadContact
                                                                            }))
                                                                        },
                                                                        // {
                                                                        //     icon: <MdOutlineModeEditOutline />,
                                                                        //     text: "Edit",
                                                                        //     color: "green",
                                                                        //     permission: collection?.can_edit,
                                                                        //     onClick: () => {
                                                                        //         setIsDrawerOpen((prev) => ({
                                                                        //             ...prev,
                                                                        //             editLead: !prev.editLead
                                                                        //         }));
                                                                        //         setDrawerData((prev) => ({
                                                                        //             ...prev,
                                                                        //             editLead: lead.id
                                                                        //         }));
                                                                        //     }
                                                                        // },
                                                                        {
                                                                            icon: <TbListDetails />,
                                                                            text: "Activity",
                                                                            permission: true,
                                                                            color: "orange",
                                                                            onClick: () => setIsDrawerOpen((prev) => ({
                                                                                ...prev,
                                                                                details: !prev.details
                                                                            }))
                                                                        },
                                                                        {
                                                                            icon: <RiDeleteBin6Line />,
                                                                            text: "Delete",
                                                                            color: "failure",
                                                                            permission: collection?.can_delete,
                                                                            onClick: () => {
                                                                                setSelectedLeads([{ [lead.id]: lead.assignedTo_id }]);
                                                                                setConfirmOpen(true);
                                                                            }
                                                                        }
                                                                    ].map((btn, index) => (
                                                                        btn.permission ?
                                                                            <Button
                                                                                key={index}
                                                                                theme={button_custom}
                                                                                color={btn.color}
                                                                                size="xs"
                                                                                onClick={btn.onClick}
                                                                            >
                                                                                <div className="flex items-center gap-1">
                                                                                    {btn.icon}
                                                                                    <span className="text-xs">{btn.text}</span>
                                                                                </div>
                                                                            </Button> : <></>
                                                                    ))}
                                                                </div>
                                                            </Table.Cell>

                                                            <Table.Cell>
                                                                <div>
                                                                    <button
                                                                        className="p-1.5"
                                                                        id="parentID"
                                                                        onClick={() =>
                                                                            ToggleChild(
                                                                                "parentID",
                                                                                "childTr"
                                                                            )
                                                                        }
                                                                    >
                                                                        <IoIosArrowDown />
                                                                    </button>
                                                                </div>
                                                            </Table.Cell>
                                                        </Table.Row>
                                                    );
                                                })
                                                : <div className="text-center">
                                                    <NoRecord />
                                                </div>
                                        }




                                        {/* <Table.Row
                                            className="hidden bg-gray-100 transition duration-150 ease-in-out"
                                            id="childTr"
                                        >
                                            <Table.Cell colSpan={9}>
                                                <Details></Details>
                                            </Table.Cell>
                                        </Table.Row> */}
                                        {/* <Table.Row className="">
                                            <Table.Cell>
                                                <Checkbox color="blue" className="rowCheckBox" />
                                            </Table.Cell>
                                            <Table.Cell className="whitespace-nowrap dark:text-white">
                                                <div className="flex justify-between ">
                                                    <div className="flex flex-col">
                                                        <div className="text-blue-600 text-sm ">
                                                            Ruchika Khatri (2568)
                                                        </div>
                                                        <div className="text-xs">
                                                            Dunes Factory Pvt. Ltd.
                                                        </div>
                                                    </div>
                                                    <Tooltip
                                                        className="p-1 px-2 bg-slate-700"
                                                        content="Approval Pending">
                                                        <div className="bg-red-600 h-fit p-0.5 rounded-full">
                                                            <RiHistoryFill className="text-white text-xl" />
                                                        </div>
                                                    </Tooltip>

                                                </div>
                                            </Table.Cell>
                                            <Table.Cell>
                                                <div className="flex gap-1 items-center">
                                                    <Tooltip
                                                        content="Abhishek"
                                                        className="bg-slate-700 p-1"
                                                    >
                                                        <Avatar size="md" img="https://img.freepik.com/premium-photo/profile-icon-white-background_941097-160810.jpg" alt="avatar of Jese" rounded />
                                                    </Tooltip>
                                                    <FaLongArrowAltRight className="text-red-600" />
                                                    <Tooltip
                                                        content="Abhishek"
                                                        className="bg-slate-700 p-1"
                                                    >
                                                        <Avatar size="md" img="https://img.freepik.com/premium-photo/profile-icon-white-background_941097-160810.jpg" alt="avatar of Jese" rounded />
                                                    </Tooltip>
                                                </div>
                                            </Table.Cell>
                                            <Table.Cell className="whitespace-nowrap dark:text-white">
                                                <div className="flex flex-col">
                                                    <div className="text-blue-600 text-sm ">
                                                        12/08/24
                                                    </div>
                                                    <div className="text-xs">
                                                        2:24 PM
                                                    </div>
                                                </div>
                                            </Table.Cell>
                                            <Table.Cell>
                                                <Badge className="w-fit text-nowrap" color="indigo">New Lead</Badge>
                                            </Table.Cell>

                                            <Table.Cell>
                                                <div className="flex gap-2 justify-between ">
                                                    <div className="">
                                                        <div className="text-sm">
                                                            Better follow-up needed to improve lead interaction.
                                                        </div>
                                                    </div>
                                                    <div className="flex gap-2 h-fit text-nowrap">
                                                        <Button
                                                            theme={
                                                                button_custom
                                                            }
                                                            color="blue"
                                                            size="xs"
                                                            onClick={() =>
                                                                setIsDrawerOpen((prev) => ({
                                                                    ...prev,
                                                                    leadContact: !prev.leadContact
                                                                }))
                                                            }
                                                        >
                                                            <div className="flex items-center gap-1">
                                                                <MdOutlinePermContactCalendar />
                                                                <span className="text-xs">Lead Contact</span>
                                                            </div>
                                                        </Button>
                                                        <Button
                                                            theme={
                                                                button_custom
                                                            }
                                                            color="green"
                                                            size="xs"
                                                            onClick={() =>
                                                                setIsDrawerOpen((prev) => ({
                                                                    ...prev,
                                                                    editLead: !prev.editLead
                                                                }))
                                                            }
                                                        >
                                                            <div className="flex items-center gap-1">
                                                                <MdOutlineModeEditOutline />
                                                                <span className="text-xs">Edit</span>
                                                            </div>
                                                        </Button>
                                                        <Button
                                                            theme={
                                                                button_custom
                                                            }
                                                            color="orange"
                                                            size="xs"
                                                            onClick={() =>
                                                                setIsDrawerOpen((prev) => ({
                                                                    ...prev,
                                                                    details: !prev.details
                                                                }))
                                                            }
                                                        >
                                                            <div className="flex items-center gap-1">
                                                                <MdOutlinePlagiarism />
                                                                <span className="text-xs">Details</span>
                                                            </div>
                                                        </Button>
                                                        <Button
                                                            theme={
                                                                button_custom
                                                            }
                                                            color="failure"
                                                            size="xs"
                                                            onClick={() =>
                                                                setIsDrawerOpen((prev) => ({
                                                                    ...prev,
                                                                    deleteLead: !prev.deleteLead
                                                                }))
                                                            }
                                                        >
                                                            <div className="flex items-center gap-1">
                                                                <RiDeleteBin6Line />
                                                                <span className="text-xs">Delete</span>
                                                            </div>
                                                        </Button>

                                                    </div>
                                                    <div>
                                                        <button
                                                            className="p-1.5"
                                                            id="parentID"
                                                            onClick={() =>
                                                                ToggleChild(
                                                                    "parentID",
                                                                    "childTr"
                                                                )
                                                            }
                                                        >
                                                            <svg
                                                                xmlns="http://www.w3.org/2000/svg"
                                                                height="23px"
                                                                viewBox="0 0 24 24"
                                                                width="23px"
                                                                className="fill-gray-500"
                                                            >
                                                                <path
                                                                    d="M0 0h24v24H0V0z"
                                                                    fill="none"
                                                                />
                                                                <path d="M7.41 8.59 12 13.17l4.59-4.58L18 10l-6 6-6-6 1.41-1.41z" />
                                                            </svg>
                                                        </button>
                                                    </div>
                                                </div>
                                            </Table.Cell>
                                        </Table.Row> */}
                                        {/* <Table.Row className="">
                                            <Table.Cell>
                                                <Checkbox color="blue" className="rowCheckBox" />
                                            </Table.Cell>
                                            <Table.Cell className="whitespace-nowrap dark:text-white">
                                                <div className="flex justify-between gap-2">
                                                    <div className="flex flex-col">
                                                        <div className="text-blue-600 text-sm ">
                                                            Ruchika Khatri (2568)
                                                        </div>
                                                        <div className="text-xs">
                                                            Dunes Factory Pvt. Ltd.
                                                        </div>
                                                    </div>
                                                    <Tooltip
                                                        className="p-1 px-2 bg-slate-700"
                                                        content="Approval Pending">
                                                        <div className="bg-red-600 h-fit p-0.5 rounded-full">
                                                            <RiHistoryFill className="text-white text-xl" />
                                                        </div>
                                                    </Tooltip>
                                                </div>
                                            </Table.Cell>
                                            <Table.Cell>
                                                <div className="flex gap-1 items-center">
                                                    <Tooltip
                                                        content="Abhishek"
                                                        className="bg-slate-700 p-1"
                                                    >
                                                        <Avatar
                                                            size="sm"
                                                            img="https://img.freepik.com/premium-photo/profile-icon-white-background_941097-160810.jpg"
                                                            className="rounded-full"
                                                        />
                                                    </Tooltip>
                                                </div>
                                            </Table.Cell>
                                            <Table.Cell className="whitespace-nowrap dark:text-white">
                                                <div className="flex flex-col">
                                                    <div className="text-blue-600 text-sm ">
                                                        12/08/24
                                                    </div>
                                                    <div className="text-xs">
                                                        2:24 PM
                                                    </div>
                                                </div>
                                            </Table.Cell>
                                            <Table.Cell>
                                                <Badge className="w-fit" color="indigo">New Lead</Badge>
                                            </Table.Cell>
                                            <Table.Cell>
                                                <div className="flex gap-2 justify-between ">
                                                    <div className="">
                                                        <div className="text-sm">
                                                            Better follow-up needed to improve lead interaction.
                                                        </div>
                                                    </div>
                                                    <div className="flex gap-2 h-fit text-nowrap">
                                                        <Button
                                                            theme={
                                                                button_custom
                                                            }
                                                            color="blue"
                                                            size="xs"
                                                            onClick={() =>
                                                                setIsDrawerOpen((prev) => ({
                                                                    ...prev,
                                                                    leadContact: !prev.leadContact
                                                                }))
                                                            }
                                                        >
                                                            <div className="flex items-center gap-1">
                                                                <MdOutlinePermContactCalendar />
                                                                <span className="text-xs">Lead Contact</span>
                                                            </div>
                                                        </Button>
                                                        <Button
                                                            theme={
                                                                button_custom
                                                            }
                                                            color="green"
                                                            size="xs"
                                                            onClick={() =>
                                                                setIsDrawerOpen((prev) => ({
                                                                    ...prev,
                                                                    editLead: !prev.editLead
                                                                }))
                                                            }
                                                        >
                                                            <div className="flex items-center gap-1">
                                                                <MdOutlineModeEditOutline />
                                                                <span className="text-xs">Edit</span>
                                                            </div>
                                                        </Button>
                                                        <Button
                                                            theme={
                                                                button_custom
                                                            }
                                                            color="orange"
                                                            size="xs"
                                                            onClick={() =>
                                                                setIsDrawerOpen((prev) => ({
                                                                    ...prev,
                                                                    details: !prev.details
                                                                }))
                                                            }
                                                        >
                                                            <div className="flex items-center gap-1">
                                                                <MdOutlinePlagiarism />
                                                                <span className="text-xs">Details</span>
                                                            </div>
                                                        </Button>
                                                        <Button
                                                            theme={
                                                                button_custom
                                                            }
                                                            color="failure"
                                                            size="xs"
                                                            onClick={() =>
                                                                setIsDrawerOpen((prev) => ({
                                                                    ...prev,
                                                                    deleteLead: !prev.deleteLead
                                                                }))
                                                            }
                                                        >
                                                            <div className="flex items-center gap-1">
                                                                <RiDeleteBin6Line />
                                                                <span className="text-xs">Delete</span>
                                                            </div>
                                                        </Button>

                                                    </div>
                                                    <div>
                                                        <button
                                                            className="p-1.5"
                                                            id="parentID"
                                                            onClick={() =>
                                                                ToggleChild(
                                                                    "parentID",
                                                                    "childTr"
                                                                )
                                                            }
                                                        >
                                                            <svg
                                                                xmlns="http://www.w3.org/2000/svg"
                                                                height="23px"
                                                                viewBox="0 0 24 24"
                                                                width="23px"
                                                                className="fill-gray-500"
                                                            >
                                                                <path
                                                                    d="M0 0h24v24H0V0z"
                                                                    fill="none"
                                                                />
                                                                <path d="M7.41 8.59 12 13.17l4.59-4.58L18 10l-6 6-6-6 1.41-1.41z" />
                                                            </svg>
                                                        </button>
                                                    </div>
                                                </div>
                                            </Table.Cell>
                                        </Table.Row> */}
                                        {/* <Table.Row className="hidden" id="childTr">
                                            <Table.Cell colSpan={7}>
                                                <Details></Details>
                                            </Table.Cell>
                                        </Table.Row> */}
                                    </Table.Body>
                                </Table>
                            </div>
                            <div className="w-full p-3 bg-white">
                                <div className="flex flex-wrap justify-between gap-2 lg:gap-0 md:gap-0">
                                    <div className="flex items-center gap-4">
                                        <PerPageDropdown
                                            getDataFields={collection?.getData || null}
                                            routeName={"leadsAndCustomers.leads.index"}
                                            data={collection?.leads}
                                        />
                                    </div>
                                    <Paginate tableData={collection?.leads} />
                                </div>
                            </div>
                        </div>

                    </div>
                </div>
            </div>
            {/* lead Contacts */}
            {
                isDrawerOpen.leadContact ? (
                    <Drawer
                        theme={customDrawer}
                        open={isDrawerOpen.leadContact}
                        onClose={() => setIsDrawerOpen((prev) => ({ ...prev, leadContact: !prev.leadContact }))}
                        position="right"
                        className="w-full xl:w-10/12 lg:w-full md:w-full p-0"
                    >
                        <DrawerHeader className="h-0" titleIcon="false" />
                        <DrawerItems>
                            <div className="bg-white p-2">
                                <div className="flex items-start justify-between flex-wrap">
                                    <div className="flex flex-col">
                                        <span className="text-blue-600 text-base font-medium">Ruchika Khatri (2568)</span>
                                        <span className="text-sm ">Dunes Factory Pvt. Ltd.</span>
                                        <div className="text-black text-sm">
                                            <span className="font-medium me-1">Reason:</span>
                                            <span>
                                                She need ₹1200/- WhatsApp cloud for 1 year.
                                            </span>
                                        </div>
                                    </div>
                                    <div>
                                        <div className="flex items-start gap-2 me-7">
                                            <div>
                                                <h4 className="text-base font-medium text-blue-600">
                                                    24/03/2024
                                                </h4>
                                                <h6 className="text-xs">
                                                    02:45 PM
                                                </h6>
                                            </div>
                                            <img
                                                className="rounded-full h-12 w-12"
                                                src="https://img.freepik.com/free-vector/businessman-character-avatar-isolated_24877-60111.jpg?t=st=1720614745~exp=1720618345~hmac=38ff343a9443982af738325ad4d54d1d39ba540be4c28013b03cf063347d255e&w=200"
                                                alt=""
                                            />
                                            {/* <Button theme={button_custom} size="xxs" color="transparentForTab"
                                            onClick={handleClose}>
                                            <IoClose className="text-slate-500 text-2xl" />
                                        </Button> */}
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <LeadContact />
                        </DrawerItems>
                    </Drawer>
                ) : (
                    <></>
                )
            }

            {/* Followup */}
            {
                isDrawerOpen.followup ? (
                    <Drawer
                        theme={customDrawer}
                        open={isDrawerOpen.followup}
                        onClose={() => setIsDrawerOpen((prev) => ({ ...prev, followup: !prev.followup }))}
                        position="right"
                        className="w-full xl:w-10/12 lg:w-full md:w-full p-0"
                    >
                        <DrawerHeader className="h-0" titleIcon="false" />
                        <DrawerItems>
                            <div className="bg-white p-2">
                                <div className="flex items-start justify-between flex-wrap">
                                    <div className="flex flex-col">
                                        <span className="text-blue-600 text-base font-medium">Ruchika Khatri (2568)</span>
                                        <span className="text-sm ">Dunes Factory Pvt. Ltd.</span>
                                        <div className="text-black text-sm">
                                            <span className="font-medium me-1">Reason:</span>
                                            <span>
                                                She need ₹1200/- WhatsApp cloud for 1 year.
                                            </span>
                                        </div>
                                    </div>
                                    <div>
                                        <div className="flex items-start gap-2 me-7 pe-3">
                                            <div>
                                                <h4 className="text-base font-medium text-blue-600">
                                                    24/03/2024
                                                </h4>
                                                <h6 className="text-xs">
                                                    02:45 PM
                                                </h6>
                                            </div>
                                            <img
                                                className="rounded-full h-12 w-12"
                                                src="https://img.freepik.com/free-vector/businessman-character-avatar-isolated_24877-60111.jpg?t=st=1720614745~exp=1720618345~hmac=38ff343a9443982af738325ad4d54d1d39ba540be4c28013b03cf063347d255e&w=200"
                                                alt=""
                                            />
                                            {/* <Button theme={button_custom} size="xxs" color="transparentForTab"
                                            onClick={handleClose}>
                                            <IoClose className="text-slate-500 text-2xl" />
                                        </Button> */}
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <Followup />
                        </DrawerItems>
                    </Drawer>
                ) : (
                    <></>
                )
            }

            {/* Details */}
            {
                isDrawerOpen.details ? (
                    <Drawer
                        theme={customDrawer}
                        open={isDrawerOpen.details}
                        onClose={() => setIsDrawerOpen((prev) => ({ ...prev, details: !prev.details }))}
                        position="right"
                        className="w-full xl:w-10/12 lg:w-full md:w-full p-0"
                    >
                        <DrawerHeader className="h-0" titleIcon="false" />
                        <DrawerItems>
                            <div className="bg-white p-2">
                                <div className="flex items-start justify-between flex-wrap">
                                    <div className="flex flex-col">
                                        <span className="text-blue-600 text-base font-medium">Ruchika Khatri (2568)</span>
                                        <span className="text-sm ">Dunes Factory Pvt. Ltd.</span>
                                        <div className="text-black text-sm">
                                            <span className="font-medium me-1">Reason:</span>
                                            <span>
                                                She need ₹1200/- WhatsApp cloud for 1 year.
                                            </span>
                                        </div>
                                    </div>
                                    <div>
                                        <div className="flex items-start gap-2 me-7 pe-3">
                                            <div>
                                                <h4 className="text-base font-medium text-blue-600">
                                                    24/03/2024
                                                </h4>
                                                <h6 className="text-xs">
                                                    02:45 PM
                                                </h6>
                                            </div>
                                            <img
                                                className="rounded-full h-12 w-12"
                                                src="https://img.freepik.com/free-vector/businessman-character-avatar-isolated_24877-60111.jpg?t=st=1720614745~exp=1720618345~hmac=38ff343a9443982af738325ad4d54d1d39ba540be4c28013b03cf063347d255e&w=200"
                                                alt=""
                                            />
                                            {/* <Button theme={button_custom} size="xxs" color="transparentForTab"
                                            onClick={handleClose}>
                                            <IoClose className="text-slate-500 text-2xl" />
                                        </Button> */}
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <IndexDetails />
                        </DrawerItems>
                    </Drawer>
                ) : (
                    <></>
                )
            }

            {/* Assign Leads */}
            {
                isDrawerOpen.assignLead ? (
                    <Drawer
                        theme={customDrawer}
                        open={isDrawerOpen.assignLead}
                        onClose={() => setIsDrawerOpen((prev) => ({ ...prev, assignLead: !prev.assignLead }))}
                        position="right"
                        className="w-full xl:w-10/12 lg:w-full md:w-full p-0"
                    >
                        <DrawerHeader className="h-0" titleIcon="false" />
                        <DrawerItems>
                            <AssignLead onClose={() => setIsDrawerOpen((prev) => ({ ...prev, assignLead: !prev.assignLead }))} leadIds={selectedLeads} setSelectedLeads={setSelectedLeads} />
                        </DrawerItems>
                    </Drawer>
                ) : (<></>)
            }
            {/* lead Add */}
            {
                isDrawerOpen.addLead ? (
                    <Drawer
                        theme={customDrawer}
                        open={isDrawerOpen.addLead}
                        onClose={() => setIsDrawerOpen((prev) => ({ ...prev, addLead: !prev.addLead }))}
                        position="right"
                        className="w-full xl:w-1/2 lg:w-full md:w-full p-0"
                    >
                        <DrawerHeader
                            className="p-2"
                            title="Add New Lead"
                            titleIcon={
                                MdOutlineGroupAdd
                            }
                        />
                        <DrawerItems>
                            <Add onClose={() => setIsDrawerOpen((prev) => ({ ...prev, addLead: !prev.addLead }))} />
                        </DrawerItems>
                    </Drawer>
                ) : (
                    <></>
                )
            }
            {/* lead Edit */}
            {
                isDrawerOpen.editLead ? (
                    <Drawer
                        theme={customDrawer}
                        open={isDrawerOpen.editLead}
                        onClose={() => setIsDrawerOpen((prev) => ({ ...prev, editLead: !prev.editLead }))}
                        position="right"
                        className="w-full xl:w-1/2 lg:w-full md:w-full p-0"
                    >
                        <DrawerHeader
                            className="p-2"
                            title="Edit Lead"
                            titleIcon={
                                MdOutlineGroupAdd
                            }
                        />
                        <DrawerItems>
                            <Edit onClose={() => setIsDrawerOpen((prev) => ({ ...prev, editLead: !prev.editLead }))} leadId={DrawerData.editLead} />
                        </DrawerItems>
                    </Drawer>
                ) : (
                    <></>
                )
            }

            {/* delete confirm box popup */}
            {
                isConfirmOpen &&
                <ConfirmBox
                    isOpen={isConfirmOpen}
                    onClose={() => setConfirmOpen(false)} // Close the confirm box
                    onAction={handleConfirmBoxResult} // Handle the user's choice
                    title="Are you sure you want to delete this?"
                    message="This action cannot be undone."
                    confirmText="Yes, Delete!"
                    cancelText="No, Keep It"
                    confirmColor="orange"
                    cancelColor="gray"
                    icon={<FaRegTrashCan />}
                />
            }

        </Main >
    );
}
export default Index;
