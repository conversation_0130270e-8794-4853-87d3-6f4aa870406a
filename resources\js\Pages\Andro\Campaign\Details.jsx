import ConfirmBox from "@/Components/HelperComponents/ConfirmBox";
import NoRecord from "@/Components/HelperComponents/NoRecord";
import { button_custom, table_custom } from "@/Pages/Helpers/DesignHelper";
import {
    changeFlag,
    isDateGreaterThanToday
} from "@/Pages/Helpers/Helper";
import { router } from "@inertiajs/react";
import { Badge, Button, Label, Modal, Table, Tooltip } from "flowbite-react";
import { useEffect, useRef, useState } from "react";
import { Chart } from "react-google-charts";
import { FaRegTrashCan, FaWhatsapp } from "react-icons/fa6";
import { IoMdAdd, IoMdClose, IoMdQrScanner } from "react-icons/io";
import { IoPauseOutline } from "react-icons/io5";
import { MdOutlineRocketLaunch } from "react-icons/md";
import { RiFileUserLine } from "react-icons/ri";
import { TbFileTypeCsv, TbHandStop, TbReport } from "react-icons/tb";
import QRCode from "./QrCode";

function Details({ campaign }) {
    const [campaignData, setCampaignData] = useState(null);
    const [isLoading, setIsLoading] = useState(true);
    const [disabledBtnId, setDisabledBtnId] = useState(null);

    const selectRef = useRef(null);

    // const { data: campData, loading: campLoading, error: campError } = useFetch(route("mail.campaign.show", { id: campaign }), refetch, "*");


    function campaignShowData() {
        fetch(route("mail.campaign.show", { id: campaign }))
            .then((res) => {
                return res.json();
            })
            .then((data) => {
                if (data.status == true) {
                    // setMessages(data.data.messages);
                    setCampaignData(data.collection);
                    setIsLoading(false);
                    if (selectRef.current) {
                        selectRef.current.value = "";
                    }
                } else {
                    // setMessages([]);
                    setCampaignData(null);
                    setIsLoading(false);
                    console.error("Error in fetching data.");
                }
            });
    }

    useEffect(() => {
        campaignShowData();
    }, []);

    const HandleChangeFlag = (Column, id, flag) => {
        let table = "mail_campaign";
        changeFlag(table, Column, id, flag);
        campaignShowData();
    };
    const selectInput_custom = {
        root: {
            base: "relative",
        },
        popup: {
            root: {
                base: "absolute top-10 z-50 block pt-2",
                inline: "relative top-0 z-auto",
                inner: "inline-block rounded-lg bg-white p-4 shadow-lg dark:bg-gray-700",
            },
            header: {
                base: "",
                title: "px-2 py-3 text-center font-semibold text-gray-900 dark:text-white",
                selectors: {
                    base: "mb-2 flex justify-between",
                    button: {
                        base: "rounded-lg bg-white px-5 py-2.5 text-sm font-semibold text-gray-900 hover:bg-gray-100 focus:outline-none focus:ring-2 focus:ring-gray-200 dark:bg-gray-700 dark:text-white dark:hover:bg-gray-600",
                        prev: "",
                        next: "",
                        view: "",
                    },
                },
            },
            view: {
                base: "p-1",
            },
            footer: {
                base: "mt-2 flex space-x-2",
                button: {
                    base: "w-full rounded-lg px-5 py-2 text-center text-sm font-medium focus:ring-4 focus:ring-cyan-300",
                    today: "bg-cyan-700 text-white hover:bg-cyan-800 dark:bg-cyan-600 dark:hover:bg-cyan-700",
                    clear: "border border-gray-300 bg-white text-gray-900 hover:bg-gray-100 dark:border-gray-600 dark:bg-gray-700 dark:text-white dark:hover:bg-gray-600",
                },
            },
        },
        views: {
            days: {
                header: {
                    base: "mb-1 grid grid-cols-7",
                    title: "h-6 text-center text-sm font-medium leading-6 text-gray-500 dark:text-gray-400",
                },
                items: {
                    base: "grid w-64 grid-cols-7",
                    item: {
                        base: "block flex-1 cursor-pointer rounded-lg border-0 text-center text-sm font-semibold leading-9 text-gray-900 hover:bg-gray-100 dark:text-white dark:hover:bg-gray-600",
                        selected: "bg-cyan-700 text-white hover:bg-cyan-600",
                        disabled: "text-gray-500",
                    },
                },
            },
            months: {
                items: {
                    base: "grid w-64 grid-cols-4",
                    item: {
                        base: "block flex-1 cursor-pointer rounded-lg border-0 text-center text-sm font-semibold leading-9 text-gray-900 hover:bg-gray-100 dark:text-white dark:hover:bg-gray-600",
                        selected: "bg-cyan-700 text-white hover:bg-cyan-600",
                        disabled: "text-gray-500",
                    },
                },
            },
            years: {
                items: {
                    base: "grid w-64 grid-cols-4",
                    item: {
                        base: "block flex-1 cursor-pointer rounded-lg border-0 text-center text-sm font-semibold leading-9 text-gray-900 hover:bg-gray-100 dark:text-white dark:hover:bg-gray-600",
                        selected: "bg-cyan-700 text-white hover:bg-cyan-600",
                        disabled: "text-gray-500",
                    },
                },
            },
            decades: {
                items: {
                    base: "grid w-64 grid-cols-4",
                    item: {
                        base: "block flex-1 cursor-pointer rounded-lg border-0 text-center text-sm font-semibold leading-9 text-gray-900 hover:bg-gray-100 dark:text-white dark:hover:bg-gray-600",
                        selected: "bg-cyan-700 text-white hover:bg-cyan-600",
                        disabled: "text-gray-500",
                    },
                },
            },
        },
    };

    const statusCodes = {
        0: { bg: "purple", text: "Draft" },
        1: { bg: "pink", text: "Started" },
        2: { bg: "info", text: "Paused" },
        3: { bg: "success", text: "Completed" },
        4: { bg: "failure", text: "Terminated" },
        6: { bg: "indigo", text: "Scheduled" },
    };

    const [chartData, setChartData] = useState();
    const [chartOptions, setChartOptions] = useState();
    const [isConfirmOpen, setConfirmOpen] = useState(false);
    const [openModal, setOpenModal] = useState(false);
    const [selectedGateways, setSelectedGateways] = useState([]);


    const [selectedGateway, setSelectedGateway] = useState();

    const updateChartData = () => {
        var pending =
            campaignData.campaign.totalContacts -
            campaignData.campaign.completedContacts;
        var failed = campaignData.campaign.failed_count;
        var nonWapp = campaignData.campaign.non_whatsapp_count;
        var complete = campaignData.campaign.completedContacts - failed - nonWapp;

        setChartData([
            ["Status", "Sent", "Pending", "Failed", "Non Whatsapp"],
            ["Detail", complete, pending, failed, nonWapp],
        ]);
        setChartOptions({
            title:
                campaignData.campaign.completedContacts +
                " Out of " +
                campaignData.campaign.totalContacts +
                " Completed",
            chartArea: { width: "70%", height: "100px" },
            colors: ["#28a745", "#c53da9", "#dc3545", "#ff6f00", "#dc3912"],
            isStacked: true,
            hAxis: {
                title: "Total Contacts",
                minValue: 0,
            },
        });
    };
    useEffect(() => {
        if (campaignData && campaignData?.campaign) {
            updateChartData();
        }
    }, [campaignData]);

    const addChannelToCampaign = () => {
        if (selectedGateway) {
            router.post(route("andro.campaign.update.add.gateway"), {
                campaign: campaign,
                gateway: selectedGateway
            }, {
                preserveState: true,
                preserveScroll: true,
                onSuccess: (page) => {
                    campaignShowData();
                },
                onError: (errors) => {
                    console.error("Error:", errors);
                }
            });
            campaignShowData();
        } else {
            alert("Select Gateway.");
            return false;
        }
    };



    function deleteRecords() {
        if (selectedGateways.length > 0) {
            router.delete(route("andro.campaign.gateway.destroy", { campaign: selectedGateways.toLocaleString() }));
        }
    }

    const handleConfirmBoxResult = (res) => {
        if (res) {
            deleteRecords();
        }
        campaignShowData();
        setSelectedGateways([]);
        setConfirmOpen(false);
    };


    return (
        <>
            <div className="grid grid-flow-row-dense grid-cols-12 p-0 pt-2 border-none md:grid-cols-12 sm:grid-cols-1 lg:gap-2">
                <div className="relative flex flex-col gap-2 pt-0 dark:text-gray-400 lg:p-0 xl:col-span-8 lg:col-span-8 md:col-span-12 col-span-full">
                    {/* Status, Buttons */}
                    <div className="flex items-center justify-between gap-1">
                        <div className="flex items-center gap-1">
                            {campaignData &&
                                campaignData?.campaign ? (
                                isDateGreaterThanToday(
                                    campaignData.campaign.startTime
                                ) ? (
                                    [0].includes(
                                        campaignData.campaign.status
                                    ) ? (
                                        <Badge className="cursor-default" color={statusCodes[6].bg} >{statusCodes[6].text} </Badge>
                                    ) : (
                                        <Badge className="cursor-default" color={statusCodes[campaignData.campaign.status].bg} >
                                            {statusCodes[campaignData.campaign.status].text}
                                        </Badge>
                                    )
                                ) : (
                                    <Badge className="cursor-default" color={statusCodes[campaignData.campaign.status].bg} > {statusCodes[campaignData.campaign.status].text} </Badge>
                                )
                            ) : (
                                <NoRecord loading={true} />
                            )}
                        </div>
                        {campaignData &&
                            campaignData?.campaign ? (
                            <div className="relative flex items-center justify-between gap-3">
                                <div className="flex items-center gap-3 ">
                                    {campaignData.campaign.isReportReady ==
                                        1 ? (
                                        <Tooltip content="Download Report">
                                            <Button theme={button_custom} color="blue" size="xs">
                                                <TbFileTypeCsv />
                                            </Button>
                                        </Tooltip>
                                    ) : (
                                        <></>
                                    )}
                                    <Tooltip content="Request Report">
                                        <Button
                                            onClick={() => {
                                                setDisabledBtnId(`generateReport${campaignData.campaign.id}`);
                                                HandleChangeFlag("reportRequest", campaignData.campaign.id, 1);
                                            }
                                            }
                                            theme={button_custom}
                                            color="blue"
                                            size="xs"
                                            disabled={!isLoading && (
                                                (
                                                    campaignData.campaign.reportRequest == 1 || campaignData.campaign.status != 3)
                                                || (disabledBtnId == `generateReport${campaignData.campaign.id}`)) ? true : false}
                                        >
                                            <TbReport className="text-sm" />
                                            <span className="text-xs ms-1">
                                                Generate Report
                                            </span>
                                        </Button>
                                    </Tooltip>

                                    <Button
                                        onClick={() => {
                                            setDisabledBtnId(`pause${campaignData.campaign.id}`)
                                            HandleChangeFlag(
                                                "status",
                                                campaignData.campaign.id,
                                                2
                                            )
                                        }
                                        }
                                        theme={button_custom}
                                        color="orange"
                                        size="xs"
                                        disabled={
                                            ((![1].includes(
                                                campaignData.campaign.status
                                            )) || (disabledBtnId == `pause${campaignData.campaign.id}`)) ? true : false
                                        }
                                    >
                                        <IoPauseOutline className="text-sm" />
                                        <span className="text-xs ms-1">
                                            Pause
                                        </span>
                                    </Button>

                                    <Button
                                        onClick={() => {
                                            setDisabledBtnId(`start${campaignData.campaign.id}`)
                                            HandleChangeFlag(
                                                "status",
                                                campaignData.campaign.id,
                                                1
                                            )
                                        }
                                        }
                                        theme={button_custom}
                                        color="success"
                                        size="xs"
                                        disabled={
                                            ((![0, 2].includes(
                                                campaignData.campaign.status
                                            )) || (disabledBtnId == `start${campaignData.campaign.id}`)) ? true : false
                                        }
                                    >
                                        {/* <IoPlayOutline className="text-sm" /> */}
                                        <div className="flex items-center gap-1">
                                            <MdOutlineRocketLaunch className="" />
                                            <span className="text-xs">
                                                Play
                                            </span>
                                        </div>
                                    </Button>

                                    <Button
                                        onClick={() => {
                                            setDisabledBtnId(`stop${campaignData.campaign.id}`)
                                            HandleChangeFlag(
                                                "status",
                                                campaignData.campaign.id,
                                                4
                                            )
                                        }
                                        }
                                        theme={button_custom}
                                        color="failure"
                                        size="xs"
                                        disabled={
                                            (![1, 2].includes(
                                                (campaignData.campaign.status
                                                )) || (disabledBtnId == `stop${campaignData.campaign.id}`)) ? true : false
                                        }
                                    >
                                        <TbHandStop className="text-sm" />
                                        <span className="text-xs ms-1">
                                            Stop
                                        </span>
                                    </Button>
                                </div>
                            </div>
                        ) : (
                            <NoRecord loading={true} />
                        )}
                    </div>

                    {/* Template Name, Category */}
                    <div className="bg-white rounded-lg p-3 flex flex-col gap-1.5">
                        {/* <Progress color="blue" progress={45} /> */}
                        {/* <div id="example3.1" style={{ height: "200px" }}></div> */}
                        {campaignData &&
                            campaignData?.campaign ? (
                            <div>
                                <Chart
                                    chartType="BarChart"
                                    width="100%"
                                    height="150px"
                                    data={chartData}
                                    options={chartOptions}
                                />
                            </div>
                        ) : (
                            <NoRecord loading={true} />
                        )}
                        <div className="p-2 mt-2 text-sm bg-white border rounded-lg">
                            <div className="grid grid-flow-row-dense grid-cols-12 gap-2 mt-2 border-none md:grid-cols-12 sm:grid-cols-1">
                                <div className="relative flex flex-col gap-2 p-2 border rounded-md lg:col-span-6 md:col-span-6 col-span-full">
                                    <div className="flex items-center justify-between w-full">
                                        <span>Start Time</span>
                                        <span className="text-gray-500">
                                            {campaignData?.campaign.startDatetime}
                                        </span>
                                    </div>
                                </div>
                                <div className="relative flex flex-col gap-2 p-2 border rounded-md lg:col-span-6 md:col-span-6 col-span-full">
                                    <div className="flex items-center justify-between w-full">
                                        <span>Stop Time</span>
                                        <span className="text-gray-500">

                                            {campaignData?.campaign.endDatetime}

                                        </span>
                                    </div>
                                </div>
                            </div>
                            <div className="flex items-center gap-1.5 w-full border p-2 mt-2 rounded-md">
                                <span>Sleep After </span>
                                <span className="text-gray-500">{campaignData?.campaign.sleepAfterMsgs}</span>
                                <span>Messages for </span>
                                <span className="text-gray-500">{campaignData?.campaign.sleepForSeconds}</span>
                                <span>Seconds. </span>
                            </div>
                        </div>
                    </div>

                    {/* Gateway */}
                    <div className="bg-white rounded-lg p-2 px-3 flex flex-col gap-1.5">
                        <div className="flex items-center justify-between gap-2">
                            <div className="flex items-center gap-2">
                                <FaWhatsapp className="text-slate-400" />
                                <span>Gateway</span>
                            </div>
                        </div>
                        <div className="overflow-x-auto border rounded-lg">
                            <Table hoverable theme={table_custom}>
                                <TableBody className="border-b divide-y">
                                    {
                                        campaignData?.campaign?.gateways ? (
                                            campaignData?.campaign.gateways.map(
                                                (gateway, k) => {
                                                    return (
                                                        <TableRow
                                                            className="bg-white dark:border-gray-700 dark:bg-gray-800"
                                                            id={
                                                                "campaign-gateway-2" +
                                                                k
                                                            }
                                                            key={k}
                                                        >
                                                            <TableCell className="font-medium text-gray-900 whitespace-nowrap dark:text-white">
                                                                {
                                                                    gateway.name
                                                                }
                                                            </TableCell>
                                                            {campaignData.campaign
                                                                .status != 4 ? (
                                                                <TableCell>
                                                                    <div className="flex items-center justify-end gap-2">
                                                                        <Button
                                                                            theme={
                                                                                button_custom
                                                                            }
                                                                            color="gray"
                                                                            size="xs"
                                                                            onClick={() =>
                                                                                setOpenModal(
                                                                                    true
                                                                                )
                                                                            }
                                                                        >
                                                                            <IoMdQrScanner className="text-sm" />
                                                                            <span className="text-xs ms-1">
                                                                                Login
                                                                            </span>
                                                                        </Button>

                                                                        <div className="flex">
                                                                            <Button
                                                                                theme={
                                                                                    button_custom
                                                                                }
                                                                                color="gray"
                                                                                size="xs"
                                                                                onClick={() => { setConfirmOpen(true); setSelectedGateways([gateway.pivot.id]) }}
                                                                            >
                                                                                <IoMdClose className="text-sm" />
                                                                                <span className="text-xs ms-1">
                                                                                    Remove
                                                                                </span>
                                                                            </Button>

                                                                        </div>
                                                                    </div>
                                                                </TableCell>
                                                            ) : (
                                                                <></>
                                                            )}
                                                        </TableRow>
                                                    );
                                                }
                                            )
                                        ) : (
                                            <TableRow className="bg-white dark:border-gray-700 dark:bg-gray-800">
                                                <TableCell colSpan={2}>
                                                    <NoRecord loading={isLoading} />
                                                </TableCell>
                                            </TableRow>
                                        )}
                                </TableBody>
                            </Table>
                            {campaignData &&
                                campaignData?.remainingGateways ? (
                                campaignData.remainingGateways.length > 0 &&
                                    ![4, 3].includes(
                                        campaignData.campaign.status
                                    ) ? (
                                    <div className="flex items-center gap-2 p-2">
                                        <Label className="">
                                            Add Gateway
                                        </Label>
                                        <div className="max-w-md">
                                            <select

                                                theme={selectInput_custom}
                                                className="focus:ring-blue-600 focus:border-blue-600 rounded-md text-sm p-1.5 border-gray-300"
                                                id="Gateway"
                                                ref={selectRef}
                                                required
                                                onChange={(e) =>
                                                    setSelectedGateway(
                                                        e.target.value
                                                    )
                                                }
                                            >
                                                <option value="" selected={true}>
                                                    Select Gateway
                                                </option>
                                                {campaignData.remainingGateways
                                                    .length != 0 ? (
                                                    campaignData.remainingGateways.map(
                                                        (ch, k) => (
                                                            <option
                                                                value={ch.id}
                                                                key={
                                                                    "Select-Gateway" +
                                                                    k
                                                                }
                                                            >
                                                                {ch.name}
                                                            </option>
                                                        )
                                                    )
                                                ) : (
                                                    <option value="">
                                                        No record
                                                    </option>
                                                )}
                                            </select>
                                        </div>
                                        <Button
                                            theme={button_custom}
                                            color="blue"
                                            size="xs"
                                            onClick={() =>
                                                addChannelToCampaign()
                                            }
                                        >
                                            <IoMdAdd />
                                        </Button>
                                    </div>
                                ) : (
                                    <></>
                                )
                            ) : (
                                <NoRecord loading={isLoading} />
                            )}
                        </div>
                    </div>

                    {/* Contact List */}
                    <div className="bg-white rounded-lg p-2 px-3 flex flex-col gap-1.5 mb-2">
                        <div className="flex items-center gap-2">
                            <RiFileUserLine className="text-slate-400" />
                            <span>Contact List</span>
                        </div>
                        <div className="overflow-x-auto border rounded-lg">
                            <Table hoverable theme={table_custom}>
                                <TableBody className="divide-y">
                                    {campaignData &&
                                        campaignData?.campaign ? (
                                        campaignData.campaign.contact_lists.map(
                                            (cList, k) => {
                                                return (
                                                    <TableRow className="bg-white dark:border-gray-700 dark:bg-gray-800" key={k}>
                                                        <TableCell className="font-medium text-gray-900 whitespace-nowrap dark:text-white">
                                                            {
                                                                cList.name
                                                            }
                                                        </TableCell>
                                                    </TableRow>
                                                );
                                            }
                                        )
                                    ) : (
                                        <TableRow>
                                            <TableCell>
                                                <NoRecord loading={isLoading} />
                                            </TableCell>
                                        </TableRow>
                                    )}
                                </TableBody>
                            </Table>
                        </div>
                    </div>
                </div>
                <div className="col-span-12 mb-2 xl:col-span-4 lg:col-span-4 md:col-span-12 sm:col-span-12 lg:flex">
                    <div
                        className="flex flex-wrap justify-start w-full h-full overflow-auto bg-white rounded-lg "
                        style={{ maxHeight: "700px" }}
                    >

                    </div>
                </div>
            </div>
            {openModal ? (
                <Modal show={openModal} onClose={() => setOpenModal(false)}>
                    <ModalHeader>Login</ModalHeader>
                    <ModalBody>
                        <QRCode />
                    </ModalBody>
                </Modal>
            ) : (
                ""
            )}
            {isConfirmOpen &&
                <ConfirmBox
                    isOpen={isConfirmOpen}
                    onClose={() => setConfirmOpen(false)}
                    onAction={handleConfirmBoxResult}
                    title="Are you sure you want to delete this?"
                    message="This action cannot be undone."
                    confirmText="Yes, Delete!"
                    cancelText="No, Keep It"
                    confirmColor="orange"
                    cancelColor="gray"
                    icon={<FaRegTrashCan />}
                />
            }

        </>
    );
}

export default Details;

