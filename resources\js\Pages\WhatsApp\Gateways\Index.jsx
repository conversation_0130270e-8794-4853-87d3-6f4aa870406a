import { Head, Link, router, useForm } from "@inertiajs/react";
import {
    <PERSON><PERSON>,
    Button,
    Checkbox,
    Drawer,
    Dropdown,
    Modal,
    Table,
    ToggleSwitch,
    Tooltip,
} from "flowbite-react";
import { useEffect, useState } from "react";
import { FaEye } from "react-icons/fa";
import { IoQrCode } from "react-icons/io5";

import { MdDeleteOutline, MdOutlineEdit } from "react-icons/md";

import ConfirmBox from "@/Components/HelperComponents/ConfirmBox";
import NoRecord from "@/Components/HelperComponents/NoRecord";
import Paginate from "@/Components/HelperComponents/Paginate";
import PerPageDropdown from "@/Components/HelperComponents/PerPageDropdown";
import SortLink from "@/Components/SortLink";
import {
    button_custom,
    customDrawer,
    page_badge_theme,
    table_custom,
    toggle_custom
} from "@/Pages/Helpers/DesignHelper";
import { changeFlag, convertDateFormat, fetchJson } from "@/Pages/Helpers/Helper";
import $ from "jquery";
import { FaStar } from "react-icons/fa6";
import { GrDirections } from "react-icons/gr";
import { IoMdAdd } from "react-icons/io";
import { RiDeleteBin6Line } from "react-icons/ri";
import WaMain from "../WaMain";
import Add from "./Add";
import Edit from "./Edit";
import QRCode from "./QRCode";
import ViewSavedChats from "./ViewSavedChats";

export default function Index({ collection }) {

    const currentPageRoute = "whatsapp.gateway.index";
    const { target, getData } = collection;

    const channels = collection.channels.data;
    const [openChatId, setOpenChatId] = useState(0);
    const [openQrModal, setOpenQrModal] = useState(false);
    const [isChannelEditOpen, setIsChannelEditOpen] = useState(false);
    const [isCheckAll, setIsCheckAll] = useState(false);
    const [AddChannel, setAddChannel] = useState(false);
    const [isChatListOpen, setIsChatListOpen] = useState(false);

    const [selectedGateway, setSelectedGateway] = useState();

    const [checkValue, setCheckValue] = useState([]);
    const [QrChannelID, setQrChannelID] = useState('');
    const [updateDefaultID, setUpdateDefaultID] = useState(null);
    const [isUpdateDefaultConfirmOpen, setIsUpdateDefaultConfirmOpen] = useState();
    const [isDeleteConfirmOpen, setIsDeleteConfirmOpen] = useState();
    const [isMultipleDeleteConfirmOpen, SetIsMultipleDeleteConfirmOpen] = useState(false);
    const [deleteID, setDeleteID] = useState(null);
    const [handleSocket, setHandleSocket] = useState(null);
    const [Settings, setSettings] = useState(null);


    useEffect(() => {
        if (openQrModal == false && handleSocket) {
            handleSocket.close();
        }
    }, [openQrModal]);

    const {
        data,
        setData,
        delete: destroy,
        processing,
        errors,
    } = useForm({
        id: [],
    });


    function getCheckedIds(e) {
        let previousIds = checkValue;
        if (e.target.checked) {
            if (!previousIds.includes(e.target.id)) {
                previousIds.push(e.target.id);
                setCheckValue(previousIds);
            }
        } else {
            const newIds = previousIds.filter((item) => item !== e.target.id);
            setCheckValue(newIds);
        }
    }

    // executes when user click table header checkbox
    function headerCheckBoxChecked(e) {
        let previousIds = [];
        if (e.target.checked && e.target.id == 0 && channels.length > 0) {
            channels.map((channels, key) => {
                if (!previousIds.includes(channels.id)) {
                    previousIds.push(channels.id);
                    setCheckValue(previousIds);
                }
            });
            setIsCheckAll(true);
            $(".rowCheckBox").prop("checked", true);
        } else {
            setCheckValue(previousIds);
            setIsCheckAll(false);
            $(".rowCheckBox").prop("checked", false);
        }
    }

    // handle checkBox Check or uncheck
    function checkAddcheckBoxChecked() {
        let allCheckBoxes = $(".rowCheckBox");
        let checkedCheckBoxes = $(".rowCheckBox:checked");

        if (allCheckBoxes.length == checkedCheckBoxes.length) {
            setIsCheckAll(true);
        } else {
            setIsCheckAll(false);
        }
    }

    function deleteRecords() {
        if (checkValue.length > 0) {
            setData({ id: checkValue });
            destroy(
                route("Integration.whatsapp.whatsappweb.destroy", {
                    whatsappweb: checkValue,
                }),
                {
                    onSuccess: () => {
                        setDisabledBtnId(null);
                        setCheckValue([]);
                        setIsCheckAll(false);
                        $(".rowCheckBox").prop("checked", false);
                    }
                }
            );
        }
    }
    const changeSaveChat = (channelId, flag) => {
        changeFlag("wa_gateway", "savechat", channelId, flag);
    };
    const changeActive = (channelId, flag) => {
        if (flag == 0) {
            logout(channelId);
        }
        changeFlag("wa_gateway", "isActive", channelId, flag);
    };

    const updateDefault = () => {
        Promise.all([
            router.post(route("whatsapp.gateway.updateDefault"), { id: updateDefaultID })
        ]).then(([resp]) => {

        });

    }

    function handleConfirmUpdateDefault(res) {
        if (updateDefaultID && res) {
            updateDefault();
            setIsUpdateDefaultConfirmOpen(false);
            setUpdateDefaultID(null);
        } else {
            setUpdateDefaultID(null);
            setIsUpdateDefaultConfirmOpen(false);
        }
    }

    function handleConfirmDelete(res) {
        if (deleteID && res) {
            router.delete(route('whatsapp.gateway.destroy', { id: deleteID }))
            setIsDeleteConfirmOpen(false);
            setDeleteID(null);
        } else {
            setIsDeleteConfirmOpen(false);
            setDeleteID(null);
        }
    }


    function handleMultipleDelete(res) {
        if (res) {
            deleteRecords();
            SetIsMultipleDeleteConfirmOpen(false);
        } else {
            SetIsMultipleDeleteConfirmOpen(false);
        }
    }

    const statusColor = {
        close: { bg: "failure", text: "Close" },
        open: { bg: "success", text: "Success" },
        reinitiating: { bg: "indigo", text: "Reinitiating" },
    };



    const fetchSettings = () => {
        Promise.all([
            fetchJson(route("helper.getSettings"), {}, true)
        ]).then(([container]) => {
            setSettings(container.data)
        });
    }
    useEffect(() => {
        fetchSettings();
    }, []);
    const logout = async (id) => {
        if (Settings) {

            if (!(Settings.hasOwnProperty('nodeUrl'))) {
                alert('Something Went Wrong.');
                return false;
            }

            await fetch(`https://${Settings.nodeUrl}/logout/${id}`).then(() => {
                window.location.reload(false);
            });
        }
    }
    return (
        <WaMain>
            <Head title="Gateways" />
            <div className="px-2 ">
                <div className="w-full mt-2 overflow-auto bg-white border rounded ">
                    {/*----------- Advance Settings----------- */}

                    {/* -----------Table----------- */}
                    <div className="flex justify-between p-2">
                        {/* <Button.Group className=""> */}
                        <div>
                            {
                                collection.can_delete &&
                                <Button isProcessing={processing}
                                    className=""
                                    size="xs"
                                    color="gray"
                                    theme={button_custom}
                                    // onClick={() => window.confirm("Do you really want to delete Gateway ?") && deleteRecords()}
                                    onClick={() => SetIsMultipleDeleteConfirmOpen(true)}
                                >
                                    <div className="flex items-center gap-1">
                                        <MdDeleteOutline className="text-slate-500" />
                                        <span className="text-xs">Delete</span>
                                    </div>
                                </Button>
                            }
                        </div>

                        {/* </Button.Group> */}
                        {/* <Button.Group theme={buttongroup_custom} outline> */}

                        <div className="flex gap-2" >
                            {
                                collection.can_add &&
                                <Button
                                    className="border pe-1"
                                    size="xs"
                                    color="gray"
                                    theme={button_custom}
                                    onClick={() => setAddChannel(true)}
                                >
                                    <div className="flex items-center gap-1">
                                        <IoMdAdd className="text-slate-500" />
                                        <span className="text-xs">Add</span>
                                    </div>
                                </Button>
                            }
                        </div>

                        {/* </Button.Group> */}

                    </div>
                    <div className="bg-white border rounded-lg">
                        {/* documents table */}
                        <div className="overflow-x-auto h-[70vh]">
                            <Table hoverable theme={table_custom} className="border-b">
                                <Table.Head className=" bg-slate-100 text-nowrap">
                                    <Table.HeadCell>
                                        <Checkbox
                                            color="blue"
                                            checked={isCheckAll}
                                            id={0}
                                            onChange={(e) => {
                                                headerCheckBoxChecked(e);
                                            }}
                                        />
                                    </Table.HeadCell>
                                    <Table.HeadCell>
                                        <SortLink showName={'Id'} routeName={currentPageRoute} column={'id'} sortType={getData?.sortBy == "asc" ? "desc" : "asc"} />
                                    </Table.HeadCell>

                                    <Table.HeadCell className="text-nowrap">
                                        <SortLink routeName={currentPageRoute} column={'name'} sortType={getData?.sortBy == "asc" ? "desc" : "asc"} />
                                    </Table.HeadCell>
                                    <Table.HeadCell>
                                        Delay
                                    </Table.HeadCell>
                                    <Table.HeadCell>Connect</Table.HeadCell>
                                    <Table.HeadCell>
                                        <SortLink routeName={currentPageRoute} column={'status'} sortType={getData?.sortBy == "asc" ? "desc" : "asc"} />
                                    </Table.HeadCell>
                                    <Table.HeadCell>
                                        <SortLink showName={'Save chat'} routeName={currentPageRoute} column={'savechat'} sortType={getData?.sortBy == "asc" ? "desc" : "asc"} />
                                    </Table.HeadCell>
                                    <Table.HeadCell>
                                        Last Status change
                                    </Table.HeadCell>
                                    <Table.HeadCell className="text-nowrap">
                                        Auto-reply Rule
                                    </Table.HeadCell>
                                    <Table.HeadCell>
                                        <SortLink showName={'Active'} routeName={currentPageRoute} column={'isActive'} sortType={getData?.sortBy == "asc" ? "desc" : "asc"} />
                                    </Table.HeadCell>
                                    <Table.HeadCell>
                                        <div className="flex items-center justify-between">
                                            <div >
                                                <SortLink showName={'User'} routeName={currentPageRoute} column={'user_id'} sortType={getData?.sortBy == "asc" ? "desc" : "asc"} params={{ userID: getData?.userID }} />
                                            </div>
                                            <div >

                                            </div>
                                        </div>
                                    </Table.HeadCell>

                                    <Table.HeadCell>
                                        Workers
                                    </Table.HeadCell>
                                    <Table.HeadCell>Action</Table.HeadCell>
                                </Table.Head>

                                <Table.Body className="divide-y">
                                    {channels.length > 0 ? (
                                        channels.map((channel, key) => {
                                            return (
                                                <Table.Row key={key}>
                                                    <Table.Cell>
                                                        <Checkbox
                                                            color={"blue"}
                                                            className="rowCheckBox"
                                                            id={channel.id}
                                                            onChange={(e) => {
                                                                getCheckedIds(
                                                                    e
                                                                );
                                                                checkAddcheckBoxChecked();
                                                            }}
                                                        />
                                                    </Table.Cell>
                                                    <Table.Cell className="font-medium text-gray-900 whitespace-nowrap dark:text-white">
                                                        {channel.id}
                                                    </Table.Cell>
                                                    <Table.Cell className="flex items-center justify-between text-nowrap">
                                                        {channel.name}
                                                        {channel.isDefault
                                                            ?
                                                            <Button
                                                                theme={
                                                                    button_custom
                                                                }
                                                                color="transparentForTab"
                                                                className="cursor-default"
                                                            >
                                                                <Badge theme={page_badge_theme} size="xs" color="info">Default</Badge>
                                                            </Button>
                                                            :
                                                            <Button
                                                                theme={
                                                                    button_custom
                                                                }
                                                                color="transparentForTab"
                                                                // onClick={() => updateDefault(channel.id)}
                                                                onClick={() => {
                                                                    setUpdateDefaultID(channel.id)
                                                                    setIsUpdateDefaultConfirmOpen(true);
                                                                }}
                                                            >
                                                                <Badge theme={page_badge_theme} size="xs" color="indigo">Set Default</Badge>
                                                            </Button>
                                                        }
                                                    </Table.Cell>
                                                    <Table.Cell>
                                                        {channel.randomDelay ==
                                                            1 ? (
                                                            <>
                                                                {
                                                                    channel.randomDelayFrom
                                                                }
                                                                &nbsp;To&nbsp;
                                                                {
                                                                    channel.randomDelayTo
                                                                }
                                                            </>
                                                        ) : (
                                                            channel.delaySeconds
                                                        )}
                                                    </Table.Cell>
                                                    <Table.Cell className="text-center">
                                                        {(channel.isActive && channel.status != 'open') ? (
                                                            <div className="flex justify-center">
                                                                <Tooltip content="Scan QR">
                                                                    <Button
                                                                        onClick={() => {
                                                                            setOpenQrModal(true);
                                                                            setQrChannelID(channel.id)
                                                                        }}
                                                                        theme={
                                                                            button_custom
                                                                        }
                                                                        color="success"
                                                                        size="xs"
                                                                    >
                                                                        <IoQrCode />
                                                                    </Button>
                                                                </Tooltip>
                                                            </div>
                                                        ) : (
                                                            <></>

                                                        )}
                                                        {Settings && Settings.hasOwnProperty('nodeUrl') &&
                                                            channel.status == 'open' &&
                                                            <Button size="xs" color="failure" className="rounded"
                                                                onClick={() => logout(channel.id)}>
                                                                Logout
                                                            </Button>
                                                        }
                                                    </Table.Cell>
                                                    <Table.Cell>
                                                        <div className="w-fit">
                                                            {/* {channel.status} */}
                                                            <Badge
                                                                color={
                                                                    statusColor[
                                                                        channel
                                                                            .status
                                                                    ].bg ??
                                                                    "info"
                                                                }
                                                                className="capitalize"
                                                            >
                                                                {channel.status}
                                                            </Badge>
                                                        </div>
                                                    </Table.Cell>
                                                    <Table.Cell>
                                                        <div className="flex items-center gap-3">
                                                            <Tooltip content="Save Chat">
                                                                <ToggleSwitch
                                                                    theme={
                                                                        toggle_custom
                                                                    }
                                                                    sizing="xs"
                                                                    checked={
                                                                        channel.savechat ===
                                                                        1
                                                                    }
                                                                    onChange={() =>
                                                                        changeSaveChat(
                                                                            channel.id,
                                                                            channel.savechat ===
                                                                                1
                                                                                ? 0
                                                                                : 1
                                                                        )
                                                                    }
                                                                />
                                                            </Tooltip>

                                                            {channel.savechat ===
                                                                1 && (
                                                                    <Tooltip content="View Chat">
                                                                        <Button
                                                                            color={
                                                                                "blue"
                                                                            }
                                                                            theme={
                                                                                button_custom
                                                                            }
                                                                            size="xs"
                                                                            onClick={() => {
                                                                                setIsChatListOpen(
                                                                                    true
                                                                                );
                                                                                setOpenChatId(
                                                                                    channel.id
                                                                                );
                                                                            }}
                                                                        >
                                                                            <FaEye className="text-sm" />
                                                                            <span className="text-xs ms-1">
                                                                                View
                                                                            </span>
                                                                        </Button>
                                                                    </Tooltip>
                                                                )}
                                                        </div>
                                                    </Table.Cell>
                                                    <Table.Cell>
                                                        {channel.lastStatusChange ? (
                                                            convertDateFormat(
                                                                channel.lastStatusChange
                                                            )
                                                        ) : (
                                                            <>-</>
                                                        )}
                                                    </Table.Cell>
                                                    <Table.Cell>
                                                        {channel.auto_reply_data
                                                            ? channel
                                                                .auto_reply_data
                                                                .name
                                                            : "-"}
                                                    </Table.Cell>
                                                    <Table.Cell>
                                                        <ToggleSwitch
                                                            theme={
                                                                toggle_custom
                                                            }
                                                            sizing="xs"
                                                            checked={
                                                                channel.isActive
                                                            }
                                                            onChange={() =>
                                                                changeActive(
                                                                    channel.id,
                                                                    channel.isActive ===
                                                                        1
                                                                        ? 0
                                                                        : 1
                                                                )
                                                            }
                                                        />
                                                    </Table.Cell>
                                                    <Table.Cell>
                                                        {channel.user.username}
                                                    </Table.Cell>
                                                    <Table.Cell>
                                                        {channel.worker ? (
                                                            channel.worker.name
                                                        ) : (
                                                            <>-</>
                                                        )}
                                                    </Table.Cell>
                                                    <Table.Cell>
                                                        <div className="flex gap-2">

                                                            {collection.can_edit &&
                                                                <Tooltip content="Edit">
                                                                    <Button
                                                                        theme={
                                                                            button_custom
                                                                        }
                                                                        onClick={() => {
                                                                            setIsChannelEditOpen(
                                                                                true
                                                                            );
                                                                            setSelectedGateway(channel)
                                                                        }}
                                                                        color="success"
                                                                        size="xs"
                                                                    >
                                                                        <MdOutlineEdit className="text-xs" />
                                                                        <span className="text-xs ms-1">
                                                                            Edit
                                                                        </span>
                                                                    </Button>
                                                                </Tooltip>
                                                            }
                                                            {collection.can_delete &&
                                                                <Tooltip content="Delete">
                                                                    <Button
                                                                        theme={
                                                                            button_custom
                                                                        }
                                                                        color="failure"
                                                                        size="xs"
                                                                        onClick={() => {
                                                                            setDeleteID(channel.id)
                                                                            setIsDeleteConfirmOpen(true);
                                                                        }}
                                                                    >
                                                                        <RiDeleteBin6Line className="text-sm" />
                                                                        <span className="text-xs ms-1">
                                                                            Delete
                                                                        </span>
                                                                    </Button>
                                                                </Tooltip>
                                                            }
                                                        </div>
                                                    </Table.Cell>
                                                </Table.Row>
                                            );
                                        })
                                    ) : (
                                        <Table.Row className="bg-white dark:border-gray-700 dark:bg-gray-800">
                                            <Table.Cell colSpan={17}>
                                                <NoRecord />
                                            </Table.Cell>
                                        </Table.Row>
                                    )}
                                </Table.Body>
                            </Table>
                        </div>
                    </div>

                    <div className="bottom-0 w-full p-3 bg-white rounded-lg float-end dark:border-gray-500 dark:rounded-lg dark:bg-gray-700">
                        <div className="flex flex-wrap justify-between gap-2 lg:justify-between lg:gap-0 md:gap-0">
                            <div className="flex items-center gap-4">
                                <PerPageDropdown
                                    getDataFields={getData ?? null}
                                    routeName={"whatsapp.gateway.index"}
                                    data={collection.channels}
                                    idName={"name"}
                                    id={target}
                                />
                            </div>

                            <Paginate tableData={collection.channels} />
                        </div>
                    </div>
                </div>
            </div>

            {/*-------------- Add Channel ------------*/}
            {
                AddChannel ? (
                    <Drawer
                        open={AddChannel}
                        onClose={() => setAddChannel(false)}
                        position="right"
                        theme={customDrawer}
                        className="lg:w-4/5 md:w-4/5 xl:w-2/3 "
                    >
                        <Drawer.Header
                            title="Add Gateway"
                            titleIcon={GrDirections}
                        />
                        <Drawer.Items className="py-2">
                            <Add
                                onClose={() => setAddChannel(false)}
                            />
                        </Drawer.Items>
                    </Drawer>
                ) : (
                    false
                )
            }

            {/* --------------------Edit whatsappweb Channel-------------------------- */}
            {
                isChannelEditOpen === true ? (
                    <Drawer
                        theme={customDrawer}
                        className="lg:w-3/5 md:w-4/5 xl:w-3/4"
                        open={isChannelEditOpen}
                        onClose={() => setIsChannelEditOpen(false)}
                        position="right"
                    >
                        <Drawer.Header
                            titleIcon={GrDirections}
                            title="Edit Gateway"
                        />
                        <Drawer.Items className="py-2">
                            <Edit
                                onClose={() => setIsChannelEditOpen(false)}
                                gateway={selectedGateway}
                            />
                        </Drawer.Items>
                    </Drawer>
                ) : (
                    <></>
                )
            }
            {/*-------------- eye chat table ------------*/}
            {
                isChatListOpen == true ? (
                    <Drawer
                        open={isChatListOpen}
                        onClose={() => setIsChatListOpen(false)}
                        position="right"
                        theme={customDrawer}
                        className="w-full lg:w-3/5 md:w-3/5"
                    >
                        <Drawer.Header title="View Saved chats" titleIcon={FaEye} />
                        <Drawer.Items className="py-4">
                            <div className="overflow-x-auto">
                                <ViewSavedChats data={{ channel: openChatId }} />
                            </div>
                        </Drawer.Items>
                    </Drawer>
                ) : (
                    <></>
                )
            }

            {
                openQrModal ? (
                    <Modal show={openQrModal} onClose={() => setOpenQrModal(false)}>
                        <Modal.Header>Login</Modal.Header>
                        <Modal.Body>
                            <QRCode close={setOpenQrModal} isOpen={openQrModal} parentSocket={setHandleSocket} settings={Settings} channelId={QrChannelID} />
                        </Modal.Body>
                    </Modal>
                ) : (
                    ""
                )
            }

            {
                isUpdateDefaultConfirmOpen &&
                <ConfirmBox
                    isOpen={isUpdateDefaultConfirmOpen}
                    onClose={() => setIsUpdateDefaultConfirmOpen(false)} // Close the confirm box
                    onAction={handleConfirmUpdateDefault} // Handle the user's choice
                    title="Update Default Gateway "
                    message="Do you want to update default gateway."
                    confirmText="Confirm"
                    cancelText="Cancel"
                    confirmColor="text-yellow-400"
                    confirmBtnBgColor="bg-yellow-100"
                    confirmBtnTextColor="text-yellow-600"
                    iconBackground="bg-yellow-100"
                    iconColor="text-yellow-400"
                    icon={<FaStar />}
                />
            }
            {
                isDeleteConfirmOpen &&
                <ConfirmBox
                    isOpen={isDeleteConfirmOpen}
                    onClose={() => setIsDeleteConfirmOpen(false)} // Close the confirm box
                    onAction={handleConfirmDelete} // Handle the user's choice
                    title="Delete Gateway "
                    message="Do you want to Delete gateway."
                    confirmText="Confirm"
                    cancelText="Cancel"
                    confirmColor="text-red-400"
                    confirmBtnBgColor="bg-red-100"
                    confirmBtnTextColor="text-red-600"
                    iconBackground="bg-red-100"
                    iconColor="text-red-400"

                />
            }
            {
                isMultipleDeleteConfirmOpen &&
                <ConfirmBox
                    isOpen={isMultipleDeleteConfirmOpen}
                    onClose={() => SetIsMultipleDeleteConfirmOpen(false)} // Close the confirm box
                    onAction={handleMultipleDelete} // Handle the user's choice
                    title="Delete Gateway "
                    message="Do you want to Delete gateway."
                    confirmText="Yes, Delete!"
                    cancelText="No, Keep It"
                    confirmColor="orange"
                    cancelColor="gray"

                />
            }

        </WaMain >
    );
}


