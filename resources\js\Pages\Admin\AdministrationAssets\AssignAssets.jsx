import {
    <PERSON><PERSON>,
    <PERSON><PERSON><PERSON>,
    Table,
    Label,
    TextInput,
    Select,
    Tabs,
    Badge,
    Datepicker,
} from "flowbite-react";
import { useState, useEffect } from "react";
import $ from "jquery";
import { BiCheckDouble } from "react-icons/bi";
import { IoCloseSharp } from "react-icons/io5";
import { BiBlanket } from "react-icons/bi";
import { MdOutlineInventory2, MdOutlineArrowDownward } from "react-icons/md";
import { RiArrowLeftUpLine } from "react-icons/ri";
import { SlCalender } from "react-icons/sl";
import { button_custom, custom_tabs, table_custom } from "../../Helpers/DesignHelper";

export default function AssignAssets() {
    const textinput_custom = {
        colors: {
            gray: "border-gray-300 bg-gray-50 text-gray-900 focus:border-blue-500 focus:ring-blue-500 dark:border-gray-600 dark:bg-gray-700 dark:text-white dark:placeholder-gray-400 dark:focus:border-blue-500 dark:focus:ring-blue-500",
        },
    };
    const custom_datepicker = {
        root: {
            base: "relative",
        },
        field: {
            base: "relative w-full",
            icon: {
                base: "pointer-events-none absolute inset-y-0 end-3.5 flex items-center pl-3",
                svg: "h-5 w-5 text-slate-400 dark:text-gray-400",
            },
            rightIcon: {
                base: "pointer-events-none absolute border-s inset-y-0 right-0 flex items-center ps-2 pr-3  text-slate-300",
                svg: "h-5 w-5 text-slate-400 dark:text-gray-400 ",
            },
        },
        input: {
            base: "block w-full border disabled:cursor-not-allowed disabled:opacity-50",
            sizes: {
                sm: "p-2 sm:text-xs",
                md: "p-2.5 text-sm",
                lg: "p-4 sm:text-base",
            },
            gray: "border-blue-300 bg-blue-50 text-blue-900 placeholder-blue-700 focus:border-blue-500 focus:ring-blue-500 dark:border-blue-400 dark:bg-blue-100 dark:focus:border-blue-500 dark:focus:ring-blue-500",
        },

        popup: {
            root: {
                base: "absolute bottom-20 z-50 block pt-2",
                inline: "relative top-0 z-auto",
                inner: "inline-block rounded-lg bg-white p-4 shadow-lg dark:bg-gray-700",
            },
            header: {
                base: "",
                title: "px-2 py-3 text-center font-semibold text-gray-900 dark:text-white",
                selectors: {
                    base: "mb-2 flex justify-between",
                    button: {
                        base: "rounded-lg bg-white px-5 py-2.5 text-sm font-semibold text-gray-900 hover:bg-gray-100 focus:outline-none focus:ring-2 focus:ring-gray-200 dark:bg-gray-700 dark:text-white dark:hover:bg-gray-600",
                        prev: "",
                        next: "",
                        view: "",
                    },
                },
            },
            view: {
                base: "p-1",
            },
            footer: {
                base: "mt-2 flex space-x-2",
                button: {
                    base: "w-full rounded-lg px-5 py-2 text-center text-sm font-medium focus:ring-4 focus:ring-blue-300",
                    today: "bg-blue-700 text-white hover:bg-blue-800 dark:bg-blue-600 dark:hover:bg-blue-700",
                    clear: "border border-gray-300 bg-white text-gray-900 hover:bg-gray-100 dark:border-gray-600 dark:bg-gray-700 dark:text-white dark:hover:bg-gray-600",
                },
            },
        },
        views: {
            days: {
                header: {
                    base: "mb-1 grid grid-cols-7",
                    title: "h-6 text-center text-sm font-medium leading-6 text-gray-500 dark:text-gray-400",
                },
                items: {
                    base: "grid w-64 grid-cols-7",
                    item: {
                        base: "block flex-1 cursor-pointer rounded-lg border-0 text-center text-sm font-semibold leading-9 text-gray-900 hover:bg-gray-100 dark:text-white dark:hover:bg-gray-600",
                        selected: "bg-blue-700 text-white hover:bg-blue-600",
                        disabled: "text-gray-500",
                    },
                },
            },
            months: {
                items: {
                    base: "grid w-64 grid-cols-4",
                    item: {
                        base: "block flex-1 cursor-pointer rounded-lg border-0 text-center text-sm font-semibold leading-9 text-gray-900 hover:bg-gray-100 dark:text-white dark:hover:bg-gray-600",
                        selected: "bg-cyan-700 text-white hover:bg-cyan-600",
                        disabled: "text-gray-500",
                    },
                },
            },
            years: {
                items: {
                    base: "grid w-64 grid-cols-4",
                    item: {
                        base: "block flex-1 cursor-pointer rounded-lg border-0 text-center text-sm font-semibold leading-9 text-gray-900 hover:bg-gray-100 dark:text-white dark:hover:bg-gray-600",
                        selected: "bg-cyan-700 text-white hover:bg-cyan-600",
                        disabled: "text-gray-500",
                    },
                },
            },
            decades: {
                items: {
                    base: "grid w-64 grid-cols-4",
                    item: {
                        base: "block flex-1 cursor-pointer rounded-lg border-0 text-center text-sm font-semibold leading-9 text-gray-900 hover:bg-gray-100 dark:text-white dark:hover:bg-gray-600",
                        selected: "bg-cyan-700 text-white hover:bg-cyan-600",
                        disabled: "text-gray-500",
                    },
                },
            },
        },
    };

    const [isCheckAll, setIsCheckAll] = useState(false);

    useEffect(() => {
        if (isCheckAll) {
            $(".rowCheckBox").prop("checked", true);
        } else {
            $(".rowCheckBox").prop("checked", false);
        }
    }, [isCheckAll]);

    return (
        <>
            <div className="px-2">
                <Tabs
                    theme={custom_tabs}
                    aria-label="Tabs with underline"
                >
                    <Tabs.Item
                        className=""
                        active
                        title={
                            <div className="flex items-center gap-2">
                                Assign Assets
                                <sup>
                                    <Badge
                                        className="rounded-full bg-fuchsia-600 text-white"
                                        size="xs"
                                    >
                                        05
                                    </Badge>
                                </sup>
                            </div>
                        }
                        icon={BiBlanket}
                    >
                        <div className=" flex flex-col justify-between">
                            <div className="bg-white h-[60vh] rounded-lg border">
                                {/* documents table */}
                                <div className="overflow-x-auto">
                                    <Table hoverable theme={table_custom}>
                                        <TableHead className=" bg-slate-100">
                                            <TableHeadCell>
                                                Date
                                            </TableHeadCell>

                                            <TableHeadCell>
                                                <h3>Employee</h3>
                                            </TableHeadCell>

                                            <TableHeadCell>
                                                <h3>Department</h3>
                                            </TableHeadCell>

                                            <TableHeadCell>
                                                <h3>Assets</h3>
                                            </TableHeadCell>

                                            <TableHeadCell>
                                                <h3>Quantity</h3>
                                            </TableHeadCell>

                                            <TableHeadCell className="w-28">
                                                <h3>Reason</h3>
                                            </TableHeadCell>
                                        </TableHead>

                                        <TableBody className="divide-y">
                                            <TableRow className="bg-white dark:border-gray-700 dark:bg-gray-800">
                                                <TableCell className="text-blue-500">
                                                    1/07/2024
                                                </TableCell>

                                                <TableCell className="font-medium text-gray-900 whitespace-nowrap dark:text-white">
                                                    <div className="flex flex-wrap items-center gap-2 text-nowrap w-fit">
                                                        <img
                                                            src="https://img.freepik.com/premium-photo/profile-icon-white-background_941097-160810.jpg?w=50"
                                                            alt=""
                                                            className="rounded-full"
                                                        />
                                                        <div>
                                                            <div className="text-blue-500 text-sm text-nowrap">
                                                                Rohan Preet
                                                                (2568)
                                                            </div>
                                                            <div className="text-xs text-nowrap">
                                                                Sales Executive
                                                            </div>
                                                        </div>
                                                    </div>
                                                </TableCell>
                                                <TableCell>
                                                    Sales Team
                                                </TableCell>
                                                <TableCell>
                                                    Keyboard (2)
                                                </TableCell>
                                                <TableCell>1</TableCell>
                                                <TableCell className="text-nowrap">
                                                    <div className="flex items-center justify-between gap-4">
                                                        <span>
                                                            Arrow keys not
                                                            working.
                                                        </span>
                                                        <div className="flex gap-2 justify-between">
                                                            <div className="flex gap-2">
                                                                <Tooltip
                                                                    content="Reject"
                                                                    className="bg-slate-700 p-1 px-2"
                                                                >
                                                                    <Button
                                                                        theme={
                                                                            button_custom
                                                                        }
                                                                        color="failure"
                                                                        size="xs"
                                                                    >
                                                                        {" "}
                                                                        <IoCloseSharp />
                                                                    </Button>
                                                                </Tooltip>

                                                                <Tooltip
                                                                    content="Approve"
                                                                    className="bg-slate-700 p-1 px-2"
                                                                >
                                                                    <Button
                                                                        theme={
                                                                            button_custom
                                                                        }
                                                                        size="xs"
                                                                    >
                                                                        {" "}
                                                                        <BiCheckDouble />
                                                                    </Button>
                                                                </Tooltip>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </TableCell>
                                            </TableRow>
                                        </TableBody>
                                    </Table>
                                </div>
                            </div>

                            <div className="float-end border bottom-0 p-3 w-full bg-white rounded-b-lg">
                                <div className="flex flex-wrap justify-between lg:gap-0 md:gap-0 gap-2">
                                    <div className="flex gap-3 items-center">
                                        <div className="text-gray-400 text-sm ">
                                            Showing 1 to 25 of 62 entries
                                        </div>
                                        <div>
                                            <select
                                                id="countries"
                                                className=" bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded focus:ring-blue-500 focus:border-blue-500 block p-1 ps-2 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-blue-500 dark:focus:border-blue-500"
                                            >
                                                <option
                                                    value={10}
                                                    defaultValue={10}
                                                >
                                                    10
                                                </option>
                                                <option value={15}>15</option>
                                                <option value={20}>20</option>
                                                <option value={25}>25</option>
                                                <option value={30}>30</option>
                                            </select>
                                        </div>
                                    </div>
                                    <div className="flex">
                                        <Button
                                            size="xs"
                                            color="gray"
                                            className="border-e-0 text-gray-400 px-2 rounded text-xs "
                                        >
                                            Previous
                                        </Button>
                                        <Button
                                            size="xs"
                                            color="blue"
                                            className="border text-white border-blue-600 bg-blue-600 px-2.5 rounded-none text-sm "
                                        >
                                            1
                                        </Button>
                                        <Button
                                            size="xs"
                                            color="gray"
                                            className="border text-blue-600 px-2.5 rounded-none text-xs "
                                        >
                                            2
                                        </Button>
                                        <Button
                                            size="xs"
                                            color="gray"
                                            className="border-s-0 text-blue-600 px-2 rounded text-xs "
                                        >
                                            Next
                                        </Button>
                                    </div>
                                </div>
                            </div>

                            <div className="">
                                <div className="flex items-center gap-1 bg-[#F8F7FC] p-2 rounded-s rounded-e mt-2">
                                    <BiBlanket className="text-slate-400 text-lg" />
                                    <h4 className="text-base">Assign Assets</h4>
                                </div>
                                <div className="bg-white pt-2 px-4">
                                    <div className="">
                                        <form className="flex items-end gap-5 flex-wrap">
                                            <div className="max-w-md">
                                                <div className="mb-2 block">
                                                    <Label
                                                        htmlFor="countries"
                                                        value="Employee Name"
                                                    />
                                                </div>
                                                <Select id="countries" required>
                                                    <option>
                                                        Select the employee
                                                    </option>
                                                    <option>Canada</option>
                                                    <option>France</option>
                                                    <option>Germany</option>
                                                </Select>
                                            </div>
                                            <div>
                                                <div className="mb-2 block">
                                                    <Label
                                                        htmlFor="PrincipalPaid"
                                                        value="Quantity"
                                                    />
                                                </div>
                                                <TextInput
                                                    id="PrincipalPaid"
                                                    type="number"
                                                    placeholder="0"
                                                    required
                                                />
                                            </div>
                                            <div>
                                                <div className="mb-2 block">
                                                    <Label
                                                        htmlFor="paymentMethod"
                                                        value="Payment method"
                                                    />
                                                </div>
                                                <Datepicker
                                                    placement="top"
                                                    color="gray"
                                                    icon=""
                                                    rightIcon={SlCalender}
                                                    theme={custom_datepicker}
                                                />
                                            </div>
                                        </form>
                                        <div className="flex justify-end gap-5 py-3">
                                            <Button
                                                theme={button_custom}
                                                color="failure"
                                                className="rounded"
                                                size="xs"
                                            >
                                                <div className="flex items-center gap-0.5">
                                                    <IoCloseSharp className="text-xl" />
                                                    <span className="text-sm">
                                                        Close
                                                    </span>
                                                </div>
                                            </Button>
                                            <Button
                                                theme={button_custom}
                                                color="blue"
                                                className="rounded"
                                                size="xs"
                                            >
                                                <div className="flex items-center gap-0.5">
                                                    <BiCheckDouble className="text-xl" />
                                                    <span className="text-sm">
                                                        Assign
                                                    </span>
                                                </div>
                                            </Button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </Tabs.Item>
                    <Tabs.Item
                        title={
                            <div className="flex items-center gap-2">
                                Stock Manage
                                <sup>
                                    <Badge
                                        className="rounded-full bg-sky-600 text-white"
                                        size="xs"
                                    >
                                        05
                                    </Badge>
                                </sup>
                            </div>
                        }
                        icon={MdOutlineInventory2}
                    >
                        <div className=" flex flex-col justify-between">
                            <div className="bg-white h-[60vh] rounded-lg border">
                                {/* documents table */}
                                <div className="overflow-x-auto">
                                    <Table hoverable theme={table_custom}>
                                        <TableHead className=" bg-slate-100">
                                            <TableHeadCell>
                                                Date
                                            </TableHeadCell>
                                            <TableHeadCell>
                                                <h3>Type</h3>
                                            </TableHeadCell>

                                            <TableHeadCell>
                                                <h3>Employee</h3>
                                            </TableHeadCell>

                                            <TableHeadCell>
                                                <h3>Quantity</h3>
                                            </TableHeadCell>

                                            <TableHeadCell>
                                                <h3>Remaining Stock</h3>
                                            </TableHeadCell>

                                            <TableHeadCell>
                                                <h3>Remarks</h3>
                                            </TableHeadCell>
                                        </TableHead>

                                        <TableBody className="divide-y">
                                            <TableRow className="bg-white dark:border-gray-700 dark:bg-gray-800">
                                                <TableCell className="text-blue-500">
                                                    1/07/2024
                                                </TableCell>
                                                <TableCell className="text-blue-500">
                                                    <div className="bg-green-50 text-green-600 w-fit px-3 py-0.5 rounded border border-green-600">
                                                        Stock in
                                                    </div>
                                                </TableCell>

                                                <TableCell className="font-medium text-gray-900 whitespace-nowrap dark:text-white">
                                                    -
                                                </TableCell>
                                                <TableCell>15</TableCell>
                                                <TableCell>15</TableCell>
                                                <TableCell>
                                                    initial stock for new batch
                                                </TableCell>
                                            </TableRow>
                                            <TableRow className="bg-white dark:border-gray-700 dark:bg-gray-800">
                                                <TableCell className="text-blue-500">
                                                    1/07/2024
                                                </TableCell>
                                                <TableCell className="text-blue-500">
                                                    <div className="bg-blue-50 text-blue-600 w-fit px-3 py-0.5 rounded border border-blue-600">
                                                        Assign
                                                    </div>
                                                </TableCell>

                                                <TableCell className="font-medium text-gray-900 whitespace-nowrap dark:text-white">
                                                    <div className="flex flex-wrap items-center gap-2 text-nowrap w-fit">
                                                        <img
                                                            src="https://img.freepik.com/premium-photo/profile-icon-white-background_941097-160810.jpg?w=50"
                                                            alt=""
                                                            className="rounded-full"
                                                        />
                                                        <div>
                                                            <div className="text-blue-500 text-sm text-nowrap">
                                                                Rohan Preet
                                                                (2568)
                                                            </div>
                                                            <div className="text-xs text-nowrap">
                                                                Sales Executive
                                                            </div>
                                                        </div>
                                                    </div>
                                                </TableCell>
                                                <TableCell>15</TableCell>
                                                <TableCell>15</TableCell>
                                                <TableCell>
                                                    initial stock for new batch
                                                </TableCell>
                                            </TableRow>
                                            <TableRow className="bg-white dark:border-gray-700 dark:bg-gray-800">
                                                <TableCell className="text-blue-500">
                                                    1/07/2024
                                                </TableCell>
                                                <TableCell className="text-blue-500">
                                                    <div className="bg-red-50 text-red-600 w-fit px-3 py-0.5 rounded border border-red-600">
                                                        Stock Out
                                                    </div>
                                                </TableCell>

                                                <TableCell className="font-medium text-gray-900 whitespace-nowrap dark:text-white">
                                                    <div className="flex flex-wrap items-center gap-2 text-nowrap w-fit">
                                                        <img
                                                            src="https://img.freepik.com/premium-photo/profile-icon-white-background_941097-160810.jpg?w=50"
                                                            alt=""
                                                            className="rounded-full"
                                                        />
                                                        <div>
                                                            <div className="text-blue-500 text-sm text-nowrap">
                                                                Rohan Preet
                                                                (2568)
                                                            </div>
                                                            <div className="text-xs text-nowrap">
                                                                Sales Executive
                                                            </div>
                                                        </div>
                                                    </div>
                                                </TableCell>
                                                <TableCell>15</TableCell>
                                                <TableCell>15</TableCell>
                                                <TableCell>
                                                    initial stock for new batch
                                                </TableCell>
                                            </TableRow>
                                        </TableBody>
                                    </Table>
                                </div>
                            </div>

                            <div className="float-end border bottom-0 p-3 w-full bg-white ">
                                <div className="flex flex-wrap justify-between lg:gap-0 md:gap-0 gap-2">
                                    <div className="flex gap-3 items-center">
                                        <div className="text-gray-400 text-sm ">
                                            Showing 1 to 25 of 62 entries
                                        </div>
                                        <div>
                                            <select
                                                id="countries"
                                                className="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded focus:ring-blue-500 focus:border-blue-500 block p-1 ps-2 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-blue-500 dark:focus:border-blue-500"
                                            >
                                                <option
                                                    value={10}
                                                    defaultValue={10}
                                                >
                                                    10
                                                </option>
                                                <option value={15}>15</option>
                                                <option value={20}>20</option>
                                                <option value={25}>25</option>
                                                <option value={30}>30</option>
                                            </select>
                                        </div>
                                    </div>
                                    <div className="flex">
                                        <Button
                                            size="xs"
                                            color="gray"
                                            className="border-e-0 text-gray-400 px-2 rounded text-xs "
                                        >
                                            Previous
                                        </Button>
                                        <Button
                                            size="xs"
                                            color="blue"
                                            className="border text-white border-blue-600 bg-blue-600 px-2.5 rounded-none text-sm "
                                        >
                                            1
                                        </Button>
                                        <Button
                                            size="xs"
                                            color="gray"
                                            className="border text-blue-600 px-2.5 rounded-none text-xs "
                                        >
                                            2
                                        </Button>
                                        <Button
                                            size="xs"
                                            color="gray"
                                            className="border-s-0 text-blue-600 px-2 rounded text-xs "
                                        >
                                            Next
                                        </Button>
                                    </div>
                                </div>
                            </div>

                            <div className="">
                                <div className="flex items-center gap-1 bg-[#F8F7FC] p-2 rounded-s rounded-e mt-2">
                                    <MdOutlineInventory2 className="text-slate-400 text-lg" />
                                    <h4 className="text-base">Stock Manage</h4>
                                </div>
                                <div className="bg-white pt-2 px-4">
                                    <div className="">
                                        <form className="flex items-end gap-5 flex-wrap">
                                            <div>
                                                <div className="mb-2 block">
                                                    <Label
                                                        htmlFor="paymentMethod"
                                                        value="Payment method"
                                                    />
                                                </div>
                                                <Datepicker
                                                    color="gray"
                                                    icon=""
                                                    rightIcon={SlCalender}
                                                    theme={custom_datepicker}
                                                />
                                            </div>
                                            <div>
                                                <div className="mb-2 block">
                                                    <Label
                                                        htmlFor="PrincipalPaid"
                                                        value="Number of Products"
                                                    />
                                                </div>
                                                <TextInput
                                                    theme={textinput_custom}
                                                    color="customBlue"
                                                    id="PrincipalPaid"
                                                    type="number"
                                                    placeholder="Ex: 30"
                                                    required
                                                />
                                            </div>
                                            <div>
                                                <div className="mb-2 block">
                                                    <Label
                                                        htmlFor="PrincipalPaid"
                                                        value="Remarks"
                                                    />
                                                </div>
                                                <TextInput
                                                    id="PrincipalPaid"
                                                    type="text"
                                                    placeholder="Write remark"
                                                    required
                                                />
                                            </div>
                                        </form>
                                        <div className="flex justify-end gap-5 py-3">
                                            <Button
                                                theme={button_custom}
                                                color="failure"
                                                className="rounded"
                                                size="xs"
                                            >
                                                <div className="flex items-center gap-0.5">
                                                    <RiArrowLeftUpLine className="text-xl" />
                                                    <span className="text-sm">
                                                        Stock Out
                                                    </span>
                                                </div>
                                            </Button>
                                            <Button
                                                theme={button_custom}
                                                color="green"
                                                className="rounded"
                                                size="xs"
                                            >
                                                <div className="flex items-center gap-0.5">
                                                    <MdOutlineArrowDownward className="text-xl" />
                                                    <span className="text-sm">
                                                        Stock In
                                                    </span>
                                                </div>
                                            </Button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </Tabs.Item>
                </Tabs>
            </div>
        </>
    );
}

