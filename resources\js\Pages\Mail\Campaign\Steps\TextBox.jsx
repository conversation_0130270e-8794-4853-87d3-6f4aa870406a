import {
    button_custom,
    customDrawer,
    textarea_custom
} from "@/Pages/Helpers/DesignHelper";
import { fetchJson } from "@/Pages/Helpers/Helper";
import EmojiPicker from "emoji-picker-react";
import {
    <PERSON><PERSON>,
    Drawer,
    FileInput,
    Textarea
} from "flowbite-react";
import { useEffect, useRef, useState } from "react";
import { CgAttachment } from "react-icons/cg";
import {
    FaBold,
    FaItalic,
    FaRegFaceSmileBeam,
    FaRegNoteSticky,
    FaStrikethrough,
} from "react-icons/fa6";
import { HiOutlineTemplate } from "react-icons/hi";
import Templates from "./Templates";

function TextBox({ data, setData,  setTemp = null, errors }) {

    const [isOpenTemplates, setIsOpenTemplates] = useState(false);
    const [openEmoji, setOpenEmoji] = useState(false);
    const textareaRef = useRef(null);
    const fileInputRef = useRef(null);
    const [gateways, setGateways] = useState();
    const validImageExtensions = ["jpeg", "jpg", "webp", 'png', "gif"];


    useEffect(() => {
        Promise.all([fetchJson(route('helper.getAllChannels'), {}, true)])
            .then(([content]) => {
                setGateways(content.channels);

            });
    }, []);

    const textFormat = (formatAs) => {
        const textarea = textareaRef.current;
        const start = textarea.selectionStart;
        const end = textarea.selectionEnd;

        // Get the selected text
        const selectedText = data.msg.substring(start, end);

        // Wrap the selected text in ** to make it bold in markdown
        const boldText = `${formatAs + selectedText + formatAs}`;

        // Update the message state with the bold text
        setData(
            "msg",
            data.msg.substring(0, start) +
            boldText +
            data.msg.substring(end)
        );

        // Set the cursor position after the inserted bold text
        setTimeout(() => {
            textarea.setSelectionRange(
                start + boldText.length,
                start + boldText.length
            );
            textarea.focus();
        }, 0);
    };



    const handleClick = () => {
        fileInputRef.current.click();
    };

    const handleFileChange = (event) => {
        const selectedFiles = Array.from(event.target.files);

        // Filter images and create preview URLs
        const imageFiles = selectedFiles
            // .filter((file) => file.type.startsWith("image/"))
            .map((file) => {
                return {
                    file,
                    previewUrl: URL.createObjectURL(file),
                };
            });
        setData("attachment", imageFiles);
    };

    const removeImage = (index) => {
        const result = Object.values(data.attachment);
        delete result[index];
        let filteredArray = result.filter(
            (item) => item !== null && item !== undefined
        );
        setData("attachment", filteredArray);
    };
    function checkImage(fileName) {
        const extension = fileName.split(".").pop();
        return validImageExtensions.includes(extension);
    }


    return (
        <div className="bg-[#F0EEED]">
            {/*-------- Message -----------*/}
            <div className="">
                <div className="">
                    {openEmoji && (
                        <div
                            className="absolute z-50 "
                            style={{ top: "25%", bottom: "25%" }}
                        >
                            <EmojiPicker
                                open={openEmoji}
                                lazyLoadEmojis={true}
                                onEmojiClick={(emojiObject) =>
                                    setData(
                                        "msg",
                                        (data.msg += emojiObject.emoji)
                                    )
                                }
                            />
                        </div>
                    )}

                    <div className="p-2 border border-gray-300 rounded-md bg-gray-50">
                        <div className="grid grid-cols-3 md:grid-cols-5 lg:grid-cols-5 xl:grid-cols-7">
                            {data.hasOwnProperty('attachment') ?
                                <>
                                    {
                                        data.attachment.map((item, k) => {
                                            return (
                                                <div
                                                    className="flex justify-center"
                                                    key={"images-item" + k}
                                                >
                                                    <div className="relative w-full p-1">
                                                        {checkImage(item.file.name) ? (
                                                            <img
                                                                src={window.URL.createObjectURL(
                                                                    item.file
                                                                )}
                                                                className="w-full my-2 rounded-md "
                                                            />
                                                        ) : (
                                                            <div className="p-3 text-3xl text-center text-gray-300">{`.${item.file.name
                                                                .split(".")
                                                                .pop()}`}</div>
                                                        )}
                                                        <div className="absolute top-0 end-0">
                                                            <Button
                                                                size="xs"
                                                                color="failure"
                                                                className="rounded-full opacity-80"
                                                                onClick={() =>
                                                                    removeImage(k)
                                                                }
                                                            // gradientDuoTone="purpleToBlue"
                                                            >
                                                                x
                                                            </Button>
                                                        </div>
                                                    </div>
                                                </div>
                                            );
                                        })
                                    }
                                </>
                                : <></>}
                        </div>

                        <Textarea
                            theme={textarea_custom}
                            name="message"
                            placeholder="Type Message here..."
                            rows={4}
                            id="myTextarea"
                            ref={textareaRef}
                            value={data.msg}
                            onFocus={() => setOpenEmoji(false)}
                            color={errors.msg ? "failure" : "gray"}
                            onChange={(e) => setData("msg", e.target.value)}
                            className="border-0 focus:ring-transparent"
                            helperText={errors.msg && errors.msg}
                        />

                        <div className="flex flex-col flex-wrap justify-between gap-1 flex-center lg:flex-row">
                            <div className="flex flex-wrap items-center gap-1 space-x-1">
                                <div className="">
                                    <label htmlFor="message-file-input">
                                        <Button
                                            className="px-0"
                                            size="xs"
                                            color="gray"
                                            theme={button_custom}
                                            onClick={() => handleClick()}
                                        >
                                            <div className="flex items-center gap-1">
                                                <CgAttachment className="text-slate-500" />
                                                <span className="text-sm">
                                                    Add File
                                                </span>
                                            </div>
                                        </Button>
                                    </label>
                                    <FileInput
                                        id="message-file-input"
                                        ref={fileInputRef}
                                        className="hidden"
                                        onChange={(e) => handleFileChange(e)}
                                    />
                                </div>
                                <div className="">
                                    <Button
                                        className="px-0"
                                        size="xs"
                                        color="gray"
                                        onClick={() => setOpenEmoji(!openEmoji)}
                                    >
                                        <FaRegFaceSmileBeam />
                                    </Button>
                                </div>
                                <div className="">
                                    <Button
                                        className="px-0"
                                        size="xs"
                                        color="gray"
                                        id="boldButton"
                                        onClick={() => textFormat("*")}
                                    >
                                        <FaBold />
                                    </Button>
                                </div>
                                <div className="">
                                    <Button
                                        className="px-0"
                                        size="xs"
                                        color="gray"
                                        id="italicButton"
                                        onClick={() => textFormat("_")}
                                    >
                                        <FaItalic />
                                    </Button>
                                </div>
                                <div className="">
                                    <Button
                                        className="px-0"
                                        size="xs"
                                        color="gray"
                                        id="strike"
                                        onClick={() => textFormat("~")}
                                    >
                                        <FaStrikethrough />
                                    </Button>
                                </div>
                            </div>

                            <div className="flex flex-wrap gap-2">
                                <div className="">
                                    <Button
                                        theme={button_custom}
                                        size="xs"
                                        // outline
                                        className="rounded-lg bg-gray-50"
                                        color="gray"
                                        id="varName"
                                        onClick={() => setIsOpenTemplates(true)}
                                    >
                                        <div className="flex items-center gap-1 text-sm">
                                            <FaRegNoteSticky className="text-lg text-slate-600" />
                                            Select Template
                                        </div>
                                    </Button>
                                </div>
                                <div className="">
                                    <select
                                        className="p-1 px-3 text-sm border-gray-300 rounded-md hover:bg-gray-50"
                                        name="cars"
                                        onChange={(e) => setData('gateway', e.target.value)}
                                    >
                                        <option value="">Gateway</option>
                                        {gateways ?
                                            <>
                                                {gateways.map((gate) => {
                                                    return <option key={'gate-' + gate.id} value={gate.id}>{gate.name}</option>

                                                })}
                                            </>
                                            :
                                            <></>
                                        }
                                    </select>
                                </div>

                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <span className="text-sm text-red-600 ms-4">
                Maximum upload file size: 10 MB.
            </span>

            {
                isOpenTemplates && (
                    <Drawer
                        theme={customDrawer}
                        open={isOpenTemplates}
                        onClose={() => setIsOpenTemplates(false)}
                        position="right"
                        className="w-full xl:w-10/12 lg:w-full md:w-full"
                    >
                        <DrawerHeader
                            titleIcon={HiOutlineTemplate}
                            title="Template library"
                        />
                        <DrawerItems>
                            <Templates setTemp={setTemp} closeDrawer={setIsOpenTemplates} />
                        </DrawerItems>
                    </Drawer>
                )
            }
        </div >
    );
}

export default TextBox;



