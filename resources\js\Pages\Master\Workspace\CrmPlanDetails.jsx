import { button_custom, table_custom } from '@/Pages/Helpers/DesignHelper'
import { Button, Table, TableBody, TableCell, TableHead, TableHeadCell, TableRow } from 'flowbite-react'
import React from 'react'
import { IoIosArrowForward } from 'react-icons/io'
import { IoSettingsOutline } from 'react-icons/io5'
import WorkspaceMain from './workspaceMain'
import { Link } from '@inertiajs/react'

export default function CrmPlanDetails() {
    return (
        <WorkspaceMain>
            <div className='bg-white rounded-md '>
                <h2 className="pt-3 text-xl font-semibold text-center">Choose the subscription that's right for you</h2>
                <p className="mt-1 text-base text-center text-gray-500">Find the perfect plan tailored to your business needs. Compare features, choose what suits you best, and unlock the full potential of your CRM with the right subscription.</p>
                <div className="mt-4 overflow-x-auto bg-white border rounded-lg text-nowrap">
                    <Table hoverable theme={table_custom}>
                        <TableHead>
                            <TableHeadCell></TableHeadCell>
                            <TableHeadCell>Free</TableHeadCell>
                            <TableHeadCell>Pro</TableHeadCell>
                            <TableHeadCell>Enterprise</TableHeadCell>
                        </TableHead>
                        <TableBody className="divide-y">
                            <TableRow className="bg-white dark:border-gray-700 dark:bg-gray-800">
                                <TableCell>
                                    Leads & Customers Management
                                </TableCell>
                                <TableCell>✅</TableCell>
                                <TableCell className='bg-[#F1FFF1] border-s'>✅</TableCell>
                                <TableCell className='bg-[#F1FAFF] border-s'>✅</TableCell>
                            </TableRow>
                            <TableRow className="bg-white dark:border-gray-700 dark:bg-gray-800">
                                <TableCell>
                                    Billing & Invoicing
                                </TableCell>
                                <TableCell>❌</TableCell>
                                <TableCell className='bg-[#F1FFF1] border-s'>✅</TableCell>
                                <TableCell className='bg-[#F1FAFF] border-s'>✅</TableCell>
                            </TableRow>
                            <TableRow className="bg-white dark:border-gray-700 dark:bg-gray-800">
                                <TableCell>WhatsApp API Integration</TableCell>
                                <TableCell>❌</TableCell>
                                <TableCell className='bg-[#F1FFF1] border-s'>✅</TableCell>
                                <TableCell className='bg-[#F1FAFF] border-s'>✅</TableCell>
                            </TableRow>
                            <TableRow className="bg-white dark:border-gray-700 dark:bg-gray-800">
                                <TableCell>SMS & Email Campaigns</TableCell>
                                <TableCell>Limited</TableCell>
                                <TableCell className='bg-[#F1FFF1] border-s'>✅</TableCell>
                                <TableCell className='bg-[#F1FAFF] border-s'>✅</TableCell>
                            </TableRow>
                            <TableRow className="bg-white dark:border-gray-700 dark:bg-gray-800">
                                <TableCell>User Roles & Permissions</TableCell>
                                <TableCell>Basic</TableCell>
                                <TableCell className='bg-[#F1FFF1] border-s'>Advanced</TableCell>
                                <TableCell className='bg-[#F1FAFF] border-s'>Custom</TableCell>
                            </TableRow>
                            <TableRow className="bg-white dark:border-gray-700 dark:bg-gray-800">
                                <TableCell>Automation & Workflows</TableCell>
                                <TableCell>❌</TableCell>
                                <TableCell className='bg-[#F1FFF1] border-s'>✅</TableCell>
                                <TableCell className='bg-[#F1FAFF] border-s'>✅</TableCell>
                            </TableRow>
                            <TableRow className="bg-white dark:border-gray-700 dark:bg-gray-800">
                                <TableCell>Automation & Workflows</TableCell>
                                <TableCell>❌</TableCell>
                                <TableCell className='bg-[#F1FFF1] border-s'>✅</TableCell>
                                <TableCell className='bg-[#F1FAFF] border-s'>✅</TableCell>
                            </TableRow>
                            <TableRow className="bg-white dark:border-gray-700 dark:bg-gray-800">
                                <TableCell>Reports & Analytics</TableCell>
                                <TableCell>Basic</TableCell>
                                <TableCell className='bg-[#F1FFF1] border-s'>Advanced</TableCell>
                                <TableCell className='bg-[#F1FAFF] border-s'>Custom Dashboards</TableCell>
                            </TableRow>
                            <TableRow className="bg-white dark:border-gray-700 dark:bg-gray-800">
                                <TableCell>Reports & Analytics</TableCell>
                                <TableCell>Basic</TableCell>
                                <TableCell className='bg-[#F1FFF1] border-s'>Advanced</TableCell>
                                <TableCell className='bg-[#F1FAFF] border-s'>Custom Dashboards</TableCell>
                            </TableRow>
                            <TableRow className="bg-white dark:border-gray-700 dark:bg-gray-800">
                                <TableCell>Data Storage & Retention</TableCell>
                                <TableCell>Up to 3 months</TableCell>
                                <TableCell className='bg-[#F1FFF1] border-s'>1 Year</TableCell>
                                <TableCell className='bg-[#F1FAFF] border-s'>Unlimited</TableCell>
                            </TableRow>
                            <TableRow className="bg-white dark:border-gray-700 dark:bg-gray-800">
                                <TableCell>Third-party Integrations (API, Webhooks)</TableCell>
                                <TableCell>❌</TableCell>
                                <TableCell className='bg-[#F1FFF1] border-s'>✅</TableCell>
                                <TableCell className='bg-[#F1FAFF] border-s'>✅</TableCell>
                            </TableRow>
                            <TableRow className="bg-white dark:border-gray-700 dark:bg-gray-800">
                                <TableCell>Security & Compliance (2FA, Encryption)</TableCell>
                                <TableCell>❌</TableCell>
                                <TableCell className='bg-[#F1FFF1] border-s'>✅</TableCell>
                                <TableCell className='bg-[#F1FAFF] border-s'>✅</TableCell>
                            </TableRow>
                            <TableRow className="bg-white dark:border-gray-700 dark:bg-gray-800">
                                <TableCell>Customer Support</TableCell>
                                <TableCell>Email only</TableCell>
                                <TableCell className='bg-[#F1FFF1] border-s'>Email & Chat</TableCell>
                                <TableCell className='bg-[#F1FAFF] border-s'>Dedicated Account Manager</TableCell>
                            </TableRow>
                            <TableRow className="bg-white dark:border-gray-700 dark:bg-gray-800">
                                <TableCell></TableCell>
                                <TableCell>
                                    <Button theme={button_custom} color='gray' size='xs' className='w-full text-gray-500 bg-gray-100'>
                                        <span className='text-sm'>
                                            Free
                                        </span>
                                    </Button>
                                </TableCell>
                                <TableCell >
                                    <Button theme={button_custom} color='green' size='xs' className='w-full '>
                                        <span className='text-sm'>
                                            ₹ 800 / month
                                        </span>
                                    </Button></TableCell>
                                <TableCell >
                                    <Button theme={button_custom} color='blue' size='xs' className='w-full '>
                                        <span className='text-sm'>
                                            ₹ 1,500 / month
                                        </span>
                                    </Button></TableCell>
                            </TableRow>
                        </TableBody>
                    </Table>
                </div>
            </div>
            <div className='p-4 pt-2 mt-4 bg-white rounded-lg'>
                <div className='flex items-center gap-2 my-2'>
                    <IoSettingsOutline className='text-xl' />
                    <span className='text-base font-medium'>Configure your CRM</span>
                </div>
                <div className='p-3 border rounded-lg'>
                    <div className='flex items-center gap-6'>
                        <span className='text-base font-medium'>CRM Users</span>
                        <div className='flex items-center gap-2'>
                            <button className='text-3xl text-slate-400'>-</button>
                            <input type="text" defaultValue={1} className='w-1/2 h-10 border border-gray-300 rounded-md' />
                            <button className='text-3xl text-slate-400'>+</button>
                        </div>
                    </div>
                    <div className='p-2 mt-2 border border-blue-500 rounded-md bg-blue-50'>
                        <div className='flex items-center justify-between gap-2'>
                            <span className='text-start'>Total Users:</span>
                            <span className='text-end'>1</span>
                        </div>
                        <div className='flex items-center justify-between gap-2 mt-2 text-sm'>
                            <span className='text-start'>CRM Free PLan</span>
                            <span className='text-end'>₹0.00</span>
                        </div> <div className='flex items-center justify-between gap-2 mt-2 text-xl font-medium'>
                            <span className='text-start'>Total Amount:</span>
                            <span className='text-end'>Total Amount:</span>
                        </div>
                    </div>
                </div>
            </div>
            <div className='flex justify-end mt-4'>
                <Button theme={button_custom} color='blue' size='sm' as={Link} href={route('whatsappBusinessApi')}>
                    <div className='flex items-center gap-2'>
                        <span className='text-sm'>
                            Next
                        </span>
                        <IoIosArrowForward />
                    </div>
                </Button>
            </div>
        </WorkspaceMain>
    )
}

