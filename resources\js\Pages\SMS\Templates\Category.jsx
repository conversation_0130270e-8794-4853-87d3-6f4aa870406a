import { button_custom, listchat_custom } from "@/Pages/Helpers/DesignHelper";
import React, { useEffect, useState } from "react";
import Search from "@/Pages/Users/<USER>";
import { Button, Checkbox, Label, List, Modal, ModalBody, ModalHeader } from "flowbite-react";
import $ from "jquery";
import { MdDeleteOutline, MdOutlineAdd, MdOutlineEdit } from "react-icons/md";
import { HiOutlineExclamationCircle } from "react-icons/hi2";
import AddCategory from "./AddCategory";
import EditCategory from "./EditCategory";
import { deleteSingle, fetchJson } from "@/Pages/Helpers/Helper";
import { Link, router, useForm } from "@inertiajs/react";
import IconButton from "@mui/material/IconButton";
import Badge from "@mui/material/Badge";
import { styled } from "@mui/material/styles";

export default function Category({
    categories,
    category_id,
    redirectBackRoute,
    setTemplate,
    setSelectedCategory,
    selectedCategory,
}) {

    function fetchCategoryData() {
        return fetch(route("whatsapp.templates.index"), {
            headers: {
                Accept: "application/json",
            },
        })
            .then((res) => res.json())
            .then((data) => {
                setCategoryData(data.categories);
            })
            .catch((error) => {
                console.error("Error fetching badge data:", error);
            });
    }

    useEffect(() => {
        if (categories == undefined || categories == null) {
            fetchCategoryData();
        }
    }, []);

    const StyledBadge = styled(Badge)(({ theme }) => ({
        "& .MuiBadge-badge": {
            right: -8,
            top: 4,
            border: `2px solid ${theme.palette.background.paper}`,
            padding: "px 4px px 4px",
            // backgroundColor: "#1976d2",
            // color: "#1a1a1aa8"
        },
    }));
    const removeObjectAndDeleteRecord = (table, id, objKey) => {
        deleteSingle(table, id, "Category Deleted");
        router.get(route(redirectBackRoute));
    };
    const [openModal, setOpenModal] = useState(false);
    const [openEditModal, setOpenEditModal] = useState(false);
    const [editModalId, setEditModalId] = useState(null);
    const [categoryData, setCategoryData] = useState(categories);
    const [activeCategory, setActiveCategory] = useState(category_id);

    return (
        <div className="w-full bg-white  rounded-md h-fit">
            <div className="flex items-center justify-between px-3 py-2">
                <span>Categories</span>
                <Button
                    theme={button_custom}
                    color="gray"
                    size="xs"
                    onClick={() => setOpenModal(true)}
                >
                    <div className="flex items-center ">
                        <MdOutlineAdd className="text-slate-500" />
                        <span className="text-xs">Add</span>
                    </div>
                </Button>
            </div>

            <Link
                href={route("whatsapp.templates.show", { template: 0 })}
                className={`flex items-center justify-between p-3 border-b border-t text-sm  ${0 == activeCategory ? "bg-blue-100" : ""
                    }`}
                onClick={() => setActiveCategory(0)}
            >
                All&nbsp;
            </Link>

            <div className="flex bg-white lg:flex-col flex-col md:flex-col h-full overflow-y-auto">
                {categoryData &&
                    categoryData.map((category, key) => {
                        return (
                            <div
                                className={`flex items-center justify-between p-3 border-b  text-sm ${category.id == activeCategory
                                        ? "bg-blue-100"
                                        : ""
                                    }`}
                                key={key}
                            >
                                <Link
                                    href={route("whatsapp.templates.show", {
                                        template: category.id,
                                    })}
                                    className={`w-full`}
                                    onClick={() =>
                                        setActiveCategory(category.id)
                                    }
                                >
                                    {/* <StyledBadge badgeContent={category.template_count} color="primary"> */}
                                    <div className="flex items-center gap-2">
                                        {category.name}&nbsp;
                                        <div className="bg-blue-100 text-blue-600 px-1 rounded text-xs">
                                            {category.template_count}
                                        </div>
                                    </div>
                                    {/* </StyledBadge> */}
                                </Link>
                                <div>
                                    <div className="flex items-center">
                                        <Button
                                            theme={button_custom}
                                            color="withoutBorder"
                                            size="xxs"
                                            value={category.id}
                                            onClick={() => {
                                                setOpenEditModal(true);
                                                setEditModalId(category.id);
                                            }}
                                        >
                                            <MdOutlineEdit className="text-slate-400" />
                                        </Button>
                                        <Button
                                            theme={button_custom}
                                            color="withoutBorder"
                                            size="xxs"
                                            onClick={() =>
                                                window.confirm(
                                                    "Do you really want to delete Category ?"
                                                ) &&
                                                removeObjectAndDeleteRecord(
                                                    "wa_template_category",
                                                    category.id
                                                )
                                            }
                                        >
                                            <MdDeleteOutline className="text-slate-400" />
                                        </Button>
                                    </div>
                                </div>
                            </div>
                        );
                    })}
            </div>
            {openModal && (
                <Modal
                    show={openModal}
                    size="md"
                    onClose={() => setOpenModal(false)}
                    popup
                >
                    <ModalHeader className="p-2">
                        <h4 className="text-lg">Add Category</h4>
                    </ModalHeader>
                    <ModalBody className="px-4 py-3 rounded-b-lg bg-slate-100">
                        <AddCategory
                            onClose={() => setOpenModal(false)}
                        ></AddCategory>
                    </ModalBody>
                </Modal>
            )}
            {openEditModal && (
                <Modal
                    show={openEditModal}
                    size="md"
                    onClose={() => setOpenEditModal(false)}
                    popup
                >
                    <ModalHeader className="p-2">
                        <h4 className="text-lg">Edit Category</h4>
                    </ModalHeader>
                    <ModalBody className="px-4 py-3 rounded-b-lg bg-slate-100">
                        <EditCategory
                            onClose={() => setOpenEditModal(false)}
                            editId={editModalId}
                        ></EditCategory>
                    </ModalBody>
                </Modal>
            )}
        </div>
    );
}

