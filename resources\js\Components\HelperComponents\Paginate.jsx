import { Button, ButtonGroup } from "flowbite-react";
import { FaChevronLeft, FaChevronRight, FaAnglesLeft, FaAnglesRight } from "react-icons/fa6";
import { Link } from "@inertiajs/react";
import { useMemo } from "react";

function Paginate({ tableData }) {
    if (!tableData || !tableData.links) return null;
    const paginationData = tableData.links;

    const result = useMemo(() => {
        const getObjectsAroundActive = (objects, activeProperty) => {
            if (!objects || !Array.isArray(objects)) return [];
            const newOne = objects.slice(1, -1);
            const activeIndex = newOne.findIndex(obj => obj && obj[activeProperty]);
            if (activeIndex === -1) return [];

            const startIndex = Math.max(activeIndex - 2, 0);
            const endIndex = Math.min(activeIndex + 3, newOne.length);

            return newOne.slice(startIndex, endIndex);
        };

        return getObjectsAroundActive(paginationData, 'active');
    }, [paginationData]);

    const renderPageButton = (lnk, k) => {
        if (!lnk) return null;
        return lnk.url ? (
            <Button
                key={`paginationBtn${k}`}
                color={lnk.active ? "blue" : "gray"}
                size="xs"
                as={lnk.active ? undefined : Link}
                href={lnk.url}
                disabled={!lnk.url}
            >
                <div
                    className="px-1 text-xs h-fit border-e-0 rounded-e-0 text-nowrap"
                    dangerouslySetInnerHTML={{ __html: lnk.label || '' }}
                />
            </Button>
        ) : null;
    };

    if (!paginationData || paginationData.length <= 3) return null;

    return (
        <div className="flex items-center overflow-auto">
            <ButtonGroup>
                <Button size="xs" color="gray" className="focus:ring-0" as={Link} href={tableData.first_page_url || '#'} disabled={!tableData.first_page_url || tableData.current_page === 1}>
                    <div className="flex items-center">
                        <FaAnglesLeft />
                    </div>
                </Button>
                <Button size="xs" color="gray" className="focus:ring-0" as={Link} href={tableData.prev_page_url || '#'} disabled={!tableData.prev_page_url || tableData.current_page === 1}>
                    <div className="flex items-center">
                        <FaChevronLeft />
                    </div>
                </Button>
                {result.map(renderPageButton)}
                <Button size="xs" className="focus:ring-0" color="gray" as={Link} href={tableData.next_page_url || '#'} disabled={!tableData.next_page_url || tableData.current_page === tableData.last_page}>
                    <div className="flex items-center">
                        <FaChevronRight />
                    </div>
                </Button>
                <Button size="xs" className="focus:ring-0" color="gray" as={Link} href={tableData.last_page_url || '#'} disabled={!tableData.last_page_url || tableData.current_page === tableData.last_page}>
                    <div className="flex items-center">
                        <FaAnglesRight />
                    </div>
                </Button>
            </ButtonGroup>
        </div>
    );
}

export default Paginate;
