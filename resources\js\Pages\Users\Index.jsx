
import { Head } from "@inertiajs/react";
import Filters from "./Filters";
import $ from "jquery";
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>er, <PERSON><PERSON><PERSON>eader, DrawerItems, Table, Checkbox } from "flowbite-react";
import Details from "./Details";
import { CiExport } from "react-icons/ci";
import { TbColumns3 } from "react-icons/tb";
import { PiUserFocus } from "react-icons/pi";
import { useState } from "react";
import { IoClose } from "react-icons/io5";
import {
    MdDeleteOutline,
    MdOutlineContactPage,
    MdOutlineModeEdit,
} from "react-icons/md";
import Edit from "./Edit";
import Documents from "./Documents";
import Main from "@/Layouts/Main";
import { BiPlus } from "react-icons/bi";
import { customTheme_drawer } from "../Helpers/DesignHelper";
import Add from "./Add";

const customTheme = {
    root: {
        position: {
            right: {
                on: "right-0 top-0 h-screen w-[70vw] transform-none bg-slate-100",
                off: "right-0 top-0 h-screen w-80 translate-x-full",
            },
        },
    },
    header: {
        inner: {
            closeButton:
                "absolute end-2.5 top-2.5 flex h-8 w-8 items-center justify-center rounded-lg bg-transparent text-sm text-gray-400 hover:bg-gray-200 hover:text-gray-900 dark:hover:bg-gray-600 dark:hover:text-white",
            closeIcon: "h-4 w-4",
            titleIcon: "me-2.5 h-6 w-6 text-gray-400",
            titleText:
                "mb-4 inline-flex items-center text-lg font-semibold text-sky-950 dark:text-gray-400",
        },
    },
};

const tableCustomTheme = {
    head: {
        base: "group/head text-gray-700 dark:text-gray-400 ",
        cell: {
            base: "text-slate-900 dark:text-slate-300 font-medium border dark:border-slate-500 bg-slate-100 text-nowrap px-3 py-2 group-first/head:first:rounded-tl-lg group-first/head:last:rounded-tr-lg dark:bg-gray-700",
        },
    },
    body: {
        base: "group/body",
        cell: {
            base: "border-b px-3 py-1 group-first/body:group-first/row:first:rounded-tl-lg group-first/body:group-first/row:last:rounded-tr-lg group-last/body:group-last/row:first:rounded-bl-lg group-last/body:group-last/row:last:rounded-br-lg",
        },
    },
};

const buttonCustomTheme = {
    color: {
        gray: ":ring-cyan-700 border border-gray-200 bg-white text-gray-900 f enabled:hover:bg-gray-100 dark:border-gray-600 dark:bg-transparent dark:text-gray-400 dark:enabled:hover:bg-gray-700 dark:enabled:hover:text-white",
    },
    pill: {
        off: "rounded",
        on: "rounded-full",
    },
    size: {
        xs: "p-0.5 text-lg",
        sm: "px-3 py-1.5 text-sm",
        md: "px-4 py-2 text-sm",
        lg: "px-5 py-2.5 text-base",
        xl: "px-6 py-3 text-base",
    },
};


const ToggleChild = (parent, child) => {
    const parentEle = $("#" + parent);
    const ele = $("#" + child);
    if (ele.hasClass("hidden")) {
        parentEle.addClass("rotate-180");
        ele.removeClass("hidden");
    } else {
        parentEle.removeClass("rotate-180");
        ele.addClass("hidden");
    }
};

function Index() {
    const [isAddOpen, setIsAddOpen] = useState(false);
    const [isCheckAll, setIsCheckAll] = useState(false);
    const [isEditOpen, setIsEditOpen] = useState(false);
    const [isDocumentsOpen, setIsDocumentsOpen] = useState(false);

    const handleEditClose = () => setIsEditOpen(false);
    const handleDocumentsClose = () => setIsDocumentsOpen(false);

    const renderTableHead = () => (
        <Table.Head className="bg-slate-100">
            <Table.HeadCell>
                <Checkbox
                    checked={isCheckAll}
                    onChange={() => setIsCheckAll(!isCheckAll)}
                />
            </Table.HeadCell>
            {["Date & Time", "Name", "Mobile", "Email", "Report To", "Target", "Active", "Actions"].map((header, index) => (
                <Table.HeadCell key={index}>
                    <div className="flex items-center justify-between">
                        <h3>{header}</h3>
                        {index < 2 && (
                            <svg
                                xmlns="http://www.w3.org/2000/svg"
                                height="16px"
                                viewBox="0 0 24 24"
                                width="16px"
                                className="fill-gray-600"
                            >
                                <path d="M0 0h24v24H0V0z" fill="none" />
                                <path d="M12 5.83L15.17 9l1.41-1.41L12 3 7.41 7.59 8.83 9 12 5.83zm0 12.34L8.83 15l-1.41 1.41L12 21l4.59-4.59L15.17 15 12 18.17z" />
                            </svg>
                        )}
                    </div>
                </Table.HeadCell>
            ))}
        </Table.Head>
    );

    const renderTableRow = () => (
        <Table.Row className="bg-white dark:border-gray-700 dark:bg-gray-800">
            <Table.Cell>
                <Checkbox className="rowCheckBox" />
            </Table.Cell>
            <Table.Cell className="font-medium text-gray-900 whitespace-nowrap dark:text-white">
                <div className="flex justify-between">
                    <div className="flex flex-col w-20 truncate">
                        <div className="text-sm text-blue-500">12/08/24</div>
                        <div className="text-xs">2:24 PM</div>
                    </div>
                </div>
            </Table.Cell>
            <Table.Cell>
                <div className="text-sm text-blue-500 text-nowrap">
                    Rohan Preet (2568)
                </div>
                <div className="text-xs text-nowrap">Sales Executive</div>
            </Table.Cell>
            <Table.Cell>+91 8561024850</Table.Cell>
            <Table.Cell><EMAIL></Table.Cell>
            <Table.Cell>
                <Tooltip content="Abhishek" className="p-1 bg-slate-700">
                    <img
                        src="https://img.freepik.com/premium-photo/profile-icon-white-background_941097-160810.jpg?w=50"
                        alt=""
                        className="rounded-full"
                    />
                </Tooltip>
            </Table.Cell>
            <Table.Cell>70,000</Table.Cell>
            <Table.Cell>Yes</Table.Cell>
            <Table.Cell>
                <div className="flex justify-between gap-2">
                    <div className="flex gap-2">
                        <Tooltip content="Documents" className="p-1 px-2 bg-slate-700">
                            <Button
                                className="pb-1 text-white bg-orange-500"
                                theme={buttonCustomTheme}
                                size="xs"
                                onClick={() => setIsDocumentsOpen(true)}
                            >
                                <MdOutlineContactPage />
                            </Button>
                        </Tooltip>
                        <Drawer
                            theme={customTheme}
                            open={isDocumentsOpen}
                            onClose={handleDocumentsClose}
                            position="right"
                            className="w-full p-0 lg:w-4/5"
                        >
                            <DrawerItems>
                                <div className="flex items-center justify-between px-4 py-2 mb-3 bg-white">
                                    <div className="flex items-center gap-2">
                                        <img
                                            className="w-12 h-12 rounded-full"
                                            src="https://img.freepik.com/free-vector/businessman-character-avatar-isolated_24877-60111.jpg?t=st=1720614745~exp=1720618345~hmac=38ff343a9443982af738325ad4d54d1d39ba540be4c28013b03cf063347d255e&w=200"
                                            alt=""
                                        />
                                        <div>
                                            <h4 className="text-base font-medium text-blue-600">
                                                Abhishek Chatterjee(DF25)
                                            </h4>
                                            <h6>Sales Manager</h6>
                                        </div>
                                    </div>
                                    <button className="bg-transparent" onClick={handleDocumentsClose}>
                                        <IoClose className="text-[24px] text-slate-400" />
                                    </button>
                                </div>
                                <Documents />
                            </DrawerItems>
                        </Drawer>
                        <Tooltip content="Edit" className="p-1 px-2 bg-slate-700">
                            <Button
                                theme={buttonCustomTheme}
                                color="success"
                                size="xs"
                                onClick={() => setIsEditOpen(true)}
                            >
                                <MdOutlineModeEdit />
                            </Button>
                        </Tooltip>
                        <Drawer
                            theme={customTheme}
                            open={isEditOpen}
                            onClose={handleEditClose}
                            position="right"
                            className="w-full xl:w-2/5 lg:w-3/5 md:w-3/5"
                        >
                            <DrawerHeader title="Edit User" titleIcon={PiUserFocus} />
                            <DrawerItems>
                                <Edit />
                            </DrawerItems>
                        </Drawer>
                        <Tooltip content="Delete" className="p-1 px-2 bg-slate-700">
                            <Button theme={buttonCustomTheme} color="failure" size="xs">
                                <MdDeleteOutline />
                            </Button>
                        </Tooltip>
                    </div>
                    <div>
                        <button className="p-1.5" id="parentID" onClick={() => ToggleChild("parentID", "childTr")}>
                            <svg
                                xmlns="http://www.w3.org/2000/svg"
                                height="23px"
                                viewBox="0 0 24 24"
                                width="23px"
                                className="fill-gray-500"
                            >
                                <path d="M0 0h24v24H0V0z" fill="none" />
                                <path d="M7.41 8.59 12 13.17l4.59-4.58L18 10l-6 6-6-6 1.41-1.41z" />
                            </svg>
                        </button>
                    </div>
                </div>
            </Table.Cell>
        </Table.Row>
    );

    return (
        <Main>
            <Head title="User" />
            <div className="relative p-2 overflow-hidden">
                <div className="grid grid-flow-row-dense grid-cols-12 gap-2 p-0 border-none md:grid-cols-12 sm:grid-cols-1 bg-slate-100">
                    <div className="hidden lg:col-span-2 md:col-span-3 sm:col-span-3 lg:flex">
                        <div className="bg-white shadow-sm rounded-lg border dark:bg-gray-900 w-full overflow-auto min-h-[90vh] max-h-[90vh]">
                            <Filters />
                        </div>
                    </div>
                    <div className="relative pt-0 dark:text-gray-400 lg:p-0 lg:col-span-10 md:col-span-12 col-span-full">
                        <div className="mb-2 col-span-full">
                            {/* <UserRoleTabBar /> */}
                        </div>
                        <div className="min-h-[80vh] max-h-[80vh] bg-white border rounded-lg">
                            <div className="flex justify-between p-2">
                                <div className="flex">
                                    <Button
                                        theme={buttonCustomTheme}
                                        size="xs"
                                        color="gray"
                                        className="border-e-0 rounded-e-none"
                                    >
                                        <div className="flex items-center gap-1">
                                            <CiExport className="text-slate-500" />
                                            <span className="text-xs">Export</span>
                                        </div>
                                    </Button>
                                    <Button
                                        className="border rounded-s-none"
                                        size="xs"
                                        color="gray"
                                        theme={buttonCustomTheme}
                                    >
                                        <div className="flex items-center gap-1">
                                            <MdDeleteOutline className="text-slate-500" />
                                            <span className="text-xs">Delete</span>
                                        </div>
                                    </Button>
                                </div>
                                <Button
                                    className="border rounded-s-none"
                                    size="xs"
                                    color="gray"
                                    theme={buttonCustomTheme}
                                    onClick={() => setIsAddOpen(true)}
                                >
                                    <div className="flex items-center gap-1">
                                        <BiPlus className="text-slate-500" />
                                        <span className="text-xs">Add</span>
                                    </div>
                                </Button>
                            </div>
                            <div className="overflow-x-auto bg-white border rounded-lg">
                                <Table hoverable theme={tableCustomTheme}>
                                    {renderTableHead()}
                                    <Table.Body className="divide-y">
                                        {renderTableRow()}
                                        <Table.Row className="hidden bg-gray-100" id="childTr">
                                            <Table.Cell colSpan={9}>
                                                <Details />
                                            </Table.Cell>
                                        </Table.Row>
                                    </Table.Body>
                                </Table>
                            </div>
                        </div>

                    </div>
                </div>
            </div>
            {isAddOpen &&
                <Drawer
                    theme={customTheme_drawer}
                    open={isAddOpen}
                    onClose={() => setIsAddOpen(false)}
                    position="right"
                    className="w-full xl:w-4/5 lg:w-4/5 md:w-4/5"
                >
                    <DrawerHeader
                        title="Add User"
                        titleIcon={PiUserFocus}
                    />
                    <DrawerItems>
                        <Add />
                    </DrawerItems>
                </Drawer>
            }
        </Main>
    );
}

export default Index;
