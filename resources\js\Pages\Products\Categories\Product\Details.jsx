import { button_custom, custom_tabs, tabBar_underline, table_custom } from "@/Pages/Helpers/DesignHelper";
import { showAsset } from "@/Pages/Helpers/Helper";
import { Badge, Button, Modal, Table, Tabs, ToggleSwitch } from "flowbite-react";
import React, { useState } from "react";
import { MdAdd, MdModeEditOutline, MdOutlineEdit, MdOutlineFeaturedPlayList } from "react-icons/md";
import { RiDeleteBin6Line, RiGalleryView2 } from "react-icons/ri";
import { TbTable } from "react-icons/tb";
import AddProductGallery from "./AddProductGallery";
import AddProductVariants from "./AddProductVariants";

export default function Details() {
    const [openModal, setOpenModal] = useState(false);
    const [openModalProductFeature, setOpenModalProductFeature] = useState(false);
    const [openModalProductVariant, setOpenModalProductVariant] = useState(true);

    return (
        <>
            <div className="py-2">
                <Tabs aria-label="Default tabs" variant="underline" theme={tabBar_underline}>
                    <Tabs.Item active title="Product Gallery" icon={RiGalleryView2} className='p-1'>
                        <div className="overflow-x-auto">
                            <Button
                                className="float-end me-2 mb-2"
                                theme={button_custom}
                                size="xs"
                                color="gray"
                                onClick={() => setOpenModal(true)}

                            >
                                <div className="flex items-center gap-1">
                                    <MdAdd className="text-slate-500" />
                                    <span className="text-xs">
                                        Add
                                    </span>
                                </div>
                            </Button>
                            <Table hoverable theme={table_custom}>
                                <TableHead>
                                    <TableHeadCell>Product Name</TableHeadCell>
                                    <TableHeadCell>Product Image</TableHeadCell>
                                    <TableHeadCell>Primary</TableHeadCell>
                                    <TableHeadCell>Action</TableHeadCell>
                                </TableHead>
                                <TableBody className="divide-y">
                                    <TableRow className="bg-white dark:border-gray-700 dark:bg-gray-800">
                                        <TableCell>
                                            Rapbooster Pro
                                        </TableCell>
                                        <TableCell>
                                            <img src={showAsset(
                                                "/assets/img/product-img.png", ''
                                            )} alt="" />
                                        </TableCell>
                                        <TableCell>
                                            <Badge className="w-fit" color="success">Yes</Badge>
                                        </TableCell>
                                        <TableCell>
                                            <div className="flex gap-2 h-fit text-nowrap">
                                                {/* <Tooltip
                               content="Documents"
                               className="bg-slate-700 p-1 px-2"
                           > */}
                                                {/* </Tooltip> */}

                                                <Button
                                                    theme={
                                                        button_custom
                                                    }
                                                    color="green"
                                                    size="xs"
                                                // onClick={() =>
                                                //   setIsOpenEditBillingAddress(
                                                //     true
                                                //   )
                                                // }
                                                >
                                                    <div className="flex items-center gap-1">
                                                        <MdOutlineEdit />
                                                        <span className="text-xs">Edit</span>
                                                    </div>
                                                </Button>
                                                <Button
                                                    theme={
                                                        button_custom
                                                    }
                                                    color="failure"
                                                    size="xs"
                                                // onClick={() =>
                                                //   setIsOpenDocuments(
                                                //     true
                                                //   )
                                                // }
                                                >
                                                    <div className="flex items-center gap-1">
                                                        <RiDeleteBin6Line />
                                                        <span className="text-xs">Delete</span>
                                                    </div>
                                                </Button>
                                            </div>
                                        </TableCell>
                                    </TableRow>

                                </TableBody>
                            </Table>
                        </div>
                    </Tabs.Item>
                    <Tabs.Item title="Product Variant" icon={TbTable}>
                        <div className="overflow-x-auto">
                            <Button
                                className="float-end me-2 mb-2"
                                theme={button_custom}
                                size="xs"
                                color="gray"
                            >
                                <div className="flex items-center gap-1">
                                    <MdAdd className="text-slate-500" />
                                    <span className="text-xs">
                                        Add
                                    </span>
                                </div>
                            </Button>
                            <Table hoverable theme={table_custom}>
                                <TableHead>
                                    <TableHeadCell>Product Variants</TableHeadCell>
                                    <TableHeadCell>MRP</TableHeadCell>
                                    <TableHeadCell>Selling Price</TableHeadCell>
                                    <TableHeadCell>Validity</TableHeadCell>
                                    <TableHeadCell>Trial Available</TableHeadCell>
                                    <TableHeadCell>On Website</TableHeadCell>
                                    <TableHeadCell>Active</TableHeadCell>
                                    <TableHeadCell>Action</TableHeadCell>
                                </TableHead>
                                <TableBody className="divide-y">
                                    <TableRow className="bg-white dark:border-gray-700 dark:bg-gray-800">
                                        <TableCell>
                                            Product 20 - Variant A
                                        </TableCell>
                                        <TableCell>
                                            2,000
                                        </TableCell> <TableCell>
                                            2,1,999
                                        </TableCell> <TableCell>
                                            3 Months
                                        </TableCell>
                                        <TableCell>
                                            <Badge className="w-fit" color="failure">No</Badge>
                                        </TableCell>
                                        <TableCell>
                                            <Badge className="w-fit" color="success">active</Badge>
                                        </TableCell>
                                        <TableCell>
                                            <Badge className="w-fit" color="success">Yes</Badge>
                                        </TableCell>
                                        <TableCell>
                                            <div className="flex gap-2 h-fit text-nowrap">
                                                {/* <Tooltip
                               content="Documents"
                               className="bg-slate-700 p-1 px-2"
                           > */}
                                                {/* </Tooltip> */}

                                                <Button
                                                    theme={
                                                        button_custom
                                                    }
                                                    color="green"
                                                    size="xs"
                                                // onClick={() =>
                                                //   setIsOpenEditBillingAddress(
                                                //     true
                                                //   )
                                                // }
                                                >
                                                    <div className="flex items-center gap-1">
                                                        <MdOutlineEdit />
                                                        <span className="text-xs">Edit</span>
                                                    </div>
                                                </Button>
                                                <Button
                                                    theme={
                                                        button_custom
                                                    }
                                                    color="failure"
                                                    size="xs"
                                                // onClick={() =>
                                                //   setIsOpenDocuments(
                                                //     true
                                                //   )
                                                // }
                                                >
                                                    <div className="flex items-center gap-1">
                                                        <RiDeleteBin6Line />
                                                        <span className="text-xs">Delete</span>
                                                    </div>
                                                </Button>
                                            </div>
                                        </TableCell>
                                    </TableRow>

                                </TableBody>
                            </Table>
                        </div>
                    </Tabs.Item>
                    <Tabs.Item title="Product Feature" icon={MdOutlineFeaturedPlayList} >
                        <div className="overflow-x-auto bg-white rounded-md border">
                            <Button
                                className="float-end m-2"
                                theme={button_custom}
                                size="xs"
                                color="gray"
                            >
                                <div className="flex items-center gap-1">
                                    <MdAdd className="text-slate-500" />
                                    <span className="text-xs">
                                        Add
                                    </span>
                                </div>
                            </Button>
                            <div className="">

                                <Table hoverable theme={table_custom}>
                                    <TableHead>
                                        <TableHeadCell>Product Feature</TableHeadCell>
                                        <TableHeadCell>Icon Class</TableHeadCell>
                                        <TableHeadCell>Icon Color</TableHeadCell>
                                        <TableHeadCell>On Website</TableHeadCell>
                                        <TableHeadCell>Active</TableHeadCell>
                                        <TableHeadCell>Action</TableHeadCell>
                                    </TableHead>
                                    <TableBody className="divide-y">
                                        <TableRow className="bg-white dark:border-gray-700 dark:bg-gray-800">
                                            <TableCell>
                                                Advanced Data Analysis
                                            </TableCell>
                                            <TableCell>
                                                fas fa-chart-pie
                                            </TableCell>
                                            <TableCell>
                                                #28A745
                                            </TableCell>
                                            <TableCell>
                                                <Badge className="w-fit" color="success">active</Badge>
                                            </TableCell>
                                            <TableCell>
                                                <Badge className="w-fit" color="success">Yes</Badge>
                                            </TableCell>
                                            <TableCell>
                                                <div className="flex gap-2 h-fit text-nowrap">
                                                    {/* <Tooltip
                               content="Documents"
                               className="bg-slate-700 p-1 px-2"
                           > */}
                                                    {/* </Tooltip> */}

                                                    <Button
                                                        theme={
                                                            button_custom
                                                        }
                                                        color="green"
                                                        size="xs"
                                                    // onClick={() =>
                                                    //   setIsOpenEditBillingAddress(
                                                    //     true
                                                    //   )
                                                    // }
                                                    >
                                                        <div className="flex items-center gap-1">
                                                            <MdOutlineEdit />
                                                            <span className="text-xs">Edit</span>
                                                        </div>
                                                    </Button>
                                                    <Button
                                                        theme={
                                                            button_custom
                                                        }
                                                        color="failure"
                                                        size="xs"
                                                    // onClick={() =>
                                                    //   setIsOpenDocuments(
                                                    //     true
                                                    //   )
                                                    // }
                                                    >
                                                        <div className="flex items-center gap-1">
                                                            <RiDeleteBin6Line />
                                                            <span className="text-xs">Delete</span>
                                                        </div>
                                                    </Button>
                                                </div>
                                            </TableCell>
                                        </TableRow>

                                    </TableBody>
                                </Table>
                            </div>
                        </div>
                    </Tabs.Item>
                </Tabs>

            </div>
            {/* Add / Edit Product Gallery */}
            <Modal show={openModal} onClose={() => setOpenModal(false)}>
                <ModalHeader className="p-2">
                    <div className="flex items-center gap-1">
                        <MdOutlineEdit className="text-slate-400" />
                        Edit Product Gallery <span className="text-blue-600">(Rapbooster Plus)</span>
                    </div>
                </ModalHeader>
                <ModalBody className="bg-slate-100 p-2 rounded-b-lg">
                    <AddProductGallery></AddProductGallery>
                </ModalBody>
            </Modal>
            {/* Add / Edit Product Variant */}
            <Modal show={openModalProductVariant} onClose={() => setOpenModalProductVariant(false)}>
                <ModalHeader className="p-2">
                    <div className="flex items-center gap-1">
                        <MdOutlineEdit className="text-slate-400" />
                        Edit Product Variant <span className="text-blue-600">(Rapbooster Plus)</span>
                    </div>
                </ModalHeader>
                <ModalBody className="bg-slate-100 p-2 rounded-b-lg">
                    <AddProductVariants></AddProductVariants>
                </ModalBody>
            </Modal>
            {/* Add / Edit Product Feature */}
            <Modal show={openModalProductFeature} onClose={() => setOpenModalProductFeature(false)}>
                <ModalHeader className="p-2">
                    <div className="flex items-center gap-1">
                        Edit Product Feature <span className="text-blue-600">(Rapbooster Plus)</span>
                    </div>
                </ModalHeader>
                <ModalBody className="bg-slate-100 p-2 rounded-b-lg">
                    <AddProductGallery></AddProductGallery>
                </ModalBody>
            </Modal>
        </>
    );
}

