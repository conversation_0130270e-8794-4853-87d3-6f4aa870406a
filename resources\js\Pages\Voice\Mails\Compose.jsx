import {
    button_custom,
    customDrawer,
    input_custom,
    textarea_custom,
} from "@/Pages/Helpers/DesignHelper";
import {
    Button,
    Checkbox,
    Drawer,
    FileInput,
    Label,
    Textarea,
    TextInput,
} from "flowbite-react";
import React from "react";
import {
    FaRegNoteSticky,
} from "react-icons/fa6";
import { MdOutlineFingerprint, MdOutlineHandyman } from "react-icons/md";
import { VscSend } from "react-icons/vsc";
import SideMenu from "./SideMenu";
import { useState } from "react";
import { HiOutlineTemplate } from "react-icons/hi";
import Templates from "./Templates";

export default function Compose() {
    const [isOpenTemplates, setIsOpenTemplates] = useState(false);

    return (
        <>
            <div className="p-2 overflow-hidden">
                <div
                    className="grid grid-cols-12 gap-2 p-0 border-none grid-2 flow-row-dense md:grid-cols-12 sm:grid-cols-1 
                bg-slate-100 mt-2"
                >
                    <div className="hidden xl:col-span-2 lg:col-span-2 md:col-span-3 sm:col-span-3 lg:flex ">
                        <SideMenu />
                    </div>
                    <div className="relative pt-0 dark:text-gray-400 lg:p-0 xl:col-span-10 lg:col-span-10 md:col-span-12 col-span-full">
                        <div className="flex flex-col gap-2">
                            {/* To */}
                            <div className="">
                                <div className="text-nowrap">
                                    <Label htmlFor="email4" value="To" />
                                </div>
                                <div className="w-full">
                                    <TextInput
                                        theme={input_custom}
                                        className="w-full"
                                        id="email4"
                                        type="email"
                                        color="onlyBorder_b"
                                        icon=""
                                        rightIcon={"sfsdfsf"}
                                        // placeholder="<EMAIL>"
                                        required
                                    />
                                </div>
                            </div>
                            {/* CC */}
                            <div className="">
                                <div className="text-nowrap">
                                    <Label htmlFor="Cc" value="Cc" />
                                </div>
                                <div className="w-full">
                                    <TextInput
                                        theme={input_custom}
                                        className="w-full"
                                        id="email4"
                                        type="email"
                                        color="onlyBorder_b"
                                        icon=""
                                        rightIcon={"sfsdfsf"}
                                        // placeholder="<EMAIL>"
                                        required
                                    />
                                </div>
                            </div>
                            {/* Bcc */}
                            <div className="">
                                <div className="text-nowrap">
                                    <Label htmlFor="Bcc" value="Bcc" />
                                </div>
                                <div className="w-full">
                                    <TextInput
                                        theme={input_custom}
                                        className="w-full"
                                        id="email4"
                                        type="email"
                                        color="onlyBorder_b"
                                        icon=""
                                        rightIcon={"sfsdfsf"}
                                        // placeholder="<EMAIL>"
                                        required
                                    />
                                </div>
                            </div>
                            {/* Select Template */}
                            <div className="flex items-center gap-2">
                                <span className="text-sm">Select Template</span>
                                <Button
                                    className="px-0"
                                    size="xs"
                                    color="blue"
                                    theme={button_custom}
                                    onClick={() => setIsOpenTemplates(true)}

                                >
                                    <div className="flex items-center gap-1">
                                        <FaRegNoteSticky className="" />
                                        <span className="text-sm">
                                            Select Template
                                        </span>
                                    </div>
                                </Button>
                            </div>

                            {/* subject */}
                            <div className="mb-1">
                                <div className="text-nowrap">
                                    <Label htmlFor="email4" value="Subject" />
                                </div>
                                <TextInput
                                    theme={input_custom}
                                    className="w-full"
                                    id="email4"
                                    type="email"
                                    color="onlyBorder_b"
                                    icon=""
                                    rightIcon={"sfsdfsf"}
                                    required
                                />
                            </div>

                            {/* Subject text box */}

                            <div className="">
                                {/*-------- Message -----------*/}
                                <div className="">
                                    <div className="">
                                        <div className="text-nowrap flex items-center justify-between mb-2">
                                            <Label htmlFor="email4" value="Body" />
                                            <Button
                                                className="px-0"
                                                size="xs"
                                                color="orange"
                                                theme={button_custom}
                                            // onClick={() => handleClick()}
                                            >
                                                <div className="flex items-center gap-1">
                                                    <MdOutlineHandyman className="" />
                                                    <span className="text-sm">
                                                        Use Builder
                                                    </span>
                                                </div>
                                            </Button>
                                        </div>
                                        <div className="p-2 border border-gray-300 rounded-md bg-gray-50">
                                            <Textarea
                                                theme={textarea_custom}
                                                name="message"
                                                placeholder="Type Message here..."
                                                rows={4}
                                                id="myTextarea"
                                                onFocus={() => setOpenEmoji(false)}
                                                className="border-0 focus:ring-white"
                                            />

                                            <div className="flex flex-col flex-wrap justify-between gap-1 flex-center lg:flex-row">
                                                <div className="flex flex-wrap items-center gap-1 space-x-1">
                                                    <div className="pt-1">
                                                        <label htmlFor="message-file-input">
                                                            <Button
                                                                className="px-0"
                                                                size="xs"
                                                                color="gray"
                                                                theme={button_custom}
                                                            // onClick={() => handleClick()}
                                                            >
                                                                <div className="flex items-center gap-1">
                                                                    <MdOutlineFingerprint className="text-slate-500" />
                                                                    <span className="text-sm">
                                                                        Add File
                                                                    </span>
                                                                </div>
                                                            </Button>
                                                        </label>
                                                        <FileInput
                                                            id="message-file-input"
                                                            // ref={fileInputRef}
                                                            className="hidden"
                                                        />
                                                    </div>
                                                </div>

                                                <div className="flex flex-wrap gap-2">
                                                    <select
                                                        id="countries"
                                                        className="block p-1 text-xs rounded-md text-gray-900 border border-gray-300 bg-gray-50 focus:ring-blue-500 focus:border-blue-500 ps-2 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-blue-500 dark:focus:border-blue-500"
                                                    >
                                                        <option
                                                            value={10}
                                                            defaultValue={
                                                                10
                                                            }
                                                        >
                                                            Gateway
                                                        </option>
                                                        <option value={15}>
                                                            15
                                                        </option>
                                                        <option value={20}>
                                                            20
                                                        </option>
                                                        <option value={25}>
                                                            25
                                                        </option>
                                                        <option value={30}>
                                                            30
                                                        </option>
                                                    </select>
                                                    <div className="flex items-center gap-2">
                                                        <Checkbox color="blue" id="accept" />
                                                        <Label
                                                            htmlFor="accept"
                                                            className="flex"
                                                        >
                                                            Schedule Message&nbsp;
                                                        </Label>
                                                    </div>

                                                    <div className="">
                                                        <div className="">
                                                            <TextInput
                                                                sizing={"xs"}
                                                                theme={input_custom}
                                                                id="startTime"
                                                                name="startTime"
                                                                type="datetime-local"
                                                                // color={
                                                                //     errors.startTime
                                                                //         ? "failure"
                                                                //         : "gray"
                                                                // }
                                                                className="w-full"
                                                            />
                                                        </div>
                                                    </div>
                                                    <Button
                                                        type="submit"
                                                        size="xs"
                                                        color="blue"
                                                        id="var1"
                                                    >
                                                        <div className="flex items-center gap-1 text-sm">
                                                            <VscSend className="text-lg" />
                                                            <span className="">Send Now</span>
                                                        </div>
                                                    </Button>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <span className="text-sm text-red-600 ms-4">
                                    Maximum upload file size: 512 MB.
                                </span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <Drawer
                theme={customDrawer}
                open={isOpenTemplates}
                onClose={() => setIsOpenTemplates(false)}
                position="right"
                className="w-full xl:w-10/12 lg:w-full md:w-full"
            >
                <DrawerHeader
                    titleIcon={HiOutlineTemplate}
                    title="Template library"
                />
                <DrawerItems>
                    <Templates />
                </DrawerItems>
            </Drawer>
        </>
    );
}
