import { FilePreview } from "@/Components/FilePreview";
import FileUploader from "@/Components/FileUploader";
import TinyEditor from "@/Components/TinyEditor";
import { button_custom } from "@/Pages/Helpers/DesignHelper";
import { WaBaExtractVariables } from "@/Pages/Helpers/WabaHelper";
import { router, useForm } from "@inertiajs/react";
import { Button, Label, Modal, Select, TextInput, Tooltip } from "flowbite-react";
import { useEffect, useState } from "react";
import { IoClose } from "react-icons/io5";
import { MdOutlineAdd } from "react-icons/md";
import Templates from "./Templates";


function AddMessagesCampaign({ campaign, selectedTemplate = null, setSelectedTemplate, setSubmitDisable }) {
    const [clearAll, setClearAll] = useState(0);
    const [variables, setVariables] = useState(null);
    const [openTemplate, setOpenTemplate] = useState(false);
    const [resetState, setResetState] = useState(0);
    const variable_option = { name: 'name', mobile: 'mobile', email: 'email', var1: 'var1', var2: 'var2', var3: 'var3', var4: 'var4', var5: 'var5' };
    const { data, setData, post, processing, reset, errors } = useForm({
        message: '',
        campaign_id: campaign.id,
        varObject: null,
        template_id: '',
        files: []
    });

    useEffect(() => {
        setData(prev => ({
            ...prev,
            message: campaign.msg,

        }));
    }, []);

    useEffect(() => {
        if (selectedTemplate != null) {
            setData(prev => ({
                ...prev,
                message: selectedTemplate.body,
                template_id: selectedTemplate.id,
            }));
        }
        setResetState(pre => pre + 1)
    }, [selectedTemplate]);
    
    useEffect(() => {
        if (data.message) {
            setVariables(WaBaExtractVariables(data.message));
        } else {
            setSubmitDisable(true);
        }

    }, [data.message]);

    const addCampaignMessage = () => {
        setSubmitDisable(false);
        post(route("mail.campaign.message.store"), {
            onSuccess: () => {
                setData({
                    message: "",
                    attachment: null,
                    campaign_id: campaign.id,
                    files: null,
                    template_id: null
                });
                setClearAll(prev => prev + 1);

            },
        });
    };

    useEffect(() => {
        if (variables) {
            setData('variable_count', Object.keys(variables).length);
        }

    }, [variables])
    const handleVariableChange = (key, sub_key, value) => {
        setData(prevState => ({
            ...prevState,
            varObject: {
                ...prevState.varObject,
                [key]: {
                    [sub_key == 'column' ? 'column' : 'fixed']: value,
                    [sub_key == 'column' ? 'fixed' : 'column']: ""
                },
            }
        }));
    }
    const deleteAttachment = (id) => {
        router.delete(route('mail.campaign.removeAttachment', { id: id }))
    }

    return (
        <form>
            <div className="flex flex-col gap-1 p-2.5">

                <div className="mb-2">
                    <TinyEditor value={data.message} onChange={setData} id="tinyEditor-campaign" template={true} openTemplate={() => setOpenTemplate(true)} reRenderKey={resetState} />
                    {errors.message && <p className="mt-1 text-red-600">{errors.message}</p>}
                </div>
                <div className="flex flex-col max-w-md gap-4 p-2 mb-2">
                    {variables != null && Object.entries(variables).map(([key, value]) =>
                        <div key={`temp-inputs-${key}`}>
                            <div className="block mb-2">
                                <Label htmlFor={`var-id-${key}`} className="uppercase" value={key} />
                            </div>
                            <div className="flex gap-3 ms-3">
                                <TextInput
                                    className="w-full"
                                    id={`var-id-${key}`}
                                    type="text"
                                    sizing="sm"
                                    placeholder={key}
                                    disabled={data.varObject && data.varObject[key] && data.varObject[key]['column'].length > 0}
                                    onChange={(e) => handleVariableChange(key, "fixed", e.target.value)}
                                />
                                <Select onChange={(e) => handleVariableChange(key, "column", e.target.value)} sizing="sm"
                                    disabled={data.varObject && data.varObject[key] && data.varObject[key]['fixed'].length > 0}
                                >
                                    <option value="">Select Column</option>
                                    {Object.values(variable_option).map((column, index) => (
                                        <option key={`col-${index}`} value={column}>
                                            {column}
                                        </option>
                                    ))}
                                </Select>
                            </div>
                            {errors[key] && <div className="text-red-600">{errors[key]}</div>}
                        </div>)
                    }
                    {errors.variable_error &&
                        <div className="text-red-500">{errors.variable_error}</div>
                    }
                </div>
                <div className="p-2">
                    <Label className="mb-2">Attachments:</Label>
                    <FileUploader setData={setData} data={data} multiple={true} />
                    {errors.files && <p className="text-red-600 text-md">{errors.files}</p>}
                    {campaign?.attachments &&
                        <div className="grid grid-cols-1 gap-2 mt-3 align-top md:grid-cols-2 lg:grid-cols-3 2xl:grid-cols-4">
                            {campaign?.attachments?.map((att, key) =>
                            (
                                <div key={key + att.id} className={`relative`} >
                                    <FilePreview object={att.path} imageSize="xl" />
                                    <div className="absolute top-0 end-0">
                                        <Tooltip content="Remove Attachment" trigger="hover"
                                            theme={{ "base": "absolute z-10 inline-block rounded-lg px-1.5 py-1 text-xs font-medium shadow-sm" }}>
                                            <Button size="xs" color="clear" onClick={() => deleteAttachment(att.id)}>
                                                <IoClose className="text-xl text-red-800" />
                                            </Button>
                                        </Tooltip>
                                    </div>
                                </div>
                            )
                            )}
                        </div>
                    }
                </div>
                <div className="p-2 mb-2">
                    <div className="flex justify-end">
                        <Button
                            theme={button_custom}
                            size="xs"
                            isProcessing={processing}
                            className="rounded-md"
                            color="blue"
                            type="button"
                            onClick={addCampaignMessage}
                        >
                            <div className="flex items-center gap-1">
                                <MdOutlineAdd className="text-lg" />
                                <span className="text-sm">Add Message</span>
                            </div>
                        </Button>
                    </div>
                </div>
            </div>
            <Modal show={openTemplate} onClose={() => setOpenTemplate(false)} style={{ zIndex: "11111" }}>
                <ModalHeader>Select Template</ModalHeader>
                <ModalBody>
                    <Templates setSelectedTemplate={setSelectedTemplate} selectedTemplate={selectedTemplate} />
                </ModalBody>
                <ModalFooter className="justify-end">

                    <Button size="xs" color="failure" onClick={() => setOpenTemplate(false)}>
                        Close
                    </Button>
                </ModalFooter>
            </Modal>
        </form >
    );
}


export default AddMessagesCampaign;

