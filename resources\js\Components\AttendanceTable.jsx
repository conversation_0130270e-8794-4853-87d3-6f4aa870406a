import React from "react";
import { useState } from "react";
import { FaCircle } from "react-icons/fa6";
import { FaChevronLeft, FaChevronRight, FaChevronDown } from "react-icons/fa";
import { <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>dal<PERSON><PERSON>, ModalFooter } from "flowbite-react";
import { IoMdAdd, IoMdThumbsUp } from "react-icons/io";
import { RiArchiveLine } from "react-icons/ri";
import { TbFingerprintOff } from "react-icons/tb";
import MissedPunch from "@/Pages/Admin/Attendance/MissedPunch";
import AddEvent from "@/Pages/Admin/Attendance/AddEvent";

export default function AttendanceTable() {
    // Define months and initialize state
    const months = [
        "January",
        "February",
        "March",
        "April",
        "May",
        "June",
        "July",
        "August",
        "September",
        "October",
        "November",
        "December",
    ];

    const [selectedMonth, setSelectedMonth] = useState("August"); // Default to "August"
    const [selectedYear, setSelectedYear] = useState(2024); // Default to 2024
    const [isDropdownOpen, setIsDropdownOpen] = useState(false); // Toggle state

    // Handlers for previous and next year
    const goToPreviousYear = () => {
        setSelectedYear(selectedYear - 1);
    };

    const goToNextYear = () => {
        setSelectedYear(selectedYear + 1);
    };

    const handleMonthSelect = (month) => {
        setSelectedMonth(month);
        setIsDropdownOpen(false); // Close dropdown after selecting a month
    };

    // Toggle dropdown visibility
    const toggleDropdown = () => {
        setIsDropdownOpen(!isDropdownOpen);
    };
    //------------- Missed Punch Request Model-------------
    const [openMissedPunchModal, setMissedPunchOpenModal] = useState(false);
    const button_custom = {
        color: {
            gray: ":ring-cyan-700 border border-gray-200 bg-white text-gray-900 f enabled:hover:bg-gray-100 dark:border-gray-600 dark:bg-transparent dark:text-gray-400 dark:enabled:hover:bg-gray-700 dark:enabled:hover:text-white",
            orange: "border border-transparent bg-orange-500 text-white focus:ring-4 focus:ring-orange-300 enabled:hover:bg-orange-600 dark:bg-orange-600 dark:hover:bg-orange-700 dark:focus:ring-orange-800",
            red: "border border-red-600 bg-white text-red-600 focus:ring-4 focus:ring-red-300 enabled:hover:bg-white dark:bg-red-600 dark:hover:bg-red-700 dark:focus:ring-red-800",
        },
        pill: {
            off: "rounded",
            on: "rounded-full",
        },
        size: {
            xs: "p-0.5 text-lg",
            sm: "px-3 py-1.5 text-sm",
            md: "px-4 py-2 text-sm",
            lg: "px-5 py-2.5 text-base",
            xl: "px-6 py-3 text-base",
        },
    };
    return (
        <>
            <div className="flex items-center justify-between flex-wrap px-2 lg:pb-0 pb-2.5">
                <div className="flex text-lg lg:gap-5 gap-3 p-2.5 flex-wrap">
                    <div className="relative text-left">
                        {/* Dropdown Button */}
                        <button
                            onClick={toggleDropdown} // Toggle dropdown visibility
                            className="inline-flex justify-center w-full rounded-md font-medium text-lg items-center gap-3"
                        >
                            {selectedMonth}, {selectedYear} <FaChevronDown />
                        </button>

                        {/* Dropdown Menu */}
                        {isDropdownOpen && ( // Conditional rendering based on the state
                            <div className="absolute mt-2 w-96 rounded-md shadow-lg bg-white ring-1 ring-black ring-opacity-5 z-50">
                                <div className="p-2">
                                    {/* Year Navigation */}
                                    <div className="flex justify-between items-center mb-4">
                                        <FaChevronLeft
                                            onClick={goToPreviousYear}
                                            className="text-gray-500 cursor-pointer hover:text-gray-700"
                                        />
                                        <span className="text-lg font-bold">
                                            {selectedYear}
                                        </span>
                                        <FaChevronRight
                                            onClick={goToNextYear}
                                            className="text-gray-500 cursor-pointer hover:text-gray-700"
                                        />
                                    </div>

                                    {/* Month Grid */}
                                    <div className="grid grid-cols-4 gap-4 text-center text-sm">
                                        {months.map((month) => (
                                            <div
                                                key={month}
                                                onClick={() =>
                                                    handleMonthSelect(month)
                                                }
                                                className={`cursor-pointer py-2 px-1 rounded-lg ${selectedMonth === month
                                                    ? "text-blue-600 font-bold"
                                                    : "text-gray-700"
                                                    } hover:bg-blue-100`}
                                            >
                                                {month}
                                            </div>
                                        ))}
                                    </div>
                                </div>
                            </div>
                        )}
                    </div>

                    <h3 className="text-green-500 font-bold">
                        1896{" "}
                        <span className="text-gray-700 text-sm font-normal">
                            Total Working Minutes
                        </span>
                    </h3>
                    <h3 className="text-red-600 font-bold">
                        3 Days{" "}
                        <span className="text-gray-700 text-sm font-normal">
                            Absent
                        </span>
                    </h3>
                    <h3 className="text-orange-500 font-bold">
                        2 Days{" "}
                        <span className="text-gray-700 text-sm font-normal">
                            Missed Punch
                        </span>
                    </h3>
                </div>
                <div className="flex items-center gap-3 ps-2">
                    <div className="flex items-center gap-1 text-[#3f9f31]">
                        <FaCircle className="text-xs " />
                        Event
                    </div>
                    <div className="flex items-center gap-1 text-blue-600">
                        <FaCircle className="text-xs " />
                        Holiday
                    </div>
                </div>
            </div>
            <div className="relative overflow-auto">
                <table className="w-full text-sm text-left rtl:text-right text-gray-500 dark:text-gray-400">
                    <thead className="text-sm text-gray-700 dark:bg-gray-700 dark:text-gray-400 border border-gray-200">
                        <tr>
                            <th
                                // scope="col"
                                className="p-2 py-2 border border-gray-200"
                                style={{ minWidth: "150px" }}
                            >
                                Sun
                            </th>
                            <th
                                // scope="col"
                                style={{ minWidth: "150px" }}
                                className="p-1 py-2 border border-gray-200"
                            >
                                Mon
                            </th>
                            <th
                                // scope="col"
                                style={{ minWidth: "150px" }}
                                className="p-1 py-2 border border-gray-200"
                            >
                                Tue
                            </th>
                            <th
                                // scope="col"
                                style={{ minWidth: "150px" }}
                                className="p-1 py-2 border border-gray-200"
                            >
                                Wed
                            </th>
                            <th
                                // scope="col"
                                style={{ minWidth: "150px" }}
                                className="p-1 py-2 border border-gray-200"
                            >
                                Thu
                            </th>
                            <th
                                // scope="col"
                                style={{ minWidth: "150px" }}
                                className="p-1 py-2 border border-gray-200"
                            >
                                Fri
                            </th>
                            <th
                                // scope="col"
                                style={{ minWidth: "150px" }}
                                className="p-1 py-2 border border-gray-200"
                            >
                                Sat
                            </th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr className="bg-white border-b dark:bg-gray-800 dark:border-gray-700">
                            <td
                                className="p-2 border border-blue-500 border-t-0 bg-blue-50"
                                style={{ width: "150px" }}
                            >
                                <div className="h-36 flex flex-col justify-between">
                                    <div className="flex items-start justify-between">
                                        <span className="text-gray-700 font-medium text-lg">
                                            1
                                        </span>
                                        <Button
                                            color="red"
                                            theme={button_custom}
                                            size="xs"
                                            onClick={() =>
                                                setMissedPunchOpenModal(true)
                                            }
                                        >
                                            <IoMdAdd />
                                        </Button>
                                    </div>
                                </div>
                            </td>
                            <td
                                className="p-2 border border-gray-300 border-t-0 "
                                style={{ width: "150px" }}
                            >
                                <div className="h-36 flex flex-col justify-between">
                                    <div className="flex items-start justify-between ">
                                        <span className="text-gray-700 font-medium text-lg">
                                            2
                                        </span>
                                        <Button
                                            color="red"
                                            theme={button_custom}
                                            size="xs"
                                            onClick={() =>
                                                setMissedPunchOpenModal(true)
                                            }
                                        >
                                            <IoMdAdd />
                                        </Button>
                                    </div>
                                    <div>
                                        <div className="flex items-center gap-1 pt-1">
                                            <div className="bg-white text-[#3f9f31] border border-[#3f9f31] w-fit p-0.5 rounded font-medium">
                                                09:30 AM
                                            </div>
                                            <button className="p-1 bg-orange-500 text-white rounded">
                                                <RiArchiveLine />
                                            </button>
                                        </div>
                                        <div className="flex items-center gap-1 pt-1">
                                            <div className="bg-blue-50 border border-blue-500 text-blue-500 w-fit p-0.5 rounded font-medium">
                                                06:30 PM
                                            </div>
                                            <button className="p-1 bg-orange-500 text-white rounded">
                                                <RiArchiveLine />
                                            </button>
                                        </div>
                                        <div className="flex items-center gap-1 pt-1">
                                            <div className="bg-white text-[#3f9f31] border border-[#3f9f31] w-fit p-0.5 rounded font-medium">
                                                09:30 AM
                                            </div>
                                            <button className="p-1 bg-orange-500 text-white rounded">
                                                <RiArchiveLine />
                                            </button>
                                        </div>
                                        <div className="flex items-center gap-1 pt-1">
                                            <div className="bg-blue-50 border border-blue-500 text-blue-500 w-fit p-0.5 rounded font-medium">
                                                06:30 PM
                                            </div>
                                            <button className="p-1 bg-orange-500 text-white rounded">
                                                <RiArchiveLine />
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </td>
                            <td
                                className="p-2 border border-gray-300 border-t-0 "
                                style={{ width: "150px" }}
                            >
                                <div className="h-36 flex flex-col justify-between">
                                    <div className="flex items-start justify-between ">
                                        <span className="text-gray-700 font-medium  text-lg">
                                            3
                                        </span>
                                        <Button
                                            color="red"
                                            theme={button_custom}
                                            size="xs"
                                            onClick={() =>
                                                setMissedPunchOpenModal(true)
                                            }
                                        >
                                            <IoMdAdd />
                                        </Button>
                                    </div>
                                    <div>
                                        <div className="flex items-center gap-1 pt-1">
                                            <div className="bg-white text-[#3f9f31] border border-[#3f9f31] w-fit p-0.5 rounded font-medium">
                                                09:30 AM
                                            </div>
                                            <button className="p-1 bg-orange-500 text-white rounded">
                                                <RiArchiveLine />
                                            </button>
                                        </div>
                                        <div className="flex items-center gap-1 pt-1">
                                            <div className="bg-blue-50 border border-blue-500 text-blue-500 w-fit p-0.5 rounded font-medium">
                                                06:30 PM
                                            </div>
                                            <button className="p-1 bg-orange-500 text-white rounded">
                                                <RiArchiveLine />
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </td>
                            <td
                                className="p-2 border border-gray-300 border-t-0 bg-[#faf5e3]"
                                style={{ width: "150px" }}
                            >
                                <div className="h-36 flex flex-col justify-between">
                                    <div className="flex items-start justify-between ">
                                        <span className="text-gray-700 font-medium  text-lg">
                                            4
                                        </span>
                                        <Button
                                            color="red"
                                            theme={button_custom}
                                            size="xs"
                                            onClick={() =>
                                                setMissedPunchOpenModal(true)
                                            }
                                        >
                                            <IoMdAdd />
                                        </Button>
                                    </div>
                                    <div>
                                        <div className="flex items-center gap-1 pt-1">
                                            <div className="bg-white text-[#3f9f31] border border-[#3f9f31] w-fit p-0.5 rounded font-medium">
                                                09:30 AM
                                            </div>
                                            <button className="p-1 bg-orange-500 text-white rounded">
                                                <RiArchiveLine />
                                            </button>
                                        </div>
                                        <div className="flex items-center gap-1 pt-1">
                                            <div className="bg-blue-50 border border-blue-500 text-blue-500 w-fit p-0.5 rounded font-medium">
                                                06:30 PM
                                            </div>
                                            <button className="p-1 bg-blue-500 text-white rounded">
                                                <IoMdThumbsUp />
                                            </button>
                                            <button className="p-1 bg-orange-500 text-white rounded">
                                                <RiArchiveLine />
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </td>
                            <td
                                className="p-2 border border-gray-300 border-t-0 bg-[#faf5e3]"
                                style={{ width: "150px" }}
                            >
                                <div className="h-36 flex flex-col justify-between">
                                    <div className="flex items-start justify-between ">
                                        <span className="text-gray-700 font-medium  text-lg">
                                            5
                                        </span>
                                        <Button
                                            color="red"
                                            theme={button_custom}
                                            size="xs"
                                            onClick={() =>
                                                setMissedPunchOpenModal(true)
                                            }
                                        >
                                            <IoMdAdd />
                                        </Button>
                                    </div>
                                    <div>
                                        <div className="flex items-center gap-1 pt-1">
                                            <div className="bg-white text-[#3f9f31] border border-[#3f9f31] w-fit p-0.5 rounded font-medium">
                                                09:30 AM
                                            </div>
                                            <button className="p-1 bg-blue-500 text-white rounded">
                                                <IoMdThumbsUp />
                                            </button>
                                            <button className="p-1 bg-orange-500 text-white rounded">
                                                <RiArchiveLine />
                                            </button>
                                        </div>
                                        <div className="flex items-center gap-1 pt-1">
                                            <div className="bg-blue-50 border border-blue-500 text-blue-500 w-fit p-0.5 rounded font-medium">
                                                06:30 PM
                                            </div>
                                            <button className="p-1 bg-orange-500 text-white rounded">
                                                <RiArchiveLine />
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </td>
                            <td
                                className="p-2 border border-gray-300 border-t-0 bg-[#faf5e3]"
                                style={{ width: "150px" }}
                            >
                                <div className="h-36 flex flex-col justify-between">
                                    <div className="flex items-start justify-between ">
                                        <span className="text-gray-700 font-medium  text-lg">
                                            6
                                        </span>
                                        <Button
                                            color="red"
                                            theme={button_custom}
                                            size="xs"
                                            onClick={() =>
                                                setMissedPunchOpenModal(true)
                                            }
                                        >
                                            <IoMdAdd />
                                        </Button>
                                    </div>
                                    <div>
                                        <div className="flex items-center gap-1 pt-1">
                                            <div className="bg-white text-[#3f9f31] border border-[#3f9f31] w-fit p-0.5 rounded font-medium">
                                                09:30 AM
                                            </div>
                                            <button className="p-1 bg-blue-500 text-white rounded">
                                                <IoMdThumbsUp />
                                            </button>
                                            <button className="p-1 bg-orange-500 text-white rounded">
                                                <RiArchiveLine />
                                            </button>
                                        </div>
                                        <div className="flex items-center gap-1 pt-1">
                                            <div className="bg-blue-50 border border-blue-500 text-blue-500 w-fit p-0.5 rounded font-medium">
                                                06:30 PM
                                            </div>
                                            <button className="p-1 bg-blue-500 text-white rounded">
                                                <IoMdThumbsUp />
                                            </button>
                                            <button className="p-1 bg-orange-500 text-white rounded">
                                                <RiArchiveLine />
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </td>
                            <td
                                className="p-2 border border-gray-300 border-t-0 "
                                style={{ width: "150px" }}
                            >
                                <div className="h-36 flex flex-col justify-between">
                                    <div className="flex items-start justify-between ">
                                        <span className="text-gray-700 font-medium  text-lg">
                                            7
                                        </span>
                                        <Button
                                            color="red"
                                            theme={button_custom}
                                            size="xs"
                                        // onClick={() =>
                                        //     setIsAddOpen(true)
                                        // }
                                        >
                                            <IoMdAdd />
                                        </Button>
                                    </div>
                                    <div>
                                        <div className="flex items-center gap-1 pt-1">
                                            <div className="bg-white text-[#3f9f31] border border-[#3f9f31] w-fit p-0.5 rounded font-medium">
                                                09:30 AM
                                            </div>
                                            <button className="p-1 bg-orange-500 text-white rounded">
                                                <RiArchiveLine />
                                            </button>
                                        </div>
                                        <div className="flex items-center gap-1 pt-1">
                                            <div className="bg-blue-50 border border-blue-500 text-blue-500 w-fit p-0.5 rounded font-medium">
                                                06:30 PM
                                            </div>
                                            <button className="p-1 bg-orange-500 text-white rounded">
                                                <RiArchiveLine />
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </td>
                        </tr>
                        <tr className="bg-white border-b dark:bg-gray-800 dark:border-gray-700">
                            <td
                                className="p-2 border border-blue-500 border-t-0 bg-blue-50"
                                style={{ width: "150px" }}
                            >
                                <div className="h-36 flex flex-col justify-between">
                                    <div className="flex items-start justify-between">
                                        <span className="text-gray-700 font-medium text-lg">
                                            8
                                        </span>
                                        <Button
                                            color="red"
                                            theme={button_custom}
                                            size="xs"
                                            onClick={() => setIsAddOpen(true)}
                                        >
                                            <IoMdAdd />
                                        </Button>
                                    </div>
                                </div>
                            </td>
                            <td
                                className="p-2 border border-gray-300 border-t-0 "
                                style={{ width: "150px" }}
                            >
                                <div className="h-36 flex flex-col justify-between">
                                    <div className="flex items-start justify-between ">
                                        <span className="text-gray-700 font-medium  text-lg">
                                            9
                                        </span>
                                        <Button
                                            color="red"
                                            theme={button_custom}
                                            size="xs"
                                            onClick={() =>
                                                setMissedPunchOpenModal(true)
                                            }
                                        >
                                            <IoMdAdd />
                                        </Button>
                                    </div>
                                    <div>
                                        <div className="flex items-center gap-1 pt-1">
                                            <div className="bg-white text-[#3f9f31] border border-[#3f9f31] w-fit p-0.5 rounded font-medium">
                                                09:30 AM
                                            </div>
                                            <button className="p-1 bg-orange-500 text-white rounded">
                                                <RiArchiveLine />
                                            </button>
                                        </div>
                                        <div className="flex items-center gap-1 pt-1">
                                            <div className="bg-blue-50 border border-blue-500 text-blue-500 w-fit p-0.5 rounded font-medium">
                                                06:30 PM
                                            </div>
                                            <button className="p-1 bg-orange-500 text-white rounded">
                                                <RiArchiveLine />
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </td>
                            <td
                                className="p-2 border border-gray-300 border-t-0 "
                                style={{ width: "150px" }}
                            >
                                <div className="h-36 flex flex-col justify-between">
                                    <div className="flex items-start justify-between ">
                                        <span className="text-gray-700 font-medium  text-lg">
                                            10
                                        </span>
                                        <Button
                                            color="red"
                                            theme={button_custom}
                                            size="xs"
                                            onClick={() =>
                                                setMissedPunchOpenModal(true)
                                            }
                                        >
                                            <IoMdAdd />
                                        </Button>
                                    </div>
                                    <div>
                                        <div className="flex items-center gap-1 pt-1">
                                            <div className="bg-white text-[#3f9f31] border border-[#3f9f31] w-fit p-0.5 rounded font-medium">
                                                09:30 AM
                                            </div>
                                            <button className="p-1 bg-orange-500 text-white rounded">
                                                <RiArchiveLine />
                                            </button>
                                        </div>
                                        <div className="flex items-center gap-1 pt-1">
                                            <div className="bg-blue-50 border border-blue-500 text-blue-500 w-fit p-0.5 rounded font-medium">
                                                06:30 PM
                                            </div>
                                            <button className="p-1 bg-orange-500 text-white rounded">
                                                <RiArchiveLine />
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </td>
                            <td
                                className="p-2 border border-gray-300 border-t-0"
                                style={{ width: "150px" }}
                            >
                                <div className="h-36 flex flex-col justify-between">
                                    <div className="flex items-start justify-between ">
                                        <span className="text-gray-700 font-medium  text-lg">
                                            11
                                        </span>
                                        <Button
                                            color="red"
                                            theme={button_custom}
                                            size="xs"
                                            onClick={() =>
                                                setMissedPunchOpenModal(true)
                                            }
                                        >
                                            <IoMdAdd />
                                        </Button>
                                    </div>
                                    <div>
                                        <div className="flex items-center gap-1 pt-1">
                                            <div className="bg-white text-[#3f9f31] border border-[#3f9f31] w-fit p-0.5 rounded font-medium">
                                                09:30 AM
                                            </div>
                                            <button className="p-1 bg-orange-500 text-white rounded">
                                                <RiArchiveLine />
                                            </button>
                                        </div>
                                        <div className="flex items-center gap-1 pt-1">
                                            <div className="bg-blue-50 border border-blue-500 text-blue-500 w-fit p-0.5 rounded font-medium">
                                                06:30 PM
                                            </div>

                                            <button className="p-1 bg-orange-500 text-white rounded">
                                                <RiArchiveLine />
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </td>
                            <td
                                className="p-2 border border-gray-300 border-t-0"
                                style={{ width: "150px" }}
                            >
                                <div className="h-36 flex flex-col justify-between">
                                    <div className="flex items-start justify-between ">
                                        <span className="text-gray-700 font-medium  text-lg">
                                            12
                                        </span>
                                        <Button
                                            color="red"
                                            theme={button_custom}
                                            size="xs"
                                            onClick={() =>
                                                setMissedPunchOpenModal(true)
                                            }
                                        >
                                            <IoMdAdd />
                                        </Button>
                                    </div>
                                    <div>
                                        <div className="flex items-center gap-1 pt-1">
                                            <div className="bg-white text-[#3f9f31] border border-[#3f9f31] w-fit p-0.5 rounded font-medium">
                                                09:30 AM
                                            </div>

                                            <button className="p-1 bg-orange-500 text-white rounded">
                                                <RiArchiveLine />
                                            </button>
                                        </div>
                                        <div className="flex items-center gap-1 pt-1">
                                            <div className="bg-blue-50 border border-blue-500 text-blue-500 w-fit p-0.5 rounded font-medium">
                                                06:30 PM
                                            </div>
                                            <button className="p-1 bg-orange-500 text-white rounded">
                                                <RiArchiveLine />
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </td>
                            <td
                                className="p-2 border border-gray-300 border-t-0"
                                style={{ width: "150px" }}
                            >
                                <div className="h-36 flex flex-col justify-between">
                                    <div className="flex items-start justify-between ">
                                        <span className="text-gray-700 font-medium  text-lg">
                                            13
                                        </span>
                                        <Button
                                            color="red"
                                            theme={button_custom}
                                            size="xs"
                                            onClick={() =>
                                                setMissedPunchOpenModal(true)
                                            }
                                        >
                                            <IoMdAdd />
                                        </Button>
                                    </div>
                                    <div>
                                        <div className="flex items-center gap-1 pt-1">
                                            <div className="bg-white text-[#3f9f31] border border-[#3f9f31] w-fit p-0.5 rounded font-medium">
                                                09:30 AM
                                            </div>

                                            <button className="p-1 bg-orange-500 text-white rounded">
                                                <RiArchiveLine />
                                            </button>
                                        </div>
                                        <div className="flex items-center gap-1 pt-1">
                                            <div className="bg-blue-50 border border-blue-500 text-blue-500 w-fit p-0.5 rounded font-medium">
                                                06:30 PM
                                            </div>

                                            <button className="p-1 bg-orange-500 text-white rounded">
                                                <RiArchiveLine />
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </td>
                            <td
                                className="p-2 border border-gray-300 border-t-0 bg-blue-50"
                                style={{ width: "150px" }}
                            >
                                <div className="h-36 flex flex-col justify-between">
                                    <div className="flex items-start justify-between ">
                                        <span className="text-gray-700 font-medium  text-lg">
                                            14
                                        </span>
                                        <Button
                                            color="red"
                                            theme={button_custom}
                                            size="xs"
                                            onClick={() =>
                                                setMissedPunchOpenModal(true)
                                            }
                                        >
                                            <IoMdAdd />
                                        </Button>
                                    </div>
                                    <div>
                                        <div className="flex items-center gap-1 pt-1">
                                            <div className="bg-gray-50 text-gray-600 border border-gray-400 w-fit p-0.5 px-1 rounded font-medium">
                                                Absent
                                            </div>
                                            <button className="p-1 bg-orange-500 text-white rounded">
                                                <RiArchiveLine />
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </td>
                        </tr>
                        <tr className="bg-white border-b dark:bg-gray-800 dark:border-gray-700">
                            <td
                                className="p-2 border border-blue-500 border-t-0 bg-[#3f9f31]"
                                style={{ width: "150px" }}
                            >
                                <div className="h-36 flex flex-col justify-between">
                                    <div className="flex items-start justify-between">
                                        <span className="text-white font-medium text-lg">
                                            15
                                        </span>
                                        <Button
                                            color="red"
                                            theme={button_custom}
                                            size="xs"
                                            onClick={() =>
                                                setMissedPunchOpenModal(true)
                                            }
                                        >
                                            <IoMdAdd />
                                        </Button>
                                    </div>
                                    <h4 className="text-white">
                                        Independence Day
                                    </h4>
                                    <div>
                                        <div className="flex items-center gap-1 pt-1">
                                            <div className="bg-white text-[#3f9f31] border border-[#3f9f31] w-fit p-0.5 rounded font-medium">
                                                09:30 AM
                                            </div>
                                            <button className="p-1 bg-orange-500 text-white rounded">
                                                <RiArchiveLine />
                                            </button>
                                        </div>
                                        <div className="flex items-center gap-1 pt-1">
                                            <div className="bg-blue-50 border border-blue-500 text-blue-500 w-fit p-0.5 rounded font-medium">
                                                06:30 PM
                                            </div>
                                            <button className="p-1 bg-orange-500 text-white rounded">
                                                <RiArchiveLine />
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </td>
                            <td
                                className="p-2 border border-gray-300 border-t-0 "
                                style={{ width: "150px" }}
                            >
                                <div className="h-36 flex flex-col justify-between">
                                    <div className="flex items-start justify-between ">
                                        <span className="text-gray-700 font-medium  text-lg">
                                            16
                                        </span>
                                        <Button
                                            color="red"
                                            theme={button_custom}
                                            size="xs"
                                            onClick={() =>
                                                setMissedPunchOpenModal(true)
                                            }
                                        >
                                            <IoMdAdd />
                                        </Button>
                                    </div>
                                    <div>
                                        <div className="flex items-center gap-1 pt-1">
                                            <div className="bg-white text-[#3f9f31] border border-[#3f9f31] w-fit p-0.5 rounded font-medium">
                                                09:30 AM
                                            </div>
                                            <button className="p-1 bg-orange-500 text-white rounded">
                                                <RiArchiveLine />
                                            </button>
                                        </div>
                                        <div className="flex items-center gap-1 pt-1">
                                            <div className="bg-blue-50 border border-blue-500 text-blue-500 w-fit p-0.5 rounded font-medium">
                                                06:30 PM
                                            </div>
                                            <button className="p-1 bg-orange-500 text-white rounded">
                                                <RiArchiveLine />
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </td>
                            <td
                                className="p-2 border border-gray-300 border-t-0 "
                                style={{ width: "150px" }}
                            >
                                <div className="h-36 flex flex-col justify-between">
                                    <div className="flex items-start justify-between ">
                                        <span className="text-gray-700 font-medium  text-lg">
                                            17
                                        </span>
                                        <Button
                                            color="red"
                                            theme={button_custom}
                                            size="xs"
                                            onClick={() =>
                                                setMissedPunchOpenModal(true)
                                            }
                                        >
                                            <IoMdAdd />
                                        </Button>
                                    </div>
                                    <div>
                                        <div className="flex items-center gap-1 pt-1">
                                            <div className="bg-white text-[#3f9f31] border border-[#3f9f31] w-fit p-0.5 rounded font-medium">
                                                09:30 AM
                                            </div>
                                            <button className="p-1 bg-orange-500 text-white rounded">
                                                <RiArchiveLine />
                                            </button>
                                        </div>
                                        <div className="flex items-center gap-1 pt-1">
                                            <div className="bg-blue-50 border border-blue-500 text-blue-500 w-fit p-0.5 rounded font-medium">
                                                06:30 PM
                                            </div>
                                            <button className="p-1 bg-orange-500 text-white rounded">
                                                <RiArchiveLine />
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </td>
                            <td
                                className="p-2 border border-gray-300 border-t-0"
                                style={{ width: "150px" }}
                            >
                                <div className="h-36 flex flex-col justify-between">
                                    <div className="flex items-start justify-between ">
                                        <span className="text-gray-700 font-medium  text-lg">
                                            18
                                        </span>
                                        <Button
                                            color="red"
                                            theme={button_custom}
                                            size="xs"
                                            onClick={() =>
                                                setMissedPunchOpenModal(true)
                                            }
                                        >
                                            <IoMdAdd />
                                        </Button>
                                    </div>
                                    <div>
                                        <div className="flex items-center gap-1 pt-1">
                                            <div className="bg-white text-[#3f9f31] border border-[#3f9f31] w-fit p-0.5 rounded font-medium">
                                                09:30 AM
                                            </div>
                                            <button className="p-1 bg-orange-500 text-white rounded">
                                                <RiArchiveLine />
                                            </button>
                                        </div>
                                        <div className="flex items-center gap-1 pt-1">
                                            <div className="bg-blue-50 border border-blue-500 text-blue-500 w-fit p-0.5 rounded font-medium">
                                                06:30 PM
                                            </div>

                                            <button className="p-1 bg-orange-500 text-white rounded">
                                                <RiArchiveLine />
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </td>
                            <td
                                className="p-2 border border-gray-300 border-t-0"
                                style={{ width: "150px" }}
                            >
                                <div className="h-36 flex flex-col justify-between">
                                    <div className="flex items-start justify-between ">
                                        <span className="text-gray-700 font-medium  text-lg">
                                            19
                                        </span>
                                        <Button
                                            color="red"
                                            theme={button_custom}
                                            size="xs"
                                            onClick={() =>
                                                setMissedPunchOpenModal(true)
                                            }
                                        >
                                            <IoMdAdd />
                                        </Button>
                                    </div>
                                    <div>
                                        <div className="flex items-center gap-1 pt-1">
                                            <div className="bg-white text-[#3f9f31] border border-[#3f9f31] w-fit p-0.5 rounded font-medium">
                                                09:30 AM
                                            </div>

                                            <button className="p-1 bg-orange-500 text-white rounded">
                                                <RiArchiveLine />
                                            </button>
                                        </div>
                                        <div className="flex items-center gap-1 pt-1">
                                            <div className="bg-blue-50 border border-blue-500 text-blue-500 w-fit p-0.5 rounded font-medium">
                                                06:30 PM
                                            </div>
                                            <button className="p-1 bg-orange-500 text-white rounded">
                                                <RiArchiveLine />
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </td>
                            <td
                                className="p-2 border border-gray-300 border-t-0"
                                style={{ width: "150px" }}
                            >
                                <div className="h-36 flex flex-col justify-between">
                                    <div className="flex items-start justify-between ">
                                        <span className="text-gray-700 font-medium  text-lg">
                                            20
                                        </span>
                                        <Button
                                            color="red"
                                            theme={button_custom}
                                            size="xs"
                                            onClick={() =>
                                                setMissedPunchOpenModal(true)
                                            }
                                        >
                                            <IoMdAdd />
                                        </Button>
                                    </div>
                                    <div>
                                        <div className="flex items-center gap-1 pt-1">
                                            <div className="bg-white text-[#3f9f31] border border-[#3f9f31] w-fit p-0.5 rounded font-medium">
                                                09:30 AM
                                            </div>

                                            <button className="p-1 bg-orange-500 text-white rounded">
                                                <RiArchiveLine />
                                            </button>
                                        </div>
                                        <div className="flex items-center gap-1 pt-1">
                                            <div className="bg-blue-50 border border-blue-500 text-blue-500 w-fit p-0.5 rounded font-medium">
                                                06:30 PM
                                            </div>

                                            <button className="p-1 bg-orange-500 text-white rounded">
                                                <RiArchiveLine />
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </td>
                            <td
                                className="p-2 border border-gray-300 border-t-0"
                                style={{ width: "150px" }}
                            >
                                <div className="h-36 flex flex-col justify-between">
                                    <div className="flex items-start justify-between ">
                                        <span className="text-gray-700 font-medium  text-lg">
                                            21
                                        </span>
                                        <Button
                                            color="red"
                                            theme={button_custom}
                                            size="xs"
                                            onClick={() =>
                                                setMissedPunchOpenModal(true)
                                            }
                                        >
                                            <IoMdAdd />
                                        </Button>
                                    </div>
                                    <div>
                                        <div className="flex items-center gap-1 pt-1">
                                            <div className="bg-white text-[#3f9f31] border border-[#3f9f31] w-fit p-0.5 rounded font-medium">
                                                09:30 AM
                                            </div>

                                            <button className="p-1 bg-orange-500 text-white rounded">
                                                <RiArchiveLine />
                                            </button>
                                        </div>
                                        <div className="flex items-center gap-1 pt-1">
                                            <div className="bg-blue-50 border border-blue-500 text-blue-500 w-fit p-0.5 rounded font-medium">
                                                06:30 PM
                                            </div>

                                            <button className="p-1 bg-orange-500 text-white rounded">
                                                <RiArchiveLine />
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </td>
                        </tr>
                        <tr className="bg-white border-b dark:bg-gray-800 dark:border-gray-700">
                            <td className="p-2 border border-blue-500 border-t-0 bg-blue-50">
                                <div className="h-36 flex flex-col justify-between">
                                    <div className="flex items-start justify-between">
                                        <span className="text-gray-700 font-medium">
                                            22
                                        </span>
                                        <Button
                                            color="red"
                                            theme={button_custom}
                                            size="xs"
                                            onClick={() =>
                                                setMissedPunchOpenModal(true)
                                            }
                                        >
                                            <IoMdAdd />
                                        </Button>
                                    </div>
                                </div>
                            </td>
                            <td
                                className="p-2 border border-gray-300 border-t-0 "
                                style={{ width: "150px" }}
                            >
                                <div className="h-36 flex flex-col justify-between">
                                    <div className="flex items-start justify-between ">
                                        <span className="text-gray-700 font-medium  text-lg">
                                            16
                                        </span>
                                        <Button
                                            color="red"
                                            theme={button_custom}
                                            size="xs"
                                            onClick={() =>
                                                setMissedPunchOpenModal(true)
                                            }
                                        >
                                            <IoMdAdd />
                                        </Button>
                                    </div>
                                    <div>
                                        <div className="flex items-center gap-1 pt-1">
                                            <div className="bg-white text-[#3f9f31] border border-[#3f9f31] w-fit p-0.5 rounded font-medium">
                                                09:30 AM
                                            </div>
                                            <button className="p-1 bg-orange-500 text-white rounded">
                                                <RiArchiveLine />
                                            </button>
                                        </div>
                                        <div className="flex items-center gap-1 pt-1">
                                            <div className="bg-blue-50 border border-blue-500 text-blue-500 w-fit p-0.5 rounded font-medium">
                                                06:30 PM
                                            </div>
                                            <button className="p-1 bg-orange-500 text-white rounded">
                                                <RiArchiveLine />
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </td>
                            <td
                                className="p-2 border border-gray-300 border-t-0 "
                                style={{ width: "150px" }}
                            >
                                <div className="h-36 flex flex-col justify-between">
                                    <div className="flex items-start justify-between ">
                                        <span className="text-gray-700 font-medium  text-lg">
                                            17
                                        </span>
                                        <Button
                                            color="red"
                                            theme={button_custom}
                                            size="xs"
                                            onClick={() =>
                                                setMissedPunchOpenModal(true)
                                            }
                                        >
                                            <IoMdAdd />
                                        </Button>
                                    </div>
                                    <div>
                                        <div className="flex items-center gap-1 pt-1">
                                            <div className="bg-white text-[#3f9f31] border border-[#3f9f31] w-fit p-0.5 rounded font-medium">
                                                09:30 AM
                                            </div>
                                            <button className="p-1 bg-orange-500 text-white rounded">
                                                <RiArchiveLine />
                                            </button>
                                        </div>
                                        <div className="flex items-center gap-1 pt-1">
                                            <div className="bg-blue-50 border border-blue-500 text-blue-500 w-fit p-0.5 rounded font-medium">
                                                06:30 PM
                                            </div>
                                            <button className="p-1 bg-orange-500 text-white rounded">
                                                <RiArchiveLine />
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </td>
                            <td
                                className="p-2 border border-gray-300 border-t-0"
                                style={{
                                    backgroundImage: `url("assets/img/agent-birthday.png")`,
                                    backgroundSize: "cover",
                                    backgroundPosition: "center",
                                }}
                            >
                                <div className="h-36 flex flex-col justify-between">
                                    <div className="flex items-start justify-between ">
                                        <span className="text-gray-700 font-medium text-lg">
                                            25
                                        </span>
                                        <Button
                                            color="red"
                                            theme={button_custom}
                                            size="xs"
                                            onClick={() =>
                                                setMissedPunchOpenModal(true)
                                            }
                                        >
                                            <IoMdAdd />
                                        </Button>
                                    </div>
                                    <div>
                                        <div className="flex items-center gap-1 pt-1">
                                            <div className="bg-white text-[#3f9f31] border border-[#3f9f31] w-fit p-0.5 rounded font-medium">
                                                09:30 AM
                                            </div>
                                            <button className="p-1 bg-orange-500 text-white rounded">
                                                <RiArchiveLine />
                                            </button>
                                        </div>
                                        <div className="flex items-center gap-1 pt-1">
                                            <div className="bg-blue-50 border border-blue-500 text-blue-500 w-fit p-0.5 rounded font-medium">
                                                06:30 PM
                                            </div>

                                            <button className="p-1 bg-orange-500 text-white rounded">
                                                <RiArchiveLine />
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </td>
                            <td
                                className="p-2 border border-gray-300 border-t-0"
                                style={{ width: "150px" }}
                            >
                                <div className="h-36 flex flex-col justify-between">
                                    <div className="flex items-start justify-between ">
                                        <span className="text-gray-700 font-medium  text-lg">
                                            26
                                        </span>
                                        <Button
                                            color="red"
                                            theme={button_custom}
                                            size="xs"
                                            onClick={() =>
                                                setMissedPunchOpenModal(true)
                                            }
                                        >
                                            <IoMdAdd />
                                        </Button>
                                    </div>
                                    <div>
                                        <div className="flex items-center gap-1 pt-1">
                                            <div className="bg-white text-[#3f9f31] border border-[#3f9f31] w-fit p-0.5 rounded font-medium">
                                                09:30 AM
                                            </div>

                                            <button className="p-1 bg-orange-500 text-white rounded">
                                                <RiArchiveLine />
                                            </button>
                                        </div>
                                        <div className="flex items-center gap-1 pt-1">
                                            <div className="bg-blue-50 border border-blue-500 text-blue-500 w-fit p-0.5 rounded font-medium">
                                                06:30 PM
                                            </div>
                                            <button className="p-1 bg-orange-500 text-white rounded">
                                                <RiArchiveLine />
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </td>
                            <td
                                className="p-2 border border-gray-300 border-t-0"
                                style={{ width: "150px" }}
                            >
                                <div className="h-36 flex flex-col justify-between">
                                    <div className="flex items-start justify-between ">
                                        <span className="text-gray-700 font-medium  text-lg">
                                            27
                                        </span>
                                        <Button
                                            color="red"
                                            theme={button_custom}
                                            size="xs"
                                            onClick={() =>
                                                setMissedPunchOpenModal(true)
                                            }
                                        >
                                            <IoMdAdd />
                                        </Button>
                                    </div>
                                    <div>
                                        <div className="flex items-center gap-1 pt-1">
                                            <div className="bg-white text-[#3f9f31] border border-[#3f9f31] w-fit p-0.5 rounded font-medium">
                                                09:30 AM
                                            </div>

                                            <button className="p-1 bg-orange-500 text-white rounded">
                                                <RiArchiveLine />
                                            </button>
                                        </div>
                                        <div className="flex items-center gap-1 pt-1">
                                            <div className="bg-blue-50 border border-blue-500 text-blue-500 w-fit p-0.5 rounded font-medium">
                                                06:30 PM
                                            </div>

                                            <button className="p-1 bg-orange-500 text-white rounded">
                                                <RiArchiveLine />
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </td>
                            <td
                                className="p-2 border border-gray-300 border-t-0"
                                style={{ width: "150px" }}
                            >
                                <div className="h-36 flex flex-col justify-between">
                                    <div className="flex items-start justify-between ">
                                        <span className="text-gray-700 font-medium  text-lg">
                                            28
                                        </span>
                                        <Button
                                            color="red"
                                            theme={button_custom}
                                            size="xs"
                                            onClick={() =>
                                                setMissedPunchOpenModal(true)
                                            }
                                        >
                                            <IoMdAdd />
                                        </Button>
                                    </div>
                                    <div>
                                        <div className="flex items-center gap-1 pt-1">
                                            <div className="bg-white text-[#3f9f31] border border-[#3f9f31] w-fit p-0.5 rounded font-medium">
                                                09:30 AM
                                            </div>

                                            <button className="p-1 bg-orange-500 text-white rounded">
                                                <RiArchiveLine />
                                            </button>
                                        </div>
                                        <div className="flex items-center gap-1 pt-1">
                                            <div className="bg-blue-50 border border-blue-500 text-blue-500 w-fit p-0.5 rounded font-medium">
                                                06:30 PM
                                            </div>

                                            <button className="p-1 bg-orange-500 text-white rounded">
                                                <RiArchiveLine />
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </td>
                        </tr>
                        <tr className="bg-white border-b dark:bg-gray-800 dark:border-gray-700">
                            <td
                                className="p-2 border border-blue-500 border-t-0 bg-blue-50"
                                style={{ width: "150px" }}
                            >
                                <div className="h-36 flex flex-col justify-between">
                                    <div className="flex items-start justify-between">
                                        <span className="text-gray-700 font-medium text-lg">
                                            29
                                        </span>
                                        <Button
                                            color="red"
                                            theme={button_custom}
                                            size="xs"
                                            onClick={() =>
                                                setMissedPunchOpenModal(true)
                                            }
                                        >
                                            <IoMdAdd />
                                        </Button>
                                    </div>
                                </div>
                            </td>
                            <td
                                className="p-2 border border-gray-300 border-t-0 bg-blue-600"
                                style={{ width: "150px" }}
                            >
                                <div className="h-36 flex flex-col justify-between">
                                    <div className="flex items-start justify-between ">
                                        <span className="text-white font-medium text-lg">
                                            30
                                        </span>
                                        <Button
                                            color="red"
                                            theme={button_custom}
                                            size="xs"
                                            onClick={() =>
                                                setMissedPunchOpenModal(true)
                                            }
                                        >
                                            <IoMdAdd />
                                        </Button>
                                    </div>
                                    <h4 className="text-white">Diwali</h4>

                                    <div>
                                        <div className="flex items-center gap-1 pt-1">
                                            <div className="bg-white text-[#3f9f31] border border-[#3f9f31] w-fit p-0.5 rounded font-medium">
                                                09:30 AM
                                            </div>
                                            <button className="p-1 bg-orange-500 text-white rounded">
                                                <RiArchiveLine />
                                            </button>
                                        </div>
                                        <div className="flex items-center gap-1 pt-1">
                                            <div className="bg-blue-50 border border-blue-500 text-blue-500 w-fit p-0.5 rounded font-medium">
                                                06:30 PM
                                            </div>
                                            <button className="p-1 bg-orange-500 text-white rounded">
                                                <RiArchiveLine />
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </td>
                            <td
                                className="p-2 border border-gray-300 border-t-0 "
                                style={{ width: "150px" }}
                            >
                                <div className="h-36 flex flex-col justify-between">
                                    <div className="flex items-start justify-between ">
                                        <span className="text-gray-700 font-medium  text-lg">
                                            31
                                        </span>
                                        <Button
                                            color="red"
                                            theme={button_custom}
                                            size="xs"
                                            onClick={() =>
                                                setMissedPunchOpenModal(true)
                                            }
                                        >
                                            <IoMdAdd />
                                        </Button>
                                    </div>
                                    <div>
                                        <div className="flex items-center gap-1 pt-1">
                                            <div className="bg-white text-[#3f9f31] border border-[#3f9f31] w-fit p-0.5 rounded font-medium">
                                                09:30 AM
                                            </div>
                                            <button className="p-1 bg-orange-500 text-white rounded">
                                                <RiArchiveLine />
                                            </button>
                                        </div>
                                        <div className="flex items-center gap-1 pt-1">
                                            <div className="bg-blue-50 border border-blue-500 text-blue-500 w-fit p-0.5 rounded font-medium">
                                                06:30 PM
                                            </div>
                                            <button className="p-1 bg-orange-500 text-white rounded">
                                                <RiArchiveLine />
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>

            {/*--------------- Missed Punch Modal--------------- */}
            <Modal
                show={openMissedPunchModal}
                onClose={() => setMissedPunchOpenModal(false)}
            >
                <ModalHeader className="p-2">
                    <div className="flex items-center gap-2">
                        <TbFingerprintOff className="text-slate-400" />
                        <h4 className="text-lg">Missed Punch Request</h4>
                    </div>
                </ModalHeader>
                <ModalBody className="bg-slate-100 py-3 px-4 rounded-b-lg">
                    <MissedPunch></MissedPunch>
                </ModalBody>
            </Modal>
        </>
    );
}


