import { Head, router, useForm } from "@inertiajs/react";
import {
    <PERSON><PERSON>,
    <PERSON><PERSON>,
    Checkbox,
    Drawer,
    Modal,
    Table,
    Tooltip
} from "flowbite-react";
import { useEffect, useState } from "react";
import { FaPencilAlt } from "react-icons/fa";

import {
    MdDeleteOutline,
    MdOutlineEdit,
    MdOutlineHelpOutline,
    MdOutlinePowerOff
} from "react-icons/md";

import ConfirmBox from "@/Components/HelperComponents/ConfirmBox";
import NoRecord from "@/Components/HelperComponents/NoRecord";
import Paginate from "@/Components/HelperComponents/Paginate";
import PerPageDropdown from "@/Components/HelperComponents/PerPageDropdown";
import SortLink from "@/Components/SortLink";
import {
    button_custom,
    customDrawer,
    page_badge_theme,
    table_custom
} from "@/Pages/Helpers/DesignHelper";
import {
    FaRegCopy,
    FaStar,
    Fa<PERSON>hatsapp
} from "react-icons/fa6";
import { IoMdAdd, IoMdPower } from "react-icons/io";
import {
    RiDeleteBin5Line,
    RiDeleteBinLine
} from "react-icons/ri";
import MainSms2 from "../MainSms2";
import Add from "./Add";
import Edit from "./Edit";

export default function Index({ collection }) {
    const baseRoute = 'sms2';
    // Gateways data from the collection prop
    const gateways = collection.gateways;
    const getData = collection.getData;
    const [isGatewayEditEnable, setIsGatewayEditEnable] = useState(false);
    const [AddChannel, setAddChannel] = useState(false);

    const [SelectedGateway, setSelectedGateway] = useState(false);
    const [selectedGateways, setSelectedGateways] = useState([]);
    const [isUpdateDefaultConfirmOpen, setIsUpdateDefaultConfirmOpen] = useState(false);
    const [updateDefaultID, setUpdateDefaultID] = useState(null);
    const [openModalClear, setOpenModalClear] = useState(false);
    const [openModalActivate, setOpenModalActivate] = useState(false);
    const [isMultipleDeleteConfirmOpen, SetIsMultipleDeleteConfirmOpen] = useState(false);
    const [openModalActivateFailed, setOpenModalActivateFailed] = useState(false);

    const {
        data,
        setData,
        delete: destroy,
        processing,
        errors,
    } = useForm({
        id: [],
    });



    const handleCheckboxChange = (id) => {
        setSelectedGateways((prevSelected) =>
            prevSelected.includes(id)
                ? prevSelected.filter((gwId) => gwId !== id) // Deselect if already selected
                : [...prevSelected, id] // Add if not selected
        );
    };
    // Handle "Select All" Checkbox

    const handleSelectAll = (e) => {
        if (e.target.checked) {
            setSelectedGateways(gateways?.data?.map((gateway) => gateway.id) ?? []);
        } else {
            setSelectedGateways([]);
        }
    };

    const updateDefault = () => {
        Promise.all([
            router.post(route(baseRoute + ".gateways.updateDefault"), { id: updateDefaultID })
        ]).then(() => {

        });

    }

    function handleConfirmUpdateDefault(res) {
        if (updateDefaultID && res) {
            updateDefault();
        }
        setIsUpdateDefaultConfirmOpen(false);
        setUpdateDefaultID(null);
    }

    function deleteRecords() {
        // Consider sending as an array if backend supports it: { data: { ids: selectedGateways } }
        if (selectedGateways.length > 0) {
            destroy(route(baseRoute + ".gateways.destroy", { senderid: selectedGateways.toLocaleString() }));
        }
    }


    function handleMultipleDelete(res) {
        if (res) {
            deleteRecords(); // Ensure deleteRecords uses selectedGateways
        }
        setSelectedGateways([]); // Clear selection
        SetIsMultipleDeleteConfirmOpen(false);
    }
    const currentPageRoute = baseRoute + ".gateways.index";

    const tableHeadItems = [
        { name: 'Name', column: 'name', isLink: true },
        { name: 'Sender Id', column: 'senderId', isLink: true },
        { name: 'User', column: 'user_id', isLink: true },
        { name: 'Actions', column: '' }
    ];

    return (
        <MainSms2>
            <Head title="Gateways" />
            <div className="px-2 ">
                <div className="w-full mt-2 overflow-auto bg-white border rounded h-fit">
                    {/*----------- Advance Settings----------- */}

                    {/* -----------Table----------- */}
                    <div className="flex justify-between p-2">
                        {/* <Button.Group className="flex "> */}
                        <div>
                            {
                                collection.can_delete &&
                                <Button
                                    // className="border rounded"
                                    size="xs"
                                    color="gray"
                                    theme={button_custom}
                                    onClick={() => selectedGateways.length > 0 && SetIsMultipleDeleteConfirmOpen(true)}
                                >
                                    <div className="flex items-center gap-1">
                                        <MdDeleteOutline className="text-slate-500" />
                                        <span className="text-xs">Delete</span>
                                    </div>
                                </Button>
                            }
                        </div>

                        <div className="flex items-center gap-2">
                            {
                                collection.can_add &&
                                <Button
                                    className="border pe-1"
                                    size="xs"
                                    color="gray"
                                    theme={button_custom}
                                    onClick={() => setAddChannel(true)}
                                >
                                    <div className="flex items-center gap-1">
                                        <IoMdAdd className="text-slate-500" />
                                        <span className="text-xs">Add</span>
                                    </div>
                                </Button>
                            }
                        </div>

                    </div>
                    <div className="bg-white border rounded-lg">
                        {/* documents table */}
                        <div className="overflow-x-auto text-nowrap">
                            <Table hoverable theme={table_custom}>
                                <TableHead className=" bg-slate-100">
                                    <TableHeadCell className="w-fit">
                                        <Checkbox
                                            color="blue" onChange={handleSelectAll}
                                            checked={gateways?.data?.length > 0 && selectedGateways.length === gateways?.data?.length}
                                        />
                                    </TableHeadCell>
                                    {tableHeadItems.map((item, index) => (
                                        <TableHeadCell key={index}>
                                            {item.isLink ?
                                                <SortLink showName={item.name} routeName={currentPageRoute} column={item.column} params={getData} sortType={getData?.sort == "asc" ? "desc" : "asc"} />
                                                : item.name}
                                        </TableHeadCell>
                                    ))}

                                </TableHead>

                                <TableBody className="divide-y">
                                    {gateways.data.length > 0 ? (
                                        gateways.data.map((gateway, key) => {
                                            return (
                                                <TableRow key={key}>
                                                    <TableCell>
                                                        <Checkbox
                                                            color={"blue"}
                                                            id={gateway.id}
                                                            checked={selectedGateways.includes(gateway.id)}
                                                            onChange={() => {
                                                                handleCheckboxChange(gateway.id);
                                                            }}
                                                        />
                                                    </TableCell>
                                                    <TableCell className="">
                                                        <div className="flex items-center justify-between gap-2">
                                                            <span>
                                                                {gateway.name}
                                                            </span>
                                                            {(gateway.isDefault == 1) ?
                                                                <>
                                                                    <Button
                                                                        theme={
                                                                            button_custom
                                                                        }
                                                                        color="transparentForTab"
                                                                        className="cursor-default"
                                                                    >
                                                                        <Badge theme={page_badge_theme} size="xs" color="info">Default</Badge>
                                                                    </Button>
                                                                </>
                                                                :
                                                                <>
                                                                    <Button
                                                                        theme={
                                                                            button_custom
                                                                        }
                                                                        color="transparentForTab"
                                                                        // onClick={() => updateDefault(channel.id)}
                                                                        onClick={() => {
                                                                            setUpdateDefaultID(gateway.id)
                                                                            setIsUpdateDefaultConfirmOpen(true);
                                                                        }}
                                                                    >
                                                                        <Badge theme={page_badge_theme} size="xs" color="indigo">Set Default</Badge>
                                                                    </Button>

                                                                </>
                                                            }
                                                        </div>
                                                    </TableCell>
                                                    <TableCell className="">
                                                        {gateway.senderId}
                                                    </TableCell>
                                                    <TableCell className="">
                                                        {gateway.user.username ?? '-'}
                                                    </TableCell>
                                                    <TableCell>
                                                        <div className="flex gap-2">
                                                            {
                                                                collection.can_edit &&
                                                                <Tooltip content="Edit">
                                                                    <Button
                                                                        theme={
                                                                            button_custom
                                                                        }
                                                                        color="success"
                                                                        size="xs"
                                                                        onClick={() => {
                                                                            setIsGatewayEditEnable(
                                                                                true
                                                                            );
                                                                            setSelectedGateway(
                                                                                gateway
                                                                            );
                                                                        }}
                                                                    >
                                                                        <div className="flex items-center">
                                                                            <MdOutlineEdit className="text-sm" />
                                                                            <span className="text-xs ms-1">
                                                                                Edit
                                                                            </span>
                                                                        </div>
                                                                    </Button>
                                                                </Tooltip>
                                                            }
                                                            {
                                                                collection.can_delete &&
                                                                <Button
                                                                    theme={
                                                                        button_custom
                                                                    }
                                                                    color="failure"
                                                                    size="xs"
                                                                    onClick={() => {
                                                                        setSelectedGateways([gateway.id]);
                                                                        SetIsMultipleDeleteConfirmOpen(true);
                                                                    }}
                                                                >
                                                                    <RiDeleteBin5Line className="text-sm" />
                                                                    <span className="text-xs ms-1">
                                                                        Delete
                                                                    </span>
                                                                </Button>
                                                            }
                                                        </div>
                                                    </TableCell>
                                                </TableRow>
                                            );
                                        })
                                    ) : (
                                        <TableRow className="bg-white dark:border-gray-700 dark:bg-gray-800">
                                            <TableCell colSpan={17}>
                                                <NoRecord />
                                            </TableCell>
                                        </TableRow>
                                    )}
                                </TableBody>
                            </Table>
                        </div>
                    </div>

                    <div className="bottom-0 w-full p-3 bg-white rounded-lg float-end dark:border-gray-500 dark:rounded-lg dark:bg-gray-700">
                        <div className="flex flex-wrap justify-between gap-2 lg:justify-between lg:gap-0 md:gap-0">
                            <div className="flex items-center gap-4">
                                <PerPageDropdown
                                    getDataFields={getData ?? null}
                                    routeName={currentPageRoute}
                                    data={gateways}
                                    idName={"name"}
                                />
                            </div>

                            <Paginate tableData={gateways} />
                        </div>
                    </div>
                </div>
            </div>

            {/*-------------- Add Channel ------------*/}
            {AddChannel && (
                <Drawer
                    open={AddChannel}
                    onClose={() => setAddChannel(false)}
                    position="right"
                    theme={customDrawer}
                    className="w-full lg:w-4/5 md:w-4/5 xl:w-2/3"
                >
                    <DrawerHeader
                        title="Add Gateway"
                        titleIcon={FaPencilAlt}
                    />
                    <DrawerItems className="py-2 ">
                        <Add

                            onClose={() => setAddChannel(false)}
                        />
                    </DrawerItems>
                </Drawer>
            )}

            {/* --------------------Edit whatsappweb Channel-------------------------- */}
            {isGatewayEditEnable && (
                <Drawer
                    theme={customDrawer}
                    className="w-full lg:w-4/5 md:w-4/5 xl:w-2/3"
                    open={isGatewayEditEnable}
                    onClose={() => setIsGatewayEditEnable(false)}
                    position="right"
                >
                    <DrawerHeader
                        titleIcon={FaWhatsapp}
                        title="Edit Gateway"
                    />
                    <DrawerItems className="px-2 py-2">
                        <Edit
                            onClose={() => setIsGatewayEditEnable(false)}
                            gateway={SelectedGateway}
                        />
                    </DrawerItems>
                </Drawer>
            )}
            {/*-------------- eye chat table ------------*/}
            {/* {chatList == true && (
                <Drawer
                    open={chatList}
                    onClose={() => setChatList(false)}
                    position="right"
                    theme={customDrawer}
                    className="w-full lg:w-3/5 md:w-3/5"
                >
                    <DrawerHeader title="View Saved chats" titleIcon={FaEye} />
                    <DrawerItems className="py-4">
                        <div className="overflow-x-auto">
                            <ViewSavedChats data={{ channel: openChatId }} />
                        </div>
                    </DrawerItems>
                </Drawer>
            )} */}

            {/* {openModal ? (
                <Modal show={openModal} onClose={() => setOpenModal(false)}>
                    <ModalHeader>Login</ModalHeader>
                    <ModalBody>
                        <QRCode />
                    </ModalBody>
                </Modal>
            ) : (
                ""
            )} */}

            {
                openModalClear &&
                <Modal
                    show={openModalClear}
                    size="md"
                    onClose={() => setOpenModalClear(false)}
                    popup
                >
                    <ModalHeader />
                    <ModalBody>
                        <div className="text-center">
                            <div className="flex items-center justify-center w-12 h-12 mx-auto mb-4 bg-red-100 rounded-full dark:text-gray-200">
                                <RiDeleteBinLine className="text-2xl text-red-600" />
                            </div>
                            <h3 className="mb-5 text-base font-normal">
                                Are you sure?
                                <br /> Once you delete these 500 Campaign Reports,
                                the action cannot be reversed.
                            </h3>
                            <div className="flex justify-center gap-4">
                                <Button
                                    color="gray"
                                    onClick={() => setOpenModal(false)}
                                >
                                    No, Keep It.
                                </Button>
                                <Button
                                    color="failure"
                                    onClick={() => setOpenModal(false)}
                                >
                                    Yes, Delete!
                                </Button>
                            </div>
                        </div>
                    </ModalBody>
                </Modal>
            }
            {/* Activate */}
            {
                openModalActivate &&
                <Modal
                    show={openModalActivate}
                    size="xl"
                    onClose={() => setOpenModalActivate(false)}
                    popup
                >
                    <ModalHeader className="p-2 bg-slate-100">
                        <div className="flex items-center gap-2">
                            <IoMdPower className="text-slate-400" />
                            <h4 className="text-lg">Activate</h4>
                        </div>
                    </ModalHeader>
                    <ModalBody className="flex flex-col gap-3 px-4 pt-0 pb-4 rounded-b-lg bg-slate-100">
                        <div className="px-3 py-2 bg-white rounded-md">
                            <div className="flex items-center gap-3">
                                <div className="text-lg font-medium">
                                    Facebook App Setup
                                </div>
                                <MdOutlineHelpOutline className="text-2xl text-slate-400" />
                            </div>
                            <div>
                                <span>Callback URL</span>
                                <p className="text-gray-500">
                                    https://beta.wabhai.com/webhook/wpboxreceive/5WMYSzvMgRRUalsYBiDPNERbK3doijNB4fyFpTZGededa04f
                                    <button className="ms-2">
                                        <FaRegCopy className="text-blue-600" />
                                    </button>
                                </p>
                            </div>
                            <div>
                                <span>Verify Token</span>
                                <p className="text-gray-500">
                                    5WMYSzvMgRRUalsYBiDPNERbK3doijNB4fyFpTZGededa04f
                                    <button className="ms-2">
                                        <FaRegCopy className="text-blue-600" />
                                    </button>
                                </p>
                            </div>
                        </div>
                    </ModalBody>
                </Modal>
            }
            {/* Activation Failed */}
            {
                openModalActivateFailed &&
                <Modal
                    show={openModalActivateFailed}
                    size="md"
                    onClose={() => setOpenModalActivateFailed(false)}
                    popup
                >
                    <ModalHeader />
                    <ModalBody>
                        <div className="text-center">
                            <div className="flex items-center justify-center w-12 h-12 mx-auto mb-4 bg-red-100 rounded-full dark:text-gray-200">
                                <MdOutlinePowerOff className="text-2xl text-red-600" />
                            </div>
                            <h3 className="mb-5 text-base font-medium">
                                Activation Failed
                                <br /> Unable to activate the gateway. Please verify
                                the Callback URL and Verify Token for accuracy and
                                try again.
                            </h3>
                        </div>
                    </ModalBody>
                </Modal>
            }

            {isUpdateDefaultConfirmOpen &&
                (<ConfirmBox
                    isOpen={isUpdateDefaultConfirmOpen}
                    onClose={() => setIsUpdateDefaultConfirmOpen(false)} // Close the confirm box
                    onAction={handleConfirmUpdateDefault} // Handle the user's choice
                    title="Update Default Gateway "
                    message="Do you want to update default gateway."
                    confirmText="Confirm"
                    cancelText="Cancel"
                    confirmColor="text-yellow-400"
                    confirmBtnBgColor="bg-yellow-100"
                    confirmBtnTextColor="text-yellow-600"
                    iconBackground="bg-yellow-100"
                    iconColor="text-yellow-400"
                    icon={<FaStar />}
                    confirmDisabled={processing} // Disable confirm button while processing
                />)
            }


            {/* {isDeleteConfirmOpen &&
                <ConfirmBox
                    isOpen={isDeleteConfirmOpen}
                    onClose={() => setIsDeleteConfirmOpen(false)} // Close the confirm box
                    onAction={handleConfirmDelete} // Handle the user's choice
                    title="Delete Gateway "
                    message="Do you want to Delete gateway."
                    confirmText="Confirm"
                    cancelText="Cancel"
                    confirmColor="text-red-400"
                    confirmBtnBgColor="bg-red-100"
                    confirmBtnTextColor="text-red-600"
                    iconBackground="bg-red-100"
                    iconColor="text-red-400"

                />} */}
            {isMultipleDeleteConfirmOpen &&
                <ConfirmBox
                    isOpen={isMultipleDeleteConfirmOpen}
                    onClose={() => SetIsMultipleDeleteConfirmOpen(false)} // Close the confirm box
                    onAction={handleMultipleDelete} // Handle the user's choice
                    title="Delete Gateway "
                    message="Do you want to Delete gateway."
                    confirmText="Yes, Delete!"
                    cancelText="No, Keep It"
                    confirmColor="orange"
                    cancelColor="gray"
                    confirmDisabled={processing} // Disable confirm button while processing
                />
            }
        </MainSms2>
    );
}



