import NoRecord from "@/Components/HelperComponents/NoRecord";
import useFetch from "@/Global/useFetch";
import { button_custom } from "@/Pages/Helpers/DesignHelper";
import { Link } from "@inertiajs/react";
import { Avatar } from "@mui/material";
import { Button, Label, Modal, TextInput } from "flowbite-react";
import { useState } from "react";
import { IoIosArrowBack } from "react-icons/io";
import { VscLoading } from "react-icons/vsc";
import ChatAddContact from "./ChatAddContact";

function PeopleList({ SelectedId = "null", currentObject }) {

    const [isAddModalOpen, setIsAddModalOpen] = useState(false);
    const [refetch, setRefetch] = useState(0);

    const { data: people, loading: peopleLoading, error } = useFetch(route('helper.getPeoples'), refetch);


    return (
        <div className="w-full bg-white border rounded-lg shadow-sm dark:bg-gray-900 ">
            <div className="w-full ">
                <div className="flex items-center justify-between p-2">
                    <Button
                        theme={button_custom}
                        color="Fuchsia_custom"
                        size="xxs"
                        as={Link}
                        href={route("whatsapp.index")}
                    >
                        <div className="flex items-center text-sm ">
                            <IoIosArrowBack className="text-base" />
                            Back
                        </div>
                    </Button>
                    <Button
                        theme={button_custom}
                        color="gray"
                        size="xxs"
                        type="button"
                        onClick={() => setIsAddModalOpen(true)}
                    >
                        <div className="flex items-center text-sm px-2">
                            Add
                        </div>
                    </Button>


                </div>
                {/* <div className="pb-2 mx-3">
                    <Search inputPlaceholder="Search..."></Search>
                </div> */}
                <div className="py-2 border-t max-h-[72vh] overflow-auto">
                    <div className="w-full">
                        {peopleLoading ?
                            <VscLoading className="m-auto text-3xl text-gray-500 animate-spin" />
                            :
                            people && people.length > 0 ? (
                                Object.keys(people).length != 0 ?
                                    <>
                                        {people.map((ppl, key) => (
                                            <button
                                                onClick={() => {
                                                    SelectedId(
                                                        ppl.remoteJid?.split("@")[0] ?? ppl.mobile
                                                    );
                                                    currentObject({
                                                        mob: ppl.remoteJid?.split(
                                                            "@"
                                                        )[0] ?? ppl.mobile,
                                                        jid: ppl.remoteJid?.split(
                                                            "@"
                                                        )[0] ?? ppl.mobile,
                                                    });
                                                }}
                                                className="w-full"
                                                key={key}
                                            >
                                                <div className="grid justify-between grid-cols-12 px-2 py-1 border-b hover:bg-gray-50">
                                                    <div className="col-span-3 text-center lg:col-span-3 avatar xl:col-span-2">
                                                        <Avatar
                                                            className="m-auto"
                                                            // alt={`Avatar n°${value + 1}`}
                                                            src="https://img.freepik.com/premium-psd/smiling-man-with-crossed-arms_53876-197122.jpg"
                                                        />
                                                    </div>
                                                    <div className="col-span-6 lg:col-span-6 message xl:col-span-8 text-start">
                                                        <div className="font-medium text-gray-800">
                                                            {
                                                                ppl.remoteJid?.split(
                                                                    "@"
                                                                )[0] ?? ppl.mobile ?? '-'
                                                            }

                                                        </div>
                                                        <div className="truncate ... text-gray-600 text-sm">

                                                            {ppl.body
                                                                ? ppl.body
                                                                : "Start Chat"}
                                                        </div>
                                                    </div>
                                                    <div className="col-span-3 lg:col-span-3 badge-time xl:col-span-2">
                                                        <div className="flex justify-end mb-2 pe-2">
                                                            <div className="">
                                                                {/* <Badge
                                                                badgeContent={4}
                                                                color="primary"
                                                            /> */}
                                                            </div>
                                                        </div>
                                                        <div className="flex justify-end pe-2">
                                                            <div className="text-xs text-neutral-500">
                                                                {/* {ppl.data.messageDateTime

                                                                ?
                                                                (isDateToday(new Date(ppl.data.messageDateTime))) ?
                                                                    convertDateFormat(
                                                                        ppl.data.messageDateTime,
                                                                        "time"
                                                                    )

                                                                    :
                                                                    convertDateFormat(
                                                                        ppl.data.messageDateTime,
                                                                        "full"
                                                                    )
                                                                : ""} */}
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                            </button>
                                        ))}
                                    </>
                                    : ''
                            ) : (
                                <NoRecord />
                            )
                        }
                    </div>
                </div>
            </div>
            {
                isAddModalOpen && (
                    <Modal show={isAddModalOpen} onClose={() => setIsAddModalOpen(false)} size="md">
                        <ModalHeader className="px-3 py-2.5" >Add Contact</ModalHeader>
                        <ModalBody className="px-3 py-2.5">
                            <ChatAddContact setRefetch={setRefetch} onClose={() => setIsAddModalOpen(false)} />
                        </ModalBody>
                    </Modal>
                )
            }


        </div>
    );
}

export default PeopleList;

