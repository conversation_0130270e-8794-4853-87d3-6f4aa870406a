import {
    button_custom,
    customDrawer,
    dropdownChats_custom,
    textarea_custom,
} from "@/Pages/Helpers/DesignHelper";
import { getIconForFile } from "@/Pages/Helpers/Helper";
import Templates from "@/Pages/WhatsApp/Campaign/Steps/Templates";
import EmojiPicker from "emoji-picker-react";

import { Button, Drawer, Dropdown, DropdownItem, FileInput, Textarea } from "flowbite-react";
import { useEffect, useRef, useState } from "react";
import { CgAttachment } from "react-icons/cg";
import { FaRegSmileBeam } from "react-icons/fa";
import {
    FaBold,
    FaItalic,
    FaRegNoteSticky,
    FaStrikethrough,
} from "react-icons/fa6";
import { HiOutlineTemplate } from "react-icons/hi";
import { IoMdAdd, IoMdClose } from "react-icons/io";

function MessageBox({
    data,
    setData,
    errors,
    clearAll,
    allData,
    multipleFileUpload = false,
    fileUpload = false,
    enableTemplate = false,
    setTemp = null,
    addVariables = true,
    isOfficial = false,
    openTemplateModel = null
}) {
    const textareaRef = useRef(null);
    const [openEmoji, setOpenEmoji] = useState(false);
    const [isOpenTemplates, setIsOpenTemplates] = useState(false);
    const contact_columns = ["name", "mobile", "var1", "var2", "var3", "var4", "var5"];

    const fileInputRef = useRef(null);

    const addVar = (variable) => {
        const textarea = textareaRef.current;
        const start = textarea.selectionStart;
        const end = textarea.selectionEnd;

        // Wrap the selected text in * to make it italic in markdown
        const strikeText = isOfficial ? `{{${variable}}}` : `[[${variable}]]`;

        // Update the message state with the stike text
        setData(
            "msg",
            data.substring(0, start) + strikeText + data.substring(end)
        );

        // Set the cursor position after the inserted stike text
        setTimeout(() => {
            textarea.setSelectionRange(
                start + strikeText.length,
                start + strikeText.length
            );
            textarea.focus();
        }, 0);
    };

    const textFormat = (formatAs) => {
        const textarea = textareaRef.current;
        const start = textarea.selectionStart;
        const end = textarea.selectionEnd;

        // Get the selected text
        const selectedText = data.substring(start, end);

        // Wrap the selected text in ** to make it bold in markdown
        const boldText = `${formatAs + selectedText + formatAs}`;

        // Update the message state with the bold text
        setData(
            "msg",
            data.substring(0, start) + boldText + data.substring(end)
        );

        // Set the cursor position after the inserted bold text
        setTimeout(() => {
            textarea.setSelectionRange(
                start + boldText.length,
                start + boldText.length
            );
            textarea.focus();
        }, 0);
    };

    // file upload Functions

    const [files, setFiles] = useState([]);
    const imageExtensions = ["jpeg", "jpg", "png", "webp", "gif"];

    const handleFileChange = (event) => {
        const selectedFiles = Array.from(event.target.files);
        const filePreviews = selectedFiles.map((file) => {
            const extension = file.name.split(".").pop().toLowerCase();
            return {
                name: file.name,
                url: imageExtensions.includes(extension)
                    ? URL.createObjectURL(file)
                    : null,
                extension,
            };
        });
        setFiles(filePreviews);
        setData("files", event.target.files);
    };

    const handleRemove = (index) => {
        const TempStore = { ...allData.files };
        delete TempStore[index];

        const updatedFiles = [...files];
        if (updatedFiles[index].url) {
            URL.revokeObjectURL(updatedFiles[index].url); // Revoke URL for images
        }
        updatedFiles.splice(index, 1);
        setFiles(updatedFiles);
        setData("files", TempStore);
        fileInputRef.current.value = "";
    };

    const handleClear = () => {
        files.forEach((file) => {
            if (file.url) URL.revokeObjectURL(file.url); // Revoke URLs for all images
        });
        setFiles([]);
        setData("msg", "");
        fileInputRef.current.value = "";
    };

    useEffect(() => {
        if (clearAll > 0) {
            handleClear();
        }
    }, [clearAll]);

    // end file upload

    return (
        <div className="p-2 border border-gray-300 rounded-md bg-gray-50">
            {fileUpload && (
                <div className="grid grid-cols-3 gap-2 lg:grid-cols-5 md:grid-cols-4">
                    {files.map((file, index) => (
                        <div
                            className="relative p-2 border rounded"
                            key={index}
                        >
                            <div className="flex ">
                                <div className="text-ellipsis overflow-hidden ... m-auto">
                                    {file.url ? (
                                        <div className="flex justify-center ">
                                            <div className="m-auto">
                                                <img
                                                    className="object-contain"
                                                    src={file.url}
                                                    alt={file.name}
                                                />
                                            </div>
                                        </div>
                                    ) : (
                                        <div className="p-2 text-4xl text-gray-400 bg-gray-200 rounded-lg ">
                                            <div className="flex justify-center ">
                                                <div className="m-auto">
                                                    {getIconForFile(file.extension)}
                                                </div>
                                            </div>
                                        </div>
                                    )}

                                    <p className="text-sm truncate">{file.name}</p>
                                </div>
                                <div className="absolute top-0 end-0">
                                    <Button
                                        size="xs"
                                        color="gray"
                                        pill
                                        onClick={() => handleRemove(index)}
                                    >
                                        <IoMdClose />
                                    </Button>
                                </div>
                            </div>
                        </div>
                    ))}
                </div>
            )}

            <Textarea
                theme={textarea_custom}
                name="message"
                placeholder="Type Message here..."
                rows={4}
                id="myTextarea"
                ref={textareaRef}
                value={data}
                color={errors.msg ? "failure" : "gray"}
                onFocus={() => setOpenEmoji(false)}
                onChange={(e) => setData("msg", e.target.value)}
                className="border-0 focus:ring-white"
                helperText={errors.msg && errors.msg}
            />

            <div className="flex flex-col flex-wrap items-center justify-between md:flex-row lg:flex-row text-nowrap">
                <div className="flex flex-wrap items-center gap-2">
                    {fileUpload && (
                        <div className="">
                            <label htmlFor="File-Input">
                                <div className="flex items-center p-1 text-sm bg-white border rounded-md">
                                    <div className="">
                                        <CgAttachment className="text-xs text-slate-700" />
                                    </div>
                                    <div className="">Add File</div>
                                </div>
                                {/* </Button> */}
                            </label>
                            <div className="hidden">
                                <FileInput
                                    size={"xs"}
                                    id="File-Input"
                                    multiple={multipleFileUpload}
                                    onChange={handleFileChange}
                                    ref={fileInputRef}
                                />
                            </div>
                        </div>
                    )}

                    {enableTemplate && (
                        <div className="">
                            <Button
                                size="xs"
                                color="gray"
                                onClick={() =>
                                    setIsOpenTemplates(!isOpenTemplates)
                                }
                            >
                                <div className="flex items-center gap-1">
                                    <div className="">
                                        <FaRegNoteSticky className="text-xs text-slate-700" />
                                    </div>
                                    <div className="">Select Template</div>
                                </div>
                            </Button>
                        </div>
                    )}
                    <div className="">
                        <Button
                            size="xs"
                            color="gray"
                            onClick={() => setOpenEmoji(!openEmoji)}
                        >
                            <FaRegSmileBeam />
                        </Button>
                    </div>
                    <div className="">
                        <Button
                            size="xs"
                            color="gray"
                            id="boldButton"
                            onClick={() => textFormat("*")}
                        >
                            <FaBold />
                        </Button>
                    </div>
                    <div className="">
                        <Button
                            size="xs"
                            color="gray"
                            id="italicButton"
                            onClick={() => textFormat("_")}
                        >
                            <FaItalic />
                        </Button>
                    </div>
                    <div className="">
                        <Button
                            size="xs"
                            color="gray"
                            id="strike"
                            onClick={() => textFormat("~")}
                        >
                            <FaStrikethrough />
                        </Button>
                    </div>
                </div>

                <div className="flex flex-wrap gap-2">
                    {openTemplateModel &&
                        <Button
                            size="xs"
                            color="gray"
                            onClick={() =>
                                openTemplateModel()
                            }
                        >
                            <div className="flex items-center gap-1">
                                <div className="">
                                    <FaRegNoteSticky className="text-xs text-slate-700" />
                                </div>
                                <div className="">Select Template</div>
                            </div>
                        </Button>
                    }

                    {addVariables && (
                        <Dropdown
                            theme={dropdownChats_custom}
                            className="p-1"
                            label="Variables"
                            size="xs"
                            color="gray"
                        >
                            {contact_columns.map((cols, k) => {
                                return (
                                    <DropdownItem
                                        key={"Dropdown-item-cols" + k}
                                        as="button"
                                        onClick={() => addVar(cols)}
                                    >
                                        {cols}
                                    </DropdownItem>
                                );
                            })}
                        </Dropdown>
                    )}
                    {
                        (isOfficial && addVariables == false) ?
                            <Button
                                theme={button_custom}
                                size="xxs"
                                color="gray"
                                className="px-2"
                                onClick={() => addVar()}
                            >
                                <div className="flex items-center gap-1">
                                    <IoMdAdd />
                                    <span>Variables</span>
                                </div>
                            </Button> : <></>
                    }
                    <div>
                        <Button
                            theme={button_custom}
                            color="failure"
                            size="xxs"
                            onClick={() => handleClear()}
                        >
                            <span className="text-sm">Clear</span>
                        </Button>
                    </div>
                </div>
            </div>

            <ul>
                {errors.files &&
                    <li className="text-xs text-red-500">{errors.files}</li>
                }
                {Object.keys(errors).map((key, index) => (
                    (key.includes('attachment') || key.includes('message')) && (
                        <li key={index} className="text-xs text-red-500">
                            {errors[key]}
                        </li>
                    )
                ))}
            </ul>

            {openEmoji && (
                <div className="absolute z-50 bg-white">
                    <EmojiPicker
                        open={openEmoji}
                        lazyLoadEmojis={true}
                        onEmojiClick={(emojiObject) =>
                            setData("msg", (data += emojiObject.emoji))
                        }
                    />
                </div>
            )}

            {isOpenTemplates && (
                <Drawer
                    theme={customDrawer}
                    open={isOpenTemplates}
                    onClose={() => setIsOpenTemplates(false)}
                    position="right"
                    className="w-full xl:w-10/12 lg:w-full md:w-full"
                >
                    <Drawer.Header
                        titleIcon={HiOutlineTemplate}
                        title="Template library"
                    />
                    <Drawer.Items>
                        <Templates
                            setTemp={setTemp}
                            closeDrawer={setIsOpenTemplates}
                        />
                    </Drawer.Items>
                </Drawer>
            )}
        </div>
    );
}
export default MessageBox;
