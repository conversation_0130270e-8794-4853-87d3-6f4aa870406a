import Main from "@/Layouts/Main"
import SideMenu from "./SideMenu"
import { <PERSON><PERSON>, DrawerItems } from "flowbite-react"
import { MdArrowForwardIos } from "react-icons/md"
import { useState } from "react"
// import { FcElectronics } from "react-icons/fc";


function AdminMain({ children }) {
    const [isMenuOpen, setIsMenuOpen] = useState(false);
    return (
        <Main>
            <div className="p-2 overflow-hidden ">
                <div className="grid grid-flow-row-dense grid-cols-12 gap-2 p-0 border-none md:grid-cols-12 sm:grid-cols-1 bg-slate-100">
                    <div className="hidden lg:col-span-3 xl:col-span-2 md:col-span-3 sm:col-span-3 lg:flex">
                        <div className="bg-white shadow-sm rounded-lg border dark:bg-gray-900 w-full overflow-auto min-h-[90vh] max-h-[90vh]">
                            <SideMenu />
                        </div>
                    </div>
                    <div className="relative pt-0 dark:text-gray-400 lg:p-0 lg:col-span-9 xl:col-span-10 md:col-span-12 col-span-full ">
                        <div className="absolute z-40 -mx-3 lg:hidden">
                            <Button className="bg-white bg-opacity-50 rounded-full" color="white" size="xs" onClick={() => setIsMenuOpen(!isMenuOpen)}>
                                <MdArrowForwardIos className="text-xl text-gray-600" />
                            </Button>
                        </div>
                        {children}
                    </div>
                </div>
            </div>
            {/* {isMenuOpen && */}
                <Drawer open={isMenuOpen} onClose={() => setIsMenuOpen(!isMenuOpen)} position="left"
                    theme={{
                        "root": {
                            "base": "fixed z-40 overflow-y-auto bg-white pt-2 transition-transform dark:bg-gray-800",
                        }
                    }}
                >
                    <DrawerItems>
                        <SideMenu />
                    </DrawerItems>
                </Drawer>
            {/* } */}
        </Main>
    )
}

export default AdminMain
