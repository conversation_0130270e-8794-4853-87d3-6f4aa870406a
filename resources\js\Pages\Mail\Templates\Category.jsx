import useFetch from "@/Global/useFetch";
import { button_custom } from "@/Pages/Helpers/DesignHelper";
import { <PERSON>, router } from "@inertiajs/react";
import { <PERSON>ge, Button, Modal } from "flowbite-react";
import { useState, useEffect, memo, useCallback } from "react";
import { CgSpinner } from "react-icons/cg";
import { MdDeleteOutline, MdOutlineAdd, MdOutlineEdit } from "react-icons/md";
import AddCategory from "./AddCategory";
import EditCategory from "./EditCategory";
import ConfirmBox from "@/Components/HelperComponents/ConfirmBox";

const CategoryButton = memo(({ category, selectedCategory, collection, onEdit, onDelete, setSelectedCategory }) => {
    const isSelected = category.id == selectedCategory;
    const buttonClasses = `flex items-center justify-between border-b text-sm focus-ring-0 ${isSelected ? "bg-blue-100 " : "hover:bg-blue-100 bg-white"}`;

    return (
        <div className={buttonClasses}>
            <div className={collection?.can_edit || collection?.can_delete ? "w-full" : "w-4/5"}>
                <Button
                    className="justify-start bg-transparent border-0 focus:ring-0"
                    color="gray"
                    size="sm"
                    onClick={() => setSelectedCategory(category.id)}
                    as={Link}
                    href={route('mail.template.category.show', { category: category.id })}
                    theme={{ inner: { base: "flex w-full transition-all duration-200" } }}
                >
                    <div className="flex justify-between w-full gap-2 overflow-y-auto text-start">
                        <div className="w-2/3 truncate">{category.name}</div>
                        <Badge color="info">{category.templates_count}</Badge>
                    </div>
                </Button>
            </div>
            {(collection?.can_edit || collection?.can_delete) && (
                <div className="flex justify-end w-1/5 gap-2">
                    {collection?.can_edit && (
                        <Button theme={button_custom} color="withoutBorder" size="xxs" onClick={() => onEdit(category)}>
                            <MdOutlineEdit className={"text-slate-400"} />
                        </Button>
                    )}
                    {collection?.can_delete && (
                        <Button theme={button_custom} color="withoutBorder" size="xxs" onClick={() => onDelete(category.id)}>
                            <MdDeleteOutline className={"text-slate-400"} />
                        </Button>
                    )}
                </div>
            )}
        </div>
    );
});

const Category = ({ selectedCategory, setSelectedCategory }) => {

    const [reFetch, setReFetch] = useState(0);
    const { data: cateData, loading: cateDataLoading } = useFetch(route('mail.template.category.index', { all: true }), reFetch);
    const [collection, setCollection] = useState();
    const [modalState, setModalState] = useState({ openAdd: false, openEdit: false, selected: null });
    const [selectedCategories, setSelectedCategories] = useState([]);
    const [isMultipleDeleteConfirmOpen, setIsMultipleDeleteConfirmOpen] = useState(false);

    useEffect(() => {
        if (cateData?.collection) {
            setCollection(cateData.collection);
        }
    }, [cateData]);

    const handleMultipleDelete = useCallback((res) => {
        if (res) {
            router.delete(route("mail.template.category.destroy", { category: selectedCategories.toString() }));
            setReFetch(prev => prev + 1);
        }
        setSelectedCategories([]);
        setIsMultipleDeleteConfirmOpen(false);
    }, [selectedCategories]);

    const handleEdit = useCallback((category) => {
        setModalState({ openEdit: true, selected: category });
    }, []);

    const handleDelete = useCallback((categoryId) => {
        setSelectedCategories([categoryId]);
        setIsMultipleDeleteConfirmOpen(true);
    }, []);

    const closeModal = useCallback((type) => {
        setModalState(prev => ({ ...prev, [type]: false }));
    }, []);

    const toggleAddModal = useCallback(() => {
        setModalState(prev => ({ ...prev, openAdd: true }));
    }, []);

    return (
        <div className="w-full bg-white rounded-md h-fit">
            <div className="flex items-center justify-between gap-3 px-3 py-2">
                <span>Categories</span>
                {collection?.can_add && (
                    <Button theme={button_custom} color="gray" size="xs" onClick={toggleAddModal}>
                        <div className="flex items-center">
                            <MdOutlineAdd className="text-slate-500" />
                            <span className="text-xs">Add</span>
                        </div>
                    </Button>
                )}
            </div>

            <div className={`flex items-center justify-between border-b text-sm px-1.5 focus-ring-0 ${selectedCategory == 0 ? "bg-blue-100 " : "hover:bg-blue-100 bg-white"}`}>
                <Button className="justify-start w-full bg-transparent border-0 focus-ring-0" color="gray" as={Link} href={route('mail.templates.index')} size="sm">
                    <div className="flex items-center gap-2">All</div>
                </Button>
            </div>

            <div className="flex flex-col h-full overflow-y-auto bg-white">
                {cateDataLoading ? (
                    <div className="flex justify-center p-2">
                        <CgSpinner className="text-3xl text-blue-400 ease-in-out animate-spin" />
                    </div>
                ) : (
                    collection?.categories?.map((category, key) => (
                        <CategoryButton
                            key={category.id}
                            category={category}
                            selectedCategory={selectedCategory}
                            setSelectedCategory={setSelectedCategory}
                            collection={collection}
                            onEdit={handleEdit}
                            onDelete={handleDelete}
                        />
                    ))
                )}
            </div>

            {modalState.openAdd && (
                <Modal show={true} size="md" onClose={() => closeModal('openAdd')} popup>
                    <ModalHeader className="p-2">
                        <h4 className="text-lg">Add Category</h4>
                    </ModalHeader>
                    <ModalBody className="px-4 py-3 rounded-b-lg bg-slate-100">
                        <AddCategory onClose={() => closeModal('openAdd')} setReFetch={setReFetch} />
                    </ModalBody>
                </Modal>
            )}

            {modalState.openEdit && (
                <Modal show={true} size="md" onClose={() => closeModal('openEdit')} popup>
                    <ModalHeader className="p-2">
                        <h4 className="text-lg">Edit Category</h4>
                    </ModalHeader>
                    <ModalBody className="px-4 py-3 rounded-b-lg bg-slate-100">
                        <EditCategory onClose={() => closeModal('openEdit')} category={modalState.selected} setReFetch={setReFetch} />
                    </ModalBody>
                </Modal>
            )}

            {isMultipleDeleteConfirmOpen && (
                <ConfirmBox
                    isOpen={true}
                    onClose={() => setIsMultipleDeleteConfirmOpen(false)}
                    onAction={handleMultipleDelete}
                    title="Delete Category"
                    message="Do you want to delete the selected category?"
                    confirmText="Yes, Delete!"
                    cancelText="No, Keep It"
                    confirmColor="orange"
                    cancelColor="gray"
                />
            )}
        </div>
    );
};

export default memo(Category);

