import { button_custom, custom_carousal, input_custom, radio_custom, table_custom } from '@/Pages/Helpers/DesignHelper'
import { Button, Carousel, Label, Radio, Table, Textarea, TextInput } from 'flowbite-react'
import React from 'react'
import { IoMdAdd, IoMdClose } from 'react-icons/io'
import { MdOutlineAttachFile } from 'react-icons/md'

export default function Create() {
    return (
        <div className='bg-white rounded-lg p-2 my-2'>
            {/*  */}
            <div className='w-4/5 '>
                <div className='flex items-center gap-5 w-full '>
                    <div className='w-full'>
                        <div className="mb-2 block">
                            <Label htmlFor="email1" value="Customer" />
                        </div>
                        <TextInput className='w-full' theme={input_custom} id="email1" type="email" placeholder="Abhishe<PERSON> Bhattacharyya (8560)" required />
                    </div>
                    <div className="w-full">
                        <div className="block mb-2">
                            <Label htmlFor="paymentMethod">
                                Due Date
                                {/* <span className="text-red-600">
                                    *
                                </span> */}
                            </Label>
                        </div>
                        <div className="relative">
                            <div className="absolute inset-y-0 end-3.5 flex items-center pointer-events-none ps-4 pe-4 border-s"></div>
                            <input
                                type="date"
                                id="timeAdd"
                                className="bg-gray-50 w-full border leading-none border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block p-3 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-blue-500 dark:focus:border-blue-500"
                                // min="09:00"
                                // max="18:00"
                                // defaultValue="00:00"
                                required
                            />
                        </div>
                        {/* <input className="max-w-md rounded-md border-slate-300" type="date" id="birthdaytime" name="birthdaytime"/> */}
                    </div>

                </div>
                <div className='flex items-center gap-5 w-full mt-2 '>
                    <div className='w-full'>
                        <div className="mb-2 block">
                            <Label htmlFor="email1" value="Company" />
                        </div>
                        <TextInput theme={input_custom} id="email1" type="email" placeholder="Dunes Factory" required />
                    </div>
                    <div className='w-full'>
                        <div>
                            <Label>Account Manager</Label>
                        </div>
                        <select
                            className="rounded-md mt-2 bg-gray-50 text-sm border-gray-300 p-2.5 hover:bg-gray-50 w-full"
                            name="cars"
                            id="cars"
                        >
                            <option value="volvo">
                                Add Variable
                            </option>
                            <option value="saab">
                                Name
                            </option>
                            <option value="opel">
                                Mobile Number
                            </option>
                            <option value="audi">
                                City
                            </option>
                            <option value="audi">
                                Address
                            </option>
                        </select>
                    </div>
                </div>
            </div>
            {/* Billing Address */}
            <div className='my-2 mt-4'>
                <Label>
                    <div className='flex items-center gap-2'>
                        <span>Billing Address</span>
                        <Button theme={button_custom} size='xxs' color='blue'>
                            <IoMdAdd className='text-white text-xl' />
                        </Button>
                    </div>
                </Label>
                <div className="h-28">
                    <Carousel slide={false} theme={custom_carousal} className='gap-1.5'>

                        <div className="rounded-md border border-blue-600 w-full text-sm">
                            <div className="flex items-start p-2 gap-1">
                                <span>Opposite Bajaj RE Motors. Rani
                                    bazar, Bikaner,
                                    Rajasthan, 334001</span>
                                <Radio theme={radio_custom} color='blue' id="united-state" name="countries" value="USA" defaultChecked />
                            </div>
                        </div>
                        {/*  */}
                        <div className="rounded-md border w-full text-sm">
                            <div className="flex items-start p-2 gap-1">
                                <span>Opposite Bajaj RE Motors. Rani
                                    bazar, Bikaner,
                                    Rajasthan, 334001</span>
                                <Radio theme={radio_custom} color='blue' id="united-state" name="countries" value="USA" defaultChecked />
                            </div>
                        </div>
                        <div className="rounded-md border w-full text-sm">
                            <div className="flex items-start p-2 gap-1">
                                <span>Opposite Bajaj RE Motors. Rani
                                    bazar, Bikaner,
                                    Rajasthan, 334001</span>
                                <Radio theme={radio_custom} color='blue' id="united-state" name="countries" value="USA" defaultChecked />
                            </div>
                        </div>
                        <div className="rounded-md border w-full text-sm">
                            <div className="flex items-start p-2 gap-1">
                                <span>Opposite Bajaj RE Motors. Rani
                                    bazar, Bikaner,
                                    Rajasthan, 334001</span>
                                <Radio theme={radio_custom} color='blue' id="united-state" name="countries" value="USA" defaultChecked />
                            </div>
                        </div>
                        <div className="rounded-md border w-full text-sm">
                            <div className="flex items-start p-2 gap-1">
                                <span>Opposite Bajaj RE Motors. Rani
                                    bazar, Bikaner,
                                    Rajasthan, 334001</span>
                                <Radio theme={radio_custom} color='blue' id="united-state" name="countries" value="USA" defaultChecked />
                            </div>
                        </div>
                        <div className="rounded-md border w-full text-sm">
                            <div className="flex items-start p-2 gap-1">
                                <span>Opposite Bajaj RE Motors. Rani
                                    bazar, Bikaner,
                                    Rajasthan, 334001</span>
                                <Radio theme={radio_custom} color='blue' id="united-state" name="countries" value="USA" defaultChecked />
                            </div>
                        </div>
                        <div className="rounded-md border w-full text-sm">
                            <div className="flex items-start p-2 gap-1">
                                <span>Opposite Bajaj RE Motors. Rani
                                    bazar, Bikaner,
                                    Rajasthan, 334001</span>
                                <Radio theme={radio_custom} color='blue' id="united-state" name="countries" value="USA" defaultChecked />
                            </div>
                        </div>
                        <div className="rounded-md border w-full text-sm">
                            <div className="flex items-start p-2 gap-1">
                                <span>Opposite Bajaj RE Motors. Rani
                                    bazar, Bikaner,
                                    Rajasthan, 334001</span>
                                <Radio theme={radio_custom} color='blue' id="united-state" name="countries" value="USA" defaultChecked />
                            </div>
                        </div>
                        <div className="rounded-md border w-full text-sm">
                            <div className="flex items-start p-2 gap-1">
                                <span>Opposite Bajaj RE Motors. Rani
                                    bazar, Bikaner,
                                    Rajasthan, 334001</span>
                                <Radio theme={radio_custom} color='blue' id="united-state" name="countries" value="USA" defaultChecked />
                            </div>
                        </div>
                        <div className="rounded-md border w-full text-sm">
                            <div className="flex items-start p-2 gap-1">
                                <span>Opposite Bajaj RE Motors. Rani
                                    bazar, Bikaner,
                                    Rajasthan, 334001</span>
                                <Radio theme={radio_custom} color='blue' id="united-state" name="countries" value="USA" defaultChecked />
                            </div>
                        </div>
                        <div className="rounded-md border w-full text-sm">
                            <div className="flex items-start p-2 gap-1">
                                <span>Opposite Bajaj RE Motors. Rani
                                    bazar, Bikaner,
                                    Rajasthan, 334001</span>
                                <Radio theme={radio_custom} color='blue' id="united-state" name="countries" value="USA" defaultChecked />
                            </div>
                        </div>
                        <div className="rounded-md border w-full text-sm">
                            <div className="flex items-start p-2 gap-1">
                                <span>Opposite Bajaj RE Motors. Rani
                                    bazar, Bikaner,
                                    Rajasthan, 334001</span>
                                <Radio theme={radio_custom} color='blue' id="united-state" name="countries" value="USA" defaultChecked />
                            </div>
                        </div>
                        <div className="rounded-md border w-full text-sm">
                            <div className="flex items-start p-2 gap-1">
                                <span>Opposite Bajaj RE Motors. Rani
                                    bazar, Bikaner,
                                    Rajasthan, 334001</span>
                                <Radio theme={radio_custom} color='blue' id="united-state" name="countries" value="USA" defaultChecked />
                            </div>
                        </div>
                    </Carousel>
                </div>
            </div>
            {/*  */}
            <div className='w-4/5 '>
                <div className='flex items-center gap-5 w-full '>
                    <div className='w-full'>
                        <div className="mb-2 block">
                            <Label htmlFor="paymentMethod">
                                Name
                                <span className="text-red-600 ms-1">
                                    *
                                </span>
                            </Label>
                        </div>
                        <TextInput className='w-full' theme={input_custom} id="email1" type="email" placeholder="Abhishek Bhattacharyya (8560)" required />
                    </div>
                    <div className="w-full">
                        <div className="block mb-2">
                            <Label htmlFor="paymentMethod">
                                Designation
                            </Label>
                        </div>
                        <TextInput className='w-full' theme={input_custom} id="email1" type="email" placeholder="Type designation" required />
                    </div>

                </div>
                <div className='flex items-center gap-5 w-full mt-2 '>
                    <div className='w-full'>
                        <div className="mb-2 block">
                            <Label htmlFor="email1" value="Mobile" />
                        </div>
                        <TextInput theme={input_custom} id="email1" type="email" placeholder="Ex: +91 8566654745" required />
                    </div>
                    <div className='w-full'>
                        <div>
                            <Label>Email</Label>
                        </div>
                        <TextInput className='w-full' theme={input_custom} id="email1" type="email" placeholder="Ex: <EMAIL>" required />
                    </div>
                </div>
                <div className='w-full my-2'>
                    <div className="mb-2 block">
                        <Label htmlFor="paymentMethod">
                            Address
                        </Label>
                    </div>
                    <TextInput className='w-full' theme={input_custom} id="email1" type="email" placeholder="P. No. 885256 Daga Petrol Pump, Bada Bazar, Near City Hospital, Jaipur Road" required />
                </div>
                <div className='flex items-center gap-5 w-full mt-2 '>
                    <div className='w-full'>
                        <div>
                            <Label>City</Label>
                        </div>
                        <select
                            className="rounded-md mt-2 bg-gray-50 text-sm border-gray-300 p-2.5 hover:bg-gray-50 w-full"
                            name="cars"
                            id="cars"
                        >
                            <option value="volvo">
                                Bikaner
                            </option>
                            <option value="saab">
                                Name
                            </option>
                            <option value="opel">
                                Mobile Number
                            </option>
                            <option value="audi">
                                City
                            </option>
                            <option value="audi">
                                Address
                            </option>
                        </select>
                    </div>
                    <div className='w-full'>
                        <div>
                            <Label>State</Label>
                        </div>
                        <select
                            className="rounded-md mt-2 bg-gray-50 text-sm border-gray-300 p-2.5 hover:bg-gray-50 w-full"
                            name="cars"
                            id="cars"
                        >
                            <option value="volvo">
                                Rajasthan
                            </option>
                            <option value="saab">
                                Name
                            </option>
                            <option value="opel">
                                Mobile Number
                            </option>
                            <option value="audi">
                                City
                            </option>
                            <option value="audi">
                                Address
                            </option>
                        </select>
                    </div>
                </div>
                <div className='flex items-center gap-5 w-full mt-2 '>
                    <div className='w-full'>
                        <div>
                            <Label>Country/ Region</Label>
                        </div>
                        <select
                            className="rounded-md mt-2 bg-gray-50 text-sm border-gray-300 p-2.5 hover:bg-gray-50 w-full"
                            name="cars"
                            id="cars"
                        >
                            <option value="volvo">
                                India
                            </option>
                            <option value="saab">
                                Name
                            </option>
                            <option value="opel">
                                Mobile Number
                            </option>
                            <option value="audi">
                                City
                            </option>
                            <option value="audi">
                                Address
                            </option>
                        </select>
                    </div>
                    <div className='w-full my-2'>
                        <div className="mb-2 block">
                            <Label htmlFor="paymentMethod">
                                Pin Code
                            </Label>
                        </div>
                        <TextInput className='w-full' theme={input_custom} id="email1" type="email" defaultValue="334001" required />
                    </div>
                </div>
            </div>
            {/*  */}
            <div className="overflow-x-auto my-2 border rounded-lg">
                <Table hoverable theme={table_custom}>
                    <TableHead>
                        <TableHeadCell>Item Details</TableHeadCell>
                        <TableHeadCell>Time</TableHeadCell>
                        <TableHeadCell>Quantity</TableHeadCell>
                        <TableHeadCell>Rate</TableHeadCell>
                        <TableHeadCell>
                            Total
                        </TableHeadCell>
                    </TableHead>
                    <TableBody className="divide-y">
                        <TableRow className="bg-white dark:border-gray-700 dark:bg-gray-800">
                            <TableCell className="">
                                Type or click to select an item.
                            </TableCell>
                            <TableCell></TableCell>
                            <TableCell></TableCell>
                            <TableCell></TableCell>
                            <TableCell className='float-end'>
                                <Button theme={button_custom} size='xs' color='gray'>
                                    <IoMdClose />
                                </Button>
                            </TableCell>
                        </TableRow>
                    </TableBody>
                </Table>
            </div>
            <div className='flex items-center gap-2 w-full mt-2 justify-between'>
                <div className="w-full">
                    <div className="mb-2 block">
                        <Label htmlFor="comment" value="Customer Note" />
                    </div>
                    <Textarea className='w-full' id="comment" required rows={5}
                        helperText="Will be displayed on the invoice." />
                </div>
                <div className='bg-slate-50 p-2 rounded-md w-3/5 '>
                    <div className='flex justify-between items-startgap-2'>
                        <div>Sub Total</div>
                        <div>₹ 0.00</div>
                    </div>
                    <div>
                        <span>Discount</span>
                    </div>
                </div>
            </div>
        </div>
    )
}

