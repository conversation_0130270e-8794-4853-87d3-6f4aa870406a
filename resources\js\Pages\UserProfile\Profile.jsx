import { Dropdown, DropdownItem } from "flowbite-react";
import { FiUpload } from "react-icons/fi";
import { GiAlliedStar } from "react-icons/gi";
import { IoIosRemoveCircleOutline } from "react-icons/io";
import { MdOutlineCameraAlt } from "react-icons/md";

export default function Profile() {
    const button_custom = {
        color: {
            green: "border border-transparent bg-[#28a745] text-white focus:ring-4 focus:ring-[#d4ffde] enabled:hover:bg-[#28a745] dark:bg-[#28a745] dark:hover:bg-[#28a745] dark:focus:ring-[#d4ffde]",
        },
        pill: {
            off: "rounded",
            on: "rounded-full",
        },
        size: {
            xs: "p-0.5 text-lg",
            sm: "px-3 py-1.5 text-sm",
            md: "px-4 py-2 text-sm",
            lg: "px-5 py-2.5 text-base",
            xl: "px-6 py-3 text-base",
        },
    };
    const radioCustom = {
        root: {
            base: "h-4 w-4 border border-gray-300 text-blue-600 focus:ring-2 focus:ring-blue-500 dark:border-gray-600 dark:bg-gray-700 dark:focus:bg-blue-600 dark:focus:ring-blue-600",
        },
    };
    const custom_datepicker = {
        root: {
            base: "relative",
        },
        field: {
            base: "relative w-full border-s ps-3",
            icon: {
                base: "pointer-events-none absolute inset-y-0 end-3.5 flex items-center pl-3 border-s ps-3",
                svg: "h-5 w-5 text-slate-400 dark:text-gray-400",
            },
            rightIcon: {
                base: "pointer-events-none absolute border-s inset-y-0 right-0 flex items-center ps-2 pr-3 text-slate-300 border-s ps-3",
                svg: "h-5 w-5 text-slate-400 dark:text-gray-400 border-s ps-3",
            },
        },
        input: {
            base: "block w-full border disabled:cursor-not-allowed disabled:opacity-50",
            sizes: {
                sm: "p-2 sm:text-xs",
                md: "p-2.5 text-sm",
                lg: "p-4 sm:text-base",
            },
            gray: "border-blue-300 bg-blue-50 text-blue-900 placeholder-blue-700 focus:border-blue-500 focus:ring-blue-500 dark:border-blue-400 dark:bg-blue-100 dark:focus:border-blue-500 dark:focus:ring-blue-500",
        },

        popup: {
            root: {
                base: "absolute top-10 z-50 block pt-2",
                inline: "relative top-0 z-auto",
                inner: "inline-block rounded-lg bg-white p-4 shadow-lg dark:bg-gray-700",
            },
            header: {
                base: "",
                title: "px-2 py-3 text-center font-semibold text-gray-900 dark:text-white",
                selectors: {
                    base: "mb-2 flex justify-between",
                    button: {
                        base: "rounded-lg bg-white px-5 py-2.5 text-sm font-semibold text-gray-900 hover:bg-gray-100 focus:outline-none focus:ring-2 focus:ring-gray-200 dark:bg-gray-700 dark:text-white dark:hover:bg-gray-600",
                        prev: "",
                        next: "",
                        view: "",
                    },
                },
            },
            view: {
                base: "p-1",
            },
            footer: {
                base: "mt-2 flex space-x-2",
                button: {
                    base: "w-full rounded-lg px-5 py-2 text-center text-sm font-medium focus:ring-4 focus:ring-blue-300",
                    today: "bg-blue-700 text-white hover:bg-blue-800 dark:bg-blue-600 dark:hover:bg-blue-700",
                    clear: "border border-gray-300 bg-white text-gray-900 hover:bg-gray-100 dark:border-gray-600 dark:bg-gray-700 dark:text-white dark:hover:bg-gray-600",
                },
            },
        },
        views: {
            days: {
                header: {
                    base: "mb-1 grid grid-cols-7",
                    title: "h-6 text-center text-sm font-medium leading-6 text-gray-500 dark:text-gray-400",
                },
                items: {
                    base: "grid w-64 grid-cols-7",
                    item: {
                        base: "block flex-1 cursor-pointer rounded-lg border-0 text-center text-sm font-semibold leading-9 text-gray-900 hover:bg-gray-100 dark:text-white dark:hover:bg-gray-600",
                        selected: "bg-blue-700 text-white hover:bg-blue-600",
                        disabled: "text-gray-500",
                    },
                },
            },
            months: {
                items: {
                    base: "grid w-64 grid-cols-4",
                    item: {
                        base: "block flex-1 cursor-pointer rounded-lg border-0 text-center text-sm font-semibold leading-9 text-gray-900 hover:bg-gray-100 dark:text-white dark:hover:bg-gray-600",
                        selected: "bg-cyan-700 text-white hover:bg-cyan-600",
                        disabled: "text-gray-500",
                    },
                },
            },
            years: {
                items: {
                    base: "grid w-64 grid-cols-4",
                    item: {
                        base: "block flex-1 cursor-pointer rounded-lg border-0 text-center text-sm font-semibold leading-9 text-gray-900 hover:bg-gray-100 dark:text-white dark:hover:bg-gray-600",
                        selected: "bg-cyan-700 text-white hover:bg-cyan-600",
                        disabled: "text-gray-500",
                    },
                },
            },
            decades: {
                items: {
                    base: "grid w-64 grid-cols-4",
                    item: {
                        base: "block flex-1 cursor-pointer rounded-lg border-0 text-center text-sm font-semibold leading-9 text-gray-900 hover:bg-gray-100 dark:text-white dark:hover:bg-gray-600",
                        selected: "bg-cyan-700 text-white hover:bg-cyan-600",
                        disabled: "text-gray-500",
                    },
                },
            },
        },
    };
    return (
        <>
            {/* -------------------User Image & File Upload------------------ */}
            <div className="px-4 py-4 pt-5 rounded-lg">
                <div className="min-h-screen p-6 bg-white rounded">
                    {/* -------------------User Image & File Upload------------------ */}
                    <div className="flex flex-wrap items-center gap-4">
                        <Dropdown
                            className="text-white"
                            arrowIcon={false}
                            inline
                            label={
                                <div className="relative w-20 h-20 group">
                                    {/* Icon that appears on hover */}
                                    <div className="absolute top-0 left-0 flex items-center justify-center w-full h-full transition-opacity rounded-full opacity-0 bg-slate bg-opacity-90 group-hover:opacity-100 ">
                                        <MdOutlineCameraAlt className="z-10 w-6 h-6 text-slate-300" />
                                    </div>

                                    {/* Image with hover effect */}
                                    <img
                                        src="https://img.freepik.com/free-vector/businessman-character-avatar-isolated_24877-60111.jpg?t=st=1720614745~exp=1720618345~hmac=38ff343a9443982af738325ad4d54d1d39ba540be4c28013b03cf063347d255e&w=200"
                                        alt=""
                                        className="z-0 object-cover w-20 h-20 transition-all rounded-full group-hover:brightness-50"
                                    />
                                </div>
                            }
                        >
                            <DropdownItem>
                                <div className="flex items-center gap-2">
                                    <FiUpload />
                                    <h4>Upload New</h4>
                                </div>
                            </DropdownItem>
                            <DropdownItem>
                                <div className="flex items-center gap-2 text-red-700">
                                    <IoIosRemoveCircleOutline />
                                    <h4>Remove</h4>
                                </div>
                            </DropdownItem>
                        </Dropdown>
                        <div className="flex flex-col">
                            <h4
                                className="text-slate-600 dark:text-gray-400 pt-1.5 ps-0.5
                        lg:text-base md:text-[15px] text-xs
                        font-medium"
                            >
                                Prakash Chaudhary (DF33)
                            </h4>
                            <h4 className="text-slate-600 dark:text-gray-400 pt-1.5 ps-0.5 lg:text-sm text-xs font-medium">
                                <EMAIL>
                            </h4>
                        </div>
                    </div>

                    {/* -------------------Input fields------------------ */}
                    <div className="w-full gap-10 pt-4 lg:flex sm:flex-row">
                        {/* -------------------First Column------------------ */}
                        <div className="flex flex-col w-64 gap-4">
                            {/* -------------------1.Name------------------ */}
                            <div>
                                <label
                                    htmlFor="nameAdd"
                                    className="block mb-1 text-sm font-medium text-slate-900 dark:text-white"
                                >
                                    Name
                                </label>
                                <span className="text-sm text-gray-500">
                                    Prakash Chaudhary
                                </span>
                            </div>
                            {/* -------------------2.Mobile Number------------------ */}
                            <div>
                                <label
                                    htmlFor="mobileNumberAdd"
                                    className="block mb-2 text-sm font-medium text-slate-900 dark:text-white"
                                >
                                    Mobile Number
                                </label>
                                <span className="flex items-center gap-2 text-sm text-gray-500">
                                    +91 8562145689
                                    <GiAlliedStar className="text-lg text-slate-400" />
                                </span>
                            </div>
                            {/* -------------------3.Email------------------ */}
                            <div>
                                <label
                                    htmlFor="helper-text"
                                    className="block mb-2 text-sm font-medium text-gray-900 dark:text-white"
                                >
                                    Email
                                </label>
                                <span className="flex items-center gap-2 text-sm text-gray-500">
                                    <EMAIL>
                                    <GiAlliedStar className="text-lg text-slate-400" />
                                </span>
                            </div>
                        </div>
                        {/* -------------------Second Column------------------ */}
                        <div className="flex flex-col gap-4 pt-4 lg:pt-0 md:pt-4">
                            {/* -------------------1.Designation------------------ */}
                            <div>
                                <label
                                    htmlFor="designation"
                                    className="block mb-2 text-sm font-medium text-gray-900 dark:text-white"
                                >
                                    Designation
                                </label>
                                <span className="text-sm text-gray-500">
                                    Sales Manager
                                </span>
                            </div>
                            {/* -------------------2.Official Number------------------ */}
                            <div>
                                <label
                                    htmlFor="officialNumberAdd"
                                    className="block mb-2 text-sm font-medium text-slate-900 dark:text-white"
                                >
                                    Official Number
                                </label>
                                <span className="flex items-center gap-2 text-sm text-slate-500">
                                    +91 8562145689
                                    <GiAlliedStar className="text-lg text-blue-600" />
                                </span>
                            </div>
                            {/* -------------------2.Official Email------------------ */}
                            <div>
                                <label
                                    htmlFor="officialEmailAdd"
                                    className="block mb-2 text-sm font-medium text-slate-900 dark:text-white"
                                >
                                    Official Email
                                </label>
                                <span className="flex items-center gap-2 text-sm text-gray-500">
                                    +91 8562145689
                                    <GiAlliedStar className="text-lg text-blue-600" />
                                </span>
                            </div>
                        </div>
                    </div>
                    {/* -------------------1.Designation------------------ */}
                    <div className="pt-4">
                        <label
                            htmlFor="address"
                            className="block mb-2 text-sm font-medium text-gray-900 dark:text-white"
                        >
                            Address
                        </label>
                        <span className="text-sm text-gray-500">
                            PNO. 88562 Daga Petrol Pump, Bada Bazar, Near City
                            Hospital, Jaipur Road
                        </span>
                    </div>
                    <div className="w-full gap-10 pt-5 lg:flex sm:flex-row">
                        {/* -------------------First Column------------------ */}
                        <div className="flex flex-col w-64 gap-4">
                            {/* -------------------1.city------------------ */}
                            <div>
                                <label
                                    htmlFor="city"
                                    className="block mb-1 text-sm font-medium text-slate-900 dark:text-white"
                                >
                                    City
                                </label>
                                <span className="text-sm text-gray-500">
                                    Bikaner
                                </span>
                            </div>
                            {/* -------------------2.country------------------ */}
                            <div>
                                <label
                                    htmlFor="country"
                                    className="block mb-2 text-sm font-medium text-slate-900 dark:text-white"
                                >
                                    Country
                                </label>
                                <span className="flex items-center gap-2 text-sm text-gray-500">
                                    <GiAlliedStar className="text-lg text-slate-400" />
                                    India
                                </span>
                            </div>
                        </div>
                        {/* -------------------Second Column------------------ */}
                        <div className="flex flex-col gap-4 pt-4 lg:pt-0 md:pt-4">
                            {/* -------------------1.Designation------------------ */}
                            <div>
                                <label
                                    htmlFor="state"
                                    className="block mb-2 text-sm font-medium text-gray-900 dark:text-white"
                                >
                                    State
                                </label>
                                <span className="text-sm text-gray-500">
                                    Rajasthan
                                </span>
                            </div>
                            {/* -------------------2.Official Number------------------ */}
                            <div>
                                <label
                                    htmlFor="pinCode"
                                    className="block mb-2 text-sm font-medium text-slate-900 dark:text-white"
                                >
                                    PinCode
                                </label>
                                <span className="flex items-center gap-2 text-sm text-slate-500">
                                    334401
                                    <GiAlliedStar className="text-lg text-slate-400" />
                                </span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </>
    );
}
