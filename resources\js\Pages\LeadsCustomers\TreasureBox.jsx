import Main from "@/Layouts/Main";
import React from "react";
import TabBar from "./TabBar";
import { Head } from "@inertiajs/react";

export default function Index() {
    return (
        <Main>
            <Head title="Treasure Box" />
            <div className="relative p-2 overflow-hidden">
                {/* <div className="grid grid-cols-12 gap-2 p-0 border-none grid-2 flow-row-dense md:grid-cols-12 sm:grid-cols-1 bg-slate-100"> */}
                {/* <div className="hidden xl:col-span-2 lg:col-span-2 md:col-span-3 sm:col-span-3 lg:flex ">
                        <SideMenu />
                    </div>
                    <div className="relative pt-0 dark:text-gray-400 lg:p-0 xl:col-span-9 lg:col-span-8 md:col-span-12 col-span-full"> */}
                <TabBar />
                <h2>Treasure Box</h2>
                {/* </div> */}
                {/* </div> */}
            </div>
        </Main>
    );
}
