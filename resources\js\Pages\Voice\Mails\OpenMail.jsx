import Main from "@/Layouts/Main";
import React, { useState } from "react";
import SideMenu from "./SideMenu";
import TabBar from "../TabBar";
import {
    ButtonGroup,
    Button,
    Checkbox,
    Drawer,
    Modal,
    Table,
    Tooltip,
} from "flowbite-react";
import {
    button_custom,
    customDrawer,
    table_custom,
} from "@/Pages/Helpers/DesignHelper";
import { CiEdit, CiExport } from "react-icons/ci";
import {
    MdAutorenew,
    MdBlock,
    MdDeleteOutline,
    MdOutlineEdit,
    MdOutlineMoveToInbox,
    MdOutlineRemoveRedEye,
    MdOutlineVpnKey,
} from "react-icons/md";
import { IoClose } from "react-icons/io5";
import { TbColumns3 } from "react-icons/tb";
import { BiHide } from "react-icons/bi";
import { TiStarOutline } from "react-icons/ti";
import { RiCheckDoubleFill, RiDeleteBin6Line } from "react-icons/ri";
import Compose from "./Compose";
import { VscReply } from "react-icons/vsc";
import { showAsset } from "@/Pages/Helpers/Helper";
import { FaAngleLeft } from "react-icons/fa6";

export default function OpenMail() {
    const [openModal, setOpenModal] = useState(false);

    return (
        <Main>
            <div className="p-2 overflow-hidden">
                <TabBar />
                <div
                    className="grid grid-cols-12 gap-2 p-0 border-none grid-2 flow-row-dense md:grid-cols-12 sm:grid-cols-1 
                bg-slate-100 mt-2"
                >
                    <div className="hidden xl:col-span-2 lg:col-span-2 md:col-span-3 sm:col-span-3 lg:flex ">
                        <SideMenu />
                    </div>
                    <div className="relative pt-0 dark:text-gray-400 lg:p-0 xl:col-span-10 lg:col-span-10 md:col-span-12 col-span-full">
                        <div className=" rounded-md  h-full">
                            <div className="min-h-[80vh] max-h-[80vh] bg-white border rounded-lg">
                                <div className="flex justify-between p-2">
                                    <div className="flex gap-2">
                                        <Button
                                            className="ps-1"
                                            size="xs"
                                            color="gray"
                                            onClick={() =>
                                                window.history.back ()
                                            }
                                            theme={button_custom}
                                        >
                                            <div className="flex items-center text-xs">
                                                <FaAngleLeft className="text-slate-400 me-1" />
                                                <span>Back</span>
                                            </div>
                                        </Button>
                                        <Button
                                            className="ps-1"
                                            size="xs"
                                            color="gray"
                                            theme={button_custom}
                                        >
                                            <MdAutorenew className="text-slate-400" />
                                        </Button>
                                        <ButtonGroup>
                                            <Button
                                                theme={button_custom}
                                                size="xs"
                                                color="gray"
                                                // className="border-e-0 rounded-e-none"
                                            >
                                                <div className="flex items-center gap-1">
                                                    <BiHide className="text-slate-400" />
                                                    <span className="text-xs">
                                                        Mark Unread
                                                    </span>
                                                </div>
                                            </Button>
                                            <Button
                                                className="ps-1"
                                                size="xs"
                                                color="gray"
                                                theme={button_custom}
                                            >
                                                <div className="flex items-center gap-1">
                                                    <MdOutlineMoveToInbox className="text-slate-400" />
                                                    <span className="text-xs">
                                                        Archive
                                                    </span>
                                                </div>
                                            </Button>
                                            <Button
                                                className="ps-1"
                                                size="xs"
                                                color="gray"
                                                theme={button_custom}
                                            >
                                                <div className="flex items-center gap-1">
                                                    <TiStarOutline className="text-slate-400" />
                                                    <span className="text-xs">
                                                        Pinned
                                                    </span>
                                                </div>
                                            </Button>
                                            <Button
                                                className="ps-1"
                                                size="xs"
                                                color="gray"
                                                theme={button_custom}
                                            >
                                                <div className="flex items-center gap-1">
                                                    <MdBlock className="text-slate-400" />
                                                    <span className="text-xs">
                                                        Spam
                                                    </span>
                                                </div>
                                            </Button>
                                            <Button
                                                className="ps-1"
                                                size="xs"
                                                color="gray"
                                                theme={button_custom}
                                            >
                                                <div className="flex items-center gap-1">
                                                    <RiDeleteBin6Line className="text-slate-400" />
                                                    <span className="text-xs">
                                                        Delete
                                                    </span>
                                                </div>
                                            </Button>
                                        </ButtonGroup>
                                    </div>
                                    <div className="">
                                        <Button
                                            theme={button_custom}
                                            size="xs"
                                            color="gray"
                                            type="button"
                                            onClick={() => setOpenModal(true)}
                                        >
                                            <div className="flex items-center gap-1">
                                                <CiEdit className="text-slate-500" />
                                                <span className="text-xs">
                                                    Compose
                                                </span>
                                            </div>
                                        </Button>
                                    </div>
                                </div>
                                <div className="px-2">
                                    <h2 className="text-xl font-medium">
                                        Welcome to RapBooster
                                    </h2>
                                    <div className="flex items-center justify-between">
                                        <span className="text-sm">
                                            Rudra Choudhary {"<"}
                                            <EMAIL>{">"}
                                        </span>
                                        <div className="flex items-center gap-2">
                                            <span className="text-xs text-slate-500">
                                                30/04/24 | 02:46 PM{" "}
                                            </span>
                                            <Button
                                                className="ps-1"
                                                size="xs"
                                                color="gray"
                                                theme={button_custom}
                                            >
                                                <VscReply className="text-slate-400" />
                                            </Button>
                                        </div>
                                    </div>
                                </div>
                                <div className="overflow-x-auto bg-slate-50 border rounded-lg p-1 m-2 flex flex-col justify-center items-center">
                                    <div className="w-2/5">
                                        <div className="flex items-center gap-2 bg-white p-2 rounded-t-lg">
                                            <img
                                                className="h-9"
                                                src={showAsset(
                                                    "/assets/img/RBBlueFull.png"
                                                )}
                                                alt=""
                                            />
                                        </div>
                                        <div className=" flex flex-col items-center rounded-b-lg">
                                            <div className="p-2 bg-white my-2 rounded-lg flex flex-col items-center">
                                                <img
                                                    className=""
                                                    src={showAsset(
                                                        "/assets/img/mailviewopen.png"
                                                    )}
                                                    alt=""
                                                />
                                                <div className="mt-2">
                                                    <h1 className="text-xl font-medium">
                                                        Your One-Stop Solution
                                                        for Digital Growth
                                                    </h1>
                                                    <p>
                                                        At RapBooster, we offer
                                                        a wide range of
                                                        cutting-edge digital
                                                        tools and services
                                                        designed to propel your
                                                        business forward.
                                                        Whether you are a
                                                        startup or an
                                                        established enterprise,
                                                        our platform provides
                                                        everything you need to
                                                        enhance your digital
                                                        presence and optimize
                                                        your marketing
                                                        strategies. From SEO
                                                        optimization and social
                                                        media management to
                                                        advanced analytics and
                                                        content creation.
                                                    </p>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div className="absolute bottom-0 w-full p-3 bg-white border rounded-b-lg float-end">
                                <div className="flex flex-wrap justify-between gap-2 lg:gap-0 md:gap-0">
                                    <div className="flex items-center gap-3">
                                        <div className="text-sm text-gray-400 ">
                                            Showing 1 to 25 of 62 entries
                                        </div>
                                        <div>
                                            <select
                                                id="countries"
                                                className="block p-1 text-xs text-gray-900 border border-gray-300 rounded bg-gray-50 focus:ring-blue-500 focus:border-blue-500 ps-2 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-blue-500 dark:focus:border-blue-500"
                                            >
                                                <option
                                                    value={10}
                                                    defaultValue={10}
                                                >
                                                    10
                                                </option>
                                                <option value={15}>15</option>
                                                <option value={20}>20</option>
                                                <option value={25}>25</option>
                                                <option value={30}>30</option>
                                            </select>
                                        </div>
                                    </div>
                                    <div className="flex">
                                        <Button className="px-2 text-xs text-gray-400 border rounded ">
                                            Previous
                                        </Button>
                                        <Button className="border text-white border-blue-600 bg-blue-600 px-2.5 rounded-1 rounded-0 text-sm ">
                                            1
                                        </Button>
                                        <Button className="border text-blue-600 px-2.5 rounded-1 rounded-0 text-xs ">
                                            2
                                        </Button>
                                        <Button className="px-2 text-xs text-blue-600 border rounded ">
                                            Next
                                        </Button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <Modal
                    size="5xl"
                    show={openModal}
                    onClose={() => setOpenModal(false)}
                >
                    <ModalHeader className="bg-slate-100 p-2">
                        {/* <div className="flex items-center gap-2">
                            <MdOutlineEdit />
                            <h4 className="text-lg">Edit Role</h4>
                        </div> */}
                    </ModalHeader>
                    <ModalBody className="px-4 py-3 rounded-b-lg bg-slate-100">
                        <Compose></Compose>
                    </ModalBody>
                </Modal>
            </div>
        </Main>
    );
}

