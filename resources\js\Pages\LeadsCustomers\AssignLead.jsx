import DrawerPagination from '@/Components/HelperComponents/DrawerPagination';
import NoRecord from '@/Components/HelperComponents/NoRecord';
import PerPageDropdown from '@/Components/HelperComponents/PerPageDropdown';
import SearchBar from '@/Global/SearchBar';
import useFetch from '@/Global/useFetch';
import { button_custom, table_custom } from '@/Pages/Helpers/DesignHelper';
import { router, useForm } from '@inertiajs/react';
import { Button, Table, Tooltip } from 'flowbite-react';
import { useEffect, useState } from 'react';
import { MdOutlinePersonAddAlt } from 'react-icons/md';
import { PiCaretUpDownBold } from 'react-icons/pi';

export default function AssignLead({ onClose, leadIds, setSelectedLeads }) {
    const [agentsData, setAgentsData] = useState(null);
    const [selectedAgents, setSelectedAgents] = useState([]);
    const { data: allAgentsData, loading: allAgentsLoading } = useFetch(route('leads.getLeadUserAssignData'));

    const { data, setData, post, processing } = useForm({
        leads: leadIds,
        assignId: selectedAgents,
        type: 'single',
    });

    useEffect(() => {
        if (allAgentsData)
            setAgentsData(allAgentsData);
    }, [allAgentsData]);

    const handleUserAssign = () => {
        post(route('leads.assign'), {
            onSuccess: () => {
                setSelectedLeads([]);
                onClose();
                router.get(route('leadsAndCustomers.leads.index'));
            },
        });
    }

    return (
        <>
            <div className="bg-white p-2">
                <div>
                    <h3 className="text-lg font-medium">
                        {leadIds.length} Lead{(leadIds.length > 1 ? 's' : '')} Selected
                        {(data.type == 'multiple' && selectedAgents.length > 0) ? ` | Each selected user will get ${Math.floor(leadIds.length / selectedAgents.length)} leads` : ""}
                        {(data.type == 'multiple' && selectedAgents.length > 0 && leadIds.length % selectedAgents.length !== 0) ? ` (${leadIds.length % selectedAgents.length} leads remaining)` : ""}
                    </h3>
                </div>
            </div>
            <div className='bg-white my-2 flex px-2.5'>
                <div className="p-2">
                    <div className="relative">
                        <SearchBar
                            url={route('leads.getLeadUserAssignData')}
                            setData={setAgentsData}
                        />
                    </div>
                </div>
                <div className='flex gap-3'>
                    {
                        leadIds.length > 1 ? (
                            <>
                                <label htmlFor="one" className='flex items-center gap-2'>
                                    <input
                                        type="radio"
                                        name='assignLead'
                                        id='one'
                                        defaultChecked
                                        onChange={() => setData('type', 'single')}
                                    />
                                    <span>Assign To One</span>
                                </label>
                                <label htmlFor="multiple" className='flex items-center gap-2'>
                                    <input
                                        type="radio"
                                        name='assignLead'
                                        id='multiple'
                                        onChange={() => setData('type', 'multiple')}
                                    />
                                    <span>Assign To Multiple </span>
                                </label>
                            </>
                        ) : <></>
                    }
                </div>
                {
                    data?.type == 'multiple' && selectedAgents.length > 0 && leadIds.length > 1 ? (
                        <div className='align-middle mt-3 ms-4'>
                            <Button
                                theme={
                                    button_custom
                                }
                                color="Fuchsia_custom"
                                size="xs"
                                onFocus={
                                    () => { setData('assignId', selectedAgents); }
                                }
                                onMouseEnter={
                                    () => { setData('assignId', selectedAgents); }
                                }
                                disabled={processing}
                                isProcessing={processing}
                                onClick={() => {
                                    handleUserAssign();
                                }}

                            >
                                <div className="flex items-center gap-1">
                                    <MdOutlinePersonAddAlt />
                                    <span className="text-sm">Assign</span>
                                </div>
                            </Button>
                        </div>
                    ) : <></>
                }
            </div>
            <div className="p-2 flex flex-col justify-between">
                <div className="bg-white h-full rounded-lg border">
                    {/* documents table */}
                    <div className="overflow-x-auto">
                        <Table hoverable theme={table_custom}>
                            <Table.Head className=" bg-slate-100">
                                <Table.HeadCell>
                                    <div className="flex items-center justify-between gap-2">
                                        <h3>Agents Name</h3>
                                        <PiCaretUpDownBold />
                                    </div>
                                </Table.HeadCell>
                                <Table.HeadCell>Today Leads</Table.HeadCell>
                                <Table.HeadCell>
                                    Current Month Leads
                                </Table.HeadCell>
                                <Table.HeadCell>Total Leads</Table.HeadCell>
                                <Table.HeadCell>Due Tasks</Table.HeadCell>
                                <Table.HeadCell className='w-56'></Table.HeadCell>
                            </Table.Head>

                            <Table.Body className="divide-y">
                                {
                                    agentsData && agentsData.leadUserAssign.data.length > 0 ? agentsData?.leadUserAssign?.data.map((agent, index) => {
                                        return (
                                            <Table.Row className="bg-white dark:border-gray-700 dark:bg-gray-800" key={index}>
                                                <Table.Cell>
                                                    <div className='flex items-center gap-2'>
                                                        <Tooltip
                                                            className="p-1 px-2 bg-slate-700"
                                                            content={agent.name || "N/A"}
                                                        >
                                                            <div className="relative inline-block">
                                                                <img
                                                                    src={agent.image || "https://img.freepik.com/free-vector/businessman-character-avatar-isolated_24877-60111.jpg?t=st=1720614745~exp=1720618345~hmac=38ff343a9443982af738325ad4d54d1d39ba540be4c28013b03cf063347d255e&w=200"}
                                                                    alt="Profile"
                                                                    className="w-9 h-9 rounded-full"
                                                                />
                                                                <div
                                                                    className="absolute top-0 right-0 w-3 h-3 bg-green-500 rounded-full border-2 border-white"
                                                                ></div>
                                                            </div>
                                                        </Tooltip>
                                                        {agent.name || "N/A"} ({agent.id})
                                                    </div>
                                                </Table.Cell>

                                                <Table.Cell className="whitespace-nowrap dark:text-white">
                                                    {agent.todayLeads}
                                                </Table.Cell>
                                                <Table.Cell>
                                                    {agent.currentMonthLeads}
                                                </Table.Cell>
                                                <Table.Cell>
                                                    {agent.totalLeads}
                                                </Table.Cell>
                                                <Table.Cell>{agent.dueLeads || "-"}</Table.Cell>
                                                <Table.Cell className="text-nowrap">
                                                    {
                                                        data.type == 'multiple' ?
                                                            <input
                                                                type="checkbox"
                                                                name="assignLead"
                                                                className='border-gray-400 rounded'
                                                                id="assignLead"
                                                                checked={selectedAgents.includes(agent.id)}
                                                                onChange={(e) => {
                                                                    if (e.target.checked) {
                                                                        setSelectedAgents([...selectedAgents, agent.id]);
                                                                    } else {
                                                                        setSelectedAgents(selectedAgents.filter(id => id !== agent.id));
                                                                    }
                                                                }}
                                                            /> :
                                                            <Button
                                                                theme={
                                                                    button_custom
                                                                }
                                                                color="Fuchsia_custom"
                                                                size="xs"
                                                                onFocus={
                                                                    () => {
                                                                        setSelectedAgents([agent?.id]);
                                                                        setData('assignId', [agent?.id]);
                                                                    }
                                                                }
                                                                onMouseEnter={
                                                                    () => {
                                                                        setSelectedAgents([agent?.id]);
                                                                        setData('assignId', [agent?.id]);
                                                                    }
                                                                }
                                                                disabled={processing}
                                                                isProcessing={processing}
                                                                onClick={() => {
                                                                    handleUserAssign();
                                                                }}

                                                            >
                                                                <div className="flex items-center gap-1">
                                                                    <MdOutlinePersonAddAlt />
                                                                    <span className="text-sm">Assign</span>
                                                                </div>
                                                            </Button>
                                                    }
                                                </Table.Cell>
                                            </Table.Row>
                                        );
                                    }) :
                                        <Table.Row className="bg-white dark:border-gray-700 dark:bg-gray-800">
                                            <Table.Cell
                                                colSpan={11}
                                                className="text-center"
                                            >
                                                <NoRecord loading={allAgentsLoading} type='circle' spinnerSize='md' />
                                            </Table.Cell>
                                        </Table.Row>
                                }
                            </Table.Body>
                        </Table>
                    </div>
                </div>


                <div className="w-full p-3 bg-white">
                    <div className="flex flex-wrap justify-between gap-2 lg:gap-0 md:gap-0">
                        <div className="flex items-center gap-4">
                            <PerPageDropdown
                                getDataFields={null}
                                routeName={"leads.getLeadUserAssignData"}
                                data={allAgentsData?.leadUserAssign}
                                showPerPageDropdown={false}
                            />
                        </div>
                        <DrawerPagination tableData={agentsData?.leadUserAssign} setData={setAgentsData} />
                    </div>
                </div>
            </div>
        </>
    )
}
