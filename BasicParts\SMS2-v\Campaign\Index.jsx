import { <PERSON>, <PERSON>, router } from "@inertiajs/react";
import {
    <PERSON><PERSON>,
    But<PERSON>,
    Checkbox,
    Drawer,
    DrawerHeader,
    DrawerItems,
    Progress,
    Table,
    Tooltip
} from "flowbite-react";
import { useState } from "react";
import { IoMdAdd } from "react-icons/io";
import {
    MdOutlineCampaign,
    MdOutlineEdit
} from "react-icons/md";
import { PiCaretUpDownBold } from "react-icons/pi";
import { TbFileTypeCsv, TbHandStop, TbReport } from "react-icons/tb";

import {
    changeFlag,
    convertDateFormat,
    isDateGreaterThanToday,
} from "@/Pages/Helpers/Helper";

import NoRecord from "@/Components/HelperComponents/NoRecord";
import Paginate from "@/Components/HelperComponents/Paginate";
import PerPageDropdown from "@/Components/HelperComponents/PerPageDropdown";
import $ from "jquery";
import { MdDeleteOutline } from "react-icons/md";

import ConfirmBox from "@/Components/HelperComponents/ConfirmBox";
import {
    button_custom,
    customDrawer,
    table_custom
} from "@/Pages/Helpers/DesignHelper";
import { FaRegTrashCan } from "react-icons/fa6";
import { FiPlay } from "react-icons/fi";
import { IoPauseOutline } from "react-icons/io5";
import { RiDeleteBin6Line } from "react-icons/ri";
import Details from "./Details";
import View from "./View";
import SortLink from "@/Components/SortLink";
import MainSms2 from "../MainSms2";

export default function Index({ collection }) {
    const baseRoute = 'sms2';
    const getData = collection.getData;
    const currentPageRoute = baseRoute + '.campaign.index';
    const campaigns = collection.campaigns;

    const [disabledBtnId, setDisabledBtnId] = useState(null);
    const [isWarningConfirmOpen, setWarningConfirmOpen] = useState(false);
    // ----------------Edit --------------------
    const [isDetailsOpen, setIsDetailsOpen] = useState(false);
    const [campaignID, setCampaignID] = useState();
    const [checkValue, setCheckValue] = useState([]);
    const [Checked, setChecked] = useState([]);

    const [isMultipleDeleteConfirmOpen, SetIsMultipleDeleteConfirmOpen] = useState(false);
    const [isOpenDetail, setIsOpenDetail] = useState(false);
    const statusCodes = {
        0: { bg: "gray", text: "Draft" },
        1: { bg: "success", text: "Started" },
        2: { bg: "pink", text: "Paused" },
        3: { bg: "success", text: "Completed" },
        4: { bg: "failure", text: "Terminated" },
        6: { bg: "indigo", text: "Scheduled" },
    };



    const handleCheckboxChange = (id) => {
        setChecked((prevSelected) =>
            prevSelected.includes(id)
                ? prevSelected.filter((gwId) => gwId !== id) // Deselect if already selected
                : [...prevSelected, id] // Add if not selected
        );
    };

    const handleSelectAll = (e) => {
        if (e.target.checked) {
            if (Checked.length === campaigns.length) {
                setChecked([]);
            } else {
                setChecked(campaigns?.data?.map((campaigns) => campaigns.id));
            }
        } else {
            setChecked([]);
        }
    };


    function deleteRecords() {
        if (Checked.length > 0) {
            router.delete(route(baseRoute + '.campaign.destroy', { campaign: Checked.toLocaleString() }));
        }
    }
    const HandleChangeFlag = (Column, id, flag) => {
        let table = "mail_campaign";
        changeFlag(table, Column, id, flag);
    };

    const handleMultipleDelete = (res) => {
        if (res) {
            deleteRecords(); // Your function to delete records (ensure it handles the selection).
        }
        // Reset checkboxes and state
        setChecked([]); // Clear selected checkboxes
        SetIsMultipleDeleteConfirmOpen(false); // Close the confirmation dialog
        // setIsCheckAll(false); // Reset the "Select All" checkbox
    };

    function handleWarningConfirmBoxResult(result) {
        if (checkValue.length > 0 && result) {
            setWarningConfirmOpen(false);
            setCheckValue([]);
            HandleChangeFlag(
                "status",
                checkValue,
                4
            )
        } else {
            setCheckValue([]);
        }
        setWarningConfirmOpen(false);
    }

    const tableHeadItems = [
        { name: 'Start Time', column: 'startDatetime', isLink: true },
        { name: 'Name', column: 'name', isLink: true },
        { name: 'Progress', column: '' },
        { name: 'Status', column: 'status', isLink: true },
        { name: 'Sleep After', column: 'sleepAfterMsgs', isLink: true },
        { name: 'Sleep For', column: 'sleepForSeconds', isLink: true },
        { name: 'User', column: 'user_id', isLink: true },
        { name: 'Actions', column: '' }
    ];
    return (
        <MainSms2>
            <Head title="Campaigns" />
            <div className="px-2 pb-2 rounded-lg">
                <div className="h-fit bg-white rounded border overflow-auto w-full mt-2.5">
                    <div className="flex justify-between p-2">
                        <div>
                            {/* <Button.Group className=""> */}
                            {
                                collection.can_delete &&
                                <Button
                                    size="xs"
                                    color="gray"
                                    theme={button_custom}
                                    onClick={() =>
                                        SetIsMultipleDeleteConfirmOpen(true)
                                    }
                                >
                                    <div className="flex items-center gap-1">
                                        <MdDeleteOutline className="text-slate-500" />
                                        <span className="text-xs">Delete</span>
                                    </div>
                                </Button>
                            }
                        </div>
                        <div className="flex items-center gap-2">

                            {
                                collection.can_add &&
                                <div>
                                    <Button
                                        as={Link}
                                        size="xs"
                                        color="gray"
                                        theme={button_custom}
                                        href={route(baseRoute + ".campaign.create")}

                                    >
                                        <div className="flex items-center gap-1 text-xs">
                                            <IoMdAdd className="text-sm text-slate-500" />
                                            <span>Add</span>
                                        </div>
                                    </Button>
                                </div>
                            }
                        </div>
                    </div>
                    <div className="h-full">
                        <div className="overflow-x-auto bg-white border rounded-lg text-nowrap">
                            <Table hoverable theme={table_custom}>
                                <Table.Head className="bg-slate-100">
                                    <Table.HeadCell>
                                        <Checkbox
                                            color="blue"
                                            onChange={handleSelectAll}
                                            checked={Checked.length == campaigns?.data?.length}
                                        />
                                    </Table.HeadCell>

                                    {tableHeadItems.map((item, index) => (
                                        <Table.HeadCell key={index}>
                                            {item.isLink ?
                                                <SortLink showName={item.name} routeName={currentPageRoute} column={item.column} params={getData} sortType={getData?.sort == "asc" ? "desc" : "asc"} />
                                                : item.name}
                                        </Table.HeadCell>
                                    ))}

                                </Table.Head>
                                <Table.Body className="divide-y">
                                    {campaigns.data.length != 0 ? (
                                        campaigns.data.map((campaign, k) => {
                                            return (
                                                <Table.Row

                                                    className="items-center bg-white dark:border-gray-700 dark:bg-gray-800"
                                                    key={"campaign" + k}
                                                >
                                                    <Table.Cell>
                                                        <Checkbox
                                                            color={"blue"}
                                                            className="rowCheckBox"
                                                            checked={Checked.includes(campaign.id)}
                                                            onChange={() => handleCheckboxChange(campaign.id)}
                                                        />
                                                    </Table.Cell>
                                                    <Table.Cell>
                                                        <div className="flex flex-col">
                                                            <span>
                                                                {
                                                                    convertDateFormat(
                                                                        campaign.startDatetime
                                                                        , "date")
                                                                }
                                                            </span>
                                                            <span>
                                                                {
                                                                    convertDateFormat(
                                                                        campaign.startDatetime
                                                                        , "time")
                                                                }
                                                            </span>
                                                        </div>
                                                    </Table.Cell>
                                                    <Table.Cell
                                                        onClick={() => {
                                                            setIsOpenDetail((collection.can_view) ? true : false);
                                                            setCampaignID({
                                                                id: campaign.id,
                                                                name: campaign.name,
                                                                status: campaign.status,
                                                            });
                                                        }}
                                                        className="text-blue-600 cursor-pointer "
                                                    >
                                                        {campaign.name}
                                                    </Table.Cell>
                                                    <Table.Cell>
                                                        <div className="flex flex-col gap-1 py-1">
                                                            <Progress
                                                                color="blue"
                                                                progress={
                                                                    (campaign.completedContacts == 0) ? 0 :
                                                                        ((campaign.completedContacts /
                                                                            campaign.totalContacts) *
                                                                            100)
                                                                }
                                                            />
                                                            <span className="">
                                                                {
                                                                    campaign.completedContacts
                                                                }
                                                                &nbsp;Out
                                                                of&nbsp;
                                                                {
                                                                    campaign.totalContacts
                                                                }
                                                                &nbsp;Completed
                                                            </span>
                                                        </div>
                                                    </Table.Cell>
                                                    <Table.Cell>
                                                        <div className="flex">
                                                            {isDateGreaterThanToday(
                                                                campaign.startDatetime
                                                            ) ? (
                                                                [0].includes(
                                                                    campaign.status
                                                                ) ? (
                                                                    <Badge
                                                                        className="cursor-default"
                                                                        color={
                                                                            statusCodes[6]
                                                                                .bg
                                                                        }
                                                                    >
                                                                        {
                                                                            statusCodes[6]
                                                                                .text
                                                                        }
                                                                    </Badge>
                                                                ) : (
                                                                    <Badge
                                                                        className="cursor-default"
                                                                        color={
                                                                            statusCodes[
                                                                                campaign
                                                                                    .status
                                                                            ].bg
                                                                        }
                                                                    >
                                                                        {
                                                                            statusCodes[
                                                                                campaign
                                                                                    .status
                                                                            ]
                                                                                .text
                                                                        }
                                                                    </Badge>
                                                                )
                                                            ) : (
                                                                <Badge
                                                                    className="cursor-default"
                                                                    color={
                                                                        statusCodes[
                                                                            campaign
                                                                                .status
                                                                        ].bg
                                                                    }
                                                                >
                                                                    {
                                                                        statusCodes[
                                                                            campaign
                                                                                .status
                                                                        ].text
                                                                    }
                                                                </Badge>
                                                            )}
                                                        </div>
                                                    </Table.Cell>
                                                    <Table.Cell>
                                                        {
                                                            campaign.sleepAfterMsgs
                                                        }
                                                        sec.
                                                    </Table.Cell>
                                                    <Table.Cell>
                                                        {
                                                            campaign.sleepForSeconds
                                                        }
                                                        sec.
                                                    </Table.Cell>
                                                    <Table.Cell>
                                                        {campaign.user.username}
                                                    </Table.Cell>
                                                    <Table.Cell>
                                                        <div className="flex items-center gap-3 ">
                                                            {collection.can_edit && [0, 2].includes(campaign.status) &&
                                                                <Tooltip placement="left-end"
                                                                    content="Edit Campaign"
                                                                >
                                                                    <Button
                                                                        theme={
                                                                            button_custom
                                                                        }
                                                                        color="success"
                                                                        size="xs"
                                                                        as={Link}
                                                                        href={route(baseRoute + '.campaign.edit', { campaign: campaign.id })}
                                                                    >
                                                                        <MdOutlineEdit className="text-sm" />
                                                                        <span className="text-xs ms-1">
                                                                            Edit
                                                                        </span>
                                                                    </Button>
                                                                </Tooltip>
                                                            }
                                                            {[3].includes(campaign.status)
                                                                ? (
                                                                    <>
                                                                        <Tooltip
                                                                            placement="left"
                                                                            content={"CSV Report Download"}
                                                                        >

                                                                            {(campaign.reportUrl == null) ?
                                                                                <Button
                                                                                    theme={
                                                                                        button_custom
                                                                                    }
                                                                                    onClick={() => setDisabledBtnId(`export${campaign.id}`)}
                                                                                    color="blue"
                                                                                    size="xs"
                                                                                    isProcessing={campaign.isReportReady == 0}
                                                                                    disabled={
                                                                                        ((campaign.isReportReady == 0)
                                                                                            ||
                                                                                            (disabledBtnId == `export${campaign.id}`)
                                                                                        ) ? true : false
                                                                                    }
                                                                                >
                                                                                    <TbFileTypeCsv className="text-sm" />
                                                                                    <span className="text-xs ms-1">
                                                                                        Export
                                                                                    </span>
                                                                                </Button>
                                                                                :
                                                                                <Button
                                                                                    theme={
                                                                                        button_custom
                                                                                    }
                                                                                    onClick={() => setDisabledBtnId(`export${campaign.id}`)}
                                                                                    color="blue"
                                                                                    size="xs"
                                                                                    isProcessing={campaign.isReportReady == 0}
                                                                                    disabled={
                                                                                        ((campaign.isReportReady == 0)
                                                                                            ||
                                                                                            (disabledBtnId == `export${campaign.id}`)
                                                                                        ) ? true : false
                                                                                    }
                                                                                >
                                                                                    <TbFileTypeCsv className="text-sm" />
                                                                                    <span className="text-xs ms-1">
                                                                                        Export
                                                                                    </span>
                                                                                </Button>
                                                                            }

                                                                        </Tooltip>

                                                                        {campaign.reportRequest ==
                                                                            0 ? (
                                                                            <Tooltip
                                                                                content={" Generate Report"}
                                                                            >
                                                                                <Button
                                                                                    onClick={() => {
                                                                                        setDisabledBtnId(`generateReport${campaign.id}`)
                                                                                        HandleChangeFlag("reportRequest", campaign.id, 1)
                                                                                    }
                                                                                    }
                                                                                    theme={
                                                                                        button_custom
                                                                                    }
                                                                                    color="blue"
                                                                                    disabled={(disabledBtnId == `generateReport${campaign.id}`) ? true : false}
                                                                                    size="xs"
                                                                                >
                                                                                    <TbReport className="text-sm" />
                                                                                    <span className="text-xs ms-1">
                                                                                        Generate
                                                                                        Report
                                                                                    </span>
                                                                                </Button>
                                                                            </Tooltip>
                                                                        ) : (
                                                                            <>

                                                                            </>
                                                                        )}
                                                                    </>
                                                                ) : (
                                                                    <></>
                                                                )}

                                                            {[1].includes(
                                                                campaign.status
                                                            ) ? (
                                                                <Tooltip
                                                                    placement="left"
                                                                    content={"Pause Campaign"}
                                                                >
                                                                    <Button
                                                                        onClick={() => {
                                                                            setDisabledBtnId(`pause${campaign.id}`);
                                                                            HandleChangeFlag("status", campaign.id, 2)
                                                                        }
                                                                        }
                                                                        disabled={(disabledBtnId == `pause${campaign.id}`) ? true : false}
                                                                        theme={
                                                                            button_custom
                                                                        }
                                                                        color="Fuchsia_custom"
                                                                        size="xs"
                                                                    >
                                                                        <IoPauseOutline className="text-sm" />
                                                                        <span className="text-xs ms-1">
                                                                            Pause
                                                                        </span>
                                                                    </Button>
                                                                </Tooltip>
                                                            ) : (
                                                                <></>
                                                            )}
                                                            {[
                                                                0, 2,
                                                            ].includes(
                                                                campaign.status
                                                            ) ? (
                                                                <Tooltip content="Play">
                                                                    <Button
                                                                        onClick={() => {
                                                                            setDisabledBtnId(`play${campaign.id}`);
                                                                            HandleChangeFlag(
                                                                                "status",
                                                                                campaign.id,
                                                                                1
                                                                            )
                                                                        }
                                                                        }
                                                                        disabled={(disabledBtnId == `play${campaign.id}`) ? true : false}
                                                                        theme={
                                                                            button_custom
                                                                        }
                                                                        color="orange"
                                                                        size="xs"
                                                                    >
                                                                        <FiPlay className="text-sm" />
                                                                        <span className="text-xs ms-1">
                                                                            Play
                                                                        </span>
                                                                    </Button>
                                                                </Tooltip>
                                                            ) : (
                                                                <></>
                                                            )}
                                                            {[
                                                                1, 2,
                                                            ].includes(
                                                                campaign.status
                                                            ) ? (
                                                                <Tooltip content="Stop">
                                                                    <Button
                                                                        onClick={() => {
                                                                            setWarningConfirmOpen(true);
                                                                            setCheckValue([campaign.id]);
                                                                            setDisabledBtnId(`stop${campaign.id}`);
                                                                        }
                                                                        }
                                                                        disabled={(disabledBtnId == `stop${campaign.id}`) ? true : false}
                                                                        theme={
                                                                            button_custom
                                                                        }
                                                                        color="Pink_custom"
                                                                        size="xs"
                                                                    >
                                                                        <TbHandStop className="text-sm" />
                                                                        <span className="text-xs ms-1">
                                                                            Stop
                                                                        </span>
                                                                    </Button>
                                                                </Tooltip>
                                                            ) : (
                                                                <></>
                                                            )}
                                                            {
                                                                ([
                                                                    1
                                                                ].includes(
                                                                    campaign.status
                                                                )) ? <></> :
                                                                    collection.can_delete &&
                                                                    <Tooltip content="Delete">
                                                                        <Button
                                                                            theme={
                                                                                button_custom
                                                                            }
                                                                            disabled={(disabledBtnId == `delete${campaign.id}`) ? true : false}
                                                                            color="failure"
                                                                            size="xs"
                                                                            onClick={() => { setChecked([campaign.id]); SetIsMultipleDeleteConfirmOpen(true); }}
                                                                        >
                                                                            <RiDeleteBin6Line className="text-sm" />
                                                                            <span className="text-xs ms-1">
                                                                                Delete
                                                                            </span>
                                                                        </Button>
                                                                    </Tooltip>
                                                            }
                                                        </div>
                                                    </Table.Cell>
                                                </Table.Row>
                                            );
                                        })
                                    ) : (
                                        <Table.Row>
                                            <Table.Cell colSpan={tableHeadItems.length + 1}>
                                                <NoRecord />
                                            </Table.Cell>
                                        </Table.Row>
                                    )}
                                </Table.Body>
                            </Table>
                        </div>
                    </div>

                    <div className="w-full p-3 bg-white">
                        <div className="flex flex-wrap justify-between gap-2 lg:gap-0 md:gap-0">
                            <div className="flex items-center gap-4">
                                <PerPageDropdown
                                    getDataFields={getData ?? null}
                                    routeName={baseRoute + ".campaign.index"}
                                    data={campaigns}
                                />
                            </div>
                            <Paginate tableData={campaigns} />
                        </div>
                    </div>
                </div>
            </div>

            {
                isDetailsOpen ? (
                    <Drawer
                        theme={customDrawer}
                        className="w-full lg:w-4/6 md:w-4/5"
                        open={isDetailsOpen}
                        onClose={() => setIsDetailsOpen(false)}
                        position="right"
                    >
                        <DrawerHeader
                            titleIcon={MdOutlineCampaign}
                            title={<span>Details ({campaignID.name})</span>}
                        />
                        <DrawerItems className="py-2">
                            <View campaign={campaignID.id}></View>
                        </DrawerItems>
                    </Drawer>
                ) : (
                    <></>
                )
            }
            {
                isOpenDetail ? (
                    <Drawer
                        theme={customDrawer}
                        className="w-full lg:w-5/6 md:w-4/5"
                        open={isOpenDetail}
                        onClose={() => setIsOpenDetail(false)}
                        position="right"
                    >
                        <DrawerHeader
                            titleIcon={MdOutlineCampaign}
                            title={
                                <div className="flex items-center gap-2">
                                    Campaign Details ({campaignID.name})

                                </div>
                            }
                        />
                        <DrawerItems className="pb-2">
                            <Details campaign={campaignID.id}></Details>
                        </DrawerItems>
                    </Drawer>
                ) : (
                    <></>
                )
            }

            {/* delete confirm box popup */}
            {isMultipleDeleteConfirmOpen &&
                <ConfirmBox
                    isOpen={isMultipleDeleteConfirmOpen}
                    onClose={() => SetIsMultipleDeleteConfirmOpen(false)} // Close the confirm box
                    onAction={handleMultipleDelete} // Handle the user's choice
                    title="Are you sure you want to delete this?"
                    message="This action cannot be undone."
                    confirmText="Yes, Delete!"
                    cancelText="No, Keep It"
                    confirmColor="orange"
                    cancelColor="gray"
                    icon={<FaRegTrashCan />}
                />
            }
            {/* warning confirm box popup */}
            {
                isWarningConfirmOpen &&
                <ConfirmBox
                    isOpen={isWarningConfirmOpen}
                    onClose={() => setWarningConfirmOpen(false)} // Close the confirm box
                    onAction={handleWarningConfirmBoxResult} // Handle the user's choice
                    title="Terminate Campaign ?"
                    message="Are you sure you want to terminate this campaign?"
                    confirmText="Yes, Terminate!"
                    cancelText="No, Keep It"
                    confirmColor="orange"
                    cancelColor="gray"
                    icon={<FaRegTrashCan />}
                />
            }
        </MainSms2 >
    );
}
