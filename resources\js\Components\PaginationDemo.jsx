import React from 'react';
import { Card, Table, TableHead, TableHeadCell, TableBody, TableRow, TableCell } from 'flowbite-react';
import PerPageDropdown from '@/Components/HelperComponents/PerPageDropdown';
import Paginate from '@/Components/HelperComponents/Paginate';
import DrawerPagination from '@/Components/HelperComponents/DrawerPagination';
import { useTheme } from '@/Contexts/ThemeContext';

const PaginationDemo = () => {
    const { isDarkMode } = useTheme();

    // Mock pagination data
    const mockTableData = {
        current_page: 2,
        last_page: 5,
        from: 11,
        to: 20,
        total: 50,
        first_page_url: "?page=1",
        prev_page_url: "?page=1",
        next_page_url: "?page=3",
        last_page_url: "?page=5",
        links: [
            { url: null, label: "&laquo; Previous", active: false },
            { url: "?page=1", label: "1", active: false },
            { url: "?page=2", label: "2", active: true },
            { url: "?page=3", label: "3", active: false },
            { url: "?page=4", label: "4", active: false },
            { url: "?page=5", label: "5", active: false },
            { url: "?page=3", label: "Next &raquo;", active: false }
        ]
    };

    const mockData = [
        { id: 1, name: "John Doe", email: "<EMAIL>", role: "Admin" },
        { id: 2, name: "Jane Smith", email: "<EMAIL>", role: "User" },
        { id: 3, name: "Bob Johnson", email: "<EMAIL>", role: "Editor" },
        { id: 4, name: "Alice Brown", email: "<EMAIL>", role: "User" },
        { id: 5, name: "Charlie Wilson", email: "<EMAIL>", role: "Admin" }
    ];

    return (
        <div className="p-6 space-y-6">
            <div className="text-center">
                <h2 className="text-2xl font-bold text-gray-900 dark:text-white mb-2">
                    Pagination Components Demo
                </h2>
                <p className="text-gray-600 dark:text-gray-300">
                    All pagination components now support dark mode with smooth transitions
                </p>
            </div>

            {/* Table with Pagination */}
            <Card className="dark:bg-gray-800">
                <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
                    Table with Standard Pagination
                </h3>
                
                <div className="overflow-x-auto">
                    <Table className="dark:bg-gray-800">
                        <TableHead className="dark:bg-gray-700">
                            <TableHeadCell className="dark:text-gray-200">ID</TableHeadCell>
                            <TableHeadCell className="dark:text-gray-200">Name</TableHeadCell>
                            <TableHeadCell className="dark:text-gray-200">Email</TableHeadCell>
                            <TableHeadCell className="dark:text-gray-200">Role</TableHeadCell>
                        </TableHead>
                        <TableBody>
                            {mockData.map((item) => (
                                <TableRow key={item.id} className="dark:bg-gray-800 dark:border-gray-700">
                                    <TableCell className="dark:text-gray-300">{item.id}</TableCell>
                                    <TableCell className="dark:text-gray-300">{item.name}</TableCell>
                                    <TableCell className="dark:text-gray-300">{item.email}</TableCell>
                                    <TableCell className="dark:text-gray-300">{item.role}</TableCell>
                                </TableRow>
                            ))}
                        </TableBody>
                    </Table>
                </div>

                {/* Pagination Footer */}
                <div className="flex flex-wrap justify-between items-center gap-4 p-4 bg-white dark:bg-gray-800 border-t border-gray-200 dark:border-gray-700">
                    <PerPageDropdown
                        routeName="demo.pagination"
                        data={mockTableData}
                        customPerPage={10}
                        routeParams={{}}
                    />
                    <Paginate tableData={mockTableData} />
                </div>
            </Card>

            {/* Drawer Pagination Demo */}
            <Card className="dark:bg-gray-800">
                <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
                    Drawer Pagination Component
                </h3>
                <p className="text-gray-600 dark:text-gray-300 mb-4">
                    This pagination component is used in drawers and modals with AJAX loading
                </p>
                
                <div className="flex justify-center p-4 bg-gray-50 dark:bg-gray-700 rounded-lg">
                    <DrawerPagination 
                        tableData={mockTableData} 
                        setData={(data) => console.log('Data updated:', data)} 
                    />
                </div>
            </Card>

            {/* Features Overview */}
            <Card className="dark:bg-gray-800">
                <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
                    Dark Mode Features
                </h3>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div className="space-y-2">
                        <h4 className="font-medium text-gray-900 dark:text-white">
                            PerPageDropdown Component
                        </h4>
                        <ul className="text-sm text-gray-600 dark:text-gray-300 space-y-1">
                            <li>• Dark mode dropdown styling</li>
                            <li>• Smooth color transitions</li>
                            <li>• Proper contrast for accessibility</li>
                            <li>• Hover effects in both themes</li>
                        </ul>
                    </div>
                    <div className="space-y-2">
                        <h4 className="font-medium text-gray-900 dark:text-white">
                            Pagination Buttons
                        </h4>
                        <ul className="text-sm text-gray-600 dark:text-gray-300 space-y-1">
                            <li>• Consistent button theming</li>
                            <li>• Active state indicators</li>
                            <li>• Disabled state styling</li>
                            <li>• Grouped button borders</li>
                        </ul>
                    </div>
                </div>
            </Card>
        </div>
    );
};

export default PaginationDemo;
