<?php

use Illuminate\Support\Facades\Route;
use Inertia\Inertia;

Route::get('/dashboard/sales-performance', function () {
    return Inertia::render('Dashboard/SalesPerformance');
})->name('dashboard.salesPerformance');

Route::name('tasks.')->prefix('tasks')->group(function () {
    Route::get('/', function () {
        return Inertia::render('Tasks/Index');
    })->name('index');

    Route::get('/due', function () {
        return Inertia::render('Tasks/Due');
    })->name('due');

    Route::get('/upcoming', function () {
        return Inertia::render('Tasks/Upcoming');
    })->name('upcoming');

    Route::get('/done', function () {
        return Inertia::render('Tasks/Done');
    })->name('done');
});


// hrms routes
Route::name('hrms.')->prefix('hrms')->group(function () {
    Route::get('/', function () {
        return Inertia::render("HRMS/Employees/Index");
    })->name('dashboard');

    Route::get('/payroll', function () {
        return Inertia::render('HRMS/Payroll/Index');
    })->name('payroll');

    Route::get('/payruns', function () {
        return Inertia::render('HRMS/Payruns/Index');
    })->name('payruns');

    Route::get('/settings', function () {
        return Inertia::render('HRMS/Settings/Index');
    })->name('settings');
});
