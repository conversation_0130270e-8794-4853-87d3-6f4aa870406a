import ConfirmBox from "@/Components/HelperComponents/ConfirmBox";
import Paginate from "@/Components/HelperComponents/Paginate";
import PerPageDropdown from "@/Components/HelperComponents/PerPageDropdown";
import SortLink from "@/Components/SortLink";
import {
    button_custom,
    customDrawer,
    table_custom
} from "@/Pages/Helpers/DesignHelper";
import { convertDateFormat, isScheduled } from "@/Pages/Helpers/Helper";
import { Link, router } from "@inertiajs/react";
import {
    Badge,
    Button,
    Drawer,
    Table
} from "flowbite-react";
import { lazy, useState } from 'react';
import { BiHide } from "react-icons/bi";
import {
    MdAutorenew,
    MdBlock,
    MdOutlineDeleteOutline,
    MdOutlineEdit,
    MdOutlineMoveToInbox
} from "react-icons/md";
import { RiDeleteBin6Line } from "react-icons/ri";
import { TiStarOutline } from "react-icons/ti";
const View = lazy(() => import("./View"));
const InboxMain = lazy(() => import('../InboxMain'));

function Index({ collection }) {
    const { getData, mails } = collection;

    const currentPageRoute = "mail.sendmail.index";
    const [isMultipleDeleteConfirmOpen, SetIsMultipleDeleteConfirmOpen] = useState(false);
    const [selectedItem, setSelectedItem] = useState(null);

    const [openView, setOpenView] = useState(false);

    const [selectedCheckBox, setSelectedCheckBox] = useState([]);

    function deleteRecords() {
        if (selectedCheckBox.length > 0) {
            router.delete(route("mail.sendmail.destroy", { sendmail: selectedCheckBox.toLocaleString() }));
        }
    }
    const handleMultipleDelete = (res) => {

        if (res) {
            deleteRecords(); // Your function to delete records (ensure it handles the selection).
        }
        setSelectedCheckBox([]); // Clear selected checkboxes
        SetIsMultipleDeleteConfirmOpen(false); // Close the confirmation dialog
    };
    return (
        <InboxMain>
            <div className="h-full rounded-md ">
                <div className="bg-white border rounded-lg ">
                    <div className="flex justify-between p-2">
                        <div className="flex gap-2">
                            <Button
                                className="ps-1"
                                size="xs"
                                color="gray"
                                theme={button_custom}
                            >
                                <MdAutorenew className="text-slate-400" />
                            </Button>
                            <Button.Group>
                                <Button
                                    theme={button_custom}
                                    size="xs"
                                    color="gray"
                                // className="border-e-0 rounded-e-none"
                                >
                                    <div className="flex items-center gap-1">
                                        <BiHide className="text-slate-400" />
                                        <span className="text-xs">
                                            Mark Unread
                                        </span>
                                    </div>
                                </Button>
                                <Button
                                    className="ps-1"
                                    size="xs"
                                    color="gray"
                                    theme={button_custom}
                                >
                                    <div className="flex items-center gap-1">
                                        <MdOutlineMoveToInbox className="text-slate-400" />
                                        <span className="text-xs">
                                            Archive
                                        </span>
                                    </div>
                                </Button>
                                <Button
                                    className="ps-1"
                                    size="xs"
                                    color="gray"
                                    theme={button_custom}
                                >
                                    <div className="flex items-center gap-1">
                                        <TiStarOutline className="text-slate-400" />
                                        <span className="text-xs">
                                            Pinned
                                        </span>
                                    </div>
                                </Button>
                                <Button
                                    className="ps-1"
                                    size="xs"
                                    color="gray"
                                    theme={button_custom}
                                >
                                    <div className="flex items-center gap-1">
                                        <MdBlock className="text-slate-400" />
                                        <span className="text-xs">
                                            Spam
                                        </span>
                                    </div>
                                </Button>
                                <Button
                                    className="ps-1"
                                    size="xs"
                                    color="gray"
                                    theme={button_custom}
                                    onClick={() => SetIsMultipleDeleteConfirmOpen(!isMultipleDeleteConfirmOpen)}

                                >
                                    <div className="flex items-center gap-1">
                                        <RiDeleteBin6Line className="text-slate-400" />
                                        <span className="text-xs">
                                            Delete
                                        </span>
                                    </div>
                                </Button>
                            </Button.Group>
                        </div>
                        <div className="">
                            {/* <Button
                                    theme={button_custom}
                                    size="xs"
                                    color="gray"
                                    type="button"
                                    onClick={() => setOpenModal(true)}
                                >
                                    <div className="flex items-center gap-1">
                                        <CiEdit className="text-slate-500" />
                                        <span className="text-xs">
                                            Compose
                                        </span>
                                    </div>
                                </Button> */}
                        </div>
                    </div>
                    <div className="overflow-auto bg-white border rounded-lg">
                        <Table hoverable theme={table_custom}>
                            <Table.Head className="bg-slate-100 text-nowrap">

                                <Table.HeadCell>
                                    <SortLink showName={'Date & Time'} routeName={currentPageRoute} column={'name'} params={getData} sortType={getData?.sort == "asc" ? "desc" : "asc"} />
                                </Table.HeadCell>
                                <Table.HeadCell>
                                    <SortLink showName={'To'} routeName={currentPageRoute} column={'to'} params={getData} sortType={getData?.sort == "asc" ? "desc" : "asc"} />
                                </Table.HeadCell>
                                <Table.HeadCell>
                                    Subject
                                </Table.HeadCell>
                                <Table.HeadCell>
                                    Gateway
                                </Table.HeadCell>
                                <Table.HeadCell>
                                    <SortLink showName={'User'} routeName={currentPageRoute} column={'user_id'} params={getData} sortType={getData?.sort == "asc" ? "desc" : "asc"} />
                                </Table.HeadCell>
                                <Table.HeadCell>
                                    Action
                                </Table.HeadCell>
                            </Table.Head>
                            <Table.Body className="divide-y">
                                {mails?.data?.map((r, k) =>
                                    <Table.Row key={`${r.name}-${k}`} >

                                        <Table.Cell onClick={() => { setOpenView(true); setSelectedItem(r); }} className="flex items-center gap-3 font-medium text-gray-900 whitespace-nowrap dark:text-white">
                                            <div className="flex flex-col w-20">
                                                <div className="text-sm text-blue-500 ">
                                                    {convertDateFormat(r.sentDatetime ?? r.scheduleDatetime, 'date')}
                                                </div>
                                                <div className="text-xs text-gray-600">

                                                    {convertDateFormat(r.sentDatetime ?? r.scheduleDatetime, 'time')}
                                                </div>
                                            </div>
                                            {r.scheduleDatetime && isScheduled(r.scheduleDatetime) &&
                                                <Badge size="xs" color="indigo" className="py-0 px-0.5 text-[10px]">Scheduled</Badge>
                                            }
                                        </Table.Cell>
                                        <Table.Cell onClick={() => { setOpenView(true); setSelectedItem(r); }}>
                                            <div className="text-blue-500 truncate max-w-40">
                                                {r.to}
                                            </div>
                                        </Table.Cell>
                                        <Table.Cell>
                                            {r.subject}
                                        </Table.Cell>
                                        <Table.Cell>
                                            {r?.gateway?.name ?? '-'}
                                        </Table.Cell>
                                        <Table.Cell>
                                            {r?.user?.username ?? '-'}
                                        </Table.Cell>
                                        <Table.Cell>
                                            {r.scheduleDatetime && isScheduled(r.scheduleDatetime) &&
                                                <div className="flex gap-2">
                                                    <Button size="xs" theme={button_custom} color="failure"
                                                        onClick={() => {
                                                            SetIsMultipleDeleteConfirmOpen(!isMultipleDeleteConfirmOpen);
                                                            setSelectedCheckBox([r.id])
                                                        }}>
                                                        <MdOutlineDeleteOutline />
                                                    </Button>
                                                    <Button size="xs" theme={button_custom} color="purple"
                                                        as={Link}
                                                        href={route('mail.compose', { mail: r.id })}
                                                    >
                                                        <MdOutlineEdit />
                                                    </Button>
                                                </div>
                                            }
                                        </Table.Cell>
                                    </Table.Row>
                                )}
                            </Table.Body>
                        </Table>
                    </div>
                </div >
                <div className="bottom-0 w-full p-3 bg-white rounded-lg float-end dark:border-gray-500 dark:rounded-lg dark:bg-gray-700">
                    <div className="flex flex-wrap justify-between gap-2 lg:justify-between lg:gap-0 md:gap-0">
                        <div className="flex items-center gap-4">
                            <PerPageDropdown
                                getDataFields={getData ?? null}
                                routeName={"mail.sendmail.index"}
                                data={mails}
                                idName={"name"}
                            // id={target}
                            />
                        </div>

                        <Paginate tableData={mails} />
                    </div>
                </div>
            </div >
            {isMultipleDeleteConfirmOpen &&
                <ConfirmBox
                    isOpen={isMultipleDeleteConfirmOpen}
                    onClose={() => SetIsMultipleDeleteConfirmOpen(false)} // Close the confirm box
                    onAction={handleMultipleDelete} // Handle the user's choice
                    title="Delete Gateway "
                    message="Do you want to Delete gateway."
                    confirmText="Yes, Delete!"
                    cancelText="No, Keep It"
                    confirmColor="orange"
                    cancelColor="gray"

                />
            }
            {openView == true &&
                <Drawer open={openView} onClose={() => setOpenView(false)} position="right" theme={customDrawer} className="w-full xl:w-6/12 lg:w-8/12 md:w-full">
                    <DrawerHeader title="Email Preview" />
                    <DrawerItems>
                        <View mail={selectedItem} />
                    </DrawerItems>
                </Drawer>
            }
        </InboxMain >
    )
}
export default Index;
