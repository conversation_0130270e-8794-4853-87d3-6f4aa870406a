import Main from "@/Layouts/Main";
import React, { useState } from "react";
import TabBar from "./TabBar";
import { <PERSON>ge, Button, ButtonGroup, Checkbox, Table, ToggleSwitch } from "flowbite-react";
import {
    button_custom,
    table_custom,
    toggle_custom,
} from "../Helpers/DesignHelper";
import { TbColumns3 } from "react-icons/tb";
import { IoMdAdd } from "react-icons/io";
import {
    MdChecklistRtl,
    MdDeleteOutline,
    MdOutlineEdit,
    MdOutlineModeEdit,
} from "react-icons/md";
import { PiCaretUpDownBold } from "react-icons/pi";
import { IoQrCode } from "react-icons/io5";
import { FaEye } from "react-icons/fa6";
import { TruncatedCell } from "@/Components/ExpandMsg";
import { Link } from "@inertiajs/react";

export default function Bots() {
    // ----------------Edit --------------------
    const [isOpen, setIsOpen] = useState(false);
    const handleClose = () => setIsOpen(false);

    return (
        <Main>
            <div className="p-2 overflow-hidden">
                <TabBar />
                <div className="pb-2 rounded-lg">
                    <div className="h-fit bg-white rounded border overflow-auto w-full mt-2.5">
                        <div className="flex justify-between p-2">
                            <div className="">
                                <Button
                                    theme={button_custom}
                                    size="xs"
                                    color="gray"
                                    id="dropdownInformationButton"
                                    data-dropdown-toggle="dropdownNotification"
                                    type="button"
                                >
                                    <div className="flex items-center gap-1 text-xs">
                                        <TbColumns3 className="text-slate-500 ms-1" />
                                        <span className="text-xs">Columns</span>
                                    </div>
                                </Button>
                            </div>
                            <div className="">
                                <ButtonGroup>
                                    <Button
                                        className="pe-1"
                                        size="xs"
                                        color="gray"
                                        theme={button_custom}
                                        onClick={() => setIsAddOpen(true)}
                                    >
                                        <div className="flex items-center gap-1">
                                            <IoMdAdd className="text-slate-500" />
                                            <span className="text-xs">Add</span>
                                        </div>
                                    </Button>
                                </ButtonGroup>

                                {/* Dropdown menu */}
                                <div
                                    id="dropdownNotification"
                                    className="z-20 hidden w-full max-w-sm bg-white border divide-y divide-gray-100 rounded-lg shadow-lg dark:bg-gray-800 dark:divide-gray-700"
                                    aria-labelledby="dropdownNotificationButton"
                                >
                                    <div className="block px-4 py-2 font-medium text-center text-gray-700 rounded-t-lg bg-gray-50 dark:bg-gray-800 dark:text-white">
                                        <div className="flex justify-between">
                                            <div className="">
                                                <div className="flex items-center gap-1 p-1 text-gray-400 group">
                                                    <svg
                                                        xmlns="http://www.w3.org/2000/svg"
                                                        className=" fill-slate-400"
                                                        height="24px"
                                                        viewBox="0 0 24 24"
                                                        width="24px"
                                                    >
                                                        <rect
                                                            fill="none"
                                                            height="24"
                                                            width="24"
                                                        />
                                                        <path d="M20,4H4C2.9,4,2,4.9,2,6v12c0,1.1,0.9,2,2,2h16c1.1,0,2-0.9,2-2V6C22,4.9,21.1,4,20,4z M8,18H4V6h4V18z M14,18h-4V6h4V18z M20,18h-4V6h4V18z" />
                                                    </svg>
                                                    Column
                                                </div>
                                            </div>
                                            <div className="">
                                                <button className="flex p-1 text-blue-600">
                                                    <svg
                                                        xmlns="http://www.w3.org/2000/svg"
                                                        height="24px"
                                                        viewBox="0 0 24 24"
                                                        width="24px"
                                                        className="fill-blue-600"
                                                    >
                                                        <g>
                                                            <path
                                                                d="M0,0h24v24H0V0z"
                                                                fill="none"
                                                            />
                                                        </g>
                                                        <g>
                                                            <g>
                                                                <path d="M6,13c0-1.65,0.67-3.15,1.76-4.24L6.34,7.34C4.9,8.79,4,10.79,4,13c0,4.08,3.05,7.44,7,7.93v-2.02 C8.17,18.43,6,15.97,6,13z M20,13c0-4.42-3.58-8-8-8c-0.06,0-0.12,0.01-0.18,0.01l1.09-1.09L11.5,2.5L8,6l3.5,3.5l1.41-1.41 l-1.08-1.08C11.89,7.01,11.95,7,12,7c3.31,0,6,2.69,6,6c0,2.97-2.17,5.43-5,5.91v2.02C16.95,20.44,20,17.08,20,13z" />
                                                            </g>
                                                        </g>
                                                    </svg>
                                                    Reset
                                                </button>
                                            </div>
                                        </div>
                                    </div>
                                    <div className="divide-y divide-gray-100 dark:divide-gray-700">
                                        <div className="flex flex-col">
                                            <div className="relative overflow-x-auto shadow-md sm:rounded-lg ">
                                                <table className="w-full text-sm text-left text-gray-500 rtl:text-right dark:text-gray-400">
                                                    <thead className="text-xs text-gray-700 uppercase dark:text-gray-400">
                                                        <tr>
                                                            <th className="p-2 text-center bg-gray-50 dark:bg-gray-800 w-14 ">
                                                                List
                                                            </th>
                                                            <th className="p-2 text-center bg-gray-50 dark:bg-gray-800 w-14">
                                                                Details
                                                            </th>
                                                            <th className="p-2"></th>
                                                        </tr>
                                                    </thead>
                                                    <tbody className="overflow-auto ">
                                                        <tr className="border-b border-gray-200 dark:border-gray-700">
                                                            <td className="p-2 font-medium text-center text-gray-900 max-w-max whitespace-nowrap bg-gray-50 dark:text-white dark:bg-gray-800">
                                                                <Checkbox></Checkbox>
                                                            </td>
                                                            <td className="p-2 font-medium text-center text-gray-900 whitespace-nowrap bg-gray-50 dark:text-white dark:bg-gray-800">
                                                                <Checkbox></Checkbox>
                                                            </td>
                                                            <td className="px-6 py-4 max-w-max">
                                                                Name
                                                            </td>
                                                        </tr>
                                                        <tr className="border-b border-gray-200 dark:border-gray-700">
                                                            <td className="p-2 font-medium text-center text-gray-900 max-w-max whitespace-nowrap bg-gray-50 dark:text-white dark:bg-gray-800">
                                                                <Checkbox></Checkbox>
                                                            </td>
                                                            <td className="p-2 font-medium text-center text-gray-900 whitespace-nowrap bg-gray-50 dark:text-white dark:bg-gray-800">
                                                                <Checkbox></Checkbox>
                                                            </td>
                                                            <td className="px-6 py-4 max-w-max">
                                                                Name
                                                            </td>
                                                        </tr>
                                                        <tr className="border-b border-gray-200 dark:border-gray-700">
                                                            <td className="p-2 font-medium text-center text-gray-900 max-w-max whitespace-nowrap bg-gray-50 dark:text-white dark:bg-gray-800">
                                                                <Checkbox></Checkbox>
                                                            </td>
                                                            <td className="p-2 font-medium text-center text-gray-900 whitespace-nowrap bg-gray-50 dark:text-white dark:bg-gray-800">
                                                                <Checkbox></Checkbox>
                                                            </td>
                                                            <td className="px-6 py-4 max-w-max">
                                                                Name
                                                            </td>
                                                        </tr>
                                                        <tr className="border-b border-gray-200 dark:border-gray-700">
                                                            <td className="p-2 font-medium text-center text-gray-900 max-w-max whitespace-nowrap bg-gray-50 dark:text-white dark:bg-gray-800">
                                                                <Checkbox></Checkbox>
                                                            </td>
                                                            <td className="p-2 font-medium text-center text-gray-900 whitespace-nowrap bg-gray-50 dark:text-white dark:bg-gray-800">
                                                                <Checkbox></Checkbox>
                                                            </td>
                                                            <td className="px-6 py-4 max-w-max">
                                                                Name
                                                            </td>
                                                        </tr>
                                                    </tbody>
                                                </table>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div className="h-full">
                            <div className="overflow-x-auto bg-white border rounded-lg text-nowrap">
                                <Table theme={table_custom}>
                                    <TableHead>
                                        <TableHeadCell>
                                            <Checkbox
                                                color="blue"
                                                defaultChecked
                                            />
                                        </TableHeadCell>
                                        <TableHeadCell>
                                            <div className="flex items-center justify-between gap-2">
                                                <h3>Id</h3>
                                                <PiCaretUpDownBold />
                                            </div>
                                        </TableHeadCell>
                                        <TableHeadCell>
                                            <div className="flex items-center justify-between gap-2">
                                                <h3>User</h3>
                                                <PiCaretUpDownBold />
                                            </div>
                                        </TableHeadCell>
                                        <TableHeadCell>
                                            <div className="flex items-center justify-between gap-2">
                                                <h3>Name</h3>
                                                <PiCaretUpDownBold />
                                            </div>
                                        </TableHeadCell>
                                        <TableHeadCell>
                                            <div className="flex items-center justify-between gap-2">
                                                <h3>
                                                    Welcome Msg / Wrong Keyword
                                                </h3>
                                                <PiCaretUpDownBold />
                                            </div>
                                        </TableHeadCell>

                                        <TableHeadCell>Actions</TableHeadCell>
                                    </TableHead>
                                    <TableBody className="divide-y">
                                        <TableRow>
                                            <TableCell>
                                                <Checkbox
                                                    color={"blue"}
                                                    className="rowCheckBox"
                                                    // id={channel.id}
                                                    // onChange={getCheckedIds}
                                                />
                                            </TableCell>
                                            <TableCell className="font-medium text-gray-900 whitespace-nowrap dark:text-white">
                                                cuzivyruky
                                            </TableCell>
                                            <TableCell>ghgh</TableCell>
                                            <TableCell>ffggfd</TableCell>
                                            <TableCell className="max-w-40">
                                                <TruncatedCell content={"-"} />

                                                {/* jsdkbsdkbsdkgkdbgksdbvksbhkbvhsdbvhdvhdskvkjsvjsdvhjsdvhjsvdhjvjdsvfsdvfvsdjfsdjfvsdvfvsdvfjdsvfdvfvsdfjvsdjfvsjdfvjsdvfffffffffffffffffffffffffffffdsjvffyafqwhasssssssvnf */}
                                                {/* {auto.isWelcomeMsg ==
                                                            1
                                                                ? "Yes"
                                                                : "No"} */}
                                            </TableCell>
                                            <TableCell>
                                                <div className="flex items-center gap-3 pt-1">
                                                    <Button
                                                        className=""
                                                        theme={button_custom}
                                                        color="success"
                                                        size="xs"
                                                        // onClick={() => {
                                                        //     setIsEditAutoReply(
                                                        //         true
                                                        //     );
                                                        //     setautoReplyEditData(
                                                        //         {
                                                        //             id: auto.id,
                                                        //             name: auto.name,
                                                        //             message:
                                                        //                 auto.msg,
                                                        //             isWelcomeMsg:
                                                        //                 auto.isWelcomeMsg,
                                                        //             media: auto.wa_media,
                                                        //         }
                                                        //     );
                                                        // }}
                                                    >
                                                        <MdOutlineModeEdit className="text-sm" />
                                                        <span className="text-xs ms-1">
                                                            Edit
                                                        </span>
                                                    </Button>

                                                    {/* <Button
                                                                    theme={
                                                                        button_custom
                                                                    }
                                                                    color="gray"
                                                                    size="xs"
                                                                    onClick={() => {
                                                                        setIsViewOpen(
                                                                            true
                                                                        ),
                                                                            setautoReplyEditData(
                                                                                {
                                                                                    id: auto.id,
                                                                                    name: auto.name,
                                                                                    user: auto
                                                                                        .user
                                                                                        .username,
                                                                                    message:
                                                                                        auto.msg,
                                                                                    isWelcomeMsg:
                                                                                        auto.isWelcomeMsg,
                                                                                    total: auto
                                                                                        .rules
                                                                                        .length,
                                                                                    media: auto.wa_media,
                                                                                }
                                                                            );
                                                                    }}
                                                                >
                                                                    <IoEyeOutline className="text-sm" />
                                                                    <span className="text-xs ms-1">
                                                                        View
                                                                    </span>
                                                                </Button> */}

                                                    <div className="relative flex me-1.5">
                                                        <ButtonGroup outline>
                                                            <Button
                                                                className="items-center"
                                                                // onClick={() => {
                                                                //     setIsAddKeyword(
                                                                //         true
                                                                //     );
                                                                //     setautoReplyEditData(
                                                                //         {
                                                                //             id: auto.id,
                                                                //             name: auto.name,
                                                                //         }
                                                                //     );
                                                                // }}
                                                                theme={
                                                                    button_custom
                                                                }
                                                                size={"xs"}
                                                                color="gray"
                                                            >
                                                                <IoMdAdd className="text-sm" />
                                                            </Button>
                                                            <Button
                                                                className="items-center"
                                                                as={Link}
                                                                href={route(
                                                                    "whatsapp.bot.listKeyword",
                                                                    {
                                                                        id: "1",
                                                                    }
                                                                )}
                                                                theme={
                                                                    button_custom
                                                                }
                                                                size={"xs"}
                                                                color="blue"
                                                            >
                                                                <MdChecklistRtl className="text-sm ms-1" />
                                                                <span className="text-xs ms-1">
                                                                    Keyword
                                                                </span>
                                                            </Button>
                                                            {/* {auto.rules.length >
                                                            0 ? (
                                                                <div className="absolute inline-flex items-center justify-center mt-0.5 w-4 h-4 min-w-max min-h-max text-xs font-bold text-white bg-red-600 rounded-full -top-2 -end-3 dark:border-gray-900">
                                                                    {
                                                                        auto
                                                                            .rules
                                                                            .length
                                                                    }
                                                                </div>
                                                            ) : (
                                                                <></>
                                                            )} */}
                                                        </ButtonGroup>
                                                        {/* <sup>
                                                                        <Badge theme={badge_custom} color={"failure"} size={"xs"} className="rounded-full">
                                                                            2
                                                                        </Badge>
                                                                    </sup> */}
                                                    </div>
                                                </div>
                                            </TableCell>
                                        </TableRow>
                                    </TableBody>
                                </Table>
                            </div>
                        </div>
                        <div className="flex flex-wrap justify-between gap-2 p-2 lg:gap-0 md:gap-0">
                            <div className="flex items-center gap-4">
                                <Button
                                    className="border rounded"
                                    size="xs"
                                    color="gray"
                                    theme={button_custom}
                                    // onClick={deleteRecords}
                                >
                                    <div className="flex items-center gap-1">
                                        <MdDeleteOutline className="text-slate-500" />
                                        <span className="text-xs">Delete</span>
                                    </div>
                                </Button>
                                {/* <PerPageDropdown
                                        getDataFields={getData ?? null}
                                        routeName={"whatsapp.campaign.index"}
                                        data={campaigns}
                                    /> */}
                            </div>
                            {/* <Paginate tableData={campaigns} /> */}
                        </div>
                    </div>
                </div>
                {/* </div> */}
                {/* </div> */}
            </div>
        </Main>
    );
}

