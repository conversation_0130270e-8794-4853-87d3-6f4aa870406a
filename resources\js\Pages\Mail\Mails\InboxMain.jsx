
import { button_custom } from "@/Pages/Helpers/DesignHelper";
import { Head } from "@inertiajs/react";
import { Button, DrawerItems } from "flowbite-react";
import { lazy, useState } from "react";
import { MdArrowForwardIos } from "react-icons/md";
const SideMenu = lazy(() => import("./SideMenu"));
const AlertBox = lazy(() => import("@/Components/HelperComponents/AlertBox"));

export default function InboxMain({ children }) {
    const [openSideBar, setOpenSideBar] = useState(false);
    return (
        <div className="p-2 overflow-hidden">
            <AlertBox />
            <Head title="Inbox" />
            <div
                className="grid grid-cols-12 gap-2 p-0 mt-2 border-none grid-2 flow-row-dense md:grid-cols-12 sm:grid-cols-1 bg-slate-100"
            >
                <div className="hidden xl:col-span-2 lg:col-span-3 md:col-span-3 sm:col-span-3 lg:flex ">
                    <SideMenu />
                </div>
                <div className="relative pt-0 rounded-lg dark:text-gray-400 lg:p-0 xl:col-span-10 lg:col-span-9 md:col-span-12 col-span-full">
                    <div className="relative flex xl:hidden lg:hidden">

                        <Button onClick={() => setOpenSideBar(!openSideBar)} size="xs" color="white" theme={button_custom} className="absolute z-30 mt-2 border ms-1">

                            <MdArrowForwardIos className="text-gray-500"/>
                        </Button>


                        <Drawer open={openSideBar} onClose={() => setOpenSideBar(!openSideBar)}
                            theme={{
                                "root": {
                                    "base": "fixed z-40 overflow-y-auto bg-white p-0 transition-transform dark:bg-gray-800",
                                }
                            }}
                        >
                            {/* <DrawerHeader  /> */}
                            <DrawerItems>
                                <SideMenu />
                            </DrawerItems>
                        </Drawer>
                    </div>
                    {children}
                </div>
            </div>
        </div>

    )
}

