import AlertBox from '@/Components/HelperComponents/AlertBox';
import { showAsset } from '@/Pages/Helpers/Helper';
import { usePage } from '@inertiajs/react';
import { Avatar, Dropdown, DropdownHeader, DropdownItem, Navbar, NavbarBrand, NavbarCollapse, NavbarLink, NavbarToggle } from 'flowbite-react';

export default function WorkspaceMain({ children }) {
    const user = usePage().props.auth.user;
    return (
        <div>
            <AlertBox />
            <Navbar className="mb-2 rounded-lg">
                <div className="flex items-center justify-start flex-1">
                    <NavbarBrand href="/">
                        <img
                            src={showAsset("/assets/img/clatoslogofull.png", '')}
                            className="mr-3 h-6 sm:h-9"
                        />
                    </NavbarBrand>
                    <NavbarCollapse className="ml-4">
                        <NavbarLink href={route('workspace')}>Workspace</NavbarLink>
                    </NavbarCollapse>
                </div>
                <div className="order-2 hidden md:flex items-center justify-end">
                    <Dropdown
                        arrowIcon={false}
                        inline
                        label={
                            <div className="w-10 h-10 flex items-center justify-center bg-blue-500 rounded-full">
                                <span className="text-white text-lg font-semibold">
                                    {user.name.charAt(0).toUpperCase()}
                                </span>
                            </div>
                        }
                    >
                        <DropdownHeader>
                            <span className="block text-sm">{user.name}</span>
                            <span className="block truncate text-sm font-medium">{user.email}</span>
                        </DropdownHeader>
                        {/* <DropdownItem>Dashboard</DropdownItem>
                        <DropdownItem>Settings</DropdownItem>
                        <DropdownItem>Earnings</DropdownItem> */}
                        {/* <DropdownDivider /> */}
                        <DropdownItem href={route('logout')}>Sign out</DropdownItem>
                    </Dropdown>
                </div>
                <NavbarToggle />
            </Navbar>

            <div className='p-4 h-screen'>
                {children}
                <div className='text-center text-sm text-gray-500'>
                    Need help? <span className='text-blue-700 font-medium'> Contact Support</span>
                </div>
            </div>
        </div >
    )
}
