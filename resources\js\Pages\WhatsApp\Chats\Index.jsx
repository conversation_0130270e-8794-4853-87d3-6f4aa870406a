import { <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON> } from "flowbite-react";
import { useEffect, useRef, useState } from "react";

import {
    convertDateFormat,
    fetchJson,
    showAsset
} from "@/Pages/Helpers/Helper";

import { FilePreview } from "@/Components/FilePreview";
import ConfirmBox from "@/Components/HelperComponents/ConfirmBox";
import { Head, usePage } from "@inertiajs/react";
import { GrSend } from "react-icons/gr";
import { LuListFilter } from "react-icons/lu";
import { RiDeleteBinLine } from "react-icons/ri";
import { VscSend } from "react-icons/vsc";
import PeopleList from "./PeopleList";
import TextBox from "./TextBox";
import { FaRegHandPointer } from "react-icons/fa6";
import AlertBox from "@/Components/HelperComponents/AlertBox";

function Index() {
    // const page = usePage().props;
    const user = usePage().props.auth.user;
    const [messageData, setMessageData] = useState([]);
    const [isLoading, setIsLoading] = useState(false);
    const divRef = useRef(null);
    const [selectedUser, setSelectedUser] = useState();
    const [nextPageUrl, setNextPageUrl] = useState(null);
    const [groupData, setGroupData] = useState([]);
    const [current, setCurrent] = useState();
    const [isListOpen, setIsListOpen] = useState(false);
    // confirm data and Box
    const [isDeleteConfirmMessageOpen, setIsDeleteConfirmMessageOpen] = useState(false);
    const [isSendNowConfirmMessageOpen, setIsSendNowConfirmMessageOpen] = useState(false);
    const [sendNowObject, setSendNowObject] = useState({});
    const [deleteScheduledObject, setDeleteScheduledObject] = useState({});




    useEffect(() => {
        if (selectedUser) {
            resetMsgWindow();
        }
        setIsListOpen(false)
    }, [selectedUser]);

    const resetStates = async () => {
        try {
            setGroupData([]);
            setMessageData([]);
            setIsLoading(true);
            setNextPageUrl(null);
            return true;
        } catch (e) {
        }
    };
    const resetMsgWindow = async () => {
        try {
            await resetStates();
            fetchChat();
            divRef.current.scrollTop = divRef.current.scrollHeight;
            let divElement = document.getElementById("chatsBox");
            divElement.scrollTop = divElement.scrollHeight;
        } catch (error) {
            console.error("msg window function error :", error);
        }
    };

    const fetchChat = () => {
        setIsLoading(true);
        Promise.all([
            fetchJson(route("whatsapp.chats.show", { chat: selectedUser }), {}, true),
        ]).then(([chatData]) => {
            setNextPageUrl(chatData.collection.messages2.next_page_url ?? chatData.collection.messages1.next_page_url);
            setIsLoading(false);
            setMessageData(chatData.data);
        });
    };

    useEffect(() => {
        setGroupData(reverseGroupedObject(groupByDate(messageData)));
    }, [messageData]);

    const renderChange = () => {
        fetchChat();
        divRef.current.scrollTop = divRef.current.scrollHeight;
        let divElement = document.getElementById('chatsBox');
        divElement.scrollTop = divElement.scrollHeight;
    };

    const loadMore = () => {
        setIsLoading(true);
        Promise.all([fetchJson(nextPageUrl, {}, true)])
            .then(([chatData]) => {
                setIsLoading(false);
                setMessageData([...messageData, ...chatData.data]);
                setNextPageUrl(chatData.data.next_page_url);
                setNextPageUrl(chatData.collection.messages2.next_page_url ?? chatData.collection.messages1.next_page_url);

            });
    };

    const groupByDate = (data) => {
        return data.reduce((acc, item) => {
            const date = new Date(item.messageDateTime);
            const year = date.getFullYear();
            const month = date.getMonth(); // 0-indexed month
            const day = date.getDate();

            // Create a key for the group (YYYY-MM-DD format)
            const key = `${year}-${(month + 1).toString().padStart(2, '0')}-${day.toString().padStart(2, '0')}`;

            // Initialize an empty array for the date if it doesn't exist
            if (!acc[key]) {
                acc[key] = [];
            }

            // Push the current item to the corresponding date group
            acc[key].push(item);

            return acc;
        }, {});
    };

    const reverseGroupedObject = (grouped) => {
        return Object.keys(grouped)
            .sort((a, b) => new Date(a) - new Date(b)) 
            .reduce((acc, key) => {
                acc[key] = grouped[key]; 
                return acc;
            }, {});
    };


    // Delete Scheduled
    const deleteScheduled = (table, message) => {
        Promise.all([
            fetchJson(route("whatsapp.chat.deleteScheduled", { table: table, message: message }), {}, true),
        ]).then(([collect]) => {
            fetchChat();
        });
    }

    // Send Now Scheduled
    const SendScheduled = (table, message) => {
        Promise.all([
            fetchJson(route("whatsapp.chat.sendNowScheduled", { table: table, message: message }), {}, true),
        ]).then(([collect]) => {
            fetchChat();
        });
    }

    function isToday(dateString) {
        const today = new Date();
        const givenDate = new Date(dateString);

        return (
            today.getDate() === givenDate.getDate() &&
            today.getMonth() === givenDate.getMonth() &&
            today.getFullYear() === givenDate.getFullYear()
        );
    }

    function handleConfirmDelete(result) {
        if (Object.keys(deleteScheduledObject).length > 0 && result) {
            deleteScheduled(deleteScheduledObject.table, deleteScheduledObject.id);
            setIsDeleteConfirmMessageOpen(false);
            setDeleteScheduledObject({});
        } else {
            setIsDeleteConfirmMessageOpen(false);
            setDeleteScheduledObject({});
        }
    }

    function handleConfirmSendNow(result) {
        if (Object.keys(deleteScheduledObject).length > 0 && result) {
            SendScheduled(deleteScheduledObject.table, deleteScheduledObject.id);
            setIsSendNowConfirmMessageOpen(false);
            setSendNowObject({});
        } else {
            setIsSendNowConfirmMessageOpen(false);
            setSendNowObject({});
        }
    }

    return (

        <div className="">

            <Head title="Chats" />
            <AlertBox />
            <div className="absolute w-full h-32 bg-green-500"></div>

            <div className="relative h-screen p-2 inbox-screen">
                <div className="grid h-full grid-cols-12 gap-2 p-0 border-none grid-2 flow-row-dense md:grid-cols-12 sm:grid-cols-1 ">
                    <div className="hidden xl:col-span-3 lg:col-span-4 md:col-span-5 sm:col-span-3 lg:flex ">
                        <PeopleList
                            SelectedId={setSelectedUser}
                            currentObject={setCurrent}
                        />
                    </div>
                    <div className="relative border rounded-lg dark:text-gray-400 lg:p-0 xl:col-span-9 lg:col-span-8 md:col-span-12 col-span-full border-s-0">

                        {selectedUser ? (
                            <div className="border rounded-lg border-s-0">
                                <div className="bg-[#EEEEEE] rounded-t-lg w-full">
                                    <div className="flex items-center gap-2 p-2">
                                        <div className="lg:hidden ms-3">
                                            <div className="relative inline-block text-left">
                                                {/* Dropdown Button */}
                                                <button
                                                    onClick={() => setIsListOpen(!isListOpen)}
                                                    className="inline-flex justify-center w-full p-2 text-sm font-medium text-white bg-blue-700 rounded-md shadow-sm"
                                                >
                                                    <LuListFilter className="text-lg" />
                                                </button>
                                            </div>
                                        </div>
                                        <div className="flex flex-col text-start">
                                            <h5 className="font-medium text-blue-600">
                                                {selectedUser}
                                            </h5>

                                        </div>
                                    </div>
                                </div>
                                <div className="bg-[url('/assets/img/bgwtsp.png')]">
                                    <div
                                        className="p-4 space-y-4 overflow-auto scroll-smooth"
                                        id="chatsBox"
                                        ref={divRef}
                                        style={{ height: "71vh" }}
                                    >
                                        {/* Date Label */}
                                        <div className="flex flex-col items-center">
                                            {isLoading && (
                                                <Spinner size="xl" />
                                            )}
                                            {nextPageUrl ? (
                                                <Button size="xs" className={``} onClick={loadMore} >
                                                    Load More
                                                </Button>
                                            ) : (
                                                "End of chat"
                                            )}
                                        </div>
                                        {groupData && (
                                            <div>
                                                {Object.keys(groupData).map(
                                                    (date) => (
                                                        <div
                                                            className="flex flex-col gap-2"
                                                            key={date}
                                                        >
                                                            <div className="text-sm text-center text-gray-700 ">
                                                                <span className="px-2 py-1 rounded-md w-fit bg-sky-200">
                                                                    {isToday(date) ? 'today' : convertDateFormat(date, "date")}
                                                                </span>
                                                            </div>
                                                            {groupData[date]
                                                                .sort((a, b) => a.id - b.id)
                                                                .map((msg, k) => (
                                                                    <div
                                                                        className={`${msg.user_id == user.id ? " justify-end " : " justify-start "}  flex `}
                                                                        key={"chatItem-" + k}
                                                                    >
                                                                        <div
                                                                            className={`${msg.user_id == user.id
                                                                                ? " justify-end "
                                                                                : " justify-start "
                                                                                } flex w-3/4 text-sm lg:w-2/4 md:w-3/4`}
                                                                        >
                                                                            <div
                                                                                className={`${(new Date(msg.messageDateTime) > new Date())
                                                                                    ? "bg-amber-100"

                                                                                    :
                                                                                    (msg.user_id == user.id)
                                                                                        ? " bg-[#DCF8C6]  "
                                                                                        : " bg-white "
                                                                                    } p-2 bg-[#DCF8C6] rounded-lg shadow-md`}
                                                                            >
                                                                                <span className="text-sm text-blue-600">
                                                                                    {msg.remoteJid?.split("@")[0] ?? msg.mobile}
                                                                                </span>

                                                                                {msg.file &&
                                                                                    <FilePreview object={msg.file} imageSize="xl" width=" w-fit" fileBg="bg-[#cbebbc]" />
                                                                                }

                                                                                <p className="text-gray-800">
                                                                                    {
                                                                                        msg.body
                                                                                    }

                                                                                </p>
                                                                                <span className="mt-1 text-xs text-gray-500">
                                                                                    <div className="flex items-center justify-end gap-1">
                                                                                        <span className="text-xs">
                                                                                            {msg.channel ? msg.channel.name + ' | ' : ''}
                                                                                        </span>
                                                                                        {
                                                                                            (new Date(msg.messageDateTime) > new Date())
                                                                                                ?

                                                                                                <div className="flex items-center justify-end gap-2" >
                                                                                                    <div className="text-gray-600">
                                                                                                        Your message will be sent at
                                                                                                        &nbsp;
                                                                                                        <span className="leading-2">
                                                                                                            {
                                                                                                                convertDateFormat(
                                                                                                                    msg.messageDateTime,
                                                                                                                    "time"
                                                                                                                )}
                                                                                                        </span>
                                                                                                    </div>

                                                                                                    <Tooltip content={'Send Now'} className="p-1 text-sm" style="light">
                                                                                                        <button onClick={() => { setIsSendNowConfirmMessageOpen(true); setDeleteScheduledObject({ table: msg.tableName, id: msg.id }) }}>
                                                                                                            <VscSend className="text-lg text-green-800 " />
                                                                                                        </button>

                                                                                                    </Tooltip>
                                                                                                    <Tooltip content={'Delete'} className="p-1 text-sm" style="light">

                                                                                                        <button onClick={() => { setIsDeleteConfirmMessageOpen(true); setDeleteScheduledObject({ table: msg.tableName, id: msg.id }) }}>
                                                                                                            <RiDeleteBinLine className="text-lg text-red-500 " />
                                                                                                        </button>
                                                                                                    </Tooltip>


                                                                                                </div>
                                                                                                :
                                                                                                <div>
                                                                                                    {
                                                                                                        convertDateFormat(
                                                                                                            msg.messageDateTime,
                                                                                                            "time"
                                                                                                        )}
                                                                                                </div>
                                                                                        }
                                                                                    </div>
                                                                                </span>
                                                                            </div>
                                                                        </div>
                                                                    </div>
                                                                )
                                                                )}
                                                        </div>
                                                    )
                                                )}

                                            </div>
                                        )}
                                    </div>
                                </div>

                                <TextBox
                                    handleChange={renderChange}
                                    sendTo={current.mob}
                                />
                            </div>
                        ) : (
                            <div className="bg-[#EEEEEE] rounded-t-lg w-full h-full   p-2">
                                <div className="flex items-center justify-center h-full">
                                    <div className="hidden text-2xl text-gray-400 lg:flex lg:text-4xl md:text-4xl ">
                                        Start your conversation Here ....
                                    </div>
                                    <div className="flex items-center gap-1 text-xl text-blue-400 underline lg:hidden lg:text-4xl md:text-4xl decoration-blue-500" onClick={() => setIsListOpen(!isListOpen)}>
                                        <span>
                                            Start your conversation Here
                                        </span>
                                        <FaRegHandPointer />
                                    </div>
                                </div>
                            </div>
                        )}
                    </div>
                </div>

            </div>
            <Drawer open={isListOpen} onClose={() => setIsListOpen(!isListOpen)} className="w-5/6">
                <DrawerItems>
                    <PeopleList SelectedId={setSelectedUser} currentObject={setCurrent} />
                </DrawerItems>
            </Drawer>
            {/* {isListOpen &&
                } */}
            {isDeleteConfirmMessageOpen &&
                <ConfirmBox
                    isOpen={isDeleteConfirmMessageOpen}
                    onClose={() => setIsDeleteConfirmMessageOpen(false)} // Close the confirm box
                    onAction={handleConfirmDelete} // Handle the user's choice
                    title="Are you sure you want to delete?"
                    message="This action cannot be undone."
                    confirmText="Yes, Delete!"
                    cancelText="No, Keep It"
                    confirmColor="orange"
                    cancelColor="gray"
                />}
            {isSendNowConfirmMessageOpen &&
                <ConfirmBox
                    isOpen={isSendNowConfirmMessageOpen}
                    onClose={() => setIsSendNowConfirmMessageOpen(false)} // Close the confirm box
                    onAction={handleConfirmSendNow} // Handle the user's choice
                    title="Send Scheduled Message now"
                    message=" "
                    confirmText="Confirm"
                    cancelText="Cancel"
                    confirmColor="text-green-500"
                    confirmBtnBgColor="bg-green-100"
                    confirmBtnTextColor="text-green-600"

                    iconBackground="bg-green-100"
                    iconColor="text-green-500"
                    icon={<GrSend />}
                />}
        </div>

    );
}

export default Index;



