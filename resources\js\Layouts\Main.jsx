import { lazy, Suspense, memo, useMemo } from 'react';
import { usePage } from "@inertiajs/react";
import { LoadingSkeleton } from '@/Components/OptimizedLoader';

// Optimized lazy imports with better error boundaries
const GlobalSideBar = lazy(() =>
    import("@/Components/GlobalSideBar").catch(error => {
        console.error('Failed to load GlobalSideBar:', error);
        return { default: () => <div className="w-16 bg-gray-100 animate-pulse h-full"></div> };
    })
);

const AlertBox = lazy(() =>
    import("@/Components/HelperComponents/AlertBox").catch(error => {
        console.error('Failed to load AlertBox:', error);
        return { default: () => null };
    })
);

const NAVBar = lazy(() =>
    import("@/Components/NAVBarNew").catch(error => {
        console.error('Failed to load NAVBar:', error);
        return { default: () => <div className="h-14 bg-gray-100 animate-pulse"></div> };
    })
);

// Memoized loading component with proper fallbacks
const OptimizedLazyComponent = memo(({ Component, props, fallbackType = 'default', className = '' }) => (
    <Suspense fallback={<LoadingSkeleton type={fallbackType} className={className} />}>
        <Component {...props} />
    </Suspense>
));

// Memoized footer component to prevent unnecessary re-renders
const Footer = memo(() => (
    <div className="flex flex-col justify-end h-[6vh]">
        <div className="bg-white dark:bg-gray-700 p-2.5 mt-2 mx-2 mb-2 rounded-lg bottom-0">
            <h6 className="text-sm dark:text-white">
                Copyright © 2024 Rapbooster Cloud by Dunes Factory Pvt Ltd
            </h6>
        </div>
    </div>
));

const Main = memo(function Main({ children }) {

    const { navMenu: navItems, sideMenu: sidebar_items } = usePage().props;

    // Memoize props to prevent unnecessary re-renders
    const navBarProps = useMemo(() => ({
        items: navItems,
        sidebar_items
    }), [navItems, sidebar_items]);

    const sideBarProps = useMemo(() => ({
        sidebar_items
    }), [sidebar_items]);

    // Memoize the main content area
    const mainContent = useMemo(() => (
        <div className="flex flex-col w-[-webkit-fill-available] overflow-auto">
            <div className="overflow-auto h-[94vh]">{children}</div>
            <Footer />
        </div>
    ), [children]);

    return (
        <div className="flex h-screen">
            <div
                className="relative flex flex-col justify-between w-full h-screen overflow-auto lg:w-full dark:text-gray-400 lg:p-0 xl:w-full"
                style={{ scrollbarWidth: "none" }}
            >
                <div>
                    <OptimizedLazyComponent
                        Component={NAVBar}
                        props={navBarProps}
                        fallbackType="navbar"
                        className="h-14"
                    />

                    <div className="flex overflow-auto">
                        <OptimizedLazyComponent
                            Component={GlobalSideBar}
                            props={sideBarProps}
                            fallbackType="sidebar"
                            className="w-16"
                        />
                        {mainContent}
                    </div>

                    <OptimizedLazyComponent
                        Component={AlertBox}
                        props={{}}
                        fallbackType="default"
                    />

                </div>
            </div>
        </div>
    );
});

export default Main;
