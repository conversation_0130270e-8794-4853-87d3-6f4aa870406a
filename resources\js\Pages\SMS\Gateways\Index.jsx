import Main from "@/Layouts/Main";
import { button_custom, customDrawer, customModal, table_custom, toggle_custom } from "@/Pages/Helpers/DesignHelper";
import { Badge, Button, ButtonGroup, Checkbox, Drawer, Modal, Table, ToggleSwitch } from "flowbite-react";
import { useState } from "react";
import { IoMdAdd } from "react-icons/io";
import { MdDeleteOutline, MdOutlineRemoveRedEye } from "react-icons/md";
import { PiCaretUpDownBold } from "react-icons/pi";
import { RiPagesLine } from "react-icons/ri";
import { TbColumns3, TbSettingsCode } from "react-icons/tb";
import TabBar from "../TabBar";
import Add from "./Add";
import GatewayDetails from "./GatewayDetails";

export default function Gateways() {
    // ----------------Edit --------------------
    // const [isOpen, setIsOpen] = useState(false);
    // const handleClose = () => setIsOpen(false);
    const [switch2, setSwitch2] = useState(false);
    const [isOpenDetail, setIsOpenDetail] = useState(false);
    // const [openModal, setOpenModal] = useState(false);
    const [isAddOpen, setIsAddOpen] = useState(false);


    return (
        <Main>
            <div className="p-2 overflow-hidden">
                <TabBar />
                <div className="pb-2 rounded-lg">
                    <div className="h-fit bg-white rounded border overflow-auto w-full mt-2.5">
                        <div className="flex justify-between p-2">
                            <ButtonGroup className="">
                                <Button
                                    className=""
                                    size="xs"
                                    color="gray"
                                    theme={button_custom}
                                // onClick={deleteRecords}
                                >
                                    <div className="flex items-center gap-1">
                                        <MdDeleteOutline className="text-slate-500" />
                                        <span className="text-xs">Delete</span>
                                    </div>
                                </Button>
                                <Button
                                    theme={button_custom}
                                    size="xs"
                                    color="gray"
                                    id="dropdownInformationButton"
                                    data-dropdown-toggle="dropdownNotification"
                                    type="button"
                                >
                                    <div className="flex items-center gap-1 text-xs">
                                        <TbColumns3 className="text-slate-500 ms-1" />
                                        <span className="text-xs">
                                            Columns
                                        </span>
                                    </div>
                                </Button>

                            </ButtonGroup>
                            <div className="">
                                <Button
                                    className="pe-1"
                                    size="xs"
                                    color="gray"
                                    theme={button_custom}
                                    onClick={() => setIsAddOpen(true)}
                                >
                                    <div className="flex items-center gap-1">
                                        <IoMdAdd className="text-xs text-slate-500" />
                                        <span className="text-xs">Add</span>
                                    </div>
                                </Button>
                                {/* Dropdown menu */}
                                <div
                                    id="dropdownNotification"
                                    className="z-20 hidden w-full max-w-sm bg-white border divide-y divide-gray-100 rounded-lg shadow-lg dark:bg-gray-800 dark:divide-gray-700"
                                    aria-labelledby="dropdownNotificationButton"
                                >
                                    <div className="block px-4 py-2 font-medium text-center text-gray-700 rounded-t-lg bg-gray-50 dark:bg-gray-800 dark:text-white">
                                        <div className="flex justify-between">
                                            <div className="">
                                                <div className="flex items-center gap-1 p-1 text-gray-400 group">
                                                    <svg
                                                        xmlns="http://www.w3.org/2000/svg"
                                                        className=" fill-slate-400"
                                                        height="24px"
                                                        viewBox="0 0 24 24"
                                                        width="24px"
                                                    >
                                                        <rect
                                                            fill="none"
                                                            height="24"
                                                            width="24"
                                                        />
                                                        <path d="M20,4H4C2.9,4,2,4.9,2,6v12c0,1.1,0.9,2,2,2h16c1.1,0,2-0.9,2-2V6C22,4.9,21.1,4,20,4z M8,18H4V6h4V18z M14,18h-4V6h4V18z M20,18h-4V6h4V18z" />
                                                    </svg>
                                                    Column
                                                </div>
                                            </div>
                                            <div className="">
                                                <button className="flex p-1 text-blue-600">
                                                    <svg
                                                        xmlns="http://www.w3.org/2000/svg"
                                                        height="24px"
                                                        viewBox="0 0 24 24"
                                                        width="24px"
                                                        className="fill-blue-600"
                                                    >
                                                        <g>
                                                            <path
                                                                d="M0,0h24v24H0V0z"
                                                                fill="none"
                                                            />
                                                        </g>
                                                        <g>
                                                            <g>
                                                                <path d="M6,13c0-1.65,0.67-3.15,1.76-4.24L6.34,7.34C4.9,8.79,4,10.79,4,13c0,4.08,3.05,7.44,7,7.93v-2.02 C8.17,18.43,6,15.97,6,13z M20,13c0-4.42-3.58-8-8-8c-0.06,0-0.12,0.01-0.18,0.01l1.09-1.09L11.5,2.5L8,6l3.5,3.5l1.41-1.41 l-1.08-1.08C11.89,7.01,11.95,7,12,7c3.31,0,6,2.69,6,6c0,2.97-2.17,5.43-5,5.91v2.02C16.95,20.44,20,17.08,20,13z" />
                                                            </g>
                                                        </g>
                                                    </svg>

                                                    noy
                                                </button>
                                            </div>
                                        </div>
                                    </div>
                                    <div className="divide-y divide-gray-100 dark:divide-gray-700">
                                        <div className="flex flex-col">
                                            <div className="relative overflow-x-auto shadow-md sm:rounded-lg ">
                                                <table className="w-full text-sm text-left text-gray-500 rtl:text-right dark:text-gray-400">
                                                    <thead className="text-xs text-gray-700 uppercase dark:text-gray-400">
                                                        <tr>
                                                            <th className="p-2 text-center bg-gray-50 dark:bg-gray-800 w-14 ">
                                                                List
                                                            </th>
                                                            <th className="p-2 text-center bg-gray-50 dark:bg-gray-800 w-14">
                                                                Details
                                                            </th>
                                                            <th className="p-2"></th>
                                                        </tr>
                                                    </thead>
                                                    <tbody className="overflow-auto ">
                                                        <tr className="border-b border-gray-200 dark:border-gray-700">
                                                            <td className="p-2 font-medium text-center text-gray-900 max-w-max whitespace-nowrap bg-gray-50 dark:text-white dark:bg-gray-800">
                                                                <Checkbox></Checkbox>
                                                            </td>
                                                            <td className="p-2 font-medium text-center text-gray-900 whitespace-nowrap bg-gray-50 dark:text-white dark:bg-gray-800">
                                                                <Checkbox></Checkbox>
                                                            </td>
                                                            <td className="px-6 py-4 max-w-max">
                                                                Name
                                                            </td>
                                                        </tr>
                                                        <tr className="border-b border-gray-200 dark:border-gray-700">
                                                            <td className="p-2 font-medium text-center text-gray-900 max-w-max whitespace-nowrap bg-gray-50 dark:text-white dark:bg-gray-800">
                                                                <Checkbox></Checkbox>
                                                            </td>
                                                            <td className="p-2 font-medium text-center text-gray-900 whitespace-nowrap bg-gray-50 dark:text-white dark:bg-gray-800">
                                                                <Checkbox></Checkbox>
                                                            </td>
                                                            <td className="px-6 py-4 max-w-max">
                                                                Name
                                                            </td>
                                                        </tr>
                                                        <tr className="border-b border-gray-200 dark:border-gray-700">
                                                            <td className="p-2 font-medium text-center text-gray-900 max-w-max whitespace-nowrap bg-gray-50 dark:text-white dark:bg-gray-800">
                                                                <Checkbox></Checkbox>
                                                            </td>
                                                            <td className="p-2 font-medium text-center text-gray-900 whitespace-nowrap bg-gray-50 dark:text-white dark:bg-gray-800">
                                                                <Checkbox></Checkbox>
                                                            </td>
                                                            <td className="px-6 py-4 max-w-max">
                                                                Name
                                                            </td>
                                                        </tr>
                                                        <tr className="border-b border-gray-200 dark:border-gray-700">
                                                            <td className="p-2 font-medium text-center text-gray-900 max-w-max whitespace-nowrap bg-gray-50 dark:text-white dark:bg-gray-800">
                                                                <Checkbox></Checkbox>
                                                            </td>
                                                            <td className="p-2 font-medium text-center text-gray-900 whitespace-nowrap bg-gray-50 dark:text-white dark:bg-gray-800">
                                                                <Checkbox></Checkbox>
                                                            </td>
                                                            <td className="px-6 py-4 max-w-max">
                                                                Name
                                                            </td>
                                                        </tr>
                                                    </tbody>
                                                </table>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div className="h-full">
                            <div className="overflow-x-auto bg-white border rounded-lg text-nowrap">
                                <Table theme={table_custom}>
                                    <Table.Head>
                                        <Table.HeadCell>
                                            <Checkbox
                                                color="blue"
                                                defaultChecked
                                            />
                                        </Table.HeadCell>
                                        <Table.HeadCell>
                                            <div className="flex items-center justify-between gap-2">
                                                <h3>Last Active</h3>
                                                <PiCaretUpDownBold />
                                            </div>
                                        </Table.HeadCell>
                                        <Table.HeadCell>
                                            <div className="flex items-center justify-between gap-2">
                                                <h3>Gateway Name</h3>
                                                <PiCaretUpDownBold />
                                            </div>
                                        </Table.HeadCell>
                                        <Table.HeadCell>
                                            Delay
                                        </Table.HeadCell>
                                        {/* <Table.HeadCell>
                                        </Table.HeadCell> */}
                                        <Table.HeadCell>
                                            <div className="flex items-center justify-between gap-2">
                                                <h3>SIMs Linked</h3>
                                                <PiCaretUpDownBold />
                                            </div>
                                        </Table.HeadCell>
                                        <Table.HeadCell>Status</Table.HeadCell>
                                        <Table.HeadCell>Save Chat</Table.HeadCell>
                                        <Table.HeadCell>
                                            <div className="flex items-center justify-between gap-2">
                                                <h3>Device Name</h3>
                                                <PiCaretUpDownBold />
                                            </div>
                                        </Table.HeadCell>
                                        <Table.HeadCell>
                                            <div className="flex items-center justify-between gap-2">
                                                <h3>Android Version</h3>
                                                <PiCaretUpDownBold />
                                            </div>
                                        </Table.HeadCell>
                                        <Table.HeadCell>
                                            Active
                                        </Table.HeadCell>
                                        <Table.HeadCell>Action</Table.HeadCell>
                                    </Table.Head>
                                    <Table.Body className="divide-y">
                                        <Table.Row>
                                            <Table.Cell>
                                                <Checkbox
                                                    color={"blue"}
                                                    className="rowCheckBox"
                                                // id={channel.id}
                                                // onChange={getCheckedIds}
                                                />
                                            </Table.Cell>
                                            <Table.Cell className="font-medium text-gray-900 whitespace-nowrap dark:text-white">
                                                <div className="text-blue-600">28/03/2024</div>
                                                <span className="text-xs font-normal">
                                                    02:45 PM
                                                </span>
                                            </Table.Cell>
                                            <Table.Cell>Gateway 1</Table.Cell>
                                            <Table.Cell>13</Table.Cell>
                                            {/* <Table.Cell>-</Table.Cell> */}
                                            <Table.Cell>
                                                2
                                            </Table.Cell>
                                            <Table.Cell> <div className="w-fit">
                                                <Badge
                                                    color={"info"}
                                                    className="capitalize"
                                                >
                                                    Close
                                                </Badge>
                                            </div>
                                            </Table.Cell>
                                            <Table.Cell>
                                                <div className="flex items-center gap-2">
                                                    <ToggleSwitch theme={toggle_custom} color="blue" sizing="xs" checked={switch2} onChange={setSwitch2} />
                                                    <Button theme={button_custom} size="xs" color="blue" className="">
                                                        <div className="flex items-center gap-1">
                                                            <MdOutlineRemoveRedEye className="text-sm" />
                                                            <span className="text-xs">View</span>
                                                        </div>
                                                    </Button>
                                                </div>
                                            </Table.Cell>
                                            <Table.Cell>2 / Minute</Table.Cell>
                                            <Table.Cell>2 / Minute</Table.Cell>
                                            <Table.Cell>2 / Minute</Table.Cell>
                                            <Table.Cell className="flex gap-3">
                                                <Button
                                                    theme={button_custom}
                                                    color="success"
                                                    size="xs"
                                                >
                                                    <TbSettingsCode className="text-sm" />
                                                    <span className="text-xs ms-1">
                                                        Configure
                                                    </span>
                                                </Button>
                                                <Button size="xs" color="purple"
                                                    onClick={() => setIsOpenDetail(true)} theme={button_custom} >
                                                    <MdOutlineRemoveRedEye className="text-sm" />
                                                    <span className="text-xs ms-1">
                                                        Details
                                                    </span>
                                                </Button>
                                            </Table.Cell>
                                        </Table.Row>
                                    </Table.Body>
                                </Table>
                            </div>
                        </div>
                    </div>
                </div>
                {/* </div> */}
                {/* </div> */}

                <Drawer
                    theme={customDrawer}
                    className="w-full lg:w-5/6 md:w-4/5"
                    open={isOpenDetail}
                    onClose={() => setIsOpenDetail(false)}
                    position="right"
                >
                    <Drawer.Header titleIcon={RiPagesLine} title={"Gateway Details"} />
                    <Drawer.Items className="py-2">
                        <GatewayDetails />
                    </Drawer.Items>
                </Drawer>
                {/* Add Gateway */}
                <Modal theme={customModal} show={isAddOpen} onClose={() => setIsAddOpen(false)}>
                    <Modal.Header>Add Your Android Phone</Modal.Header>
                    <Modal.Body>
                        <Add />

                    </Modal.Body>
                </Modal>
            </div>
        </Main>
    );
}


